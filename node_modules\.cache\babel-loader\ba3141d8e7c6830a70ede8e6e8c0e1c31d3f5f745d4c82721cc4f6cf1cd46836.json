{"ast": null, "code": "import * as React from \"react\";\nfunction EnvironmentOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EnvironmentOutline-EnvironmentOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EnvironmentOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EnvironmentOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,3.5 C33.9411255,3.5 42,11.5788332 42,21.5445791 C42,25.9993411 40.3897111,30.0770685 37.7208235,33.2247049 L37.2250176,33.7978077 C35.2208664,36.0735085 31.7308408,39.6031944 26.7549407,44.3868657 C25.2643222,45.8198986 22.9549789,45.8692779 21.4079378,44.5350287 L21.2451724,44.3867789 L19.1467394,42.3579922 C14.8756818,38.2034745 11.9204384,35.1597658 10.2810094,33.2268662 C7.61102615,30.0789353 6,26.0003608 6,21.5445791 C6,11.5788332 14.0588745,3.5 24,3.5 Z M24,6.5 C15.7093624,6.5 9,13.2395436 9,21.5386187 C9,25.1530011 10.2723798,28.5634094 12.5573493,31.2628393 L12.8664218,31.6228801 C13.9827463,32.9066746 15.6545139,34.6660948 17.8732465,36.8907091 L18.5552667,37.5720388 C19.7192871,38.7308133 21.0198367,40.0056254 22.4558558,41.3951714 L23.3113705,42.2211339 C23.6973221,42.592947 24.3028472,42.5929565 24.6888103,42.2211556 L26.3185495,40.6444516 C30.771579,36.3165234 33.8245067,33.1750973 35.4441776,31.2610351 C37.7282017,28.561867 39,25.1521808 39,21.5386187 C39,13.2395436 32.2906376,6.5 24,6.5 Z M24,14 C28.418278,14 32,17.581722 32,22 C32,26.418278 28.418278,30 24,30 C19.581722,30 16,26.418278 16,22 C16,17.581722 19.581722,14 24,14 Z M24,17 C21.2385763,17 19,19.2385763 19,22 C19,24.7614237 21.2385763,27 24,27 C26.7614237,27 29,24.7614237 29,22 C29,19.2385763 26.7614237,17 24,17 Z\",\n    id: \"EnvironmentOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default EnvironmentOutline;", "map": {"version": 3, "names": ["React", "EnvironmentOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/EnvironmentOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction EnvironmentOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EnvironmentOutline-EnvironmentOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"EnvironmentOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"EnvironmentOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,3.5 C33.9411255,3.5 42,11.5788332 42,21.5445791 C42,25.9993411 40.3897111,30.0770685 37.7208235,33.2247049 L37.2250176,33.7978077 C35.2208664,36.0735085 31.7308408,39.6031944 26.7549407,44.3868657 C25.2643222,45.8198986 22.9549789,45.8692779 21.4079378,44.5350287 L21.2451724,44.3867789 L19.1467394,42.3579922 C14.8756818,38.2034745 11.9204384,35.1597658 10.2810094,33.2268662 C7.61102615,30.0789353 6,26.0003608 6,21.5445791 C6,11.5788332 14.0588745,3.5 24,3.5 Z M24,6.5 C15.7093624,6.5 9,13.2395436 9,21.5386187 C9,25.1530011 10.2723798,28.5634094 12.5573493,31.2628393 L12.8664218,31.6228801 C13.9827463,32.9066746 15.6545139,34.6660948 17.8732465,36.8907091 L18.5552667,37.5720388 C19.7192871,38.7308133 21.0198367,40.0056254 22.4558558,41.3951714 L23.3113705,42.2211339 C23.6973221,42.592947 24.3028472,42.5929565 24.6888103,42.2211556 L26.3185495,40.6444516 C30.771579,36.3165234 33.8245067,33.1750973 35.4441776,31.2610351 C37.7282017,28.561867 39,25.1521808 39,21.5386187 C39,13.2395436 32.2906376,6.5 24,6.5 Z M24,14 C28.418278,14 32,17.581722 32,22 C32,26.418278 28.418278,30 24,30 C19.581722,30 16,26.418278 16,22 C16,17.581722 19.581722,14 24,14 Z M24,17 C21.2385763,17 19,19.2385763 19,22 C19,24.7614237 21.2385763,27 24,27 C26.7614237,27 29,24.7614237 29,22 C29,19.2385763 26.7614237,17 24,17 Z\",\n    id: \"EnvironmentOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default EnvironmentOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uCAAuC;IAC3CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,kyCAAkyC;IACryCR,EAAE,EAAE,6CAA6C;IACjDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}