{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function (event) {\n      var targets = Array.isArray(target) ? target : [target];\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n      onClickAwayRef.current(event);\n    };\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}