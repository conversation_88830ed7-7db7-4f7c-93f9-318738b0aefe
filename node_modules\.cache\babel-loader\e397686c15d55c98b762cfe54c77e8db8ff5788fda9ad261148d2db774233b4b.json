{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction } from '../utils';\nvar useSetState = function (initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setMergeState = useMemoizedFn(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  });\n  return [state, setMergeState];\n};\nexport default useSetState;", "map": {"version": 3, "names": ["__assign", "__read", "useState", "useMemoizedFn", "isFunction", "useSetState", "initialState", "_a", "state", "setState", "setMergeState", "patch", "prevState", "newState"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useSetState/index.js"], "sourcesContent": ["import { __assign, __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction } from '../utils';\nvar useSetState = function (initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setMergeState = useMemoizedFn(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  });\n  return [state, setMergeState];\n};\nexport default useSetState;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,UAAU,QAAQ,UAAU;AACrC,IAAIC,WAAW,GAAG,SAAAA,CAAUC,YAAY,EAAE;EACxC,IAAIC,EAAE,GAAGN,MAAM,CAACC,QAAQ,CAACI,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCE,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,aAAa,GAAGP,aAAa,CAAC,UAAUQ,KAAK,EAAE;IACjDF,QAAQ,CAAC,UAAUG,SAAS,EAAE;MAC5B,IAAIC,QAAQ,GAAGT,UAAU,CAACO,KAAK,CAAC,GAAGA,KAAK,CAACC,SAAS,CAAC,GAAGD,KAAK;MAC3D,OAAOE,QAAQ,GAAGb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEY,SAAS,CAAC,EAAEC,QAAQ,CAAC,GAAGD,SAAS;IAC3E,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,CAACJ,KAAK,EAAEE,aAAa,CAAC;AAC/B,CAAC;AACD,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}