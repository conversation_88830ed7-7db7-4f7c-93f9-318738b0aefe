{"ast": null, "code": "import { useClickAway } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { cloneElement, forwardRef, isValidElement, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Popup from '../popup';\nimport { defaultPopupBaseProps } from '../popup/popup-base-props';\nimport { IconContext } from './context';\nimport { ItemChildrenWrap } from './item';\nconst classPrefix = `adm-dropdown`;\nconst defaultProps = {\n  defaultActiveKey: null,\n  closeOnMaskClick: true,\n  closeOnClickAway: false,\n  getContainer: defaultPopupBaseProps['getContainer']\n};\nconst Dropdown = forwardRef((props, ref) => {\n  const {\n    dropdown: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const arrowIcon = mergeProp(componentConfig.arrowIcon, props.arrow, props.arrowIcon);\n  const [value, setValue] = usePropsValue({\n    value: mergedProps.activeKey,\n    defaultValue: mergedProps.defaultActiveKey,\n    onChange: mergedProps.onChange\n  });\n  const navRef = useRef(null);\n  const contentRef = useRef(null);\n  // 点击外部区域，关闭\n  useClickAway(() => {\n    if (!mergedProps.closeOnClickAway) return;\n    setValue(null);\n  }, [navRef, contentRef]);\n  // 计算 navs 的 top 值\n  const [top, setTop] = useState();\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (value) {\n      const rect = container.getBoundingClientRect();\n      setTop(rect.bottom);\n    }\n  }, [value]);\n  const changeActive = key => {\n    if (value === key) {\n      setValue(null);\n    } else {\n      setValue(key);\n    }\n  };\n  let popupForceRender = false;\n  const items = [];\n  const navs = React.Children.map(mergedProps.children, child => {\n    if (isValidElement(child)) {\n      const childProps = Object.assign(Object.assign({}, child.props), {\n        onClick: event => {\n          var _a, _b;\n          changeActive(child.key);\n          (_b = (_a = child.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n        },\n        active: child.key === value\n      });\n      items.push(child);\n      if (child.props.forceRender) popupForceRender = true;\n      return cloneElement(child, childProps);\n    } else {\n      return child;\n    }\n  });\n  useImperativeHandle(ref, () => ({\n    close: () => {\n      setValue(null);\n    }\n  }), [setValue]);\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-open`]: !!value\n    }),\n    ref: containerRef\n  }, React.createElement(IconContext.Provider, {\n    value: arrowIcon\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-nav`,\n    ref: navRef\n  }, navs)), React.createElement(Popup, {\n    visible: !!value,\n    position: 'top',\n    getContainer: mergedProps.getContainer,\n    className: `${classPrefix}-popup`,\n    maskClassName: `${classPrefix}-popup-mask`,\n    bodyClassName: `${classPrefix}-popup-body`,\n    style: {\n      top\n    },\n    forceRender: popupForceRender,\n    onMaskClick: mergedProps.closeOnMaskClick ? () => {\n      changeActive(null);\n    } : undefined\n  }, React.createElement(\"div\", {\n    ref: contentRef\n  }, items.map(item => {\n    const isActive = item.key === value;\n    return React.createElement(ItemChildrenWrap, {\n      key: item.key,\n      active: isActive,\n      forceRender: item.props.forceRender,\n      destroyOnClose: item.props.destroyOnClose\n    }, item.props.children);\n  })))));\n});\nexport default Dropdown;", "map": {"version": 3, "names": ["useClickAway", "classNames", "React", "cloneElement", "forwardRef", "isValidElement", "useEffect", "useImperativeHandle", "useRef", "useState", "withNativeProps", "usePropsValue", "mergeProp", "mergeProps", "useConfig", "Popup", "defaultPopupBaseProps", "IconContext", "ItemC<PERSON>dren<PERSON>", "classPrefix", "defaultProps", "defaultActiveKey", "closeOnMaskClick", "closeOnClickAway", "getContainer", "Dropdown", "props", "ref", "dropdown", "componentConfig", "mergedProps", "arrowIcon", "arrow", "value", "setValue", "active<PERSON><PERSON>", "defaultValue", "onChange", "navRef", "contentRef", "top", "setTop", "containerRef", "container", "current", "rect", "getBoundingClientRect", "bottom", "changeActive", "key", "popupForceRender", "items", "navs", "Children", "map", "children", "child", "childProps", "Object", "assign", "onClick", "event", "_a", "_b", "call", "active", "push", "forceRender", "close", "createElement", "className", "Provider", "visible", "position", "maskClassName", "bodyClassName", "style", "onMaskClick", "undefined", "item", "isActive", "destroyOnClose"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/dropdown/dropdown.js"], "sourcesContent": ["import { useClickAway } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { cloneElement, forwardRef, isValidElement, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Popup from '../popup';\nimport { defaultPopupBaseProps } from '../popup/popup-base-props';\nimport { IconContext } from './context';\nimport { ItemChildrenWrap } from './item';\nconst classPrefix = `adm-dropdown`;\nconst defaultProps = {\n  defaultActiveKey: null,\n  closeOnMaskClick: true,\n  closeOnClickAway: false,\n  getContainer: defaultPopupBaseProps['getContainer']\n};\nconst Dropdown = forwardRef((props, ref) => {\n  const {\n    dropdown: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const arrowIcon = mergeProp(componentConfig.arrowIcon, props.arrow, props.arrowIcon);\n  const [value, setValue] = usePropsValue({\n    value: mergedProps.activeKey,\n    defaultValue: mergedProps.defaultActiveKey,\n    onChange: mergedProps.onChange\n  });\n  const navRef = useRef(null);\n  const contentRef = useRef(null);\n  // 点击外部区域，关闭\n  useClickAway(() => {\n    if (!mergedProps.closeOnClickAway) return;\n    setValue(null);\n  }, [navRef, contentRef]);\n  // 计算 navs 的 top 值\n  const [top, setTop] = useState();\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n    if (value) {\n      const rect = container.getBoundingClientRect();\n      setTop(rect.bottom);\n    }\n  }, [value]);\n  const changeActive = key => {\n    if (value === key) {\n      setValue(null);\n    } else {\n      setValue(key);\n    }\n  };\n  let popupForceRender = false;\n  const items = [];\n  const navs = React.Children.map(mergedProps.children, child => {\n    if (isValidElement(child)) {\n      const childProps = Object.assign(Object.assign({}, child.props), {\n        onClick: event => {\n          var _a, _b;\n          changeActive(child.key);\n          (_b = (_a = child.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n        },\n        active: child.key === value\n      });\n      items.push(child);\n      if (child.props.forceRender) popupForceRender = true;\n      return cloneElement(child, childProps);\n    } else {\n      return child;\n    }\n  });\n  useImperativeHandle(ref, () => ({\n    close: () => {\n      setValue(null);\n    }\n  }), [setValue]);\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-open`]: !!value\n    }),\n    ref: containerRef\n  }, React.createElement(IconContext.Provider, {\n    value: arrowIcon\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-nav`,\n    ref: navRef\n  }, navs)), React.createElement(Popup, {\n    visible: !!value,\n    position: 'top',\n    getContainer: mergedProps.getContainer,\n    className: `${classPrefix}-popup`,\n    maskClassName: `${classPrefix}-popup-mask`,\n    bodyClassName: `${classPrefix}-popup-body`,\n    style: {\n      top\n    },\n    forceRender: popupForceRender,\n    onMaskClick: mergedProps.closeOnMaskClick ? () => {\n      changeActive(null);\n    } : undefined\n  }, React.createElement(\"div\", {\n    ref: contentRef\n  }, items.map(item => {\n    const isActive = item.key === value;\n    return React.createElement(ItemChildrenWrap, {\n      key: item.key,\n      active: isActive,\n      forceRender: item.props.forceRender,\n      destroyOnClose: item.props.destroyOnClose\n    }, item.props.children);\n  })))));\n});\nexport default Dropdown;"], "mappings": "AAAA,SAASA,YAAY,QAAQ,QAAQ;AACrC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,YAAY,EAAEC,UAAU,EAAEC,cAAc,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzH,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,gBAAgB,QAAQ,QAAQ;AACzC,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,YAAY,GAAG;EACnBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,IAAI;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAER,qBAAqB,CAAC,cAAc;AACpD,CAAC;AACD,MAAMS,QAAQ,GAAGrB,UAAU,CAAC,CAACsB,KAAK,EAAEC,GAAG,KAAK;EAC1C,MAAM;IACJC,QAAQ,EAAEC,eAAe,GAAG,CAAC;EAC/B,CAAC,GAAGf,SAAS,CAAC,CAAC;EACf,MAAMgB,WAAW,GAAGjB,UAAU,CAACO,YAAY,EAAES,eAAe,EAAEH,KAAK,CAAC;EACpE,MAAMK,SAAS,GAAGnB,SAAS,CAACiB,eAAe,CAACE,SAAS,EAAEL,KAAK,CAACM,KAAK,EAAEN,KAAK,CAACK,SAAS,CAAC;EACpF,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,aAAa,CAAC;IACtCsB,KAAK,EAAEH,WAAW,CAACK,SAAS;IAC5BC,YAAY,EAAEN,WAAW,CAACT,gBAAgB;IAC1CgB,QAAQ,EAAEP,WAAW,CAACO;EACxB,CAAC,CAAC;EACF,MAAMC,MAAM,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM+B,UAAU,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC/B;EACAR,YAAY,CAAC,MAAM;IACjB,IAAI,CAAC8B,WAAW,CAACP,gBAAgB,EAAE;IACnCW,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,EAAE,CAACI,MAAM,EAAEC,UAAU,CAAC,CAAC;EACxB;EACA,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EAChC,MAAMiC,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACjCF,SAAS,CAAC,MAAM;IACd,MAAMqC,SAAS,GAAGD,YAAY,CAACE,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAChB,IAAIV,KAAK,EAAE;MACT,MAAMY,IAAI,GAAGF,SAAS,CAACG,qBAAqB,CAAC,CAAC;MAC9CL,MAAM,CAACI,IAAI,CAACE,MAAM,CAAC;IACrB;EACF,CAAC,EAAE,CAACd,KAAK,CAAC,CAAC;EACX,MAAMe,YAAY,GAAGC,GAAG,IAAI;IAC1B,IAAIhB,KAAK,KAAKgB,GAAG,EAAE;MACjBf,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,MAAM;MACLA,QAAQ,CAACe,GAAG,CAAC;IACf;EACF,CAAC;EACD,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAMC,IAAI,GAAGlD,KAAK,CAACmD,QAAQ,CAACC,GAAG,CAACxB,WAAW,CAACyB,QAAQ,EAAEC,KAAK,IAAI;IAC7D,IAAInD,cAAc,CAACmD,KAAK,CAAC,EAAE;MACzB,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC9B,KAAK,CAAC,EAAE;QAC/DkC,OAAO,EAAEC,KAAK,IAAI;UAChB,IAAIC,EAAE,EAAEC,EAAE;UACVf,YAAY,CAACQ,KAAK,CAACP,GAAG,CAAC;UACvB,CAACc,EAAE,GAAG,CAACD,EAAE,GAAGN,KAAK,CAAC9B,KAAK,EAAEkC,OAAO,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACF,EAAE,EAAED,KAAK,CAAC;QAC3F,CAAC;QACDI,MAAM,EAAET,KAAK,CAACP,GAAG,KAAKhB;MACxB,CAAC,CAAC;MACFkB,KAAK,CAACe,IAAI,CAACV,KAAK,CAAC;MACjB,IAAIA,KAAK,CAAC9B,KAAK,CAACyC,WAAW,EAAEjB,gBAAgB,GAAG,IAAI;MACpD,OAAO/C,YAAY,CAACqD,KAAK,EAAEC,UAAU,CAAC;IACxC,CAAC,MAAM;MACL,OAAOD,KAAK;IACd;EACF,CAAC,CAAC;EACFjD,mBAAmB,CAACoB,GAAG,EAAE,OAAO;IAC9ByC,KAAK,EAAEA,CAAA,KAAM;MACXlC,QAAQ,CAAC,IAAI,CAAC;IAChB;EACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACf,OAAOxB,eAAe,CAACoB,WAAW,EAAE5B,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE;IAC7DC,SAAS,EAAErE,UAAU,CAACkB,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,OAAO,GAAG,CAAC,CAACc;IAC7B,CAAC,CAAC;IACFN,GAAG,EAAEe;EACP,CAAC,EAAExC,KAAK,CAACmE,aAAa,CAACpD,WAAW,CAACsD,QAAQ,EAAE;IAC3CtC,KAAK,EAAEF;EACT,CAAC,EAAE7B,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGnD,WAAW,MAAM;IAC/BQ,GAAG,EAAEW;EACP,CAAC,EAAEc,IAAI,CAAC,CAAC,EAAElD,KAAK,CAACmE,aAAa,CAACtD,KAAK,EAAE;IACpCyD,OAAO,EAAE,CAAC,CAACvC,KAAK;IAChBwC,QAAQ,EAAE,KAAK;IACfjD,YAAY,EAAEM,WAAW,CAACN,YAAY;IACtC8C,SAAS,EAAE,GAAGnD,WAAW,QAAQ;IACjCuD,aAAa,EAAE,GAAGvD,WAAW,aAAa;IAC1CwD,aAAa,EAAE,GAAGxD,WAAW,aAAa;IAC1CyD,KAAK,EAAE;MACLpC;IACF,CAAC;IACD2B,WAAW,EAAEjB,gBAAgB;IAC7B2B,WAAW,EAAE/C,WAAW,CAACR,gBAAgB,GAAG,MAAM;MAChD0B,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,GAAG8B;EACN,CAAC,EAAE5E,KAAK,CAACmE,aAAa,CAAC,KAAK,EAAE;IAC5B1C,GAAG,EAAEY;EACP,CAAC,EAAEY,KAAK,CAACG,GAAG,CAACyB,IAAI,IAAI;IACnB,MAAMC,QAAQ,GAAGD,IAAI,CAAC9B,GAAG,KAAKhB,KAAK;IACnC,OAAO/B,KAAK,CAACmE,aAAa,CAACnD,gBAAgB,EAAE;MAC3C+B,GAAG,EAAE8B,IAAI,CAAC9B,GAAG;MACbgB,MAAM,EAAEe,QAAQ;MAChBb,WAAW,EAAEY,IAAI,CAACrD,KAAK,CAACyC,WAAW;MACnCc,cAAc,EAAEF,IAAI,CAACrD,KAAK,CAACuD;IAC7B,CAAC,EAAEF,IAAI,CAACrD,KAAK,CAAC6B,QAAQ,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AACF,eAAe9B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}