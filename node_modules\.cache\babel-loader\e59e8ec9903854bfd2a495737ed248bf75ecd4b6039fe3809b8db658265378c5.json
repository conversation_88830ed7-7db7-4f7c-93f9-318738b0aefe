{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from \"../util/motion\";\nexport default (function (onInternalMotionEnd) {\n  var cacheElementRef = useRef();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});", "map": {"version": 3, "names": ["React", "useRef", "animationEndName", "transitionEndName", "onInternalMotionEnd", "cacheElementRef", "removeMotionEvents", "element", "removeEventListener", "patchMotionEvents", "current", "addEventListener", "useEffect"], "sources": ["C:/Users/<USER>/node_modules/rc-motion/es/hooks/useDomMotionEvents.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from \"../util/motion\";\nexport default (function (onInternalMotionEnd) {\n  var cacheElementRef = useRef();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,OAAO;AAC9B,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,gBAAgB;AACpE,gBAAgB,UAAUC,mBAAmB,EAAE;EAC7C,IAAIC,eAAe,GAAGJ,MAAM,CAAC,CAAC;;EAE9B;EACA,SAASK,kBAAkBA,CAACC,OAAO,EAAE;IACnC,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACC,mBAAmB,CAACL,iBAAiB,EAAEC,mBAAmB,CAAC;MACnEG,OAAO,CAACC,mBAAmB,CAACN,gBAAgB,EAAEE,mBAAmB,CAAC;IACpE;EACF;;EAEA;EACA,SAASK,iBAAiBA,CAACF,OAAO,EAAE;IAClC,IAAIF,eAAe,CAACK,OAAO,IAAIL,eAAe,CAACK,OAAO,KAAKH,OAAO,EAAE;MAClED,kBAAkB,CAACD,eAAe,CAACK,OAAO,CAAC;IAC7C;IACA,IAAIH,OAAO,IAAIA,OAAO,KAAKF,eAAe,CAACK,OAAO,EAAE;MAClDH,OAAO,CAACI,gBAAgB,CAACR,iBAAiB,EAAEC,mBAAmB,CAAC;MAChEG,OAAO,CAACI,gBAAgB,CAACT,gBAAgB,EAAEE,mBAAmB,CAAC;;MAE/D;MACAC,eAAe,CAACK,OAAO,GAAGH,OAAO;IACnC;EACF;;EAEA;EACAP,KAAK,CAACY,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBN,kBAAkB,CAACD,eAAe,CAACK,OAAO,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACD,iBAAiB,EAAEH,kBAAkB,CAAC;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}