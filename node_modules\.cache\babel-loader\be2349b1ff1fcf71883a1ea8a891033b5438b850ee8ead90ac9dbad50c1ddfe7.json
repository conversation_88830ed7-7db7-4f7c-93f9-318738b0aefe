{"ast": null, "code": "export function toCSSLength(val) {\n  return typeof val === 'number' ? `${val}px` : val;\n}", "map": {"version": 3, "names": ["toCS<PERSON><PERSON>th", "val"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/to-css-length.js"], "sourcesContent": ["export function toCSSLength(val) {\n  return typeof val === 'number' ? `${val}px` : val;\n}"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,GAAG,EAAE;EAC/B,OAAO,OAAOA,GAAG,KAAK,QAAQ,GAAG,GAAGA,GAAG,IAAI,GAAGA,GAAG;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}