{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';\nimport { MinusOutline, AddOutline } from 'antd-mobile-icons';\nimport { useMergedState } from 'rc-util';\nimport getMiniDecimal, { toFixed } from '@rc-component/mini-decimal';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Input from '../input';\nimport Button from '../button';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-stepper`;\nconst defaultProps = {\n  step: 1,\n  disabled: false,\n  allowEmpty: false\n};\nexport function InnerStepper(p, ref) {\n  const props = mergeProps(defaultProps, p);\n  const {\n    defaultValue = 0,\n    value,\n    onChange,\n    disabled,\n    step,\n    max,\n    min,\n    inputReadOnly,\n    digits,\n    stringMode,\n    formatter,\n    parser\n  } = props;\n  const {\n    locale\n  } = useConfig();\n  // ========================== Ref ==========================\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      var _a, _b;\n      return (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement) !== null && _b !== void 0 ? _b : null;\n    }\n  }));\n  // ========================== Parse / Format ==========================\n  const fixedValue = value => {\n    const fixedValue = digits !== undefined ? toFixed(value.toString(), '.', digits) : value;\n    return fixedValue.toString();\n  };\n  const getValueAsType = value => stringMode ? value.toString() : value.toNumber();\n  const parseValue = text => {\n    if (text === '') return null;\n    if (parser) {\n      return String(parser(text));\n    }\n    const decimal = getMiniDecimal(text);\n    return decimal.isInvalidate() ? null : decimal.toString();\n  };\n  const formatValue = value => {\n    if (value === null) return '';\n    return formatter ? formatter(value) : fixedValue(value);\n  };\n  // ======================== Value & InputValue ========================\n  const [mergedValue, setMergedValue] = useMergedState(defaultValue, {\n    value,\n    onChange: nextValue => {\n      onChange === null || onChange === void 0 ? void 0 : onChange(nextValue);\n    }\n  });\n  const [inputValue, setInputValue] = useState(() => formatValue(mergedValue));\n  // >>>>> Value\n  function setValueWithCheck(nextValue) {\n    if (nextValue.isNaN()) return;\n    let target = nextValue;\n    // Put into range\n    if (min !== undefined) {\n      const minDecimal = getMiniDecimal(min);\n      if (target.lessEquals(minDecimal)) {\n        target = minDecimal;\n      }\n    }\n    if (max !== undefined) {\n      const maxDecimal = getMiniDecimal(max);\n      if (maxDecimal.lessEquals(target)) {\n        target = maxDecimal;\n      }\n    }\n    // Fix digits\n    if (digits !== undefined) {\n      target = getMiniDecimal(fixedValue(getValueAsType(target)));\n    }\n    setMergedValue(getValueAsType(target));\n  }\n  // >>>>> Input\n  const handleInputChange = v => {\n    setInputValue(v);\n    const valueStr = parseValue(v);\n    if (valueStr === null) {\n      if (props.allowEmpty) {\n        setMergedValue(null);\n      } else {\n        setMergedValue(defaultValue);\n      }\n    } else {\n      setValueWithCheck(getMiniDecimal(valueStr));\n    }\n  };\n  // ============================== Focus ===============================\n  const [focused, setFocused] = useState(false);\n  const inputRef = React.useRef(null);\n  function triggerFocus(nextFocus) {\n    setFocused(nextFocus);\n    // We will convert value to original text when focus\n    if (nextFocus) {\n      setInputValue(mergedValue !== null && mergedValue !== undefined ? String(mergedValue) : '');\n    }\n  }\n  useEffect(() => {\n    var _a, _b, _c;\n    if (focused) {\n      (_c = (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement) === null || _b === void 0 ? void 0 : _b.select) === null || _c === void 0 ? void 0 : _c.call(_b);\n    }\n  }, [focused]);\n  // Focus change to format value\n  useEffect(() => {\n    if (!focused) {\n      setInputValue(formatValue(mergedValue));\n    }\n  }, [focused, mergedValue, digits]);\n  // ============================ Operations ============================\n  const handleOffset = positive => {\n    let stepValue = getMiniDecimal(step);\n    if (!positive) {\n      stepValue = stepValue.negate();\n    }\n    setValueWithCheck(getMiniDecimal(mergedValue !== null && mergedValue !== void 0 ? mergedValue : 0).add(stepValue.toString()));\n  };\n  const handleMinus = () => {\n    handleOffset(false);\n  };\n  const handlePlus = () => {\n    handleOffset(true);\n  };\n  const minusDisabled = () => {\n    if (disabled) return true;\n    if (mergedValue === null) return false;\n    if (min !== undefined) {\n      return mergedValue <= min;\n    }\n    return false;\n  };\n  const plusDisabled = () => {\n    if (disabled) return true;\n    if (mergedValue === null) return false;\n    if (max !== undefined) {\n      return mergedValue >= max;\n    }\n    return false;\n  };\n  // ============================== Render ==============================\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-active`]: focused\n    })\n  }, React.createElement(Button, {\n    className: `${classPrefix}-minus`,\n    onClick: handleMinus,\n    disabled: minusDisabled(),\n    fill: 'none',\n    shape: 'rectangular',\n    color: 'primary',\n    \"aria-label\": locale.Stepper.decrease\n  }, React.createElement(MinusOutline, null)), React.createElement(\"div\", {\n    className: `${classPrefix}-middle`\n  }, React.createElement(Input, {\n    ref: inputRef,\n    className: `${classPrefix}-input`,\n    onFocus: e => {\n      var _a;\n      triggerFocus(true);\n      (_a = props.onFocus) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    value: inputValue,\n    onChange: val => {\n      disabled || handleInputChange(val);\n    },\n    disabled: disabled,\n    onBlur: e => {\n      var _a;\n      triggerFocus(false);\n      (_a = props.onBlur) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    readOnly: inputReadOnly,\n    role: 'spinbutton',\n    \"aria-valuenow\": Number(inputValue),\n    \"aria-valuemax\": Number(max),\n    \"aria-valuemin\": Number(min),\n    inputMode: 'decimal'\n  })), React.createElement(Button, {\n    className: `${classPrefix}-plus`,\n    onClick: handlePlus,\n    disabled: plusDisabled(),\n    fill: 'none',\n    shape: 'rectangular',\n    color: 'primary',\n    \"aria-label\": locale.Stepper.increase\n  }, React.createElement(AddOutline, null))));\n}\nexport const Stepper = forwardRef(InnerStepper);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}