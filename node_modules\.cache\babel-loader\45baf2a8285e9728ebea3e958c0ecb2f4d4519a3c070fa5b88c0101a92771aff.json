{"ast": null, "code": "import \"./list.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { List } from './list';\nimport { ListItem } from './list-item';\nexport default attachPropertiesToComponent(List, {\n  Item: ListItem\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "List", "ListItem", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/list/index.js"], "sourcesContent": ["import \"./list.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { List } from './list';\nimport { ListItem } from './list-item';\nexport default attachPropertiesToComponent(List, {\n  Item: ListItem\n});"], "mappings": "AAAA,OAAO,YAAY;AACnB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,aAAa;AACtC,eAAeF,2BAA2B,CAACC,IAAI,EAAE;EAC/CE,IAAI,EAAED;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}