{"ast": null, "code": "import React from 'react';\nimport { Modal } from './modal';\nimport { renderImperatively } from '../../utils/render-imperatively';\nexport const closeFnSet = new Set();\nexport function show(props) {\n  const handler = renderImperatively(React.createElement(Modal, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      closeFnSet.delete(handler.close);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  closeFnSet.add(handler.close);\n  return handler;\n}", "map": {"version": 3, "names": ["React", "Modal", "renderImperatively", "closeFnSet", "Set", "show", "props", "handler", "createElement", "Object", "assign", "afterClose", "_a", "delete", "close", "call", "add"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/modal/show.js"], "sourcesContent": ["import React from 'react';\nimport { Modal } from './modal';\nimport { renderImperatively } from '../../utils/render-imperatively';\nexport const closeFnSet = new Set();\nexport function show(props) {\n  const handler = renderImperatively(React.createElement(Modal, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      closeFnSet.delete(handler.close);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  closeFnSet.add(handler.close);\n  return handler;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,OAAO,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;AACnC,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,MAAMC,OAAO,GAAGL,kBAAkB,CAACF,KAAK,CAACQ,aAAa,CAACP,KAAK,EAAEQ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,KAAK,EAAE;IACrFK,UAAU,EAAEA,CAAA,KAAM;MAChB,IAAIC,EAAE;MACNT,UAAU,CAACU,MAAM,CAACN,OAAO,CAACO,KAAK,CAAC;MAChC,CAACF,EAAE,GAAGN,KAAK,CAACK,UAAU,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACT,KAAK,CAAC;IAC7E;EACF,CAAC,CAAC,CAAC,CAAC;EACJH,UAAU,CAACa,GAAG,CAACT,OAAO,CAACO,KAAK,CAAC;EAC7B,OAAOP,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}