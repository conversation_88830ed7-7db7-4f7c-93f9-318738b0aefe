{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-space`;\nconst defaultProps = {\n  direction: 'horizontal'\n};\nexport const Space = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    onClick\n  } = props;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-wrap`]: props.wrap,\n      [`${classPrefix}-block`]: props.block,\n      [`${classPrefix}-${direction}`]: true,\n      [`${classPrefix}-align-${props.align}`]: !!props.align,\n      [`${classPrefix}-justify-${props.justify}`]: !!props.justify\n    }),\n    onClick: onClick\n  }, React.Children.map(props.children, child => {\n    return child !== null && child !== undefined && React.createElement(\"div\", {\n      className: `${classPrefix}-item`\n    }, child);\n  })));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}