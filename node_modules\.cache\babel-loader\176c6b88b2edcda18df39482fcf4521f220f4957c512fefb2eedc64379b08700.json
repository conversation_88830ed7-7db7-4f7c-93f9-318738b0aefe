{"ast": null, "code": "import { useMemoizedFn } from 'ahooks';\nimport { DownOutline, TextDeletionOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { useMemo, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { shuffle } from '../../utils/shuffle';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Popup from '../popup';\nimport SafeArea from '../safe-area';\nconst classPrefix = 'adm-number-keyboard';\nconst defaultProps = {\n  defaultVisible: false,\n  randomOrder: false,\n  showCloseButton: true,\n  confirmText: null,\n  closeOnConfirm: true,\n  safeArea: true,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const NumberKeyboard = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    visible,\n    title,\n    getContainer,\n    confirmText,\n    customKey,\n    randomOrder,\n    showCloseButton,\n    onInput\n  } = props;\n  const {\n    locale\n  } = useConfig();\n  const keyboardRef = useRef(null);\n  const keys = useMemo(() => {\n    const defaultKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];\n    const keyList = randomOrder ? shuffle(defaultKeys) : defaultKeys;\n    const customKeys = Array.isArray(customKey) ? customKey : [customKey];\n    keyList.push('0');\n    if (confirmText) {\n      if (customKeys.length === 2) {\n        keyList.splice(9, 0, customKeys.pop());\n      }\n      keyList.push(customKeys[0] || '');\n    } else {\n      keyList.splice(9, 0, customKeys[0] || '');\n      keyList.push(customKeys[1] || 'BACKSPACE');\n    }\n    return keyList;\n  }, [customKey, confirmText, randomOrder, randomOrder && visible]);\n  const timeoutRef = useRef(-1);\n  const intervalRef = useRef(-1);\n  const onDelete = useMemoizedFn(() => {\n    var _a;\n    (_a = props.onDelete) === null || _a === void 0 ? void 0 : _a.call(props);\n  });\n  const startContinueClear = () => {\n    timeoutRef.current = window.setTimeout(() => {\n      onDelete();\n      intervalRef.current = window.setInterval(onDelete, 150);\n    }, 700);\n  };\n  const stopContinueClear = () => {\n    clearTimeout(timeoutRef.current);\n    clearInterval(intervalRef.current);\n  };\n  const onKeyPress = (e, key) => {\n    var _a, _b;\n    e.preventDefault();\n    switch (key) {\n      case 'BACKSPACE':\n        onDelete === null || onDelete === void 0 ? void 0 : onDelete();\n        break;\n      case 'OK':\n        (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props);\n        if (props.closeOnConfirm) {\n          (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n        }\n        break;\n      default:\n        // onInput should't be called when customKey doesn't exist\n        if (key !== '') onInput === null || onInput === void 0 ? void 0 : onInput(key);\n        break;\n    }\n  };\n  const renderHeader = () => {\n    if (!showCloseButton && !title) return null;\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-header`, {\n        [`${classPrefix}-header-with-title`]: !!title\n      })\n    }, !!title && React.createElement(\"div\", {\n      className: `${classPrefix}-title`,\n      \"aria-label\": title\n    }, title), showCloseButton && React.createElement(\"span\", {\n      className: `${classPrefix}-header-close-button`,\n      onClick: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      },\n      role: 'button',\n      title: locale.common.close,\n      tabIndex: -1\n    }, React.createElement(DownOutline, null)));\n  };\n  const renderKey = (key, index) => {\n    const isNumberKey = /^\\d$/.test(key);\n    const className = classNames(`${classPrefix}-key`, {\n      [`${classPrefix}-key-number`]: isNumberKey,\n      [`${classPrefix}-key-sign`]: !isNumberKey && key,\n      [`${classPrefix}-key-mid`]: index === 9 && !!confirmText && keys.length < 12\n    });\n    const ariaProps = key ? {\n      role: 'button',\n      title: key,\n      tabIndex: -1\n    } : undefined;\n    return React.createElement(\"div\", Object.assign({\n      key: key,\n      className: className,\n      onTouchStart: () => {\n        stopContinueClear();\n        if (key === 'BACKSPACE') {\n          startContinueClear();\n        }\n      },\n      onTouchEnd: e => {\n        onKeyPress(e, key);\n        if (key === 'BACKSPACE') {\n          stopContinueClear();\n        }\n      }\n    }, ariaProps), key === 'BACKSPACE' ? React.createElement(TextDeletionOutline, null) : key);\n  };\n  return React.createElement(Popup, {\n    visible: visible,\n    getContainer: getContainer,\n    mask: false,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    className: `${classPrefix}-popup`,\n    stopPropagation: props.stopPropagation,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, withNativeProps(props, React.createElement(\"div\", {\n    ref: keyboardRef,\n    className: classPrefix,\n    onMouseDown: e => {\n      e.preventDefault();\n    }\n  }, renderHeader(), React.createElement(\"div\", {\n    className: `${classPrefix}-wrapper`\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-main`, {\n      [`${classPrefix}-main-confirmed-style`]: !!confirmText\n    })\n  }, keys.map(renderKey)), !!confirmText && React.createElement(\"div\", {\n    className: `${classPrefix}-confirm`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-key ${classPrefix}-key-extra ${classPrefix}-key-bs`,\n    onTouchStart: () => {\n      startContinueClear();\n    },\n    onTouchEnd: e => {\n      onKeyPress(e, 'BACKSPACE');\n      stopContinueClear();\n    },\n    onContextMenu: e => {\n      // Long press should not trigger native context menu\n      e.preventDefault();\n    },\n    title: locale.Input.clear,\n    role: 'button',\n    tabIndex: -1\n  }, React.createElement(TextDeletionOutline, null)), React.createElement(\"div\", {\n    className: `${classPrefix}-key ${classPrefix}-key-extra ${classPrefix}-key-ok`,\n    onTouchEnd: e => onKeyPress(e, 'OK'),\n    role: 'button',\n    tabIndex: -1,\n    \"aria-label\": confirmText\n  }, confirmText))), props.safeArea && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, React.createElement(SafeArea, {\n    position: 'bottom'\n  })))));\n};", "map": {"version": 3, "names": ["useMemoizedFn", "DownOutline", "TextDeletionOutline", "classNames", "React", "useMemo", "useRef", "withNativeProps", "shuffle", "mergeProps", "useConfig", "Popup", "SafeArea", "classPrefix", "defaultProps", "defaultVisible", "randomOrder", "showCloseButton", "confirmText", "closeOnConfirm", "safeArea", "destroyOnClose", "forceRender", "NumberKeyboard", "p", "props", "visible", "title", "getContainer", "customKey", "onInput", "locale", "keyboardRef", "keys", "defaultKeys", "keyList", "customKeys", "Array", "isArray", "push", "length", "splice", "pop", "timeoutRef", "intervalRef", "onDelete", "_a", "call", "startContinueClear", "current", "window", "setTimeout", "setInterval", "stopContinueClear", "clearTimeout", "clearInterval", "onKeyPress", "e", "key", "_b", "preventDefault", "onConfirm", "onClose", "renderHeader", "createElement", "className", "onClick", "role", "common", "close", "tabIndex", "<PERSON><PERSON><PERSON>", "index", "isNumberKey", "test", "ariaProps", "undefined", "Object", "assign", "onTouchStart", "onTouchEnd", "mask", "afterClose", "afterShow", "stopPropagation", "ref", "onMouseDown", "map", "onContextMenu", "Input", "clear", "position"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/number-keyboard/number-keyboard.js"], "sourcesContent": ["import { useMemoizedFn } from 'ahooks';\nimport { DownOutline, TextDeletionOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { useMemo, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { shuffle } from '../../utils/shuffle';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Popup from '../popup';\nimport SafeArea from '../safe-area';\nconst classPrefix = 'adm-number-keyboard';\nconst defaultProps = {\n  defaultVisible: false,\n  randomOrder: false,\n  showCloseButton: true,\n  confirmText: null,\n  closeOnConfirm: true,\n  safeArea: true,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const NumberKeyboard = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    visible,\n    title,\n    getContainer,\n    confirmText,\n    customKey,\n    randomOrder,\n    showCloseButton,\n    onInput\n  } = props;\n  const {\n    locale\n  } = useConfig();\n  const keyboardRef = useRef(null);\n  const keys = useMemo(() => {\n    const defaultKeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];\n    const keyList = randomOrder ? shuffle(defaultKeys) : defaultKeys;\n    const customKeys = Array.isArray(customKey) ? customKey : [customKey];\n    keyList.push('0');\n    if (confirmText) {\n      if (customKeys.length === 2) {\n        keyList.splice(9, 0, customKeys.pop());\n      }\n      keyList.push(customKeys[0] || '');\n    } else {\n      keyList.splice(9, 0, customKeys[0] || '');\n      keyList.push(customKeys[1] || 'BACKSPACE');\n    }\n    return keyList;\n  }, [customKey, confirmText, randomOrder, randomOrder && visible]);\n  const timeoutRef = useRef(-1);\n  const intervalRef = useRef(-1);\n  const onDelete = useMemoizedFn(() => {\n    var _a;\n    (_a = props.onDelete) === null || _a === void 0 ? void 0 : _a.call(props);\n  });\n  const startContinueClear = () => {\n    timeoutRef.current = window.setTimeout(() => {\n      onDelete();\n      intervalRef.current = window.setInterval(onDelete, 150);\n    }, 700);\n  };\n  const stopContinueClear = () => {\n    clearTimeout(timeoutRef.current);\n    clearInterval(intervalRef.current);\n  };\n  const onKeyPress = (e, key) => {\n    var _a, _b;\n    e.preventDefault();\n    switch (key) {\n      case 'BACKSPACE':\n        onDelete === null || onDelete === void 0 ? void 0 : onDelete();\n        break;\n      case 'OK':\n        (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props);\n        if (props.closeOnConfirm) {\n          (_b = props.onClose) === null || _b === void 0 ? void 0 : _b.call(props);\n        }\n        break;\n      default:\n        // onInput should't be called when customKey doesn't exist\n        if (key !== '') onInput === null || onInput === void 0 ? void 0 : onInput(key);\n        break;\n    }\n  };\n  const renderHeader = () => {\n    if (!showCloseButton && !title) return null;\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-header`, {\n        [`${classPrefix}-header-with-title`]: !!title\n      })\n    }, !!title && React.createElement(\"div\", {\n      className: `${classPrefix}-title`,\n      \"aria-label\": title\n    }, title), showCloseButton && React.createElement(\"span\", {\n      className: `${classPrefix}-header-close-button`,\n      onClick: () => {\n        var _a;\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      },\n      role: 'button',\n      title: locale.common.close,\n      tabIndex: -1\n    }, React.createElement(DownOutline, null)));\n  };\n  const renderKey = (key, index) => {\n    const isNumberKey = /^\\d$/.test(key);\n    const className = classNames(`${classPrefix}-key`, {\n      [`${classPrefix}-key-number`]: isNumberKey,\n      [`${classPrefix}-key-sign`]: !isNumberKey && key,\n      [`${classPrefix}-key-mid`]: index === 9 && !!confirmText && keys.length < 12\n    });\n    const ariaProps = key ? {\n      role: 'button',\n      title: key,\n      tabIndex: -1\n    } : undefined;\n    return React.createElement(\"div\", Object.assign({\n      key: key,\n      className: className,\n      onTouchStart: () => {\n        stopContinueClear();\n        if (key === 'BACKSPACE') {\n          startContinueClear();\n        }\n      },\n      onTouchEnd: e => {\n        onKeyPress(e, key);\n        if (key === 'BACKSPACE') {\n          stopContinueClear();\n        }\n      }\n    }, ariaProps), key === 'BACKSPACE' ? React.createElement(TextDeletionOutline, null) : key);\n  };\n  return React.createElement(Popup, {\n    visible: visible,\n    getContainer: getContainer,\n    mask: false,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    className: `${classPrefix}-popup`,\n    stopPropagation: props.stopPropagation,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, withNativeProps(props, React.createElement(\"div\", {\n    ref: keyboardRef,\n    className: classPrefix,\n    onMouseDown: e => {\n      e.preventDefault();\n    }\n  }, renderHeader(), React.createElement(\"div\", {\n    className: `${classPrefix}-wrapper`\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-main`, {\n      [`${classPrefix}-main-confirmed-style`]: !!confirmText\n    })\n  }, keys.map(renderKey)), !!confirmText && React.createElement(\"div\", {\n    className: `${classPrefix}-confirm`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-key ${classPrefix}-key-extra ${classPrefix}-key-bs`,\n    onTouchStart: () => {\n      startContinueClear();\n    },\n    onTouchEnd: e => {\n      onKeyPress(e, 'BACKSPACE');\n      stopContinueClear();\n    },\n    onContextMenu: e => {\n      // Long press should not trigger native context menu\n      e.preventDefault();\n    },\n    title: locale.Input.clear,\n    role: 'button',\n    tabIndex: -1\n  }, React.createElement(TextDeletionOutline, null)), React.createElement(\"div\", {\n    className: `${classPrefix}-key ${classPrefix}-key-extra ${classPrefix}-key-ok`,\n    onTouchEnd: e => onKeyPress(e, 'OK'),\n    role: 'button',\n    tabIndex: -1,\n    \"aria-label\": confirmText\n  }, confirmText))), props.safeArea && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, React.createElement(SafeArea, {\n    position: 'bottom'\n  })))));\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AACtC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,mBAAmB;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,QAAQ,MAAM,cAAc;AACnC,MAAMC,WAAW,GAAG,qBAAqB;AACzC,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE,KAAK;EAClBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE,IAAI;EACdC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGC,CAAC,IAAI;EACjC,MAAMC,KAAK,GAAGhB,UAAU,CAACK,YAAY,EAAEU,CAAC,CAAC;EACzC,MAAM;IACJE,OAAO;IACPC,KAAK;IACLC,YAAY;IACZV,WAAW;IACXW,SAAS;IACTb,WAAW;IACXC,eAAe;IACfa;EACF,CAAC,GAAGL,KAAK;EACT,MAAM;IACJM;EACF,CAAC,GAAGrB,SAAS,CAAC,CAAC;EACf,MAAMsB,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM2B,IAAI,GAAG5B,OAAO,CAAC,MAAM;IACzB,MAAM6B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjE,MAAMC,OAAO,GAAGnB,WAAW,GAAGR,OAAO,CAAC0B,WAAW,CAAC,GAAGA,WAAW;IAChE,MAAME,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACT,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACrEM,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC;IACjB,IAAIrB,WAAW,EAAE;MACf,IAAIkB,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;QAC3BL,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEL,UAAU,CAACM,GAAG,CAAC,CAAC,CAAC;MACxC;MACAP,OAAO,CAACI,IAAI,CAACH,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC,MAAM;MACLD,OAAO,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEL,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;MACzCD,OAAO,CAACI,IAAI,CAACH,UAAU,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;IAC5C;IACA,OAAOD,OAAO;EAChB,CAAC,EAAE,CAACN,SAAS,EAAEX,WAAW,EAAEF,WAAW,EAAEA,WAAW,IAAIU,OAAO,CAAC,CAAC;EACjE,MAAMiB,UAAU,GAAGrC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMsC,WAAW,GAAGtC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAG7C,aAAa,CAAC,MAAM;IACnC,IAAI8C,EAAE;IACN,CAACA,EAAE,GAAGrB,KAAK,CAACoB,QAAQ,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACtB,KAAK,CAAC;EAC3E,CAAC,CAAC;EACF,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BL,UAAU,CAACM,OAAO,GAAGC,MAAM,CAACC,UAAU,CAAC,MAAM;MAC3CN,QAAQ,CAAC,CAAC;MACVD,WAAW,CAACK,OAAO,GAAGC,MAAM,CAACE,WAAW,CAACP,QAAQ,EAAE,GAAG,CAAC;IACzD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EACD,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9BC,YAAY,CAACX,UAAU,CAACM,OAAO,CAAC;IAChCM,aAAa,CAACX,WAAW,CAACK,OAAO,CAAC;EACpC,CAAC;EACD,MAAMO,UAAU,GAAGA,CAACC,CAAC,EAAEC,GAAG,KAAK;IAC7B,IAAIZ,EAAE,EAAEa,EAAE;IACVF,CAAC,CAACG,cAAc,CAAC,CAAC;IAClB,QAAQF,GAAG;MACT,KAAK,WAAW;QACdb,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;QAC9D;MACF,KAAK,IAAI;QACP,CAACC,EAAE,GAAGrB,KAAK,CAACoC,SAAS,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACtB,KAAK,CAAC;QAC1E,IAAIA,KAAK,CAACN,cAAc,EAAE;UACxB,CAACwC,EAAE,GAAGlC,KAAK,CAACqC,OAAO,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACZ,IAAI,CAACtB,KAAK,CAAC;QAC1E;QACA;MACF;QACE;QACA,IAAIiC,GAAG,KAAK,EAAE,EAAE5B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4B,GAAG,CAAC;QAC9E;IACJ;EACF,CAAC;EACD,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC9C,eAAe,IAAI,CAACU,KAAK,EAAE,OAAO,IAAI;IAC3C,OAAOvB,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE9D,UAAU,CAAC,GAAGU,WAAW,SAAS,EAAE;QAC7C,CAAC,GAAGA,WAAW,oBAAoB,GAAG,CAAC,CAACc;MAC1C,CAAC;IACH,CAAC,EAAE,CAAC,CAACA,KAAK,IAAIvB,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;MACvCC,SAAS,EAAE,GAAGpD,WAAW,QAAQ;MACjC,YAAY,EAAEc;IAChB,CAAC,EAAEA,KAAK,CAAC,EAAEV,eAAe,IAAIb,KAAK,CAAC4D,aAAa,CAAC,MAAM,EAAE;MACxDC,SAAS,EAAE,GAAGpD,WAAW,sBAAsB;MAC/CqD,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIpB,EAAE;QACN,CAACA,EAAE,GAAGrB,KAAK,CAACqC,OAAO,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACtB,KAAK,CAAC;MAC1E,CAAC;MACD0C,IAAI,EAAE,QAAQ;MACdxC,KAAK,EAAEI,MAAM,CAACqC,MAAM,CAACC,KAAK;MAC1BC,QAAQ,EAAE,CAAC;IACb,CAAC,EAAElE,KAAK,CAAC4D,aAAa,CAAC/D,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;EAC7C,CAAC;EACD,MAAMsE,SAAS,GAAGA,CAACb,GAAG,EAAEc,KAAK,KAAK;IAChC,MAAMC,WAAW,GAAG,MAAM,CAACC,IAAI,CAAChB,GAAG,CAAC;IACpC,MAAMO,SAAS,GAAG9D,UAAU,CAAC,GAAGU,WAAW,MAAM,EAAE;MACjD,CAAC,GAAGA,WAAW,aAAa,GAAG4D,WAAW;MAC1C,CAAC,GAAG5D,WAAW,WAAW,GAAG,CAAC4D,WAAW,IAAIf,GAAG;MAChD,CAAC,GAAG7C,WAAW,UAAU,GAAG2D,KAAK,KAAK,CAAC,IAAI,CAAC,CAACtD,WAAW,IAAIe,IAAI,CAACO,MAAM,GAAG;IAC5E,CAAC,CAAC;IACF,MAAMmC,SAAS,GAAGjB,GAAG,GAAG;MACtBS,IAAI,EAAE,QAAQ;MACdxC,KAAK,EAAE+B,GAAG;MACVY,QAAQ,EAAE,CAAC;IACb,CAAC,GAAGM,SAAS;IACb,OAAOxE,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAEa,MAAM,CAACC,MAAM,CAAC;MAC9CpB,GAAG,EAAEA,GAAG;MACRO,SAAS,EAAEA,SAAS;MACpBc,YAAY,EAAEA,CAAA,KAAM;QAClB1B,iBAAiB,CAAC,CAAC;QACnB,IAAIK,GAAG,KAAK,WAAW,EAAE;UACvBV,kBAAkB,CAAC,CAAC;QACtB;MACF,CAAC;MACDgC,UAAU,EAAEvB,CAAC,IAAI;QACfD,UAAU,CAACC,CAAC,EAAEC,GAAG,CAAC;QAClB,IAAIA,GAAG,KAAK,WAAW,EAAE;UACvBL,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF,CAAC,EAAEsB,SAAS,CAAC,EAAEjB,GAAG,KAAK,WAAW,GAAGtD,KAAK,CAAC4D,aAAa,CAAC9D,mBAAmB,EAAE,IAAI,CAAC,GAAGwD,GAAG,CAAC;EAC5F,CAAC;EACD,OAAOtD,KAAK,CAAC4D,aAAa,CAACrD,KAAK,EAAE;IAChCe,OAAO,EAAEA,OAAO;IAChBE,YAAY,EAAEA,YAAY;IAC1BqD,IAAI,EAAE,KAAK;IACXC,UAAU,EAAEzD,KAAK,CAACyD,UAAU;IAC5BC,SAAS,EAAE1D,KAAK,CAAC0D,SAAS;IAC1BlB,SAAS,EAAE,GAAGpD,WAAW,QAAQ;IACjCuE,eAAe,EAAE3D,KAAK,CAAC2D,eAAe;IACtC/D,cAAc,EAAEI,KAAK,CAACJ,cAAc;IACpCC,WAAW,EAAEG,KAAK,CAACH;EACrB,CAAC,EAAEf,eAAe,CAACkB,KAAK,EAAErB,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IACnDqB,GAAG,EAAErD,WAAW;IAChBiC,SAAS,EAAEpD,WAAW;IACtByE,WAAW,EAAE7B,CAAC,IAAI;MAChBA,CAAC,CAACG,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,EAAEG,YAAY,CAAC,CAAC,EAAE3D,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAC5CC,SAAS,EAAE,GAAGpD,WAAW;EAC3B,CAAC,EAAET,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE9D,UAAU,CAAC,GAAGU,WAAW,OAAO,EAAE;MAC3C,CAAC,GAAGA,WAAW,uBAAuB,GAAG,CAAC,CAACK;IAC7C,CAAC;EACH,CAAC,EAAEe,IAAI,CAACsD,GAAG,CAAChB,SAAS,CAAC,CAAC,EAAE,CAAC,CAACrD,WAAW,IAAId,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IACnEC,SAAS,EAAE,GAAGpD,WAAW;EAC3B,CAAC,EAAET,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpD,WAAW,QAAQA,WAAW,cAAcA,WAAW,SAAS;IAC9EkE,YAAY,EAAEA,CAAA,KAAM;MAClB/B,kBAAkB,CAAC,CAAC;IACtB,CAAC;IACDgC,UAAU,EAAEvB,CAAC,IAAI;MACfD,UAAU,CAACC,CAAC,EAAE,WAAW,CAAC;MAC1BJ,iBAAiB,CAAC,CAAC;IACrB,CAAC;IACDmC,aAAa,EAAE/B,CAAC,IAAI;MAClB;MACAA,CAAC,CAACG,cAAc,CAAC,CAAC;IACpB,CAAC;IACDjC,KAAK,EAAEI,MAAM,CAAC0D,KAAK,CAACC,KAAK;IACzBvB,IAAI,EAAE,QAAQ;IACdG,QAAQ,EAAE,CAAC;EACb,CAAC,EAAElE,KAAK,CAAC4D,aAAa,CAAC9D,mBAAmB,EAAE,IAAI,CAAC,CAAC,EAAEE,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAC7EC,SAAS,EAAE,GAAGpD,WAAW,QAAQA,WAAW,cAAcA,WAAW,SAAS;IAC9EmE,UAAU,EAAEvB,CAAC,IAAID,UAAU,CAACC,CAAC,EAAE,IAAI,CAAC;IACpCU,IAAI,EAAE,QAAQ;IACdG,QAAQ,EAAE,CAAC,CAAC;IACZ,YAAY,EAAEpD;EAChB,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,EAAEO,KAAK,CAACL,QAAQ,IAAIhB,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;IAC9DC,SAAS,EAAE,GAAGpD,WAAW;EAC3B,CAAC,EAAET,KAAK,CAAC4D,aAAa,CAACpD,QAAQ,EAAE;IAC/B+E,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}