{"ast": null, "code": "import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useUnmount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\nexport default useUnmount;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}