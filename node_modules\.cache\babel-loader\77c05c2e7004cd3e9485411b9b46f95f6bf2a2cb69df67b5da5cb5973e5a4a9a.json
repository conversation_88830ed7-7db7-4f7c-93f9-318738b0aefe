{"ast": null, "code": "import { useMemoizedFn } from 'ahooks';\nimport React, { useEffect, useRef } from 'react';\nexport const NativeInput = props => {\n  const inputRef = useRef(null);\n  const handleClick = useMemoizedFn(e => {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n    const latestChecked = e.target.checked;\n    if (latestChecked === props.checked) return;\n    props.onChange(latestChecked);\n  });\n  useEffect(() => {\n    if (props.disabled) return;\n    if (!inputRef.current) return;\n    const input = inputRef.current;\n    input.addEventListener('click', handleClick);\n    return () => {\n      input.removeEventListener('click', handleClick);\n    };\n  }, [props.disabled, props.onChange]);\n  return React.createElement(\"input\", {\n    ref: inputRef,\n    type: props.type,\n    checked: props.checked,\n    onChange: () => {},\n    disabled: props.disabled,\n    id: props.id\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}