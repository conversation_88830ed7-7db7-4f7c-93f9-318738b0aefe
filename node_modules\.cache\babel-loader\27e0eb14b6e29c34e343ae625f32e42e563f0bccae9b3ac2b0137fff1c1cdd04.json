{"ast": null, "code": "import { CheckOutline, CloseOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport AutoCenter from '../auto-center';\nimport Mask from '../mask';\nimport SpinLoading from '../spin-loading';\nconst classPrefix = `adm-toast`;\nconst defaultProps = {\n  maskClickable: true,\n  stopPropagation: ['click']\n};\nexport const InternalToast = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    maskClickable,\n    content,\n    icon,\n    position\n  } = props;\n  const iconElement = useMemo(() => {\n    if (icon === null || icon === undefined) return null;\n    switch (icon) {\n      case 'success':\n        return React.createElement(CheckOutline, {\n          className: `${classPrefix}-icon-success`\n        });\n      case 'fail':\n        return React.createElement(CloseOutline, {\n          className: `${classPrefix}-icon-fail`\n        });\n      case 'loading':\n        return React.createElement(SpinLoading, {\n          color: 'white',\n          className: `${classPrefix}-loading`\n        });\n      default:\n        return icon;\n    }\n  }, [icon]);\n  const top = useMemo(() => {\n    switch (position) {\n      case 'top':\n        return '20%';\n      case 'bottom':\n        return '80%';\n      default:\n        return '50%';\n    }\n  }, [position]);\n  return React.createElement(Mask, {\n    visible: props.visible,\n    destroyOnClose: true,\n    opacity: 0,\n    disableBodyScroll: !maskClickable,\n    getContainer: props.getContainer,\n    afterClose: props.afterClose,\n    style: Object.assign({\n      pointerEvents: maskClickable ? 'none' : 'auto'\n    }, props.maskStyle),\n    className: classNames(`${classPrefix}-mask`, props.maskClassName),\n    stopPropagation: props.stopPropagation\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-wrap`)\n  }, React.createElement(\"div\", {\n    style: {\n      top\n    },\n    className: classNames(`${classPrefix}-main`, icon ? `${classPrefix}-main-icon` : `${classPrefix}-main-text`)\n  }, iconElement && React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, iconElement), React.createElement(AutoCenter, null, content))));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}