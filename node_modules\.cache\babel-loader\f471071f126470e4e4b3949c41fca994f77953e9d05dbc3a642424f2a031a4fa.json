{"ast": null, "code": "import * as React from \"react\";\nfunction TagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TagOutline-TagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M43,39.9791918 C43,42.1992183 41.209139,43.9989053 39,43.9989053 C38.5367887,43.9989053 38.0770973,43.9180516 37.6414221,43.7599491 L24.1274991,39.2153697 C24.0447759,39.1875508 23.9552241,39.1875508 23.8725009,39.2153697 L10.3585779,43.7599491 L10.3585779,43.7599491 C8.28076355,44.5139688 5.98810623,43.4325198 5.2377844,41.3444653 C5.08045725,40.9066429 5,40.444686 5,39.9791918 L5,9.02957027 C5,5.69953056 7.6862915,3 11,3 L37,3 C40.3137085,3 43,5.69953056 43,9.02957027 L43,39.9791918 Z M40,15.059 L8,15.059 L8,39.9791918 C8,40.0955654 8.02011431,40.2110546 8.0594461,40.3205102 C8.23362795,40.8052371 8.74027753,41.072979 9.22767097,40.9578271 L9.33964446,40.9243811 L23.8718717,36.0104888 C23.9549793,35.982387 24.0450207,35.982387 24.1281283,36.0104888 L38.6603555,40.9243811 L38.6603555,40.9243811 C38.7692743,40.9639068 38.8841972,40.9843939 39,40.9843939 C39.5128358,40.9843939 39.9355072,40.5961775 39.9932723,40.0963877 L40,39.9791918 L40,15.059 Z M37,6.01478513 L11,6.01478513 C9.40231912,6.01478513 8.09633912,7.26986025 8.00509269,8.85242874 L8,9.02957027 L8,12.044 L40,12.044 L40,9.02957027 C40,7.42401541 38.75108,6.11159905 37.1762728,6.01990293 L37,6.01478513 Z\",\n    id: \"TagOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TagOutline;", "map": {"version": 3, "names": ["React", "TagOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/TagOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TagOutline-TagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M43,39.9791918 C43,42.1992183 41.209139,43.9989053 39,43.9989053 C38.5367887,43.9989053 38.0770973,43.9180516 37.6414221,43.7599491 L24.1274991,39.2153697 C24.0447759,39.1875508 23.9552241,39.1875508 23.8725009,39.2153697 L10.3585779,43.7599491 L10.3585779,43.7599491 C8.28076355,44.5139688 5.98810623,43.4325198 5.2377844,41.3444653 C5.08045725,40.9066429 5,40.444686 5,39.9791918 L5,9.02957027 C5,5.69953056 7.6862915,3 11,3 L37,3 C40.3137085,3 43,5.69953056 43,9.02957027 L43,39.9791918 Z M40,15.059 L8,15.059 L8,39.9791918 C8,40.0955654 8.02011431,40.2110546 8.0594461,40.3205102 C8.23362795,40.8052371 8.74027753,41.072979 9.22767097,40.9578271 L9.33964446,40.9243811 L23.8718717,36.0104888 C23.9549793,35.982387 24.0450207,35.982387 24.1281283,36.0104888 L38.6603555,40.9243811 L38.6603555,40.9243811 C38.7692743,40.9639068 38.8841972,40.9843939 39,40.9843939 C39.5128358,40.9843939 39.9355072,40.5961775 39.9932723,40.0963877 L40,39.9791918 L40,15.059 Z M37,6.01478513 L11,6.01478513 C9.40231912,6.01478513 8.09633912,7.26986025 8.00509269,8.85242874 L8,9.02957027 L8,12.044 L40,12.044 L40,9.02957027 C40,7.42401541 38.75108,6.11159905 37.1762728,6.01990293 L37,6.01478513 Z\",\n    id: \"TagOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TagOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uBAAuB;IAC3BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+pCAA+pC;IAClqCR,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}