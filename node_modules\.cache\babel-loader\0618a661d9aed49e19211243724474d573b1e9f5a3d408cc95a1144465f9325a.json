{"ast": null, "code": "import React, { memo, useCallback, useEffect, useState } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Wheel } from './wheel';\nimport { useColumnsExtend } from './columns-extend';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useDebounceEffect } from 'ahooks';\nimport { defaultRenderLabel } from '../picker/picker-utils';\nimport SpinLoading from '../spin-loading';\nconst classPrefix = `adm-picker-view`;\nconst defaultProps = {\n  defaultValue: [],\n  renderLabel: defaultRenderLabel,\n  mouseWheel: false,\n  loadingContent: React.createElement(\"div\", {\n    className: `${classPrefix}-loading-content`\n  }, React.createElement(SpinLoading, null))\n};\nexport const PickerView = memo(p => {\n  const props = mergeProps(defaultProps, p);\n  const [innerValue, setInnerValue] = useState(props.value === undefined ? props.defaultValue : props.value);\n  // Sync `value` to `innerValue`\n  useEffect(() => {\n    if (props.value === undefined) return; // Uncontrolled mode\n    if (props.value === innerValue) return;\n    setInnerValue(props.value);\n  }, [props.value]);\n  useEffect(() => {\n    if (props.value === innerValue) return;\n    const timeout = window.setTimeout(() => {\n      if (props.value !== undefined && props.value !== innerValue) {\n        setInnerValue(props.value);\n      }\n    }, 1000);\n    return () => {\n      window.clearTimeout(timeout);\n    };\n  }, [props.value, innerValue]);\n  const extend = useColumnsExtend(props.columns, innerValue);\n  const columns = extend.columns;\n  useDebounceEffect(() => {\n    var _a;\n    if (props.value === innerValue) return;\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, innerValue, extend);\n  }, [innerValue], {\n    wait: 0,\n    leading: false,\n    trailing: true\n  });\n  const handleSelect = useCallback((val, index) => {\n    setInnerValue(prev => {\n      const next = [...prev];\n      next[index] = val;\n      return next;\n    });\n  }, []);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}`\n  }, props.loading ? props.loadingContent : React.createElement(React.Fragment, null, columns.map((column, index) => React.createElement(Wheel, {\n    key: index,\n    index: index,\n    column: column,\n    value: innerValue[index],\n    onSelect: handleSelect,\n    renderLabel: props.renderLabel,\n    mouseWheel: props.mouseWheel\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-mask`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-mask-top`\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-mask-middle`\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-mask-bottom`\n  })))));\n});\nPickerView.displayName = 'PickerView';", "map": {"version": 3, "names": ["React", "memo", "useCallback", "useEffect", "useState", "mergeProps", "Wheel", "useColumnsExtend", "withNativeProps", "useDebounceEffect", "defaultRenderLabel", "SpinLoading", "classPrefix", "defaultProps", "defaultValue", "renderLabel", "mouseWheel", "loadingContent", "createElement", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "p", "props", "innerValue", "setInnerValue", "value", "undefined", "timeout", "window", "setTimeout", "clearTimeout", "extend", "columns", "_a", "onChange", "call", "wait", "leading", "trailing", "handleSelect", "val", "index", "prev", "next", "loading", "Fragment", "map", "column", "key", "onSelect", "displayName"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/picker-view/picker-view.js"], "sourcesContent": ["import React, { memo, useCallback, useEffect, useState } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Wheel } from './wheel';\nimport { useColumnsExtend } from './columns-extend';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useDebounceEffect } from 'ahooks';\nimport { defaultRenderLabel } from '../picker/picker-utils';\nimport SpinLoading from '../spin-loading';\nconst classPrefix = `adm-picker-view`;\nconst defaultProps = {\n  defaultValue: [],\n  renderLabel: defaultRenderLabel,\n  mouseWheel: false,\n  loadingContent: React.createElement(\"div\", {\n    className: `${classPrefix}-loading-content`\n  }, React.createElement(SpinLoading, null))\n};\nexport const PickerView = memo(p => {\n  const props = mergeProps(defaultProps, p);\n  const [innerValue, setInnerValue] = useState(props.value === undefined ? props.defaultValue : props.value);\n  // Sync `value` to `innerValue`\n  useEffect(() => {\n    if (props.value === undefined) return; // Uncontrolled mode\n    if (props.value === innerValue) return;\n    setInnerValue(props.value);\n  }, [props.value]);\n  useEffect(() => {\n    if (props.value === innerValue) return;\n    const timeout = window.setTimeout(() => {\n      if (props.value !== undefined && props.value !== innerValue) {\n        setInnerValue(props.value);\n      }\n    }, 1000);\n    return () => {\n      window.clearTimeout(timeout);\n    };\n  }, [props.value, innerValue]);\n  const extend = useColumnsExtend(props.columns, innerValue);\n  const columns = extend.columns;\n  useDebounceEffect(() => {\n    var _a;\n    if (props.value === innerValue) return;\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, innerValue, extend);\n  }, [innerValue], {\n    wait: 0,\n    leading: false,\n    trailing: true\n  });\n  const handleSelect = useCallback((val, index) => {\n    setInnerValue(prev => {\n      const next = [...prev];\n      next[index] = val;\n      return next;\n    });\n  }, []);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}`\n  }, props.loading ? props.loadingContent : React.createElement(React.Fragment, null, columns.map((column, index) => React.createElement(Wheel, {\n    key: index,\n    index: index,\n    column: column,\n    value: innerValue[index],\n    onSelect: handleSelect,\n    renderLabel: props.renderLabel,\n    mouseWheel: props.mouseWheel\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-mask`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-mask-top`\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-mask-middle`\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-mask-bottom`\n  })))));\n});\nPickerView.displayName = 'PickerView';"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACrE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,QAAQ;AAC1C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAOC,WAAW,MAAM,iBAAiB;AACzC,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAEL,kBAAkB;EAC/BM,UAAU,EAAE,KAAK;EACjBC,cAAc,EAAEjB,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,EAAEZ,KAAK,CAACkB,aAAa,CAACP,WAAW,EAAE,IAAI,CAAC;AAC3C,CAAC;AACD,OAAO,MAAMS,UAAU,GAAGnB,IAAI,CAACoB,CAAC,IAAI;EAClC,MAAMC,KAAK,GAAGjB,UAAU,CAACQ,YAAY,EAAEQ,CAAC,CAAC;EACzC,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAACkB,KAAK,CAACG,KAAK,KAAKC,SAAS,GAAGJ,KAAK,CAACR,YAAY,GAAGQ,KAAK,CAACG,KAAK,CAAC;EAC1G;EACAtB,SAAS,CAAC,MAAM;IACd,IAAImB,KAAK,CAACG,KAAK,KAAKC,SAAS,EAAE,OAAO,CAAC;IACvC,IAAIJ,KAAK,CAACG,KAAK,KAAKF,UAAU,EAAE;IAChCC,aAAa,CAACF,KAAK,CAACG,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACH,KAAK,CAACG,KAAK,CAAC,CAAC;EACjBtB,SAAS,CAAC,MAAM;IACd,IAAImB,KAAK,CAACG,KAAK,KAAKF,UAAU,EAAE;IAChC,MAAMI,OAAO,GAAGC,MAAM,CAACC,UAAU,CAAC,MAAM;MACtC,IAAIP,KAAK,CAACG,KAAK,KAAKC,SAAS,IAAIJ,KAAK,CAACG,KAAK,KAAKF,UAAU,EAAE;QAC3DC,aAAa,CAACF,KAAK,CAACG,KAAK,CAAC;MAC5B;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM;MACXG,MAAM,CAACE,YAAY,CAACH,OAAO,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,CAACG,KAAK,EAAEF,UAAU,CAAC,CAAC;EAC7B,MAAMQ,MAAM,GAAGxB,gBAAgB,CAACe,KAAK,CAACU,OAAO,EAAET,UAAU,CAAC;EAC1D,MAAMS,OAAO,GAAGD,MAAM,CAACC,OAAO;EAC9BvB,iBAAiB,CAAC,MAAM;IACtB,IAAIwB,EAAE;IACN,IAAIX,KAAK,CAACG,KAAK,KAAKF,UAAU,EAAE;IAChC,CAACU,EAAE,GAAGX,KAAK,CAACY,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACb,KAAK,EAAEC,UAAU,EAAEQ,MAAM,CAAC;EAC/F,CAAC,EAAE,CAACR,UAAU,CAAC,EAAE;IACfa,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGrC,WAAW,CAAC,CAACsC,GAAG,EAAEC,KAAK,KAAK;IAC/CjB,aAAa,CAACkB,IAAI,IAAI;MACpB,MAAMC,IAAI,GAAG,CAAC,GAAGD,IAAI,CAAC;MACtBC,IAAI,CAACF,KAAK,CAAC,GAAGD,GAAG;MACjB,OAAOG,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAOnC,eAAe,CAACc,KAAK,EAAEtB,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,EAAEU,KAAK,CAACsB,OAAO,GAAGtB,KAAK,CAACL,cAAc,GAAGjB,KAAK,CAACkB,aAAa,CAAClB,KAAK,CAAC6C,QAAQ,EAAE,IAAI,EAAEb,OAAO,CAACc,GAAG,CAAC,CAACC,MAAM,EAAEN,KAAK,KAAKzC,KAAK,CAACkB,aAAa,CAACZ,KAAK,EAAE;IAC5I0C,GAAG,EAAEP,KAAK;IACVA,KAAK,EAAEA,KAAK;IACZM,MAAM,EAAEA,MAAM;IACdtB,KAAK,EAAEF,UAAU,CAACkB,KAAK,CAAC;IACxBQ,QAAQ,EAAEV,YAAY;IACtBxB,WAAW,EAAEO,KAAK,CAACP,WAAW;IAC9BC,UAAU,EAAEM,KAAK,CAACN;EACpB,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC9BC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,EAAEZ,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,CAAC,EAAEZ,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC7BC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,CAAC,EAAEZ,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC7BC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AACFQ,UAAU,CAAC8B,WAAW,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}