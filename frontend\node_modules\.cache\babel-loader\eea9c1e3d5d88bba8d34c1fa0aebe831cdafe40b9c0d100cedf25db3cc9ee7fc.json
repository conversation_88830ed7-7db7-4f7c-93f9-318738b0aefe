{"ast": null, "code": "var raf = function raf(callback) {\n  return +setTimeout(callback, 16);\n};\nvar caf = function caf(num) {\n  return clearTimeout(num);\n};\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = function raf(callback) {\n    return window.requestAnimationFrame(callback);\n  };\n  caf = function caf(handle) {\n    return window.cancelAnimationFrame(handle);\n  };\n}\nvar rafUUID = 0;\nvar rafIds = new Map();\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\nvar wrapperRaf = function wrapperRaf(callback) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  var id = rafUUID;\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id);\n\n      // Trigger\n      callback();\n    } else {\n      // Next raf\n      var realId = raf(function () {\n        callRef(leftTimes - 1);\n      });\n\n      // Bind real raf id\n      rafIds.set(id, realId);\n    }\n  }\n  callRef(times);\n  return id;\n};\nwrapperRaf.cancel = function (id) {\n  var realId = rafIds.get(id);\n  cleanup(id);\n  return caf(realId);\n};\nif (process.env.NODE_ENV !== 'production') {\n  wrapperRaf.ids = function () {\n    return rafIds;\n  };\n}\nexport default wrapperRaf;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}