{"ast": null, "code": "import { raf } from '@react-spring/rafz';\nexport { raf } from '@react-spring/rafz';\nimport { useRef, useEffect, useLayoutEffect, useState } from 'react';\nfunction noop() {}\nconst defineHidden = (obj, key, value) => Object.defineProperty(obj, key, {\n  value,\n  writable: true,\n  configurable: true\n});\nconst is = {\n  arr: Array.isArray,\n  obj: a => !!a && a.constructor.name === 'Object',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  und: a => a === undefined\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nconst each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], `${i}`);\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nconst toArray = a => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nconst flushCalls = (queue, ...args) => flush(queue, fn => fn(...args));\nconst isSSR = () => typeof window === 'undefined' || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nlet createStringInterpolator$1;\nlet to;\nlet colors$1 = null;\nlet skipAnimation = false;\nlet willAdvance = noop;\nconst assign = globals => {\n  if (globals.to) to = globals.to;\n  if (globals.now) raf.now = globals.now;\n  if (globals.colors !== undefined) colors$1 = globals.colors;\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator) createStringInterpolator$1 = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance) willAdvance = globals.willAdvance;\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop;\n};\nvar globals = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  get createStringInterpolator() {\n    return createStringInterpolator$1;\n  },\n  get to() {\n    return to;\n  },\n  get colors() {\n    return colors$1;\n  },\n  get skipAnimation() {\n    return skipAnimation;\n  },\n  get willAdvance() {\n    return willAdvance;\n  },\n  assign: assign\n});\nconst startQueue = new Set();\nlet currentFrame = [];\nlet prevFrame = [];\nlet priority = 0;\nconst frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      raf.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      raf(advance);\n    }\n  },\n  advance,\n  sort(animation) {\n    if (priority) {\n      raf.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  raf(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(findIndex(currentFrame, other => other.priority > animation.priority), 0, animation);\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\nconst clamp = (min, max, v) => Math.min(Math.max(v, min), max);\nconst colors = {\n  transparent: 0x00000000,\n  aliceblue: 0xf0f8ffff,\n  antiquewhite: 0xfaebd7ff,\n  aqua: 0x00ffffff,\n  aquamarine: 0x7fffd4ff,\n  azure: 0xf0ffffff,\n  beige: 0xf5f5dcff,\n  bisque: 0xffe4c4ff,\n  black: 0x000000ff,\n  blanchedalmond: 0xffebcdff,\n  blue: 0x0000ffff,\n  blueviolet: 0x8a2be2ff,\n  brown: 0xa52a2aff,\n  burlywood: 0xdeb887ff,\n  burntsienna: 0xea7e5dff,\n  cadetblue: 0x5f9ea0ff,\n  chartreuse: 0x7fff00ff,\n  chocolate: 0xd2691eff,\n  coral: 0xff7f50ff,\n  cornflowerblue: 0x6495edff,\n  cornsilk: 0xfff8dcff,\n  crimson: 0xdc143cff,\n  cyan: 0x00ffffff,\n  darkblue: 0x00008bff,\n  darkcyan: 0x008b8bff,\n  darkgoldenrod: 0xb8860bff,\n  darkgray: 0xa9a9a9ff,\n  darkgreen: 0x006400ff,\n  darkgrey: 0xa9a9a9ff,\n  darkkhaki: 0xbdb76bff,\n  darkmagenta: 0x8b008bff,\n  darkolivegreen: 0x556b2fff,\n  darkorange: 0xff8c00ff,\n  darkorchid: 0x9932ccff,\n  darkred: 0x8b0000ff,\n  darksalmon: 0xe9967aff,\n  darkseagreen: 0x8fbc8fff,\n  darkslateblue: 0x483d8bff,\n  darkslategray: 0x2f4f4fff,\n  darkslategrey: 0x2f4f4fff,\n  darkturquoise: 0x00ced1ff,\n  darkviolet: 0x9400d3ff,\n  deeppink: 0xff1493ff,\n  deepskyblue: 0x00bfffff,\n  dimgray: 0x696969ff,\n  dimgrey: 0x696969ff,\n  dodgerblue: 0x1e90ffff,\n  firebrick: 0xb22222ff,\n  floralwhite: 0xfffaf0ff,\n  forestgreen: 0x228b22ff,\n  fuchsia: 0xff00ffff,\n  gainsboro: 0xdcdcdcff,\n  ghostwhite: 0xf8f8ffff,\n  gold: 0xffd700ff,\n  goldenrod: 0xdaa520ff,\n  gray: 0x808080ff,\n  green: 0x008000ff,\n  greenyellow: 0xadff2fff,\n  grey: 0x808080ff,\n  honeydew: 0xf0fff0ff,\n  hotpink: 0xff69b4ff,\n  indianred: 0xcd5c5cff,\n  indigo: 0x4b0082ff,\n  ivory: 0xfffff0ff,\n  khaki: 0xf0e68cff,\n  lavender: 0xe6e6faff,\n  lavenderblush: 0xfff0f5ff,\n  lawngreen: 0x7cfc00ff,\n  lemonchiffon: 0xfffacdff,\n  lightblue: 0xadd8e6ff,\n  lightcoral: 0xf08080ff,\n  lightcyan: 0xe0ffffff,\n  lightgoldenrodyellow: 0xfafad2ff,\n  lightgray: 0xd3d3d3ff,\n  lightgreen: 0x90ee90ff,\n  lightgrey: 0xd3d3d3ff,\n  lightpink: 0xffb6c1ff,\n  lightsalmon: 0xffa07aff,\n  lightseagreen: 0x20b2aaff,\n  lightskyblue: 0x87cefaff,\n  lightslategray: 0x778899ff,\n  lightslategrey: 0x778899ff,\n  lightsteelblue: 0xb0c4deff,\n  lightyellow: 0xffffe0ff,\n  lime: 0x00ff00ff,\n  limegreen: 0x32cd32ff,\n  linen: 0xfaf0e6ff,\n  magenta: 0xff00ffff,\n  maroon: 0x800000ff,\n  mediumaquamarine: 0x66cdaaff,\n  mediumblue: 0x0000cdff,\n  mediumorchid: 0xba55d3ff,\n  mediumpurple: 0x9370dbff,\n  mediumseagreen: 0x3cb371ff,\n  mediumslateblue: 0x7b68eeff,\n  mediumspringgreen: 0x00fa9aff,\n  mediumturquoise: 0x48d1ccff,\n  mediumvioletred: 0xc71585ff,\n  midnightblue: 0x191970ff,\n  mintcream: 0xf5fffaff,\n  mistyrose: 0xffe4e1ff,\n  moccasin: 0xffe4b5ff,\n  navajowhite: 0xffdeadff,\n  navy: 0x000080ff,\n  oldlace: 0xfdf5e6ff,\n  olive: 0x808000ff,\n  olivedrab: 0x6b8e23ff,\n  orange: 0xffa500ff,\n  orangered: 0xff4500ff,\n  orchid: 0xda70d6ff,\n  palegoldenrod: 0xeee8aaff,\n  palegreen: 0x98fb98ff,\n  paleturquoise: 0xafeeeeff,\n  palevioletred: 0xdb7093ff,\n  papayawhip: 0xffefd5ff,\n  peachpuff: 0xffdab9ff,\n  peru: 0xcd853fff,\n  pink: 0xffc0cbff,\n  plum: 0xdda0ddff,\n  powderblue: 0xb0e0e6ff,\n  purple: 0x800080ff,\n  rebeccapurple: 0x663399ff,\n  red: 0xff0000ff,\n  rosybrown: 0xbc8f8fff,\n  royalblue: 0x4169e1ff,\n  saddlebrown: 0x8b4513ff,\n  salmon: 0xfa8072ff,\n  sandybrown: 0xf4a460ff,\n  seagreen: 0x2e8b57ff,\n  seashell: 0xfff5eeff,\n  sienna: 0xa0522dff,\n  silver: 0xc0c0c0ff,\n  skyblue: 0x87ceebff,\n  slateblue: 0x6a5acdff,\n  slategray: 0x708090ff,\n  slategrey: 0x708090ff,\n  snow: 0xfffafaff,\n  springgreen: 0x00ff7fff,\n  steelblue: 0x4682b4ff,\n  tan: 0xd2b48cff,\n  teal: 0x008080ff,\n  thistle: 0xd8bfd8ff,\n  tomato: 0xff6347ff,\n  turquoise: 0x40e0d0ff,\n  violet: 0xee82eeff,\n  wheat: 0xf5deb3ff,\n  white: 0xffffffff,\n  whitesmoke: 0xf5f5f5ff,\n  yellow: 0xffff00ff,\n  yellowgreen: 0x9acd32ff\n};\nconst NUMBER = '[-+]?\\\\d*\\\\.?\\\\d+';\nconst PERCENTAGE = NUMBER + '%';\nfunction call(...parts) {\n  return '\\\\(\\\\s*(' + parts.join(')\\\\s*,\\\\s*(') + ')\\\\s*\\\\)';\n}\nconst rgb = new RegExp('rgb' + call(NUMBER, NUMBER, NUMBER));\nconst rgba = new RegExp('rgba' + call(NUMBER, NUMBER, NUMBER, NUMBER));\nconst hsl = new RegExp('hsl' + call(NUMBER, PERCENTAGE, PERCENTAGE));\nconst hsla = new RegExp('hsla' + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER));\nconst hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nconst hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nconst hex6 = /^#([0-9a-fA-F]{6})$/;\nconst hex8 = /^#([0-9a-fA-F]{8})$/;\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === 'number') {\n    return color >>> 0 === color && color >= 0 && color <= 0xffffffff ? color : null;\n  }\n  if (match = hex6.exec(color)) return parseInt(match[1] + 'ff', 16) >>> 0;\n  if (colors$1 && colors$1[color] !== undefined) {\n    return colors$1[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 | parse255(match[2]) << 16 | parse255(match[3]) << 8 | 0x000000ff) >>> 0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 | parse255(match[2]) << 16 | parse255(match[3]) << 8 | parse1(match[4])) >>> 0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(match[1] + match[1] + match[2] + match[2] + match[3] + match[3] + 'ff', 16) >>> 0;\n  }\n  if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(match[1] + match[1] + match[2] + match[2] + match[3] + match[3] + match[4] + match[4], 16) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(parse360(match[1]), parsePercentage(match[2]), parsePercentage(match[3])) | 0x000000ff) >>> 0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(parse360(match[1]), parsePercentage(match[2]), parsePercentage(match[3])) | parse1(match[4])) >>> 0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) return 0;\n  if (int > 255) return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) return 0;\n  if (num > 1) return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0) return 0;\n  if (int > 100) return 1;\n  return int / 100;\n}\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null) return input;\n  int32Color = int32Color || 0;\n  let r = (int32Color & 0xff000000) >>> 24;\n  let g = (int32Color & 0x00ff0000) >>> 16;\n  let b = (int32Color & 0x0000ff00) >>> 8;\n  let a = (int32Color & 0x000000ff) / 255;\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n}\nconst createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output: output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator$1(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || 'extend';\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || 'extend';\n  const easing = config.easing || (t => t);\n  return input => {\n    const range = findRange(input, inputRange);\n    return interpolate(input, inputRange[range], inputRange[range + 1], outputRange[range], outputRange[range + 1], easing, extrapolateLeft, extrapolateRight, config.map);\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === 'identity') return result;else if (extrapolateLeft === 'clamp') result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === 'identity') return result;else if (extrapolateRight === 'clamp') result = inputMax;\n  }\n  if (outputMin === outputMax) return outputMin;\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity) result = -result;else if (inputMax === Infinity) result = result - inputMin;else result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity) result = -result;else if (outputMax === Infinity) result = result + outputMin;else result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i) if (inputRange[i] >= input) break;\n  return i - 1;\n}\nconst steps = (steps, direction = 'end') => progress => {\n  progress = direction === 'end' ? Math.min(progress, 0.999) : Math.max(progress, 0.001);\n  const expanded = progress * steps;\n  const rounded = direction === 'end' ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps);\n};\nconst c1 = 1.70158;\nconst c2 = c1 * 1.525;\nconst c3 = c1 + 1;\nconst c4 = 2 * Math.PI / 3;\nconst c5 = 2 * Math.PI / 4.5;\nconst bounceOut = x => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nconst easings = {\n  linear: x => x,\n  easeInQuad: x => x * x,\n  easeOutQuad: x => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: x => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: x => x * x * x,\n  easeOutCubic: x => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: x => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: x => x * x * x * x,\n  easeOutQuart: x => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: x => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: x => x * x * x * x * x,\n  easeOutQuint: x => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: x => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: x => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: x => Math.sin(x * Math.PI / 2),\n  easeInOutSine: x => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: x => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: x => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: x => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: x => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: x => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: x => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: x => c3 * x * x * x - c1 * x * x,\n  easeOutBack: x => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: x => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: x => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: x => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: x => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: x => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: x => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nconst $get = Symbol.for('FluidValue.get');\nconst $observers = Symbol.for('FluidValue.observers');\nconst hasFluidValue = arg => Boolean(arg && arg[$get]);\nconst getFluidValue = arg => arg && arg[$get] ? arg[$get]() : arg;\nconst getFluidObservers = target => target[$observers] || null;\nfunction callFluidObserver(observer, event) {\n  if (observer.eventObserved) {\n    observer.eventObserved(event);\n  } else {\n    observer(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  let observers = target[$observers];\n  if (observers) {\n    observers.forEach(observer => {\n      callFluidObserver(observer, event);\n    });\n  }\n}\nclass FluidValue {\n  constructor(get) {\n    this[$get] = void 0;\n    this[$observers] = void 0;\n    if (!get && !(get = this.get)) {\n      throw Error('Unknown getter');\n    }\n    setFluidGetter(this, get);\n  }\n}\nconst setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = new Set());\n    }\n    if (!observers.has(observer)) {\n      observers.add(observer);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer);\n      }\n    }\n  }\n  return observer;\n}\nfunction removeFluidObserver(target, observer) {\n  let observers = target[$observers];\n  if (observers && observers.has(observer)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer);\n    }\n  }\n}\nconst setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\nconst numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nconst colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nconst unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, 'i');\nconst rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nconst cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\nconst variableToRgba = input => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith('--')) {\n    const _value = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (_value) {\n      return _value;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nconst parseCSSVariable = current => {\n  const match = cssVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\nlet namedColorRegex;\nconst rgbaRound = (_, p1, p2, p3, p4) => `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;\nconst createStringInterpolator = config => {\n  if (!namedColorRegex) namedColorRegex = colors$1 ? new RegExp(`(${Object.keys(colors$1).join('|')})(?!\\\\w)`, 'g') : /^\\b$/;\n  const output = config.output.map(value => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map(value => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map((_, i) => keyframes.map(values => {\n    if (!(i in values)) {\n      throw Error('The arity of each \"output\" value must be equal');\n    }\n    return values[i];\n  }));\n  const interpolators = outputRanges.map(output => createInterpolator(_extends({}, config, {\n    output\n  })));\n  return input => {\n    var _output$find;\n    const missingUnit = !unitRegex.test(output[0]) && ((_output$find = output.find(value => unitRegex.test(value))) == null ? void 0 : _output$find.replace(numberRegex, ''));\n    let i = 0;\n    return output[0].replace(numberRegex, () => `${interpolators[i++](input)}${missingUnit || ''}`).replace(rgbaRegex, rgbaRound);\n  };\n};\nconst prefix = 'react-spring: ';\nconst once = fn => {\n  const func = fn;\n  let called = false;\n  if (typeof func != 'function') {\n    throw new TypeError(`${prefix}once requires a function parameter`);\n  }\n  return (...args) => {\n    if (!called) {\n      func(...args);\n      called = true;\n    }\n  };\n};\nconst warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(`${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`);\n}\nconst warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(`${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`);\n}\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == '#' || /\\d/.test(value) || !isSSR() && cssVariableRegex.test(value) || value in (colors$1 || {}));\n}\nlet observer;\nconst resizeHandlers = new WeakMap();\nconst handleObservation = entries => entries.forEach(({\n  target,\n  contentRect\n}) => {\n  var _resizeHandlers$get;\n  return (_resizeHandlers$get = resizeHandlers.get(target)) == null ? void 0 : _resizeHandlers$get.forEach(handler => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== 'undefined') {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers = resizeHandlers.get(target);\n    if (!elementHandlers) return;\n    elementHandlers.delete(handler);\n    if (!elementHandlers.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\nconst listeners = new Set();\nlet cleanupWindowResizeHandler;\nconst createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(callback => callback({\n      width: window.innerWidth,\n      height: window.innerHeight\n    }));\n  };\n  window.addEventListener('resize', handleResize);\n  return () => {\n    window.removeEventListener('resize', handleResize);\n  };\n};\nconst resizeWindow = callback => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = undefined;\n    }\n  };\n};\nconst onResize = (callback, {\n  container: _container = document.documentElement\n} = {}) => {\n  if (_container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, _container);\n  }\n};\nconst progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\nconst SCROLL_KEYS = {\n  x: {\n    length: 'Width',\n    position: 'Left'\n  },\n  y: {\n    length: 'Height',\n    position: 'Top'\n  }\n};\nclass ScrollHandler {\n  constructor(callback, container) {\n    this.callback = void 0;\n    this.container = void 0;\n    this.info = void 0;\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = axisName => {\n      const axis = this.info[axisName];\n      const {\n        length,\n        position\n      } = SCROLL_KEYS[axisName];\n      axis.current = this.container[`scroll${position}`];\n      axis.scrollLength = this.container['scroll' + length] - this.container['client' + length];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis('x');\n      this.updateAxis('y');\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n}\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getTarget = container => container === document.documentElement ? window : container;\nconst onScroll = (callback, {\n  container: _container = document.documentElement\n} = {}) => {\n  let containerHandlers = onScrollHandlers.get(_container);\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(_container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, _container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(_container)) {\n    const listener = () => {\n      var _containerHandlers;\n      (_containerHandlers = containerHandlers) == null ? void 0 : _containerHandlers.forEach(handler => handler.advance());\n      return true;\n    };\n    scrollListeners.set(_container, listener);\n    const target = getTarget(_container);\n    window.addEventListener('resize', listener, {\n      passive: true\n    });\n    if (_container !== document.documentElement) {\n      resizeListeners.set(_container, onResize(listener, {\n        container: _container\n      }));\n    }\n    target.addEventListener('scroll', listener, {\n      passive: true\n    });\n  }\n  const animateScroll = scrollListeners.get(_container);\n  raf(animateScroll);\n  return () => {\n    raf.cancel(animateScroll);\n    const containerHandlers = onScrollHandlers.get(_container);\n    if (!containerHandlers) return;\n    containerHandlers.delete(containerHandler);\n    if (containerHandlers.size) return;\n    const listener = scrollListeners.get(_container);\n    scrollListeners.delete(_container);\n    if (listener) {\n      var _resizeListeners$get;\n      getTarget(_container).removeEventListener('scroll', listener);\n      window.removeEventListener('resize', listener);\n      (_resizeListeners$get = resizeListeners.get(_container)) == null ? void 0 : _resizeListeners$get();\n    }\n  };\n};\nfunction useConstant(init) {\n  const ref = useRef(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\nconst useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect;\nconst useIsMounted = () => {\n  const isMounted = useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\nfunction useForceUpdate() {\n  const update = useState()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = useState(() => ({\n    inputs,\n    result: getResult()\n  }));\n  const committed = useRef();\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(inputs && cache.inputs && areInputsEqual(inputs, cache.inputs));\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  useEffect(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = undefined;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nconst useOnce = effect => useEffect(effect, emptyDeps);\nconst emptyDeps = [];\nfunction usePrev(value) {\n  const prevRef = useRef();\n  useEffect(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\nconst useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia('(prefers-reduced-motion)');\n    const handleMediaChange = e => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    mql.addEventListener('change', handleMediaChange);\n    return () => {\n      mql.removeEventListener('change', handleMediaChange);\n    };\n  }, []);\n  return reducedMotion;\n};\nexport { FluidValue, globals as Globals, addFluidObserver, callFluidObserver, callFluidObservers, clamp, colorToRgba, colors, createInterpolator, createStringInterpolator, defineHidden, deprecateDirectCall, deprecateInterpolate, each, eachProp, easings, flush, flushCalls, frameLoop, getFluidObservers, getFluidValue, hasFluidValue, hex3, hex4, hex6, hex8, hsl, hsla, is, isAnimatedString, isEqual, isSSR, noop, onResize, onScroll, once, prefix, removeFluidObserver, rgb, rgba, setFluidGetter, toArray, useConstant, useForceUpdate, useIsomorphicLayoutEffect, useMemoOne, useOnce, usePrev, useReducedMotion };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}