{"ast": null, "code": "import \"./jumbo-tabs.css\";\nimport { JumboTab, JumboTabs } from './jumbo-tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(JumboTabs, {\n  Tab: JumboTab\n});", "map": {"version": 3, "names": ["JumboTab", "JumboTabs", "attachPropertiesToComponent", "Tab"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/jumbo-tabs/index.js"], "sourcesContent": ["import \"./jumbo-tabs.css\";\nimport { JumboTab, JumboTabs } from './jumbo-tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(JumboTabs, {\n  Tab: JumboTab\n});"], "mappings": "AAAA,OAAO,kBAAkB;AACzB,SAASA,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAClD,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACD,SAAS,EAAE;EACpDE,GAAG,EAAEH;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}