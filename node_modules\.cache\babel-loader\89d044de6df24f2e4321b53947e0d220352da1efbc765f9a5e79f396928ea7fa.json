{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useConfig } from '../config-provider';\nimport \"./error-block.css\";\nconst classPrefix = `adm-error-block`;\nconst defaultProps = {\n  status: 'default'\n};\nexport function createErrorBlock(imageRecord) {\n  const ErrorBlock = p => {\n    var _a;\n    const props = mergeProps(defaultProps, p);\n    const {\n      locale\n    } = useConfig();\n    const contentPack = locale.ErrorBlock[props.status];\n    const desc = 'description' in props ? props.description : contentPack.description;\n    const title = 'title' in props ? props.title : contentPack.title;\n    const image = (_a = props.image) !== null && _a !== void 0 ? _a : imageRecord[props.status];\n    const imageNode = typeof image === 'string' ? React.createElement(\"img\", {\n      src: image,\n      alt: 'error block image'\n    }) : image;\n    return withNativeProps(props, React.createElement(\"div\", {\n      className: classNames(classPrefix, {\n        [`${classPrefix}-full-page`]: props.fullPage\n      })\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-image`\n    }, imageNode), React.createElement(\"div\", {\n      className: `${classPrefix}-description`\n    }, ![undefined, null].includes(title) && React.createElement(\"div\", {\n      className: `${classPrefix}-description-title`\n    }, title), ![undefined, null].includes(desc) && React.createElement(\"div\", {\n      className: `${classPrefix}-description-subtitle`\n    }, desc)), props.children && React.createElement(\"div\", {\n      className: `${classPrefix}-content`\n    }, props.children)));\n  };\n  return ErrorBlock;\n}", "map": {"version": 3, "names": ["React", "classNames", "mergeProps", "withNativeProps", "useConfig", "classPrefix", "defaultProps", "status", "createErrorBlock", "imageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "p", "_a", "props", "locale", "contentPack", "desc", "description", "title", "image", "imageNode", "createElement", "src", "alt", "className", "fullPage", "undefined", "includes", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/error-block/create-error-block.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useConfig } from '../config-provider';\nimport \"./error-block.css\";\nconst classPrefix = `adm-error-block`;\nconst defaultProps = {\n  status: 'default'\n};\nexport function createErrorBlock(imageRecord) {\n  const ErrorBlock = p => {\n    var _a;\n    const props = mergeProps(defaultProps, p);\n    const {\n      locale\n    } = useConfig();\n    const contentPack = locale.ErrorBlock[props.status];\n    const desc = 'description' in props ? props.description : contentPack.description;\n    const title = 'title' in props ? props.title : contentPack.title;\n    const image = (_a = props.image) !== null && _a !== void 0 ? _a : imageRecord[props.status];\n    const imageNode = typeof image === 'string' ? React.createElement(\"img\", {\n      src: image,\n      alt: 'error block image'\n    }) : image;\n    return withNativeProps(props, React.createElement(\"div\", {\n      className: classNames(classPrefix, {\n        [`${classPrefix}-full-page`]: props.fullPage\n      })\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-image`\n    }, imageNode), React.createElement(\"div\", {\n      className: `${classPrefix}-description`\n    }, ![undefined, null].includes(title) && React.createElement(\"div\", {\n      className: `${classPrefix}-description-title`\n    }, title), ![undefined, null].includes(desc) && React.createElement(\"div\", {\n      className: `${classPrefix}-description-subtitle`\n    }, desc)), props.children && React.createElement(\"div\", {\n      className: `${classPrefix}-content`\n    }, props.children)));\n  };\n  return ErrorBlock;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAO,mBAAmB;AAC1B,MAAMC,WAAW,GAAG,iBAAiB;AACrC,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,MAAMC,UAAU,GAAGC,CAAC,IAAI;IACtB,IAAIC,EAAE;IACN,MAAMC,KAAK,GAAGX,UAAU,CAACI,YAAY,EAAEK,CAAC,CAAC;IACzC,MAAM;MACJG;IACF,CAAC,GAAGV,SAAS,CAAC,CAAC;IACf,MAAMW,WAAW,GAAGD,MAAM,CAACJ,UAAU,CAACG,KAAK,CAACN,MAAM,CAAC;IACnD,MAAMS,IAAI,GAAG,aAAa,IAAIH,KAAK,GAAGA,KAAK,CAACI,WAAW,GAAGF,WAAW,CAACE,WAAW;IACjF,MAAMC,KAAK,GAAG,OAAO,IAAIL,KAAK,GAAGA,KAAK,CAACK,KAAK,GAAGH,WAAW,CAACG,KAAK;IAChE,MAAMC,KAAK,GAAG,CAACP,EAAE,GAAGC,KAAK,CAACM,KAAK,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGH,WAAW,CAACI,KAAK,CAACN,MAAM,CAAC;IAC3F,MAAMa,SAAS,GAAG,OAAOD,KAAK,KAAK,QAAQ,GAAGnB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACvEC,GAAG,EAAEH,KAAK;MACVI,GAAG,EAAE;IACP,CAAC,CAAC,GAAGJ,KAAK;IACV,OAAOhB,eAAe,CAACU,KAAK,EAAEb,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACvDG,SAAS,EAAEvB,UAAU,CAACI,WAAW,EAAE;QACjC,CAAC,GAAGA,WAAW,YAAY,GAAGQ,KAAK,CAACY;MACtC,CAAC;IACH,CAAC,EAAEzB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MAC5BG,SAAS,EAAE,GAAGnB,WAAW;IAC3B,CAAC,EAAEe,SAAS,CAAC,EAAEpB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACxCG,SAAS,EAAE,GAAGnB,WAAW;IAC3B,CAAC,EAAE,CAAC,CAACqB,SAAS,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACT,KAAK,CAAC,IAAIlB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MAClEG,SAAS,EAAE,GAAGnB,WAAW;IAC3B,CAAC,EAAEa,KAAK,CAAC,EAAE,CAAC,CAACQ,SAAS,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACX,IAAI,CAAC,IAAIhB,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACzEG,SAAS,EAAE,GAAGnB,WAAW;IAC3B,CAAC,EAAEW,IAAI,CAAC,CAAC,EAAEH,KAAK,CAACe,QAAQ,IAAI5B,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;MACtDG,SAAS,EAAE,GAAGnB,WAAW;IAC3B,CAAC,EAAEQ,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC;EACtB,CAAC;EACD,OAAOlB,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}