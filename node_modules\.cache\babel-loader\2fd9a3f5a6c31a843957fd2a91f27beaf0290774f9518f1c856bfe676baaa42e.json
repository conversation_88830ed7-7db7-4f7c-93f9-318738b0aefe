{"ast": null, "code": "import * as React from \"react\";\nfunction LikeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LikeOutline-LikeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LikeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LikeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M20.0245195,4.1214011 L23.7315534,5.49221472 C26.7196757,6.59718406 28.2994291,9.8932279 27.3528533,12.9445563 L37.0825854,12.9440637 C40.3506833,12.9440637 43,15.6357217 43,18.9560501 C43,19.1198466 42.9934114,19.2835746 42.9802505,19.4468244 L41.6877924,37.478788 C41.4362984,40.5983809 38.8709296,43 35.7901273,43 L8.94494304,43 C6.76621116,43 5,41.2055614 5,38.9920091 L5,16.9520546 C5,14.7385024 6.76621116,12.9440637 8.94494304,12.9440637 L14.763734,12.9438317 L17.4963776,5.31913403 C17.8689627,4.27910467 19.0008504,3.74286138 20.0245195,4.1214011 Z M13.476,15.9490857 L8.94494304,15.9500569 L8.94494304,15.9500569 C8.439166,15.9500569 8.02231243,16.3368683 7.96534241,16.8352005 L7.95870728,16.9520546 L7.95870728,38.9920091 C7.95870728,39.5058694 8.33943392,39.9293852 8.82992712,39.9872657 L8.94494304,39.9940068 L13.476,39.9940068 C13.6969139,39.9940068 13.876,39.8149207 13.876,39.5940068 L13.876,16.349 C13.8760857,16.1280861 13.6969996,15.949 13.4760857,15.949 C13.4760571,15.949 13.4760286,15.949 13.476,15.9490857 Z M22.7196159,8.31692433 L20.3174269,7.42886785 C20.110219,7.35226593 19.8801457,7.45814297 19.8035438,7.66535083 C19.8030863,7.66658827 19.802635,7.66782796 19.8021898,7.66906986 L16.857463,15.8835487 C16.8419374,15.9268582 16.834,15.9725215 16.834,16.0185297 L16.834,39.5940068 C16.834,39.8149207 17.0130861,39.9940068 17.234,39.9940068 L35.7901273,39.9940068 L35.7901273,39.9940068 C37.2712823,39.9940068 38.5141636,38.8837909 38.7193674,37.4117363 L38.7389599,37.2334008 L40.031418,19.2014373 C40.0379984,19.1198123 40.0412927,19.0379483 40.0412927,18.9560501 C40.0412927,17.3551775 38.8095632,16.0465885 37.2566231,15.9551598 L37.0827986,15.9500569 L23.860993,15.9508136 C23.6400791,15.9508263 23.4609828,15.7717504 23.4609702,15.5508365 C23.4609679,15.5106459 23.4670227,15.4706843 23.4789307,15.4322983 L24.5309842,12.0409582 L24.5309842,12.0409582 C24.9874031,10.569672 24.2659471,8.98929997 22.88219,8.38255136 L22.7196159,8.31692433 Z\",\n    id: \"LikeOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default LikeOutline;", "map": {"version": 3, "names": ["React", "LikeOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/LikeOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction LikeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LikeOutline-LikeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LikeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LikeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M20.0245195,4.1214011 L23.7315534,5.49221472 C26.7196757,6.59718406 28.2994291,9.8932279 27.3528533,12.9445563 L37.0825854,12.9440637 C40.3506833,12.9440637 43,15.6357217 43,18.9560501 C43,19.1198466 42.9934114,19.2835746 42.9802505,19.4468244 L41.6877924,37.478788 C41.4362984,40.5983809 38.8709296,43 35.7901273,43 L8.94494304,43 C6.76621116,43 5,41.2055614 5,38.9920091 L5,16.9520546 C5,14.7385024 6.76621116,12.9440637 8.94494304,12.9440637 L14.763734,12.9438317 L17.4963776,5.31913403 C17.8689627,4.27910467 19.0008504,3.74286138 20.0245195,4.1214011 Z M13.476,15.9490857 L8.94494304,15.9500569 L8.94494304,15.9500569 C8.439166,15.9500569 8.02231243,16.3368683 7.96534241,16.8352005 L7.95870728,16.9520546 L7.95870728,38.9920091 C7.95870728,39.5058694 8.33943392,39.9293852 8.82992712,39.9872657 L8.94494304,39.9940068 L13.476,39.9940068 C13.6969139,39.9940068 13.876,39.8149207 13.876,39.5940068 L13.876,16.349 C13.8760857,16.1280861 13.6969996,15.949 13.4760857,15.949 C13.4760571,15.949 13.4760286,15.949 13.476,15.9490857 Z M22.7196159,8.31692433 L20.3174269,7.42886785 C20.110219,7.35226593 19.8801457,7.45814297 19.8035438,7.66535083 C19.8030863,7.66658827 19.802635,7.66782796 19.8021898,7.66906986 L16.857463,15.8835487 C16.8419374,15.9268582 16.834,15.9725215 16.834,16.0185297 L16.834,39.5940068 C16.834,39.8149207 17.0130861,39.9940068 17.234,39.9940068 L35.7901273,39.9940068 L35.7901273,39.9940068 C37.2712823,39.9940068 38.5141636,38.8837909 38.7193674,37.4117363 L38.7389599,37.2334008 L40.031418,19.2014373 C40.0379984,19.1198123 40.0412927,19.0379483 40.0412927,18.9560501 C40.0412927,17.3551775 38.8095632,16.0465885 37.2566231,15.9551598 L37.0827986,15.9500569 L23.860993,15.9508136 C23.6400791,15.9508263 23.4609828,15.7717504 23.4609702,15.5508365 C23.4609679,15.5106459 23.4670227,15.4706843 23.4789307,15.4322983 L24.5309842,12.0409582 L24.5309842,12.0409582 C24.9874031,10.569672 24.2659471,8.98929997 22.88219,8.38255136 L22.7196159,8.31692433 Z\",\n    id: \"LikeOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default LikeOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,y7DAAy7D;IAC57DR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}