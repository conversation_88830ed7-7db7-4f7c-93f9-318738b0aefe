{"ast": null, "code": "import { useLayoutEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useLayoutEffect);\nexport default useEffectWithTarget;", "map": {"version": 3, "names": ["useLayoutEffect", "createEffectWithTarget", "useEffectWithTarget"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useLayoutEffect);\nexport default useEffectWithTarget;"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,IAAIC,mBAAmB,GAAGD,sBAAsB,CAACD,eAAe,CAAC;AACjE,eAAeE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}