{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useTimeout = function (fn, delay) {\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setTimeout(timerCallback, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n};\nexport default useTimeout;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useMemoizedFn", "isNumber", "useTimeout", "fn", "delay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerRef", "clear", "current", "clearTimeout", "setTimeout"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useTimeout/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useTimeout = function (fn, delay) {\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setTimeout(timerCallback, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n};\nexport default useTimeout;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAE;EACpC,IAAIC,aAAa,GAAGL,aAAa,CAACG,EAAE,CAAC;EACrC,IAAIG,QAAQ,GAAGP,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIQ,KAAK,GAAGV,WAAW,CAAC,YAAY;IAClC,IAAIS,QAAQ,CAACE,OAAO,EAAE;MACpBC,YAAY,CAACH,QAAQ,CAACE,OAAO,CAAC;IAChC;EACF,CAAC,EAAE,EAAE,CAAC;EACNV,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACjC;IACF;IACAE,QAAQ,CAACE,OAAO,GAAGE,UAAU,CAACL,aAAa,EAAED,KAAK,CAAC;IACnD,OAAOG,KAAK;EACd,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACX,OAAOG,KAAK;AACd,CAAC;AACD,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}