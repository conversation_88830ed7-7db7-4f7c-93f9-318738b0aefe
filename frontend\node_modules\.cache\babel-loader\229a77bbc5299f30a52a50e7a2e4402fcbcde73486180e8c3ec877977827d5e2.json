{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { debounce } from '../utils/lodash-polyfill';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useDebounceFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useDebounceFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var debounced = useMemo(function () {\n    return debounce(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    debounced.cancel();\n  });\n  return {\n    run: debounced,\n    cancel: debounced.cancel,\n    flush: debounced.flush\n  };\n}\nexport default useDebounceFn;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}