{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Corner = memo(props => withNativeProps(props, React.createElement(\"svg\", {\n  viewBox: '0 0 30 30'\n}, React.createElement(\"g\", {\n  stroke: 'none',\n  strokeWidth: '1',\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M30,0 C13.4314575,3.04359188e-15 -2.02906125e-15,13.4314575 0,30 L0,30 L0,0 Z',\n  fill: 'var(--adm-color-background)',\n  transform: 'translate(15.000000, 15.000000) scale(-1, -1) translate(-15.000000, -15.000000) '\n})))));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}