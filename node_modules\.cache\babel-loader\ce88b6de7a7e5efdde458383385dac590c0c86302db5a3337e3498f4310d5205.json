{"ast": null, "code": "import { useSpring } from '@react-spring/web';\nimport { useMutationEffect } from './use-mutation-effect';\nimport { bound } from './bound';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport { useIsomorphicUpdateLayoutEffect } from './use-isomorphic-update-layout-effect';\nexport const useTabListScroll = (targetRef, activeIndex) => {\n  const [{\n    scrollLeft\n  }, api] = useSpring(() => ({\n    scrollLeft: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  function animate(immediate = false) {\n    const container = targetRef.current;\n    if (!container) return;\n    if (activeIndex === undefined) return;\n    const activeTabWrapper = container.children.item(activeIndex);\n    const activeTab = activeTabWrapper.children.item(0);\n    const activeTabLeft = activeTab.offsetLeft;\n    const activeTabWidth = activeTab.offsetWidth;\n    const containerWidth = container.offsetWidth;\n    const containerScrollWidth = container.scrollWidth;\n    const containerScrollLeft = container.scrollLeft;\n    const maxScrollDistance = containerScrollWidth - containerWidth;\n    if (maxScrollDistance <= 0) return;\n    const nextScrollLeft = bound(activeTabLeft - (containerWidth - activeTabWidth) / 2, 0, containerScrollWidth - containerWidth);\n    api.start({\n      scrollLeft: nextScrollLeft,\n      from: {\n        scrollLeft: containerScrollLeft\n      },\n      immediate: immediate && !scrollLeft.isAnimating\n    });\n  }\n  useIsomorphicLayoutEffect(() => {\n    animate(true);\n  }, []);\n  useIsomorphicUpdateLayoutEffect(() => {\n    animate();\n  }, [activeIndex]);\n  useMutationEffect(() => {\n    animate(true);\n  }, targetRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  return {\n    scrollLeft,\n    animate\n  };\n};", "map": {"version": 3, "names": ["useSpring", "useMutationEffect", "bound", "useIsomorphicLayoutEffect", "useIsomorphicUpdateLayoutEffect", "useTabListScroll", "targetRef", "activeIndex", "scrollLeft", "api", "config", "tension", "clamp", "animate", "immediate", "container", "current", "undefined", "activeTabWrapper", "children", "item", "activeTab", "activeTabLeft", "offsetLeft", "activeTabWidth", "offsetWidth", "containerWidth", "containerScrollWidth", "scrollWidth", "containerScrollLeft", "maxScrollDistance", "nextScrollLeft", "start", "from", "isAnimating", "subtree", "childList", "characterData"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/use-tab-list-scroll.js"], "sourcesContent": ["import { useSpring } from '@react-spring/web';\nimport { useMutationEffect } from './use-mutation-effect';\nimport { bound } from './bound';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport { useIsomorphicUpdateLayoutEffect } from './use-isomorphic-update-layout-effect';\nexport const useTabListScroll = (targetRef, activeIndex) => {\n  const [{\n    scrollLeft\n  }, api] = useSpring(() => ({\n    scrollLeft: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  function animate(immediate = false) {\n    const container = targetRef.current;\n    if (!container) return;\n    if (activeIndex === undefined) return;\n    const activeTabWrapper = container.children.item(activeIndex);\n    const activeTab = activeTabWrapper.children.item(0);\n    const activeTabLeft = activeTab.offsetLeft;\n    const activeTabWidth = activeTab.offsetWidth;\n    const containerWidth = container.offsetWidth;\n    const containerScrollWidth = container.scrollWidth;\n    const containerScrollLeft = container.scrollLeft;\n    const maxScrollDistance = containerScrollWidth - containerWidth;\n    if (maxScrollDistance <= 0) return;\n    const nextScrollLeft = bound(activeTabLeft - (containerWidth - activeTabWidth) / 2, 0, containerScrollWidth - containerWidth);\n    api.start({\n      scrollLeft: nextScrollLeft,\n      from: {\n        scrollLeft: containerScrollLeft\n      },\n      immediate: immediate && !scrollLeft.isAnimating\n    });\n  }\n  useIsomorphicLayoutEffect(() => {\n    animate(true);\n  }, []);\n  useIsomorphicUpdateLayoutEffect(() => {\n    animate();\n  }, [activeIndex]);\n  useMutationEffect(() => {\n    animate(true);\n  }, targetRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  return {\n    scrollLeft,\n    animate\n  };\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,yBAAyB,QAAQ,QAAQ;AAClD,SAASC,+BAA+B,QAAQ,uCAAuC;AACvF,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;EAC1D,MAAM,CAAC;IACLC;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGT,SAAS,CAAC,OAAO;IACzBQ,UAAU,EAAE,CAAC;IACbE,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,SAASC,OAAOA,CAACC,SAAS,GAAG,KAAK,EAAE;IAClC,MAAMC,SAAS,GAAGT,SAAS,CAACU,OAAO;IACnC,IAAI,CAACD,SAAS,EAAE;IAChB,IAAIR,WAAW,KAAKU,SAAS,EAAE;IAC/B,MAAMC,gBAAgB,GAAGH,SAAS,CAACI,QAAQ,CAACC,IAAI,CAACb,WAAW,CAAC;IAC7D,MAAMc,SAAS,GAAGH,gBAAgB,CAACC,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC;IACnD,MAAME,aAAa,GAAGD,SAAS,CAACE,UAAU;IAC1C,MAAMC,cAAc,GAAGH,SAAS,CAACI,WAAW;IAC5C,MAAMC,cAAc,GAAGX,SAAS,CAACU,WAAW;IAC5C,MAAME,oBAAoB,GAAGZ,SAAS,CAACa,WAAW;IAClD,MAAMC,mBAAmB,GAAGd,SAAS,CAACP,UAAU;IAChD,MAAMsB,iBAAiB,GAAGH,oBAAoB,GAAGD,cAAc;IAC/D,IAAII,iBAAiB,IAAI,CAAC,EAAE;IAC5B,MAAMC,cAAc,GAAG7B,KAAK,CAACoB,aAAa,GAAG,CAACI,cAAc,GAAGF,cAAc,IAAI,CAAC,EAAE,CAAC,EAAEG,oBAAoB,GAAGD,cAAc,CAAC;IAC7HjB,GAAG,CAACuB,KAAK,CAAC;MACRxB,UAAU,EAAEuB,cAAc;MAC1BE,IAAI,EAAE;QACJzB,UAAU,EAAEqB;MACd,CAAC;MACDf,SAAS,EAAEA,SAAS,IAAI,CAACN,UAAU,CAAC0B;IACtC,CAAC,CAAC;EACJ;EACA/B,yBAAyB,CAAC,MAAM;IAC9BU,OAAO,CAAC,IAAI,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EACNT,+BAA+B,CAAC,MAAM;IACpCS,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACN,WAAW,CAAC,CAAC;EACjBN,iBAAiB,CAAC,MAAM;IACtBY,OAAO,CAAC,IAAI,CAAC;EACf,CAAC,EAAEP,SAAS,EAAE;IACZ6B,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,OAAO;IACL7B,UAAU;IACVK;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}