{"ast": null, "code": "import platform from '../platform/index.js';\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => url => {\n  url = new URL(url, platform.origin);\n  return origin.protocol === url.protocol && origin.host === url.host && (isMSIE || origin.port === url.port);\n})(new URL(platform.origin), platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)) : () => true;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}