{"ast": null, "code": "import * as React from \"react\";\nfunction DownlandOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DownlandOutline-DownlandOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DownlandOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"DownlandOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.5,4.4 L13.5,6.6 C13.5,6.8209139 13.3209139,7 13.1,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L35.9,7 C35.6790861,7 35.5,6.8209139 35.5,6.6 L35.5,4.4 C35.5,4.1790861 35.6790861,4 35.9,4 L38,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L13.1,4 C13.3209139,4 13.5,4.1790861 13.5,4.4 Z M25.9999942,4.4 L25.9996797,26.04 L25.9996797,26.04 L33.9551425,18.7714481 C33.9637608,18.763574 33.9770552,18.7638966 33.9852814,18.7721797 C33.994709,18.7816725 34,18.7945085 34,18.8078873 L34,22.6126929 C34,22.7250175 33.9527723,22.8321661 33.8698618,22.9079464 L25.5925692,30.4733951 L25.5925692,30.4733951 C25.2794059,30.7595769 24.8804299,30.8955827 24.4857797,30.8834403 C24.0908085,30.8958129 23.691422,30.7598192 23.3779936,30.4733951 L15.1301592,22.9359486 C15.047236,22.8601675 15,22.7530104 15,22.6406759 L15,19.6653642 C15,19.4444503 15.1790861,19.2653642 15.4,19.2653642 C15.4998604,19.2653642 15.5961093,19.3027166 15.6698274,19.3700793 L22.9996797,26.068 L22.9996797,26.068 L22.9999942,4.4 C23.0000032,4.17908837 23.1790884,4.00000581 23.4,4 L25.6,4 C25.8209139,3.99999419 26,4.17908029 26,4.39999419 C26,4.39999612 26,4.39999806 25.9999942,4.4 Z\",\n    id: \"DownlandOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default DownlandOutline;", "map": {"version": 3, "names": ["React", "DownlandOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/DownlandOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction DownlandOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DownlandOutline-DownlandOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DownlandOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"DownlandOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.5,4.4 L13.5,6.6 C13.5,6.8209139 13.3209139,7 13.1,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L35.9,7 C35.6790861,7 35.5,6.8209139 35.5,6.6 L35.5,4.4 C35.5,4.1790861 35.6790861,4 35.9,4 L38,4 L38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L13.1,4 C13.3209139,4 13.5,4.1790861 13.5,4.4 Z M25.9999942,4.4 L25.9996797,26.04 L25.9996797,26.04 L33.9551425,18.7714481 C33.9637608,18.763574 33.9770552,18.7638966 33.9852814,18.7721797 C33.994709,18.7816725 34,18.7945085 34,18.8078873 L34,22.6126929 C34,22.7250175 33.9527723,22.8321661 33.8698618,22.9079464 L25.5925692,30.4733951 L25.5925692,30.4733951 C25.2794059,30.7595769 24.8804299,30.8955827 24.4857797,30.8834403 C24.0908085,30.8958129 23.691422,30.7598192 23.3779936,30.4733951 L15.1301592,22.9359486 C15.047236,22.8601675 15,22.7530104 15,22.6406759 L15,19.6653642 C15,19.4444503 15.1790861,19.2653642 15.4,19.2653642 C15.4998604,19.2653642 15.5961093,19.3027166 15.6698274,19.3700793 L22.9996797,26.068 L22.9996797,26.068 L22.9999942,4.4 C23.0000032,4.17908837 23.1790884,4.00000581 23.4,4 L25.6,4 C25.8209139,3.99999419 26,4.17908029 26,4.39999419 C26,4.39999612 26,4.39999806 25.9999942,4.4 Z\",\n    id: \"DownlandOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default DownlandOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,s+CAAs+C;IACz+CR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}