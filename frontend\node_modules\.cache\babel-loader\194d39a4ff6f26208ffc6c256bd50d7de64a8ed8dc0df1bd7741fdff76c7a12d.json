{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\nexport default function useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.reconnectLimit,\n    reconnectLimit = _a === void 0 ? 3 : _a,\n    _b = options.reconnectInterval,\n    reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onMessage = options.onMessage,\n    onError = options.onError,\n    protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef();\n  var websocketRef = useRef();\n  var _d = __read(useState(), 2),\n    latestMessage = _d[0],\n    setLatestMessage = _d[1];\n  var _e = __read(useState(ReadyState.Closed), 2),\n    readyState = _e[0],\n    setReadyState = _e[1];\n  var reconnect = function () {\n    var _a;\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n  var connectWs = function () {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n    ws.onerror = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    ws.onopen = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n    ws.onmessage = function (message) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n    ws.onclose = function (event) {\n      var _a;\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      // closed by server\n      if (websocketRef.current === ws) {\n        reconnect();\n      }\n      // closed by disconnect or closed by server\n      if (!websocketRef.current || websocketRef.current === ws) {\n        setReadyState(ws.readyState || ReadyState.Closed);\n      }\n    };\n    websocketRef.current = ws;\n  };\n  var sendMessage = function (message) {\n    var _a;\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n  var connect = function () {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n  var disconnect = function () {\n    var _a;\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n    websocketRef.current = undefined;\n  };\n  useEffect(function () {\n    if (!manual && socketUrl) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}