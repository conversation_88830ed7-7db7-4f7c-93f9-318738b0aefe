{"ast": null, "code": "export function mergeFuncProps(p1, p2) {\n  const p1Keys = Object.keys(p1);\n  const p2Keys = Object.keys(p2);\n  const keys = new Set([...p1Keys, ...p2Keys]);\n  const res = {};\n  keys.forEach(key => {\n    const p1Value = p1[key];\n    const p2Value = p2[key];\n    if (typeof p1Value === 'function' && typeof p2Value === 'function') {\n      res[key] = function (...args) {\n        p1Value(...args);\n        p2Value(...args);\n      };\n    } else {\n      res[key] = p1Value || p2Value;\n    }\n  });\n  return res;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}