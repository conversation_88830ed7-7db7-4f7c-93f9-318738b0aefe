{"ast": null, "code": "import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}