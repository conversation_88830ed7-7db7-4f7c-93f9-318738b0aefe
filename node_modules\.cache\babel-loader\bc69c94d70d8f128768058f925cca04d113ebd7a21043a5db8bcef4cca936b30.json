{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nimport { isDev } from './is-dev';\nimport { devError } from './dev-log';\nlet tenPxTester = null;\nlet tester = null;\nif (canUseDom) {\n  tenPxTester = document.createElement('div');\n  tenPxTester.className = 'adm-px-tester';\n  tenPxTester.style.setProperty('--size', '10');\n  document.body.appendChild(tenPxTester);\n  tester = document.createElement('div');\n  tester.className = 'adm-px-tester';\n  document.body.appendChild(tester);\n  if (isDev) {\n    if (window.getComputedStyle(tester).position !== 'fixed') {\n      devError('Global', 'The px tester is not rendering properly. Please make sure you have imported `antd-mobile/es/global`.');\n    }\n  }\n}\nexport function convertPx(px) {\n  if (tenPxTester === null || tester === null) return px;\n  if (tenPxTester.getBoundingClientRect().height === 10) {\n    return px;\n  }\n  tester.style.setProperty('--size', px.toString());\n  return tester.getBoundingClientRect().height;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}