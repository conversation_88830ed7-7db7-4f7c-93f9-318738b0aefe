{"ast": null, "code": "!function (e, n) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = n() : \"function\" == typeof define && define.amd ? define(n) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isoWeeksInYear = n();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, n) {\n    n.prototype.isoWeeksInYear = function () {\n      var e = this.isLeapYear(),\n        n = this.endOf(\"y\").day();\n      return 4 === n || e && 5 === n ? 53 : 52;\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}