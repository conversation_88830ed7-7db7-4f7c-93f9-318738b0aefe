{"ast": null, "code": "import * as React from \"react\";\nfunction TravelOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TravelOutline-TravelOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TravelOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TravelOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M44,31 L44,38 C44,41.3137085 41.3137085,44 38,44 L31,44 L31,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31 L44,31 Z M7,31 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L17,41 L17,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,31 L7,31 Z M35.0117916,13.4953015 C35.1166207,13.7467969 35.1142891,14.0301778 35.0053359,14.2799142 L25.6937022,35.6235281 C25.5554891,35.9405947 25.1859051,36.0858047 24.8682132,35.9478636 C24.7040314,35.8765762 24.5781952,35.7386683 24.5223976,35.568873 L22.8381152,30.2710706 C22.4583134,29.0764282 22.6605228,27.7728272 23.3843858,26.749381 L27.7570153,20.567063 L20.9489383,24.5446033 C19.9288237,25.1405934 18.6974178,25.2551281 17.5848853,24.8574981 L12.4105493,23.0081401 C12.0854696,22.888567 11.9190648,22.5286234 12.0388737,22.2041835 C12.0990839,22.0411356 12.2244836,21.9103966 12.3850699,21.843247 L33.7040276,12.9570142 C34.2138006,12.7445291 34.7993065,12.9855285 35.0117916,13.4953015 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,17 L41,17 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L31,7 L31,4 L38,4 Z M17,4 L17,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,17 L4,17 L4,10 C4,6.6862915 6.6862915,4 10,4 L17,4 Z\",\n    id: \"TravelOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TravelOutline;", "map": {"version": 3, "names": ["React", "TravelOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/TravelOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TravelOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TravelOutline-TravelOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TravelOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TravelOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M44,31 L44,38 C44,41.3137085 41.3137085,44 38,44 L31,44 L31,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31 L44,31 Z M7,31 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L17,41 L17,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,31 L7,31 Z M35.0117916,13.4953015 C35.1166207,13.7467969 35.1142891,14.0301778 35.0053359,14.2799142 L25.6937022,35.6235281 C25.5554891,35.9405947 25.1859051,36.0858047 24.8682132,35.9478636 C24.7040314,35.8765762 24.5781952,35.7386683 24.5223976,35.568873 L22.8381152,30.2710706 C22.4583134,29.0764282 22.6605228,27.7728272 23.3843858,26.749381 L27.7570153,20.567063 L20.9489383,24.5446033 C19.9288237,25.1405934 18.6974178,25.2551281 17.5848853,24.8574981 L12.4105493,23.0081401 C12.0854696,22.888567 11.9190648,22.5286234 12.0388737,22.2041835 C12.0990839,22.0411356 12.2244836,21.9103966 12.3850699,21.843247 L33.7040276,12.9570142 C34.2138006,12.7445291 34.7993065,12.9855285 35.0117916,13.4953015 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,17 L41,17 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L31,7 L31,4 L38,4 Z M17,4 L17,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,17 L4,17 L4,10 C4,6.6862915 6.6862915,4 10,4 L17,4 Z\",\n    id: \"TravelOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TravelOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,svCAAsvC;IACzvCR,EAAE,EAAE,wCAAwC;IAC5CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}