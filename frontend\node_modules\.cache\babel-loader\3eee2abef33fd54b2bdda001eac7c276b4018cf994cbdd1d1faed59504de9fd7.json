{"ast": null, "code": "import { V, c as computeRubberband } from './maths-0ab39ae9.esm.js';\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nconst EVENT_TYPE_MAP = {\n  pointer: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  mouse: {\n    start: 'down',\n    change: 'move',\n    end: 'up'\n  },\n  touch: {\n    start: 'start',\n    change: 'move',\n    end: 'end'\n  },\n  gesture: {\n    start: 'start',\n    change: 'change',\n    end: 'end'\n  }\n};\nfunction capitalize(string) {\n  if (!string) return '';\n  return string[0].toUpperCase() + string.slice(1);\n}\nconst actionsWithoutCaptureSupported = ['enter', 'leave'];\nfunction hasCapture(capture = false, actionKey) {\n  return capture && !actionsWithoutCaptureSupported.includes(actionKey);\n}\nfunction toHandlerProp(device, action = '', capture = false) {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return 'on' + capitalize(device) + capitalize(actionKey) + (hasCapture(capture, actionKey) ? 'Capture' : '');\n}\nconst pointerCaptureEvents = ['gotpointercapture', 'lostpointercapture'];\nfunction parseProp(prop) {\n  let eventKey = prop.substring(2).toLowerCase();\n  const passive = !!~eventKey.indexOf('passive');\n  if (passive) eventKey = eventKey.replace('passive', '');\n  const captureKey = pointerCaptureEvents.includes(eventKey) ? 'capturecapture' : 'capture';\n  const capture = !!~eventKey.indexOf(captureKey);\n  if (capture) eventKey = eventKey.replace('capture', '');\n  return {\n    device: eventKey,\n    capture,\n    passive\n  };\n}\nfunction toDomEventType(device, action = '') {\n  const deviceProps = EVENT_TYPE_MAP[device];\n  const actionKey = deviceProps ? deviceProps[action] || action : action;\n  return device + actionKey;\n}\nfunction isTouch(event) {\n  return 'touches' in event;\n}\nfunction getPointerType(event) {\n  if (isTouch(event)) return 'touch';\n  if ('pointerType' in event) return event.pointerType;\n  return 'mouse';\n}\nfunction getCurrentTargetTouchList(event) {\n  return Array.from(event.touches).filter(e => {\n    var _event$currentTarget, _event$currentTarget$;\n    return e.target === event.currentTarget || ((_event$currentTarget = event.currentTarget) === null || _event$currentTarget === void 0 || (_event$currentTarget$ = _event$currentTarget.contains) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.call(_event$currentTarget, e.target));\n  });\n}\nfunction getTouchList(event) {\n  return event.type === 'touchend' || event.type === 'touchcancel' ? event.changedTouches : event.targetTouches;\n}\nfunction getValueEvent(event) {\n  return isTouch(event) ? getTouchList(event)[0] : event;\n}\nfunction distanceAngle(P1, P2) {\n  try {\n    const dx = P2.clientX - P1.clientX;\n    const dy = P2.clientY - P1.clientY;\n    const cx = (P2.clientX + P1.clientX) / 2;\n    const cy = (P2.clientY + P1.clientY) / 2;\n    const distance = Math.hypot(dx, dy);\n    const angle = -(Math.atan2(dx, dy) * 180) / Math.PI;\n    const origin = [cx, cy];\n    return {\n      angle,\n      distance,\n      origin\n    };\n  } catch (_unused) {}\n  return null;\n}\nfunction touchIds(event) {\n  return getCurrentTargetTouchList(event).map(touch => touch.identifier);\n}\nfunction touchDistanceAngle(event, ids) {\n  const [P1, P2] = Array.from(event.touches).filter(touch => ids.includes(touch.identifier));\n  return distanceAngle(P1, P2);\n}\nfunction pointerId(event) {\n  const valueEvent = getValueEvent(event);\n  return isTouch(event) ? valueEvent.identifier : valueEvent.pointerId;\n}\nfunction pointerValues(event) {\n  const valueEvent = getValueEvent(event);\n  return [valueEvent.clientX, valueEvent.clientY];\n}\nconst LINE_HEIGHT = 40;\nconst PAGE_HEIGHT = 800;\nfunction wheelValues(event) {\n  let {\n    deltaX,\n    deltaY,\n    deltaMode\n  } = event;\n  if (deltaMode === 1) {\n    deltaX *= LINE_HEIGHT;\n    deltaY *= LINE_HEIGHT;\n  } else if (deltaMode === 2) {\n    deltaX *= PAGE_HEIGHT;\n    deltaY *= PAGE_HEIGHT;\n  }\n  return [deltaX, deltaY];\n}\nfunction scrollValues(event) {\n  var _ref, _ref2;\n  const {\n    scrollX,\n    scrollY,\n    scrollLeft,\n    scrollTop\n  } = event.currentTarget;\n  return [(_ref = scrollX !== null && scrollX !== void 0 ? scrollX : scrollLeft) !== null && _ref !== void 0 ? _ref : 0, (_ref2 = scrollY !== null && scrollY !== void 0 ? scrollY : scrollTop) !== null && _ref2 !== void 0 ? _ref2 : 0];\n}\nfunction getEventDetails(event) {\n  const payload = {};\n  if ('buttons' in event) payload.buttons = event.buttons;\n  if ('shiftKey' in event) {\n    const {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    } = event;\n    Object.assign(payload, {\n      shiftKey,\n      altKey,\n      metaKey,\n      ctrlKey\n    });\n  }\n  return payload;\n}\nfunction call(v, ...args) {\n  if (typeof v === 'function') {\n    return v(...args);\n  } else {\n    return v;\n  }\n}\nfunction noop() {}\nfunction chain(...fns) {\n  if (fns.length === 0) return noop;\n  if (fns.length === 1) return fns[0];\n  return function () {\n    let result;\n    for (const fn of fns) {\n      result = fn.apply(this, arguments) || result;\n    }\n    return result;\n  };\n}\nfunction assignDefault(value, fallback) {\n  return Object.assign({}, fallback, value || {});\n}\nconst BEFORE_LAST_KINEMATICS_DELAY = 32;\nclass Engine {\n  constructor(ctrl, args, key) {\n    this.ctrl = ctrl;\n    this.args = args;\n    this.key = key;\n    if (!this.state) {\n      this.state = {};\n      this.computeValues([0, 0]);\n      this.computeInitial();\n      if (this.init) this.init();\n      this.reset();\n    }\n  }\n  get state() {\n    return this.ctrl.state[this.key];\n  }\n  set state(state) {\n    this.ctrl.state[this.key] = state;\n  }\n  get shared() {\n    return this.ctrl.state.shared;\n  }\n  get eventStore() {\n    return this.ctrl.gestureEventStores[this.key];\n  }\n  get timeoutStore() {\n    return this.ctrl.gestureTimeoutStores[this.key];\n  }\n  get config() {\n    return this.ctrl.config[this.key];\n  }\n  get sharedConfig() {\n    return this.ctrl.config.shared;\n  }\n  get handler() {\n    return this.ctrl.handlers[this.key];\n  }\n  reset() {\n    const {\n      state,\n      shared,\n      ingKey,\n      args\n    } = this;\n    shared[ingKey] = state._active = state.active = state._blocked = state._force = false;\n    state._step = [false, false];\n    state.intentional = false;\n    state._movement = [0, 0];\n    state._distance = [0, 0];\n    state._direction = [0, 0];\n    state._delta = [0, 0];\n    state._bounds = [[-Infinity, Infinity], [-Infinity, Infinity]];\n    state.args = args;\n    state.axis = undefined;\n    state.memo = undefined;\n    state.elapsedTime = state.timeDelta = 0;\n    state.direction = [0, 0];\n    state.distance = [0, 0];\n    state.overflow = [0, 0];\n    state._movementBound = [false, false];\n    state.velocity = [0, 0];\n    state.movement = [0, 0];\n    state.delta = [0, 0];\n    state.timeStamp = 0;\n  }\n  start(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._active) {\n      this.reset();\n      this.computeInitial();\n      state._active = true;\n      state.target = event.target;\n      state.currentTarget = event.currentTarget;\n      state.lastOffset = config.from ? call(config.from, state) : state.offset;\n      state.offset = state.lastOffset;\n      state.startTime = state.timeStamp = event.timeStamp;\n    }\n  }\n  computeValues(values) {\n    const state = this.state;\n    state._values = values;\n    state.values = this.config.transform(values);\n  }\n  computeInitial() {\n    const state = this.state;\n    state._initial = state._values;\n    state.initial = state.values;\n  }\n  compute(event) {\n    const {\n      state,\n      config,\n      shared\n    } = this;\n    state.args = this.args;\n    let dt = 0;\n    if (event) {\n      state.event = event;\n      if (config.preventDefault && event.cancelable) state.event.preventDefault();\n      state.type = event.type;\n      shared.touches = this.ctrl.pointerIds.size || this.ctrl.touchIds.size;\n      shared.locked = !!document.pointerLockElement;\n      Object.assign(shared, getEventDetails(event));\n      shared.down = shared.pressed = shared.buttons % 2 === 1 || shared.touches > 0;\n      dt = event.timeStamp - state.timeStamp;\n      state.timeStamp = event.timeStamp;\n      state.elapsedTime = state.timeStamp - state.startTime;\n    }\n    if (state._active) {\n      const _absoluteDelta = state._delta.map(Math.abs);\n      V.addTo(state._distance, _absoluteDelta);\n    }\n    if (this.axisIntent) this.axisIntent(event);\n    const [_m0, _m1] = state._movement;\n    const [t0, t1] = config.threshold;\n    const {\n      _step,\n      values\n    } = state;\n    if (config.hasCustomTransform) {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && values[0];\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && values[1];\n    } else {\n      if (_step[0] === false) _step[0] = Math.abs(_m0) >= t0 && Math.sign(_m0) * t0;\n      if (_step[1] === false) _step[1] = Math.abs(_m1) >= t1 && Math.sign(_m1) * t1;\n    }\n    state.intentional = _step[0] !== false || _step[1] !== false;\n    if (!state.intentional) return;\n    const movement = [0, 0];\n    if (config.hasCustomTransform) {\n      const [v0, v1] = values;\n      movement[0] = _step[0] !== false ? v0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? v1 - _step[1] : 0;\n    } else {\n      movement[0] = _step[0] !== false ? _m0 - _step[0] : 0;\n      movement[1] = _step[1] !== false ? _m1 - _step[1] : 0;\n    }\n    if (this.restrictToAxis && !state._blocked) this.restrictToAxis(movement);\n    const previousOffset = state.offset;\n    const gestureIsActive = state._active && !state._blocked || state.active;\n    if (gestureIsActive) {\n      state.first = state._active && !state.active;\n      state.last = !state._active && state.active;\n      state.active = shared[this.ingKey] = state._active;\n      if (event) {\n        if (state.first) {\n          if ('bounds' in config) state._bounds = call(config.bounds, state);\n          if (this.setup) this.setup();\n        }\n        state.movement = movement;\n        this.computeOffset();\n      }\n    }\n    const [ox, oy] = state.offset;\n    const [[x0, x1], [y0, y1]] = state._bounds;\n    state.overflow = [ox < x0 ? -1 : ox > x1 ? 1 : 0, oy < y0 ? -1 : oy > y1 ? 1 : 0];\n    state._movementBound[0] = state.overflow[0] ? state._movementBound[0] === false ? state._movement[0] : state._movementBound[0] : false;\n    state._movementBound[1] = state.overflow[1] ? state._movementBound[1] === false ? state._movement[1] : state._movementBound[1] : false;\n    const rubberband = state._active ? config.rubberband || [0, 0] : [0, 0];\n    state.offset = computeRubberband(state._bounds, state.offset, rubberband);\n    state.delta = V.sub(state.offset, previousOffset);\n    this.computeMovement();\n    if (gestureIsActive && (!state.last || dt > BEFORE_LAST_KINEMATICS_DELAY)) {\n      state.delta = V.sub(state.offset, previousOffset);\n      const absoluteDelta = state.delta.map(Math.abs);\n      V.addTo(state.distance, absoluteDelta);\n      state.direction = state.delta.map(Math.sign);\n      state._direction = state._delta.map(Math.sign);\n      if (!state.first && dt > 0) {\n        state.velocity = [absoluteDelta[0] / dt, absoluteDelta[1] / dt];\n        state.timeDelta = dt;\n      }\n    }\n  }\n  emit() {\n    const state = this.state;\n    const shared = this.shared;\n    const config = this.config;\n    if (!state._active) this.clean();\n    if ((state._blocked || !state.intentional) && !state._force && !config.triggerAllEvents) return;\n    const memo = this.handler(_objectSpread2(_objectSpread2(_objectSpread2({}, shared), state), {}, {\n      [this.aliasKey]: state.values\n    }));\n    if (memo !== undefined) state.memo = memo;\n  }\n  clean() {\n    this.eventStore.clean();\n    this.timeoutStore.clean();\n  }\n}\nfunction selectAxis([dx, dy], threshold) {\n  const absDx = Math.abs(dx);\n  const absDy = Math.abs(dy);\n  if (absDx > absDy && absDx > threshold) {\n    return 'x';\n  }\n  if (absDy > absDx && absDy > threshold) {\n    return 'y';\n  }\n  return undefined;\n}\nclass CoordinatesEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"aliasKey\", 'xy');\n  }\n  reset() {\n    super.reset();\n    this.state.axis = undefined;\n  }\n  init() {\n    this.state.offset = [0, 0];\n    this.state.lastOffset = [0, 0];\n  }\n  computeOffset() {\n    this.state.offset = V.add(this.state.lastOffset, this.state.movement);\n  }\n  computeMovement() {\n    this.state.movement = V.sub(this.state.offset, this.state.lastOffset);\n  }\n  axisIntent(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state.axis && event) {\n      const threshold = typeof config.axisThreshold === 'object' ? config.axisThreshold[getPointerType(event)] : config.axisThreshold;\n      state.axis = selectAxis(state._movement, threshold);\n    }\n    state._blocked = (config.lockDirection || !!config.axis) && !state.axis || !!config.axis && config.axis !== state.axis;\n  }\n  restrictToAxis(v) {\n    if (this.config.axis || this.config.lockDirection) {\n      switch (this.state.axis) {\n        case 'x':\n          v[1] = 0;\n          break;\n        case 'y':\n          v[0] = 0;\n          break;\n      }\n    }\n  }\n}\nconst identity = v => v;\nconst DEFAULT_RUBBERBAND = 0.15;\nconst commonConfigResolver = {\n  enabled(value = true) {\n    return value;\n  },\n  eventOptions(value, _k, config) {\n    return _objectSpread2(_objectSpread2({}, config.shared.eventOptions), value);\n  },\n  preventDefault(value = false) {\n    return value;\n  },\n  triggerAllEvents(value = false) {\n    return value;\n  },\n  rubberband(value = 0) {\n    switch (value) {\n      case true:\n        return [DEFAULT_RUBBERBAND, DEFAULT_RUBBERBAND];\n      case false:\n        return [0, 0];\n      default:\n        return V.toVector(value);\n    }\n  },\n  from(value) {\n    if (typeof value === 'function') return value;\n    if (value != null) return V.toVector(value);\n  },\n  transform(value, _k, config) {\n    const transform = value || config.shared.transform;\n    this.hasCustomTransform = !!transform;\n    if (process.env.NODE_ENV === 'development') {\n      const originalTransform = transform || identity;\n      return v => {\n        const r = originalTransform(v);\n        if (!isFinite(r[0]) || !isFinite(r[1])) {\n          console.warn(`[@use-gesture]: config.transform() must produce a valid result, but it was: [${r[0]},${[1]}]`);\n        }\n        return r;\n      };\n    }\n    return transform || identity;\n  },\n  threshold(value) {\n    return V.toVector(value, 0);\n  }\n};\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(commonConfigResolver, {\n    domTarget(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n      }\n      return NaN;\n    },\n    lockDirection(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`lockDirection\\` option has been merged with \\`axis\\`. Use it as in \\`{ axis: 'lock' }\\``);\n      }\n      return NaN;\n    },\n    initial(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`initial\\` option has been renamed to \\`from\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\nconst DEFAULT_AXIS_THRESHOLD = 0;\nconst coordinatesConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  axis(_v, _k, {\n    axis\n  }) {\n    this.lockDirection = axis === 'lock';\n    if (!this.lockDirection) return axis;\n  },\n  axisThreshold(value = DEFAULT_AXIS_THRESHOLD) {\n    return value;\n  },\n  bounds(value = {}) {\n    if (typeof value === 'function') {\n      return state => coordinatesConfigResolver.bounds(value(state));\n    }\n    if ('current' in value) {\n      return () => value.current;\n    }\n    if (typeof HTMLElement === 'function' && value instanceof HTMLElement) {\n      return value;\n    }\n    const {\n      left = -Infinity,\n      right = Infinity,\n      top = -Infinity,\n      bottom = Infinity\n    } = value;\n    return [[left, right], [top, bottom]];\n  }\n});\nconst KEYS_DELTA_MAP = {\n  ArrowRight: (displacement, factor = 1) => [displacement * factor, 0],\n  ArrowLeft: (displacement, factor = 1) => [-1 * displacement * factor, 0],\n  ArrowUp: (displacement, factor = 1) => [0, -1 * displacement * factor],\n  ArrowDown: (displacement, factor = 1) => [0, displacement * factor]\n};\nclass DragEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'dragging');\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._pointerId = undefined;\n    state._pointerActive = false;\n    state._keyboardActive = false;\n    state._preventScroll = false;\n    state._delayed = false;\n    state.swipe = [0, 0];\n    state.tap = false;\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n  }\n  setup() {\n    const state = this.state;\n    if (state._bounds instanceof HTMLElement) {\n      const boundRect = state._bounds.getBoundingClientRect();\n      const targetRect = state.currentTarget.getBoundingClientRect();\n      const _bounds = {\n        left: boundRect.left - targetRect.left + state.offset[0],\n        right: boundRect.right - targetRect.right + state.offset[0],\n        top: boundRect.top - targetRect.top + state.offset[1],\n        bottom: boundRect.bottom - targetRect.bottom + state.offset[1]\n      };\n      state._bounds = coordinatesConfigResolver.bounds(_bounds);\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    state.canceled = true;\n    state._active = false;\n    setTimeout(() => {\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  setActive() {\n    this.state._active = this.state._pointerActive || this.state._keyboardActive;\n  }\n  clean() {\n    this.pointerClean();\n    this.state._pointerActive = false;\n    this.state._keyboardActive = false;\n    super.clean();\n  }\n  pointerDown(event) {\n    const config = this.config;\n    const state = this.state;\n    if (event.buttons != null && (Array.isArray(config.pointerButtons) ? !config.pointerButtons.includes(event.buttons) : config.pointerButtons !== -1 && config.pointerButtons !== event.buttons)) return;\n    const ctrlIds = this.ctrl.setEventIds(event);\n    if (config.pointerCapture) {\n      event.target.setPointerCapture(event.pointerId);\n    }\n    if (ctrlIds && ctrlIds.size > 1 && state._pointerActive) return;\n    this.start(event);\n    this.setupPointer(event);\n    state._pointerId = pointerId(event);\n    state._pointerActive = true;\n    this.computeValues(pointerValues(event));\n    this.computeInitial();\n    if (config.preventScrollAxis && getPointerType(event) !== 'mouse') {\n      state._active = false;\n      this.setupScrollPrevention(event);\n    } else if (config.delay > 0) {\n      this.setupDelayTrigger(event);\n      if (config.triggerAllEvents) {\n        this.compute(event);\n        this.emit();\n      }\n    } else {\n      this.startPointerDrag(event);\n    }\n  }\n  startPointerDrag(event) {\n    const state = this.state;\n    state._active = true;\n    state._preventScroll = true;\n    state._delayed = false;\n    this.compute(event);\n    this.emit();\n  }\n  pointerMove(event) {\n    const state = this.state;\n    const config = this.config;\n    if (!state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    const _values = pointerValues(event);\n    if (document.pointerLockElement === event.target) {\n      state._delta = [event.movementX, event.movementY];\n    } else {\n      state._delta = V.sub(_values, state._values);\n      this.computeValues(_values);\n    }\n    V.addTo(state._movement, state._delta);\n    this.compute(event);\n    if (state._delayed && state.intentional) {\n      this.timeoutStore.remove('dragDelay');\n      state.active = false;\n      this.startPointerDrag(event);\n      return;\n    }\n    if (config.preventScrollAxis && !state._preventScroll) {\n      if (state.axis) {\n        if (state.axis === config.preventScrollAxis || config.preventScrollAxis === 'xy') {\n          state._active = false;\n          this.clean();\n          return;\n        } else {\n          this.timeoutStore.remove('startPointerDrag');\n          this.startPointerDrag(event);\n          return;\n        }\n      } else {\n        return;\n      }\n    }\n    this.emit();\n  }\n  pointerUp(event) {\n    this.ctrl.setEventIds(event);\n    try {\n      if (this.config.pointerCapture && event.target.hasPointerCapture(event.pointerId)) {\n        ;\n        event.target.releasePointerCapture(event.pointerId);\n      }\n    } catch (_unused) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(`[@use-gesture]: If you see this message, it's likely that you're using an outdated version of \\`@react-three/fiber\\`. \\n\\nPlease upgrade to the latest version.`);\n      }\n    }\n    const state = this.state;\n    const config = this.config;\n    if (!state._active || !state._pointerActive) return;\n    const id = pointerId(event);\n    if (state._pointerId !== undefined && id !== state._pointerId) return;\n    this.state._pointerActive = false;\n    this.setActive();\n    this.compute(event);\n    const [dx, dy] = state._distance;\n    state.tap = dx <= config.tapsThreshold && dy <= config.tapsThreshold;\n    if (state.tap && config.filterTaps) {\n      state._force = true;\n    } else {\n      const [_dx, _dy] = state._delta;\n      const [_mx, _my] = state._movement;\n      const [svx, svy] = config.swipe.velocity;\n      const [sx, sy] = config.swipe.distance;\n      const sdt = config.swipe.duration;\n      if (state.elapsedTime < sdt) {\n        const _vx = Math.abs(_dx / state.timeDelta);\n        const _vy = Math.abs(_dy / state.timeDelta);\n        if (_vx > svx && Math.abs(_mx) > sx) state.swipe[0] = Math.sign(_dx);\n        if (_vy > svy && Math.abs(_my) > sy) state.swipe[1] = Math.sign(_dy);\n      }\n    }\n    this.emit();\n  }\n  pointerClick(event) {\n    if (!this.state.tap && event.detail > 0) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  setupPointer(event) {\n    const config = this.config;\n    const device = config.device;\n    if (process.env.NODE_ENV === 'development') {\n      try {\n        if (device === 'pointer' && config.preventScrollDelay === undefined) {\n          const currentTarget = 'uv' in event ? event.sourceEvent.currentTarget : event.currentTarget;\n          const style = window.getComputedStyle(currentTarget);\n          if (style.touchAction === 'auto') {\n            console.warn(`[@use-gesture]: The drag target has its \\`touch-action\\` style property set to \\`auto\\`. It is recommended to add \\`touch-action: 'none'\\` so that the drag gesture behaves correctly on touch-enabled devices. For more information read this: https://use-gesture.netlify.app/docs/extras/#touch-action.\\n\\nThis message will only show in development mode. It won't appear in production. If this is intended, you can ignore it.`, currentTarget);\n          }\n        }\n      } catch (_unused2) {}\n    }\n    if (config.pointerLock) {\n      event.currentTarget.requestPointerLock();\n    }\n    if (!config.pointerCapture) {\n      this.eventStore.add(this.sharedConfig.window, device, 'change', this.pointerMove.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'end', this.pointerUp.bind(this));\n      this.eventStore.add(this.sharedConfig.window, device, 'cancel', this.pointerUp.bind(this));\n    }\n  }\n  pointerClean() {\n    if (this.config.pointerLock && document.pointerLockElement === this.state.currentTarget) {\n      document.exitPointerLock();\n    }\n  }\n  preventScroll(event) {\n    if (this.state._preventScroll && event.cancelable) {\n      event.preventDefault();\n    }\n  }\n  setupScrollPrevention(event) {\n    this.state._preventScroll = false;\n    persistEvent(event);\n    const remove = this.eventStore.add(this.sharedConfig.window, 'touch', 'change', this.preventScroll.bind(this), {\n      passive: false\n    });\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'end', remove);\n    this.eventStore.add(this.sharedConfig.window, 'touch', 'cancel', remove);\n    this.timeoutStore.add('startPointerDrag', this.startPointerDrag.bind(this), this.config.preventScrollDelay, event);\n  }\n  setupDelayTrigger(event) {\n    this.state._delayed = true;\n    this.timeoutStore.add('dragDelay', () => {\n      this.state._step = [0, 0];\n      this.startPointerDrag(event);\n    }, this.config.delay);\n  }\n  keyDown(event) {\n    const deltaFn = KEYS_DELTA_MAP[event.key];\n    if (deltaFn) {\n      const state = this.state;\n      const factor = event.shiftKey ? 10 : event.altKey ? 0.1 : 1;\n      this.start(event);\n      state._delta = deltaFn(this.config.keyboardDisplacement, factor);\n      state._keyboardActive = true;\n      V.addTo(state._movement, state._delta);\n      this.compute(event);\n      this.emit();\n    }\n  }\n  keyUp(event) {\n    if (!(event.key in KEYS_DELTA_MAP)) return;\n    this.state._keyboardActive = false;\n    this.setActive();\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    bindFunction(device, 'start', this.pointerDown.bind(this));\n    if (this.config.pointerCapture) {\n      bindFunction(device, 'change', this.pointerMove.bind(this));\n      bindFunction(device, 'end', this.pointerUp.bind(this));\n      bindFunction(device, 'cancel', this.pointerUp.bind(this));\n      bindFunction('lostPointerCapture', '', this.pointerUp.bind(this));\n    }\n    if (this.config.keys) {\n      bindFunction('key', 'down', this.keyDown.bind(this));\n      bindFunction('key', 'up', this.keyUp.bind(this));\n    }\n    if (this.config.filterTaps) {\n      bindFunction('click', '', this.pointerClick.bind(this), {\n        capture: true,\n        passive: false\n      });\n    }\n  }\n}\nfunction persistEvent(event) {\n  'persist' in event && typeof event.persist === 'function' && event.persist();\n}\nconst isBrowser = typeof window !== 'undefined' && window.document && window.document.createElement;\nfunction supportsTouchEvents() {\n  return isBrowser && 'ontouchstart' in window;\n}\nfunction isTouchScreen() {\n  return supportsTouchEvents() || isBrowser && window.navigator.maxTouchPoints > 1;\n}\nfunction supportsPointerEvents() {\n  return isBrowser && 'onpointerdown' in window;\n}\nfunction supportsPointerLock() {\n  return isBrowser && 'exitPointerLock' in window.document;\n}\nfunction supportsGestureEvents() {\n  try {\n    return 'constructor' in GestureEvent;\n  } catch (e) {\n    return false;\n  }\n}\nconst SUPPORT = {\n  isBrowser,\n  gesture: supportsGestureEvents(),\n  touch: supportsTouchEvents(),\n  touchscreen: isTouchScreen(),\n  pointer: supportsPointerEvents(),\n  pointerLock: supportsPointerLock()\n};\nconst DEFAULT_PREVENT_SCROLL_DELAY = 250;\nconst DEFAULT_DRAG_DELAY = 180;\nconst DEFAULT_SWIPE_VELOCITY = 0.5;\nconst DEFAULT_SWIPE_DISTANCE = 50;\nconst DEFAULT_SWIPE_DURATION = 250;\nconst DEFAULT_KEYBOARD_DISPLACEMENT = 10;\nconst DEFAULT_DRAG_AXIS_THRESHOLD = {\n  mouse: 0,\n  touch: 0,\n  pen: 8\n};\nconst dragConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  device(_v, _k, {\n    pointer: {\n      touch = false,\n      lock = false,\n      mouse = false\n    } = {}\n  }) {\n    this.pointerLock = lock && SUPPORT.pointerLock;\n    if (SUPPORT.touch && touch) return 'touch';\n    if (this.pointerLock) return 'mouse';\n    if (SUPPORT.pointer && !mouse) return 'pointer';\n    if (SUPPORT.touch) return 'touch';\n    return 'mouse';\n  },\n  preventScrollAxis(value, _k, {\n    preventScroll\n  }) {\n    this.preventScrollDelay = typeof preventScroll === 'number' ? preventScroll : preventScroll || preventScroll === undefined && value ? DEFAULT_PREVENT_SCROLL_DELAY : undefined;\n    if (!SUPPORT.touchscreen || preventScroll === false) return undefined;\n    return value ? value : preventScroll !== undefined ? 'y' : undefined;\n  },\n  pointerCapture(_v, _k, {\n    pointer: {\n      capture = true,\n      buttons = 1,\n      keys = true\n    } = {}\n  }) {\n    this.pointerButtons = buttons;\n    this.keys = keys;\n    return !this.pointerLock && this.device === 'pointer' && capture;\n  },\n  threshold(value, _k, {\n    filterTaps = false,\n    tapsThreshold = 3,\n    axis = undefined\n  }) {\n    const threshold = V.toVector(value, filterTaps ? tapsThreshold : axis ? 1 : 0);\n    this.filterTaps = filterTaps;\n    this.tapsThreshold = tapsThreshold;\n    return threshold;\n  },\n  swipe({\n    velocity = DEFAULT_SWIPE_VELOCITY,\n    distance = DEFAULT_SWIPE_DISTANCE,\n    duration = DEFAULT_SWIPE_DURATION\n  } = {}) {\n    return {\n      velocity: this.transform(V.toVector(velocity)),\n      distance: this.transform(V.toVector(distance)),\n      duration\n    };\n  },\n  delay(value = 0) {\n    switch (value) {\n      case true:\n        return DEFAULT_DRAG_DELAY;\n      case false:\n        return 0;\n      default:\n        return value;\n    }\n  },\n  axisThreshold(value) {\n    if (!value) return DEFAULT_DRAG_AXIS_THRESHOLD;\n    return _objectSpread2(_objectSpread2({}, DEFAULT_DRAG_AXIS_THRESHOLD), value);\n  },\n  keyboardDisplacement(value = DEFAULT_KEYBOARD_DISPLACEMENT) {\n    return value;\n  }\n});\nif (process.env.NODE_ENV === 'development') {\n  Object.assign(dragConfigResolver, {\n    useTouch(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`useTouch\\` option has been renamed to \\`pointer.touch\\`. Use it as in \\`{ pointer: { touch: true } }\\`.`);\n      }\n      return NaN;\n    },\n    experimental_preventWindowScrollY(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`experimental_preventWindowScrollY\\` option has been renamed to \\`preventScroll\\`.`);\n      }\n      return NaN;\n    },\n    swipeVelocity(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeVelocity\\` option has been renamed to \\`swipe.velocity\\`. Use it as in \\`{ swipe: { velocity: 0.5 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDistance(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDistance\\` option has been renamed to \\`swipe.distance\\`. Use it as in \\`{ swipe: { distance: 50 } }\\`.`);\n      }\n      return NaN;\n    },\n    swipeDuration(value) {\n      if (value !== undefined) {\n        throw Error(`[@use-gesture]: \\`swipeDuration\\` option has been renamed to \\`swipe.duration\\`. Use it as in \\`{ swipe: { duration: 250 } }\\`.`);\n      }\n      return NaN;\n    }\n  });\n}\nfunction clampStateInternalMovementToBounds(state) {\n  const [ox, oy] = state.overflow;\n  const [dx, dy] = state._delta;\n  const [dirx, diry] = state._direction;\n  if (ox < 0 && dx > 0 && dirx < 0 || ox > 0 && dx < 0 && dirx > 0) {\n    state._movement[0] = state._movementBound[0];\n  }\n  if (oy < 0 && dy > 0 && diry < 0 || oy > 0 && dy < 0 && diry > 0) {\n    state._movement[1] = state._movementBound[1];\n  }\n}\nconst SCALE_ANGLE_RATIO_INTENT_DEG = 30;\nconst PINCH_WHEEL_RATIO = 100;\nclass PinchEngine extends Engine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'pinching');\n    _defineProperty(this, \"aliasKey\", 'da');\n  }\n  init() {\n    this.state.offset = [1, 0];\n    this.state.lastOffset = [1, 0];\n    this.state._pointerEvents = new Map();\n  }\n  reset() {\n    super.reset();\n    const state = this.state;\n    state._touchIds = [];\n    state.canceled = false;\n    state.cancel = this.cancel.bind(this);\n    state.turns = 0;\n  }\n  computeOffset() {\n    const {\n      type,\n      movement,\n      lastOffset\n    } = this.state;\n    if (type === 'wheel') {\n      this.state.offset = V.add(movement, lastOffset);\n    } else {\n      this.state.offset = [(1 + movement[0]) * lastOffset[0], movement[1] + lastOffset[1]];\n    }\n  }\n  computeMovement() {\n    const {\n      offset,\n      lastOffset\n    } = this.state;\n    this.state.movement = [offset[0] / lastOffset[0], offset[1] - lastOffset[1]];\n  }\n  axisIntent() {\n    const state = this.state;\n    const [_m0, _m1] = state._movement;\n    if (!state.axis) {\n      const axisMovementDifference = Math.abs(_m0) * SCALE_ANGLE_RATIO_INTENT_DEG - Math.abs(_m1);\n      if (axisMovementDifference < 0) state.axis = 'angle';else if (axisMovementDifference > 0) state.axis = 'scale';\n    }\n  }\n  restrictToAxis(v) {\n    if (this.config.lockDirection) {\n      if (this.state.axis === 'scale') v[1] = 0;else if (this.state.axis === 'angle') v[0] = 0;\n    }\n  }\n  cancel() {\n    const state = this.state;\n    if (state.canceled) return;\n    setTimeout(() => {\n      state.canceled = true;\n      state._active = false;\n      this.compute();\n      this.emit();\n    }, 0);\n  }\n  touchStart(event) {\n    this.ctrl.setEventIds(event);\n    const state = this.state;\n    const ctrlTouchIds = this.ctrl.touchIds;\n    if (state._active) {\n      if (state._touchIds.every(id => ctrlTouchIds.has(id))) return;\n    }\n    if (ctrlTouchIds.size < 2) return;\n    this.start(event);\n    state._touchIds = Array.from(ctrlTouchIds).slice(0, 2);\n    const payload = touchDistanceAngle(event, state._touchIds);\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pointerStart(event) {\n    if (event.buttons != null && event.buttons % 2 !== 1) return;\n    this.ctrl.setEventIds(event);\n    event.target.setPointerCapture(event.pointerId);\n    const state = this.state;\n    const _pointerEvents = state._pointerEvents;\n    const ctrlPointerIds = this.ctrl.pointerIds;\n    if (state._active) {\n      if (Array.from(_pointerEvents.keys()).every(id => ctrlPointerIds.has(id))) return;\n    }\n    if (_pointerEvents.size < 2) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (state._pointerEvents.size < 2) return;\n    this.start(event);\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchStart(event, payload);\n  }\n  pinchStart(event, payload) {\n    const state = this.state;\n    state.origin = payload.origin;\n    this.computeValues([payload.distance, payload.angle]);\n    this.computeInitial();\n    this.compute(event);\n    this.emit();\n  }\n  touchMove(event) {\n    if (!this.state._active) return;\n    const payload = touchDistanceAngle(event, this.state._touchIds);\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pointerMove(event) {\n    const _pointerEvents = this.state._pointerEvents;\n    if (_pointerEvents.has(event.pointerId)) {\n      _pointerEvents.set(event.pointerId, event);\n    }\n    if (!this.state._active) return;\n    const payload = distanceAngle(...Array.from(_pointerEvents.values()));\n    if (!payload) return;\n    this.pinchMove(event, payload);\n  }\n  pinchMove(event, payload) {\n    const state = this.state;\n    const prev_a = state._values[1];\n    const delta_a = payload.angle - prev_a;\n    let delta_turns = 0;\n    if (Math.abs(delta_a) > 270) delta_turns += Math.sign(delta_a);\n    this.computeValues([payload.distance, payload.angle - 360 * delta_turns]);\n    state.origin = payload.origin;\n    state.turns = delta_turns;\n    state._movement = [state._values[0] / state._initial[0] - 1, state._values[1] - state._initial[1]];\n    this.compute(event);\n    this.emit();\n  }\n  touchEnd(event) {\n    this.ctrl.setEventIds(event);\n    if (!this.state._active) return;\n    if (this.state._touchIds.some(id => !this.ctrl.touchIds.has(id))) {\n      this.state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  pointerEnd(event) {\n    const state = this.state;\n    this.ctrl.setEventIds(event);\n    try {\n      event.target.releasePointerCapture(event.pointerId);\n    } catch (_unused) {}\n    if (state._pointerEvents.has(event.pointerId)) {\n      state._pointerEvents.delete(event.pointerId);\n    }\n    if (!state._active) return;\n    if (state._pointerEvents.size < 2) {\n      state._active = false;\n      this.compute(event);\n      this.emit();\n    }\n  }\n  gestureStart(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    if (state._active) return;\n    this.start(event);\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  gestureMove(event) {\n    if (event.cancelable) event.preventDefault();\n    if (!this.state._active) return;\n    const state = this.state;\n    this.computeValues([event.scale, event.rotation]);\n    state.origin = [event.clientX, event.clientY];\n    const _previousMovement = state._movement;\n    state._movement = [event.scale - 1, event.rotation];\n    state._delta = V.sub(state._movement, _previousMovement);\n    this.compute(event);\n    this.emit();\n  }\n  gestureEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  wheel(event) {\n    const modifierKey = this.config.modifierKey;\n    if (modifierKey && (Array.isArray(modifierKey) ? !modifierKey.find(k => event[k]) : !event[modifierKey])) return;\n    if (!this.state._active) this.wheelStart(event);else this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelStart(event) {\n    this.start(event);\n    this.wheelChange(event);\n  }\n  wheelChange(event) {\n    const isR3f = 'uv' in event;\n    if (!isR3f) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      if (process.env.NODE_ENV === 'development' && !event.defaultPrevented) {\n        console.warn(`[@use-gesture]: To properly support zoom on trackpads, try using the \\`target\\` option.\\n\\nThis message will only appear in development mode.`);\n      }\n    }\n    const state = this.state;\n    state._delta = [-wheelValues(event)[1] / PINCH_WHEEL_RATIO * state.offset[0], 0];\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.state.origin = [event.clientX, event.clientY];\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    const device = this.config.device;\n    if (!!device) {\n      bindFunction(device, 'start', this[device + 'Start'].bind(this));\n      bindFunction(device, 'change', this[device + 'Move'].bind(this));\n      bindFunction(device, 'end', this[device + 'End'].bind(this));\n      bindFunction(device, 'cancel', this[device + 'End'].bind(this));\n      bindFunction('lostPointerCapture', '', this[device + 'End'].bind(this));\n    }\n    if (this.config.pinchOnWheel) {\n      bindFunction('wheel', '', this.wheel.bind(this), {\n        passive: false\n      });\n    }\n  }\n}\nconst pinchConfigResolver = _objectSpread2(_objectSpread2({}, commonConfigResolver), {}, {\n  device(_v, _k, {\n    shared,\n    pointer: {\n      touch = false\n    } = {}\n  }) {\n    const sharedConfig = shared;\n    if (sharedConfig.target && !SUPPORT.touch && SUPPORT.gesture) return 'gesture';\n    if (SUPPORT.touch && touch) return 'touch';\n    if (SUPPORT.touchscreen) {\n      if (SUPPORT.pointer) return 'pointer';\n      if (SUPPORT.touch) return 'touch';\n    }\n  },\n  bounds(_v, _k, {\n    scaleBounds = {},\n    angleBounds = {}\n  }) {\n    const _scaleBounds = state => {\n      const D = assignDefault(call(scaleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [D.min, D.max];\n    };\n    const _angleBounds = state => {\n      const A = assignDefault(call(angleBounds, state), {\n        min: -Infinity,\n        max: Infinity\n      });\n      return [A.min, A.max];\n    };\n    if (typeof scaleBounds !== 'function' && typeof angleBounds !== 'function') return [_scaleBounds(), _angleBounds()];\n    return state => [_scaleBounds(state), _angleBounds(state)];\n  },\n  threshold(value, _k, config) {\n    this.lockDirection = config.axis === 'lock';\n    const threshold = V.toVector(value, this.lockDirection ? [0.1, 3] : 0);\n    return threshold;\n  },\n  modifierKey(value) {\n    if (value === undefined) return 'ctrlKey';\n    return value;\n  },\n  pinchOnWheel(value = true) {\n    return value;\n  }\n});\nclass MoveEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'moving');\n  }\n  move(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    if (!this.state._active) this.moveStart(event);else this.moveChange(event);\n    this.timeoutStore.add('moveEnd', this.moveEnd.bind(this));\n  }\n  moveStart(event) {\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.computeInitial();\n    this.emit();\n  }\n  moveChange(event) {\n    if (!this.state._active) return;\n    const values = pointerValues(event);\n    const state = this.state;\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  moveEnd(event) {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute(event);\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'change', this.move.bind(this));\n    bindFunction('pointer', 'leave', this.moveEnd.bind(this));\n  }\n}\nconst moveConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\nclass ScrollEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'scrolling');\n  }\n  scroll(event) {\n    if (!this.state._active) this.start(event);\n    this.scrollChange(event);\n    this.timeoutStore.add('scrollEnd', this.scrollEnd.bind(this));\n  }\n  scrollChange(event) {\n    if (event.cancelable) event.preventDefault();\n    const state = this.state;\n    const values = scrollValues(event);\n    state._delta = V.sub(values, state._values);\n    V.addTo(state._movement, state._delta);\n    this.computeValues(values);\n    this.compute(event);\n    this.emit();\n  }\n  scrollEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('scroll', '', this.scroll.bind(this));\n  }\n}\nconst scrollConfigResolver = coordinatesConfigResolver;\nclass WheelEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'wheeling');\n  }\n  wheel(event) {\n    if (!this.state._active) this.start(event);\n    this.wheelChange(event);\n    this.timeoutStore.add('wheelEnd', this.wheelEnd.bind(this));\n  }\n  wheelChange(event) {\n    const state = this.state;\n    state._delta = wheelValues(event);\n    V.addTo(state._movement, state._delta);\n    clampStateInternalMovementToBounds(state);\n    this.compute(event);\n    this.emit();\n  }\n  wheelEnd() {\n    if (!this.state._active) return;\n    this.state._active = false;\n    this.compute();\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('wheel', '', this.wheel.bind(this));\n  }\n}\nconst wheelConfigResolver = coordinatesConfigResolver;\nclass HoverEngine extends CoordinatesEngine {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"ingKey\", 'hovering');\n  }\n  enter(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    this.start(event);\n    this.computeValues(pointerValues(event));\n    this.compute(event);\n    this.emit();\n  }\n  leave(event) {\n    if (this.config.mouseOnly && event.pointerType !== 'mouse') return;\n    const state = this.state;\n    if (!state._active) return;\n    state._active = false;\n    const values = pointerValues(event);\n    state._movement = state._delta = V.sub(values, state._values);\n    this.computeValues(values);\n    this.compute(event);\n    state.delta = state.movement;\n    this.emit();\n  }\n  bind(bindFunction) {\n    bindFunction('pointer', 'enter', this.enter.bind(this));\n    bindFunction('pointer', 'leave', this.leave.bind(this));\n  }\n}\nconst hoverConfigResolver = _objectSpread2(_objectSpread2({}, coordinatesConfigResolver), {}, {\n  mouseOnly: (value = true) => value\n});\nconst EngineMap = new Map();\nconst ConfigResolverMap = new Map();\nfunction registerAction(action) {\n  EngineMap.set(action.key, action.engine);\n  ConfigResolverMap.set(action.key, action.resolver);\n}\nconst dragAction = {\n  key: 'drag',\n  engine: DragEngine,\n  resolver: dragConfigResolver\n};\nconst hoverAction = {\n  key: 'hover',\n  engine: HoverEngine,\n  resolver: hoverConfigResolver\n};\nconst moveAction = {\n  key: 'move',\n  engine: MoveEngine,\n  resolver: moveConfigResolver\n};\nconst pinchAction = {\n  key: 'pinch',\n  engine: PinchEngine,\n  resolver: pinchConfigResolver\n};\nconst scrollAction = {\n  key: 'scroll',\n  engine: ScrollEngine,\n  resolver: scrollConfigResolver\n};\nconst wheelAction = {\n  key: 'wheel',\n  engine: WheelEngine,\n  resolver: wheelConfigResolver\n};\nexport { ConfigResolverMap as C, EngineMap as E, SUPPORT as S, _objectSpread2 as _, _defineProperty as a, touchIds as b, chain as c, toHandlerProp as d, dragAction as e, pinchAction as f, hoverAction as h, isTouch as i, moveAction as m, parseProp as p, registerAction as r, scrollAction as s, toDomEventType as t, wheelAction as w };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}