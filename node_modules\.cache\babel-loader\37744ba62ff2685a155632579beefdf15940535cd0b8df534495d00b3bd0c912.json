{"ast": null, "code": "import React, { useCallback, useMemo } from 'react';\nimport PickerView from '../picker-view';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { generateDatePickerColumns, convertDateToStringArray, convertStringArrayToDate } from '../date-picker/date-picker-utils';\nimport useRenderLabel from './useRenderLabel';\nimport { TILL_NOW } from '../date-picker/util';\nconst thisYear = new Date().getFullYear();\nconst defaultProps = {\n  min: new Date(new Date().setFullYear(thisYear - 10)),\n  max: new Date(new Date().setFullYear(thisYear + 10)),\n  precision: 'day'\n};\nexport const DatePickerView = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const {\n    renderLabel\n  } = props;\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: (_a = props.defaultValue) !== null && _a !== void 0 ? _a : null\n  });\n  const mergedRenderLabel = useRenderLabel(renderLabel);\n  const pickerValue = useMemo(() => {\n    if (value === null || value === void 0 ? void 0 : value.tillNow) {\n      return [TILL_NOW, null, null];\n    }\n    return convertDateToStringArray(value, props.precision);\n  }, [value, props.precision]);\n  const onChange = useCallback(val => {\n    var _a;\n    const date = convertStringArrayToDate(val, props.precision);\n    if (date) {\n      setValue(date);\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, date);\n    }\n  }, [props.onChange, props.precision]);\n  return withNativeProps(props, React.createElement(PickerView, {\n    columns: selected => generateDatePickerColumns(selected, props.min, props.max, props.precision, mergedRenderLabel, props.filter, props.tillNow),\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    value: pickerValue,\n    mouseWheel: props.mouseWheel,\n    onChange: onChange\n  }));\n};", "map": {"version": 3, "names": ["React", "useCallback", "useMemo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withNativeProps", "mergeProps", "usePropsValue", "generateDatePickerColumns", "convertDateToStringArray", "convertStringArrayToDate", "useRenderLabel", "TILL_NOW", "thisYear", "Date", "getFullYear", "defaultProps", "min", "setFullYear", "max", "precision", "DatePickerView", "p", "_a", "props", "renderLabel", "value", "setValue", "defaultValue", "mergedRenderLabel", "picker<PERSON><PERSON><PERSON>", "tillNow", "onChange", "val", "date", "call", "createElement", "columns", "selected", "filter", "loading", "loadingContent", "mouseWheel"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/date-picker-view/date-picker-view.js"], "sourcesContent": ["import React, { useCallback, useMemo } from 'react';\nimport PickerView from '../picker-view';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { generateDatePickerColumns, convertDateToStringArray, convertStringArrayToDate } from '../date-picker/date-picker-utils';\nimport useRenderLabel from './useRenderLabel';\nimport { TILL_NOW } from '../date-picker/util';\nconst thisYear = new Date().getFullYear();\nconst defaultProps = {\n  min: new Date(new Date().setFullYear(thisYear - 10)),\n  max: new Date(new Date().setFullYear(thisYear + 10)),\n  precision: 'day'\n};\nexport const DatePickerView = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const {\n    renderLabel\n  } = props;\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: (_a = props.defaultValue) !== null && _a !== void 0 ? _a : null\n  });\n  const mergedRenderLabel = useRenderLabel(renderLabel);\n  const pickerValue = useMemo(() => {\n    if (value === null || value === void 0 ? void 0 : value.tillNow) {\n      return [TILL_NOW, null, null];\n    }\n    return convertDateToStringArray(value, props.precision);\n  }, [value, props.precision]);\n  const onChange = useCallback(val => {\n    var _a;\n    const date = convertStringArrayToDate(val, props.precision);\n    if (date) {\n      setValue(date);\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, date);\n    }\n  }, [props.onChange, props.precision]);\n  return withNativeProps(props, React.createElement(PickerView, {\n    columns: selected => generateDatePickerColumns(selected, props.min, props.max, props.precision, mergedRenderLabel, props.filter, props.tillNow),\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    value: pickerValue,\n    mouseWheel: props.mouseWheel,\n    onChange: onChange\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,yBAAyB,EAAEC,wBAAwB,EAAEC,wBAAwB,QAAQ,kCAAkC;AAChI,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACzC,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACI,WAAW,CAACL,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpDM,GAAG,EAAE,IAAIL,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACI,WAAW,CAACL,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpDO,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGC,CAAC,IAAI;EACjC,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGlB,UAAU,CAACU,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAM;IACJG;EACF,CAAC,GAAGD,KAAK;EACT,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,aAAa,CAAC;IACtCmB,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBE,YAAY,EAAE,CAACL,EAAE,GAAGC,KAAK,CAACI,YAAY,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;EAC3E,CAAC,CAAC;EACF,MAAMM,iBAAiB,GAAGlB,cAAc,CAACc,WAAW,CAAC;EACrD,MAAMK,WAAW,GAAG3B,OAAO,CAAC,MAAM;IAChC,IAAIuB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,OAAO,EAAE;MAC/D,OAAO,CAACnB,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/B;IACA,OAAOH,wBAAwB,CAACiB,KAAK,EAAEF,KAAK,CAACJ,SAAS,CAAC;EACzD,CAAC,EAAE,CAACM,KAAK,EAAEF,KAAK,CAACJ,SAAS,CAAC,CAAC;EAC5B,MAAMY,QAAQ,GAAG9B,WAAW,CAAC+B,GAAG,IAAI;IAClC,IAAIV,EAAE;IACN,MAAMW,IAAI,GAAGxB,wBAAwB,CAACuB,GAAG,EAAET,KAAK,CAACJ,SAAS,CAAC;IAC3D,IAAIc,IAAI,EAAE;MACRP,QAAQ,CAACO,IAAI,CAAC;MACd,CAACX,EAAE,GAAGC,KAAK,CAACQ,QAAQ,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,CAACX,KAAK,EAAEU,IAAI,CAAC;IACjF;EACF,CAAC,EAAE,CAACV,KAAK,CAACQ,QAAQ,EAAER,KAAK,CAACJ,SAAS,CAAC,CAAC;EACrC,OAAOf,eAAe,CAACmB,KAAK,EAAEvB,KAAK,CAACmC,aAAa,CAAChC,UAAU,EAAE;IAC5DiC,OAAO,EAAEC,QAAQ,IAAI9B,yBAAyB,CAAC8B,QAAQ,EAAEd,KAAK,CAACP,GAAG,EAAEO,KAAK,CAACL,GAAG,EAAEK,KAAK,CAACJ,SAAS,EAAES,iBAAiB,EAAEL,KAAK,CAACe,MAAM,EAAEf,KAAK,CAACO,OAAO,CAAC;IAC/IS,OAAO,EAAEhB,KAAK,CAACgB,OAAO;IACtBC,cAAc,EAAEjB,KAAK,CAACiB,cAAc;IACpCf,KAAK,EAAEI,WAAW;IAClBY,UAAU,EAAElB,KAAK,CAACkB,UAAU;IAC5BV,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}