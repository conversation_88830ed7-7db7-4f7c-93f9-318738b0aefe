{"ast": null, "code": "import \"./tabs.css\";\nimport { Tab, Tabs } from './tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(Tabs, {\n  Tab\n});", "map": {"version": 3, "names": ["Tab", "Tabs", "attachPropertiesToComponent"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/tabs/index.js"], "sourcesContent": ["import \"./tabs.css\";\nimport { Tab, Tabs } from './tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(Tabs, {\n  Tab\n});"], "mappings": "AAAA,OAAO,YAAY;AACnB,SAASA,GAAG,EAAEC,IAAI,QAAQ,QAAQ;AAClC,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACD,IAAI,EAAE;EAC/CD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}