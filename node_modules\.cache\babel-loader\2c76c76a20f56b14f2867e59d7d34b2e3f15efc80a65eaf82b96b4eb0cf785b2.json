{"ast": null, "code": "import { useRef } from 'react';\nconst MIN_DISTANCE = 10;\nfunction getDirection(x, y) {\n  if (x > y && x > MIN_DISTANCE) {\n    return 'horizontal';\n  }\n  if (y > x && y > MIN_DISTANCE) {\n    return 'vertical';\n  }\n  return '';\n}\nexport function useTouch() {\n  const startX = useRef(0);\n  const startY = useRef(0);\n  const deltaX = useRef(0);\n  const deltaY = useRef(0);\n  const offsetX = useRef(0);\n  const offsetY = useRef(0);\n  const direction = useRef('');\n  const isVertical = () => direction.current === 'vertical';\n  const isHorizontal = () => direction.current === 'horizontal';\n  const reset = () => {\n    deltaX.current = 0;\n    deltaY.current = 0;\n    offsetX.current = 0;\n    offsetY.current = 0;\n    direction.current = '';\n  };\n  const start = event => {\n    reset();\n    startX.current = event.touches[0].clientX;\n    startY.current = event.touches[0].clientY;\n  };\n  const move = event => {\n    const touch = event.touches[0];\n    // Fix: <PERSON><PERSON> back will set clientX to negative number\n    deltaX.current = touch.clientX < 0 ? 0 : touch.clientX - startX.current;\n    deltaY.current = touch.clientY - startY.current;\n    offsetX.current = Math.abs(deltaX.current);\n    offsetY.current = Math.abs(deltaY.current);\n    if (!direction.current) {\n      direction.current = getDirection(offsetX.current, offsetY.current);\n    }\n  };\n  return {\n    move,\n    start,\n    reset,\n    startX,\n    startY,\n    deltaX,\n    deltaY,\n    offsetX,\n    offsetY,\n    direction,\n    isVertical,\n    isHorizontal\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}