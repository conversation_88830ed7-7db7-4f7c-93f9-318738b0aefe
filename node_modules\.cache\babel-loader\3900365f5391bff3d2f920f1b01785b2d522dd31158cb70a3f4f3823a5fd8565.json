{"ast": null, "code": "import \"./badge.css\";\nimport { Badge, dot } from './badge';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(Badge, {\n  dot\n});", "map": {"version": 3, "names": ["Badge", "dot", "attachPropertiesToComponent"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/badge/index.js"], "sourcesContent": ["import \"./badge.css\";\nimport { Badge, dot } from './badge';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(Badge, {\n  dot\n});"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,KAAK,EAAEC,GAAG,QAAQ,SAAS;AACpC,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACF,KAAK,EAAE;EAChDC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}