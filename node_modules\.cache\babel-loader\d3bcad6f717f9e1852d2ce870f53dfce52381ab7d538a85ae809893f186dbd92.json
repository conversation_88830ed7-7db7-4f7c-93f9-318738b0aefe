{"ast": null, "code": "/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function () {\n  'use strict';\n\n  // Exit early if we're not running in a browser.\n  if (typeof window !== 'object') {\n    return;\n  }\n\n  // Exit early if all IntersectionObserver and IntersectionObserverEntry\n  // features are natively supported.\n  if ('IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n    // Minimal polyfill for Edge 15's lack of `isIntersecting`\n    // See: https://github.com/w3c/IntersectionObserver/issues/211\n    if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n      Object.defineProperty(window.IntersectionObserverEntry.prototype, 'isIntersecting', {\n        get: function () {\n          return this.intersectionRatio > 0;\n        }\n      });\n    }\n    return;\n  }\n\n  /**\n   * Returns the embedding frame element, if any.\n   * @param {!Document} doc\n   * @return {!Element}\n   */\n  function getFrameElement(doc) {\n    try {\n      return doc.defaultView && doc.defaultView.frameElement || null;\n    } catch (e) {\n      // Ignore the error.\n      return null;\n    }\n  }\n\n  /**\n   * A local reference to the root document.\n   */\n  var document = function (startDoc) {\n    var doc = startDoc;\n    var frame = getFrameElement(doc);\n    while (frame) {\n      doc = frame.ownerDocument;\n      frame = getFrameElement(doc);\n    }\n    return doc;\n  }(window.document);\n\n  /**\n   * An IntersectionObserver registry. This registry exists to hold a strong\n   * reference to IntersectionObserver instances currently observing a target\n   * element. Without this registry, instances without another reference may be\n   * garbage collected.\n   */\n  var registry = [];\n\n  /**\n   * The signal updater for cross-origin intersection. When not null, it means\n   * that the polyfill is configured to work in a cross-origin mode.\n   * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n   */\n  var crossOriginUpdater = null;\n\n  /**\n   * The current cross-origin intersection. Only used in the cross-origin mode.\n   * @type {DOMRect|ClientRect}\n   */\n  var crossOriginRect = null;\n\n  /**\n   * Creates the global IntersectionObserverEntry constructor.\n   * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n   * @param {Object} entry A dictionary of instance properties.\n   * @constructor\n   */\n  function IntersectionObserverEntry(entry) {\n    this.time = entry.time;\n    this.target = entry.target;\n    this.rootBounds = ensureDOMRect(entry.rootBounds);\n    this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n    this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n    this.isIntersecting = !!entry.intersectionRect;\n\n    // Calculates the intersection ratio.\n    var targetRect = this.boundingClientRect;\n    var targetArea = targetRect.width * targetRect.height;\n    var intersectionRect = this.intersectionRect;\n    var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n    // Sets intersection ratio.\n    if (targetArea) {\n      // Round the intersection ratio to avoid floating point math issues:\n      // https://github.com/w3c/IntersectionObserver/issues/324\n      this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n    } else {\n      // If area is zero and is intersecting, sets to 1, otherwise to 0\n      this.intersectionRatio = this.isIntersecting ? 1 : 0;\n    }\n  }\n\n  /**\n   * Creates the global IntersectionObserver constructor.\n   * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n   * @param {Function} callback The function to be invoked after intersection\n   *     changes have queued. The function is not invoked if the queue has\n   *     been emptied by calling the `takeRecords` method.\n   * @param {Object=} opt_options Optional configuration options.\n   * @constructor\n   */\n  function IntersectionObserver(callback, opt_options) {\n    var options = opt_options || {};\n    if (typeof callback != 'function') {\n      throw new Error('callback must be a function');\n    }\n    if (options.root && options.root.nodeType != 1 && options.root.nodeType != 9) {\n      throw new Error('root must be a Document or Element');\n    }\n\n    // Binds and throttles `this._checkForIntersections`.\n    this._checkForIntersections = throttle(this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n    // Private properties.\n    this._callback = callback;\n    this._observationTargets = [];\n    this._queuedEntries = [];\n    this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n    // Public properties.\n    this.thresholds = this._initThresholds(options.threshold);\n    this.root = options.root || null;\n    this.rootMargin = this._rootMarginValues.map(function (margin) {\n      return margin.value + margin.unit;\n    }).join(' ');\n\n    /** @private @const {!Array<!Document>} */\n    this._monitoringDocuments = [];\n    /** @private @const {!Array<function()>} */\n    this._monitoringUnsubscribes = [];\n  }\n\n  /**\n   * The minimum interval within which the document will be checked for\n   * intersection changes.\n   */\n  IntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n  /**\n   * The frequency in which the polyfill polls for intersection changes.\n   * this can be updated on a per instance basis and must be set prior to\n   * calling `observe` on the first target.\n   */\n  IntersectionObserver.prototype.POLL_INTERVAL = null;\n\n  /**\n   * Use a mutation observer on the root element\n   * to detect intersection changes.\n   */\n  IntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n  /**\n   * Sets up the polyfill in the cross-origin mode. The result is the\n   * updater function that accepts two arguments: `boundingClientRect` and\n   * `intersectionRect` - just as these fields would be available to the\n   * parent via `IntersectionObserverEntry`. This function should be called\n   * each time the iframe receives intersection information from the parent\n   * window, e.g. via messaging.\n   * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n   */\n  IntersectionObserver._setupCrossOriginUpdater = function () {\n    if (!crossOriginUpdater) {\n      /**\n       * @param {DOMRect|ClientRect} boundingClientRect\n       * @param {DOMRect|ClientRect} intersectionRect\n       */\n      crossOriginUpdater = function (boundingClientRect, intersectionRect) {\n        if (!boundingClientRect || !intersectionRect) {\n          crossOriginRect = getEmptyRect();\n        } else {\n          crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n        }\n        registry.forEach(function (observer) {\n          observer._checkForIntersections();\n        });\n      };\n    }\n    return crossOriginUpdater;\n  };\n\n  /**\n   * Resets the cross-origin mode.\n   */\n  IntersectionObserver._resetCrossOriginUpdater = function () {\n    crossOriginUpdater = null;\n    crossOriginRect = null;\n  };\n\n  /**\n   * Starts observing a target element for intersection changes based on\n   * the thresholds values.\n   * @param {Element} target The DOM element to observe.\n   */\n  IntersectionObserver.prototype.observe = function (target) {\n    var isTargetAlreadyObserved = this._observationTargets.some(function (item) {\n      return item.element == target;\n    });\n    if (isTargetAlreadyObserved) {\n      return;\n    }\n    if (!(target && target.nodeType == 1)) {\n      throw new Error('target must be an Element');\n    }\n    this._registerInstance();\n    this._observationTargets.push({\n      element: target,\n      entry: null\n    });\n    this._monitorIntersections(target.ownerDocument);\n    this._checkForIntersections();\n  };\n\n  /**\n   * Stops observing a target element for intersection changes.\n   * @param {Element} target The DOM element to observe.\n   */\n  IntersectionObserver.prototype.unobserve = function (target) {\n    this._observationTargets = this._observationTargets.filter(function (item) {\n      return item.element != target;\n    });\n    this._unmonitorIntersections(target.ownerDocument);\n    if (this._observationTargets.length == 0) {\n      this._unregisterInstance();\n    }\n  };\n\n  /**\n   * Stops observing all target elements for intersection changes.\n   */\n  IntersectionObserver.prototype.disconnect = function () {\n    this._observationTargets = [];\n    this._unmonitorAllIntersections();\n    this._unregisterInstance();\n  };\n\n  /**\n   * Returns any queue entries that have not yet been reported to the\n   * callback and clears the queue. This can be used in conjunction with the\n   * callback to obtain the absolute most up-to-date intersection information.\n   * @return {Array} The currently queued entries.\n   */\n  IntersectionObserver.prototype.takeRecords = function () {\n    var records = this._queuedEntries.slice();\n    this._queuedEntries = [];\n    return records;\n  };\n\n  /**\n   * Accepts the threshold value from the user configuration object and\n   * returns a sorted array of unique threshold values. If a value is not\n   * between 0 and 1 and error is thrown.\n   * @private\n   * @param {Array|number=} opt_threshold An optional threshold value or\n   *     a list of threshold values, defaulting to [0].\n   * @return {Array} A sorted list of unique and valid threshold values.\n   */\n  IntersectionObserver.prototype._initThresholds = function (opt_threshold) {\n    var threshold = opt_threshold || [0];\n    if (!Array.isArray(threshold)) threshold = [threshold];\n    return threshold.sort().filter(function (t, i, a) {\n      if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n        throw new Error('threshold must be a number between 0 and 1 inclusively');\n      }\n      return t !== a[i - 1];\n    });\n  };\n\n  /**\n   * Accepts the rootMargin value from the user configuration object\n   * and returns an array of the four margin values as an object containing\n   * the value and unit properties. If any of the values are not properly\n   * formatted or use a unit other than px or %, and error is thrown.\n   * @private\n   * @param {string=} opt_rootMargin An optional rootMargin value,\n   *     defaulting to '0px'.\n   * @return {Array<Object>} An array of margin objects with the keys\n   *     value and unit.\n   */\n  IntersectionObserver.prototype._parseRootMargin = function (opt_rootMargin) {\n    var marginString = opt_rootMargin || '0px';\n    var margins = marginString.split(/\\s+/).map(function (margin) {\n      var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n      if (!parts) {\n        throw new Error('rootMargin must be specified in pixels or percent');\n      }\n      return {\n        value: parseFloat(parts[1]),\n        unit: parts[2]\n      };\n    });\n\n    // Handles shorthand.\n    margins[1] = margins[1] || margins[0];\n    margins[2] = margins[2] || margins[0];\n    margins[3] = margins[3] || margins[1];\n    return margins;\n  };\n\n  /**\n   * Starts polling for intersection changes if the polling is not already\n   * happening, and if the page's visibility state is visible.\n   * @param {!Document} doc\n   * @private\n   */\n  IntersectionObserver.prototype._monitorIntersections = function (doc) {\n    var win = doc.defaultView;\n    if (!win) {\n      // Already destroyed.\n      return;\n    }\n    if (this._monitoringDocuments.indexOf(doc) != -1) {\n      // Already monitoring.\n      return;\n    }\n\n    // Private state for monitoring.\n    var callback = this._checkForIntersections;\n    var monitoringInterval = null;\n    var domObserver = null;\n\n    // If a poll interval is set, use polling instead of listening to\n    // resize and scroll events or DOM mutations.\n    if (this.POLL_INTERVAL) {\n      monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n    } else {\n      addEvent(win, 'resize', callback, true);\n      addEvent(doc, 'scroll', callback, true);\n      if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n        domObserver = new win.MutationObserver(callback);\n        domObserver.observe(doc, {\n          attributes: true,\n          childList: true,\n          characterData: true,\n          subtree: true\n        });\n      }\n    }\n    this._monitoringDocuments.push(doc);\n    this._monitoringUnsubscribes.push(function () {\n      // Get the window object again. When a friendly iframe is destroyed, it\n      // will be null.\n      var win = doc.defaultView;\n      if (win) {\n        if (monitoringInterval) {\n          win.clearInterval(monitoringInterval);\n        }\n        removeEvent(win, 'resize', callback, true);\n      }\n      removeEvent(doc, 'scroll', callback, true);\n      if (domObserver) {\n        domObserver.disconnect();\n      }\n    });\n\n    // Also monitor the parent.\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;\n    if (doc != rootDoc) {\n      var frame = getFrameElement(doc);\n      if (frame) {\n        this._monitorIntersections(frame.ownerDocument);\n      }\n    }\n  };\n\n  /**\n   * Stops polling for intersection changes.\n   * @param {!Document} doc\n   * @private\n   */\n  IntersectionObserver.prototype._unmonitorIntersections = function (doc) {\n    var index = this._monitoringDocuments.indexOf(doc);\n    if (index == -1) {\n      return;\n    }\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;\n\n    // Check if any dependent targets are still remaining.\n    var hasDependentTargets = this._observationTargets.some(function (item) {\n      var itemDoc = item.element.ownerDocument;\n      // Target is in this context.\n      if (itemDoc == doc) {\n        return true;\n      }\n      // Target is nested in this context.\n      while (itemDoc && itemDoc != rootDoc) {\n        var frame = getFrameElement(itemDoc);\n        itemDoc = frame && frame.ownerDocument;\n        if (itemDoc == doc) {\n          return true;\n        }\n      }\n      return false;\n    });\n    if (hasDependentTargets) {\n      return;\n    }\n\n    // Unsubscribe.\n    var unsubscribe = this._monitoringUnsubscribes[index];\n    this._monitoringDocuments.splice(index, 1);\n    this._monitoringUnsubscribes.splice(index, 1);\n    unsubscribe();\n\n    // Also unmonitor the parent.\n    if (doc != rootDoc) {\n      var frame = getFrameElement(doc);\n      if (frame) {\n        this._unmonitorIntersections(frame.ownerDocument);\n      }\n    }\n  };\n\n  /**\n   * Stops polling for intersection changes.\n   * @param {!Document} doc\n   * @private\n   */\n  IntersectionObserver.prototype._unmonitorAllIntersections = function () {\n    var unsubscribes = this._monitoringUnsubscribes.slice(0);\n    this._monitoringDocuments.length = 0;\n    this._monitoringUnsubscribes.length = 0;\n    for (var i = 0; i < unsubscribes.length; i++) {\n      unsubscribes[i]();\n    }\n  };\n\n  /**\n   * Scans each observation target for intersection changes and adds them\n   * to the internal entries queue. If new entries are found, it\n   * schedules the callback to be invoked.\n   * @private\n   */\n  IntersectionObserver.prototype._checkForIntersections = function () {\n    if (!this.root && crossOriginUpdater && !crossOriginRect) {\n      // Cross origin monitoring, but no initial data available yet.\n      return;\n    }\n    var rootIsInDom = this._rootIsInDom();\n    var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n    this._observationTargets.forEach(function (item) {\n      var target = item.element;\n      var targetRect = getBoundingClientRect(target);\n      var rootContainsTarget = this._rootContainsTarget(target);\n      var oldEntry = item.entry;\n      var intersectionRect = rootIsInDom && rootContainsTarget && this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n      var rootBounds = null;\n      if (!this._rootContainsTarget(target)) {\n        rootBounds = getEmptyRect();\n      } else if (!crossOriginUpdater || this.root) {\n        rootBounds = rootRect;\n      }\n      var newEntry = item.entry = new IntersectionObserverEntry({\n        time: now(),\n        target: target,\n        boundingClientRect: targetRect,\n        rootBounds: rootBounds,\n        intersectionRect: intersectionRect\n      });\n      if (!oldEntry) {\n        this._queuedEntries.push(newEntry);\n      } else if (rootIsInDom && rootContainsTarget) {\n        // If the new entry intersection ratio has crossed any of the\n        // thresholds, add a new entry.\n        if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n          this._queuedEntries.push(newEntry);\n        }\n      } else {\n        // If the root is not in the DOM or target is not contained within\n        // root but the previous entry for this target had an intersection,\n        // add a new record indicating removal.\n        if (oldEntry && oldEntry.isIntersecting) {\n          this._queuedEntries.push(newEntry);\n        }\n      }\n    }, this);\n    if (this._queuedEntries.length) {\n      this._callback(this.takeRecords(), this);\n    }\n  };\n\n  /**\n   * Accepts a target and root rect computes the intersection between then\n   * following the algorithm in the spec.\n   * TODO(philipwalton): at this time clip-path is not considered.\n   * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n   * @param {Element} target The target DOM element\n   * @param {Object} targetRect The bounding rect of the target.\n   * @param {Object} rootRect The bounding rect of the root after being\n   *     expanded by the rootMargin value.\n   * @return {?Object} The final intersection rect object or undefined if no\n   *     intersection is found.\n   * @private\n   */\n  IntersectionObserver.prototype._computeTargetAndRootIntersection = function (target, targetRect, rootRect) {\n    // If the element isn't displayed, an intersection can't happen.\n    if (window.getComputedStyle(target).display == 'none') return;\n    var intersectionRect = targetRect;\n    var parent = getParentNode(target);\n    var atRoot = false;\n    while (!atRoot && parent) {\n      var parentRect = null;\n      var parentComputedStyle = parent.nodeType == 1 ? window.getComputedStyle(parent) : {};\n\n      // If the parent isn't displayed, an intersection can't happen.\n      if (parentComputedStyle.display == 'none') return null;\n      if (parent == this.root || parent.nodeType == /* DOCUMENT */9) {\n        atRoot = true;\n        if (parent == this.root || parent == document) {\n          if (crossOriginUpdater && !this.root) {\n            if (!crossOriginRect || crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n              // A 0-size cross-origin intersection means no-intersection.\n              parent = null;\n              parentRect = null;\n              intersectionRect = null;\n            } else {\n              parentRect = crossOriginRect;\n            }\n          } else {\n            parentRect = rootRect;\n          }\n        } else {\n          // Check if there's a frame that can be navigated to.\n          var frame = getParentNode(parent);\n          var frameRect = frame && getBoundingClientRect(frame);\n          var frameIntersect = frame && this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n          if (frameRect && frameIntersect) {\n            parent = frame;\n            parentRect = convertFromParentRect(frameRect, frameIntersect);\n          } else {\n            parent = null;\n            intersectionRect = null;\n          }\n        }\n      } else {\n        // If the element has a non-visible overflow, and it's not the <body>\n        // or <html> element, update the intersection rect.\n        // Note: <body> and <html> cannot be clipped to a rect that's not also\n        // the document rect, so no need to compute a new intersection.\n        var doc = parent.ownerDocument;\n        if (parent != doc.body && parent != doc.documentElement && parentComputedStyle.overflow != 'visible') {\n          parentRect = getBoundingClientRect(parent);\n        }\n      }\n\n      // If either of the above conditionals set a new parentRect,\n      // calculate new intersection data.\n      if (parentRect) {\n        intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n      }\n      if (!intersectionRect) break;\n      parent = parent && getParentNode(parent);\n    }\n    return intersectionRect;\n  };\n\n  /**\n   * Returns the root rect after being expanded by the rootMargin value.\n   * @return {ClientRect} The expanded root rect.\n   * @private\n   */\n  IntersectionObserver.prototype._getRootRect = function () {\n    var rootRect;\n    if (this.root && !isDoc(this.root)) {\n      rootRect = getBoundingClientRect(this.root);\n    } else {\n      // Use <html>/<body> instead of window since scroll bars affect size.\n      var doc = isDoc(this.root) ? this.root : document;\n      var html = doc.documentElement;\n      var body = doc.body;\n      rootRect = {\n        top: 0,\n        left: 0,\n        right: html.clientWidth || body.clientWidth,\n        width: html.clientWidth || body.clientWidth,\n        bottom: html.clientHeight || body.clientHeight,\n        height: html.clientHeight || body.clientHeight\n      };\n    }\n    return this._expandRectByRootMargin(rootRect);\n  };\n\n  /**\n   * Accepts a rect and expands it by the rootMargin value.\n   * @param {DOMRect|ClientRect} rect The rect object to expand.\n   * @return {ClientRect} The expanded rect.\n   * @private\n   */\n  IntersectionObserver.prototype._expandRectByRootMargin = function (rect) {\n    var margins = this._rootMarginValues.map(function (margin, i) {\n      return margin.unit == 'px' ? margin.value : margin.value * (i % 2 ? rect.width : rect.height) / 100;\n    });\n    var newRect = {\n      top: rect.top - margins[0],\n      right: rect.right + margins[1],\n      bottom: rect.bottom + margins[2],\n      left: rect.left - margins[3]\n    };\n    newRect.width = newRect.right - newRect.left;\n    newRect.height = newRect.bottom - newRect.top;\n    return newRect;\n  };\n\n  /**\n   * Accepts an old and new entry and returns true if at least one of the\n   * threshold values has been crossed.\n   * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n   *    particular target element or null if no previous entry exists.\n   * @param {IntersectionObserverEntry} newEntry The current entry for a\n   *    particular target element.\n   * @return {boolean} Returns true if a any threshold has been crossed.\n   * @private\n   */\n  IntersectionObserver.prototype._hasCrossedThreshold = function (oldEntry, newEntry) {\n    // To make comparing easier, an entry that has a ratio of 0\n    // but does not actually intersect is given a value of -1\n    var oldRatio = oldEntry && oldEntry.isIntersecting ? oldEntry.intersectionRatio || 0 : -1;\n    var newRatio = newEntry.isIntersecting ? newEntry.intersectionRatio || 0 : -1;\n\n    // Ignore unchanged ratios\n    if (oldRatio === newRatio) return;\n    for (var i = 0; i < this.thresholds.length; i++) {\n      var threshold = this.thresholds[i];\n\n      // Return true if an entry matches a threshold or if the new ratio\n      // and the old ratio are on the opposite sides of a threshold.\n      if (threshold == oldRatio || threshold == newRatio || threshold < oldRatio !== threshold < newRatio) {\n        return true;\n      }\n    }\n  };\n\n  /**\n   * Returns whether or not the root element is an element and is in the DOM.\n   * @return {boolean} True if the root element is an element and is in the DOM.\n   * @private\n   */\n  IntersectionObserver.prototype._rootIsInDom = function () {\n    return !this.root || containsDeep(document, this.root);\n  };\n\n  /**\n   * Returns whether or not the target element is a child of root.\n   * @param {Element} target The target element to check.\n   * @return {boolean} True if the target element is a child of root.\n   * @private\n   */\n  IntersectionObserver.prototype._rootContainsTarget = function (target) {\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;\n    return containsDeep(rootDoc, target) && (!this.root || rootDoc == target.ownerDocument);\n  };\n\n  /**\n   * Adds the instance to the global IntersectionObserver registry if it isn't\n   * already present.\n   * @private\n   */\n  IntersectionObserver.prototype._registerInstance = function () {\n    if (registry.indexOf(this) < 0) {\n      registry.push(this);\n    }\n  };\n\n  /**\n   * Removes the instance from the global IntersectionObserver registry.\n   * @private\n   */\n  IntersectionObserver.prototype._unregisterInstance = function () {\n    var index = registry.indexOf(this);\n    if (index != -1) registry.splice(index, 1);\n  };\n\n  /**\n   * Returns the result of the performance.now() method or null in browsers\n   * that don't support the API.\n   * @return {number} The elapsed time since the page was requested.\n   */\n  function now() {\n    return window.performance && performance.now && performance.now();\n  }\n\n  /**\n   * Throttles a function and delays its execution, so it's only called at most\n   * once within a given time period.\n   * @param {Function} fn The function to throttle.\n   * @param {number} timeout The amount of time that must pass before the\n   *     function can be called again.\n   * @return {Function} The throttled function.\n   */\n  function throttle(fn, timeout) {\n    var timer = null;\n    return function () {\n      if (!timer) {\n        timer = setTimeout(function () {\n          fn();\n          timer = null;\n        }, timeout);\n      }\n    };\n  }\n\n  /**\n   * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n   * @param {Node} node The DOM node to add the event handler to.\n   * @param {string} event The event name.\n   * @param {Function} fn The event handler to add.\n   * @param {boolean} opt_useCapture Optionally adds the even to the capture\n   *     phase. Note: this only works in modern browsers.\n   */\n  function addEvent(node, event, fn, opt_useCapture) {\n    if (typeof node.addEventListener == 'function') {\n      node.addEventListener(event, fn, opt_useCapture || false);\n    } else if (typeof node.attachEvent == 'function') {\n      node.attachEvent('on' + event, fn);\n    }\n  }\n\n  /**\n   * Removes a previously added event handler from a DOM node.\n   * @param {Node} node The DOM node to remove the event handler from.\n   * @param {string} event The event name.\n   * @param {Function} fn The event handler to remove.\n   * @param {boolean} opt_useCapture If the event handler was added with this\n   *     flag set to true, it should be set to true here in order to remove it.\n   */\n  function removeEvent(node, event, fn, opt_useCapture) {\n    if (typeof node.removeEventListener == 'function') {\n      node.removeEventListener(event, fn, opt_useCapture || false);\n    } else if (typeof node.detachEvent == 'function') {\n      node.detachEvent('on' + event, fn);\n    }\n  }\n\n  /**\n   * Returns the intersection between two rect objects.\n   * @param {Object} rect1 The first rect.\n   * @param {Object} rect2 The second rect.\n   * @return {?Object|?ClientRect} The intersection rect or undefined if no\n   *     intersection is found.\n   */\n  function computeRectIntersection(rect1, rect2) {\n    var top = Math.max(rect1.top, rect2.top);\n    var bottom = Math.min(rect1.bottom, rect2.bottom);\n    var left = Math.max(rect1.left, rect2.left);\n    var right = Math.min(rect1.right, rect2.right);\n    var width = right - left;\n    var height = bottom - top;\n    return width >= 0 && height >= 0 && {\n      top: top,\n      bottom: bottom,\n      left: left,\n      right: right,\n      width: width,\n      height: height\n    } || null;\n  }\n\n  /**\n   * Shims the native getBoundingClientRect for compatibility with older IE.\n   * @param {Element} el The element whose bounding rect to get.\n   * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n   */\n  function getBoundingClientRect(el) {\n    var rect;\n    try {\n      rect = el.getBoundingClientRect();\n    } catch (err) {\n      // Ignore Windows 7 IE11 \"Unspecified error\"\n      // https://github.com/w3c/IntersectionObserver/pull/205\n    }\n    if (!rect) return getEmptyRect();\n\n    // Older IE\n    if (!(rect.width && rect.height)) {\n      rect = {\n        top: rect.top,\n        right: rect.right,\n        bottom: rect.bottom,\n        left: rect.left,\n        width: rect.right - rect.left,\n        height: rect.bottom - rect.top\n      };\n    }\n    return rect;\n  }\n\n  /**\n   * Returns an empty rect object. An empty rect is returned when an element\n   * is not in the DOM.\n   * @return {ClientRect} The empty rect.\n   */\n  function getEmptyRect() {\n    return {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0,\n      width: 0,\n      height: 0\n    };\n  }\n\n  /**\n   * Ensure that the result has all of the necessary fields of the DOMRect.\n   * Specifically this ensures that `x` and `y` fields are set.\n   *\n   * @param {?DOMRect|?ClientRect} rect\n   * @return {?DOMRect}\n   */\n  function ensureDOMRect(rect) {\n    // A `DOMRect` object has `x` and `y` fields.\n    if (!rect || 'x' in rect) {\n      return rect;\n    }\n    // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n    // for internally calculated Rect objects. For the purposes of\n    // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n    // for these fields.\n    return {\n      top: rect.top,\n      y: rect.top,\n      bottom: rect.bottom,\n      left: rect.left,\n      x: rect.left,\n      right: rect.right,\n      width: rect.width,\n      height: rect.height\n    };\n  }\n\n  /**\n   * Inverts the intersection and bounding rect from the parent (frame) BCR to\n   * the local BCR space.\n   * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n   * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n   * @return {ClientRect} The local root bounding rect for the parent's children.\n   */\n  function convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n    var top = parentIntersectionRect.top - parentBoundingRect.top;\n    var left = parentIntersectionRect.left - parentBoundingRect.left;\n    return {\n      top: top,\n      left: left,\n      height: parentIntersectionRect.height,\n      width: parentIntersectionRect.width,\n      bottom: top + parentIntersectionRect.height,\n      right: left + parentIntersectionRect.width\n    };\n  }\n\n  /**\n   * Checks to see if a parent element contains a child element (including inside\n   * shadow DOM).\n   * @param {Node} parent The parent element.\n   * @param {Node} child The child element.\n   * @return {boolean} True if the parent node contains the child node.\n   */\n  function containsDeep(parent, child) {\n    var node = child;\n    while (node) {\n      if (node == parent) return true;\n      node = getParentNode(node);\n    }\n    return false;\n  }\n\n  /**\n   * Gets the parent node of an element or its host element if the parent node\n   * is a shadow root.\n   * @param {Node} node The node whose parent to get.\n   * @return {Node|null} The parent node or null if no parent exists.\n   */\n  function getParentNode(node) {\n    var parent = node.parentNode;\n    if (node.nodeType == /* DOCUMENT */9 && node != document) {\n      // If this node is a document node, look for the embedding frame.\n      return getFrameElement(node);\n    }\n\n    // If the parent has element that is assigned through shadow root slot\n    if (parent && parent.assignedSlot) {\n      parent = parent.assignedSlot.parentNode;\n    }\n    if (parent && parent.nodeType == 11 && parent.host) {\n      // If the parent is a shadow root, return the host element.\n      return parent.host;\n    }\n    return parent;\n  }\n\n  /**\n   * Returns true if `node` is a Document.\n   * @param {!Node} node\n   * @returns {boolean}\n   */\n  function isDoc(node) {\n    return node && node.nodeType === 9;\n  }\n\n  // Exposes the constructors globally.\n  window.IntersectionObserver = IntersectionObserver;\n  window.IntersectionObserverEntry = IntersectionObserverEntry;\n})();", "map": {"version": 3, "names": ["window", "IntersectionObserverEntry", "prototype", "Object", "defineProperty", "get", "intersectionRatio", "getFrameElement", "doc", "defaultView", "frameElement", "e", "document", "startDoc", "frame", "ownerDocument", "registry", "crossOriginUpdater", "crossOriginRect", "entry", "time", "target", "rootBounds", "ensureDOMRect", "boundingClientRect", "intersectionRect", "getEmptyRect", "isIntersecting", "targetRect", "targetArea", "width", "height", "intersectionArea", "Number", "toFixed", "IntersectionObserver", "callback", "opt_options", "options", "Error", "root", "nodeType", "_checkForIntersections", "throttle", "bind", "THROTTLE_TIMEOUT", "_callback", "_observationTargets", "_queuedEntries", "_rootMarginValues", "_parseR<PERSON>Margin", "rootMargin", "thresholds", "_initThresholds", "threshold", "map", "margin", "value", "unit", "join", "_monitoringDocuments", "_monitoringUnsubscribes", "POLL_INTERVAL", "USE_MUTATION_OBSERVER", "_setupCrossOriginUpdater", "convertFromParentRect", "for<PERSON>ach", "observer", "_resetCrossOriginUpdater", "observe", "isTargetAlreadyObserved", "some", "item", "element", "_registerInstance", "push", "_monitorIntersections", "unobserve", "filter", "_unmonitorIntersections", "length", "_unregisterInstance", "disconnect", "_unmonitorAllIntersections", "takeRecords", "records", "slice", "opt_threshold", "Array", "isArray", "sort", "t", "i", "a", "isNaN", "opt_rootMargin", "marginString", "margins", "split", "parts", "exec", "parseFloat", "win", "indexOf", "monitoringInterval", "domObserver", "setInterval", "addEvent", "MutationObserver", "attributes", "childList", "characterData", "subtree", "clearInterval", "removeEvent", "rootDoc", "index", "hasDependentTargets", "itemDoc", "unsubscribe", "splice", "unsubscribes", "rootIsInDom", "_rootIsInDom", "rootRect", "_getRootRect", "getBoundingClientRect", "rootContainsTarget", "_rootContainsTarget", "oldEntry", "_computeTargetAndRootIntersection", "newEntry", "now", "_hasCrossedThreshold", "getComputedStyle", "display", "parent", "getParentNode", "atRoot", "parentRect", "parentComputedStyle", "frameRect", "frameIntersect", "body", "documentElement", "overflow", "computeRectIntersection", "isDoc", "html", "top", "left", "right", "clientWidth", "bottom", "clientHeight", "_expandRectByRootMargin", "rect", "newRect", "oldRatio", "newRatio", "contains<PERSON>eep", "performance", "fn", "timeout", "timer", "setTimeout", "node", "event", "opt_useCapture", "addEventListener", "attachEvent", "removeEventListener", "detachEvent", "rect1", "rect2", "Math", "max", "min", "el", "err", "y", "x", "parentBoundingRect", "parentIntersectionRect", "child", "parentNode", "assignedSlot", "host"], "sources": ["C:/Users/<USER>/node_modules/intersection-observer/intersection-observer.js"], "sourcesContent": ["/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function() {\n'use strict';\n\n// Exit early if we're not running in a browser.\nif (typeof window !== 'object') {\n  return;\n}\n\n// Exit early if all IntersectionObserver and IntersectionObserverEntry\n// features are natively supported.\nif ('IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n\n  // Minimal polyfill for Edge 15's lack of `isIntersecting`\n  // See: https://github.com/w3c/IntersectionObserver/issues/211\n  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n    Object.defineProperty(window.IntersectionObserverEntry.prototype,\n      'isIntersecting', {\n      get: function () {\n        return this.intersectionRatio > 0;\n      }\n    });\n  }\n  return;\n}\n\n/**\n * Returns the embedding frame element, if any.\n * @param {!Document} doc\n * @return {!Element}\n */\nfunction getFrameElement(doc) {\n  try {\n    return doc.defaultView && doc.defaultView.frameElement || null;\n  } catch (e) {\n    // Ignore the error.\n    return null;\n  }\n}\n\n/**\n * A local reference to the root document.\n */\nvar document = (function(startDoc) {\n  var doc = startDoc;\n  var frame = getFrameElement(doc);\n  while (frame) {\n    doc = frame.ownerDocument;\n    frame = getFrameElement(doc);\n  }\n  return doc;\n})(window.document);\n\n/**\n * An IntersectionObserver registry. This registry exists to hold a strong\n * reference to IntersectionObserver instances currently observing a target\n * element. Without this registry, instances without another reference may be\n * garbage collected.\n */\nvar registry = [];\n\n/**\n * The signal updater for cross-origin intersection. When not null, it means\n * that the polyfill is configured to work in a cross-origin mode.\n * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nvar crossOriginUpdater = null;\n\n/**\n * The current cross-origin intersection. Only used in the cross-origin mode.\n * @type {DOMRect|ClientRect}\n */\nvar crossOriginRect = null;\n\n\n/**\n * Creates the global IntersectionObserverEntry constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n * @param {Object} entry A dictionary of instance properties.\n * @constructor\n */\nfunction IntersectionObserverEntry(entry) {\n  this.time = entry.time;\n  this.target = entry.target;\n  this.rootBounds = ensureDOMRect(entry.rootBounds);\n  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n  this.isIntersecting = !!entry.intersectionRect;\n\n  // Calculates the intersection ratio.\n  var targetRect = this.boundingClientRect;\n  var targetArea = targetRect.width * targetRect.height;\n  var intersectionRect = this.intersectionRect;\n  var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n  // Sets intersection ratio.\n  if (targetArea) {\n    // Round the intersection ratio to avoid floating point math issues:\n    // https://github.com/w3c/IntersectionObserver/issues/324\n    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n  } else {\n    // If area is zero and is intersecting, sets to 1, otherwise to 0\n    this.intersectionRatio = this.isIntersecting ? 1 : 0;\n  }\n}\n\n\n/**\n * Creates the global IntersectionObserver constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n * @param {Function} callback The function to be invoked after intersection\n *     changes have queued. The function is not invoked if the queue has\n *     been emptied by calling the `takeRecords` method.\n * @param {Object=} opt_options Optional configuration options.\n * @constructor\n */\nfunction IntersectionObserver(callback, opt_options) {\n\n  var options = opt_options || {};\n\n  if (typeof callback != 'function') {\n    throw new Error('callback must be a function');\n  }\n\n  if (\n    options.root &&\n    options.root.nodeType != 1 &&\n    options.root.nodeType != 9\n  ) {\n    throw new Error('root must be a Document or Element');\n  }\n\n  // Binds and throttles `this._checkForIntersections`.\n  this._checkForIntersections = throttle(\n      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n  // Private properties.\n  this._callback = callback;\n  this._observationTargets = [];\n  this._queuedEntries = [];\n  this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n  // Public properties.\n  this.thresholds = this._initThresholds(options.threshold);\n  this.root = options.root || null;\n  this.rootMargin = this._rootMarginValues.map(function(margin) {\n    return margin.value + margin.unit;\n  }).join(' ');\n\n  /** @private @const {!Array<!Document>} */\n  this._monitoringDocuments = [];\n  /** @private @const {!Array<function()>} */\n  this._monitoringUnsubscribes = [];\n}\n\n\n/**\n * The minimum interval within which the document will be checked for\n * intersection changes.\n */\nIntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n\n/**\n * The frequency in which the polyfill polls for intersection changes.\n * this can be updated on a per instance basis and must be set prior to\n * calling `observe` on the first target.\n */\nIntersectionObserver.prototype.POLL_INTERVAL = null;\n\n/**\n * Use a mutation observer on the root element\n * to detect intersection changes.\n */\nIntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n\n/**\n * Sets up the polyfill in the cross-origin mode. The result is the\n * updater function that accepts two arguments: `boundingClientRect` and\n * `intersectionRect` - just as these fields would be available to the\n * parent via `IntersectionObserverEntry`. This function should be called\n * each time the iframe receives intersection information from the parent\n * window, e.g. via messaging.\n * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nIntersectionObserver._setupCrossOriginUpdater = function() {\n  if (!crossOriginUpdater) {\n    /**\n     * @param {DOMRect|ClientRect} boundingClientRect\n     * @param {DOMRect|ClientRect} intersectionRect\n     */\n    crossOriginUpdater = function(boundingClientRect, intersectionRect) {\n      if (!boundingClientRect || !intersectionRect) {\n        crossOriginRect = getEmptyRect();\n      } else {\n        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n      }\n      registry.forEach(function(observer) {\n        observer._checkForIntersections();\n      });\n    };\n  }\n  return crossOriginUpdater;\n};\n\n\n/**\n * Resets the cross-origin mode.\n */\nIntersectionObserver._resetCrossOriginUpdater = function() {\n  crossOriginUpdater = null;\n  crossOriginRect = null;\n};\n\n\n/**\n * Starts observing a target element for intersection changes based on\n * the thresholds values.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.observe = function(target) {\n  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {\n    return item.element == target;\n  });\n\n  if (isTargetAlreadyObserved) {\n    return;\n  }\n\n  if (!(target && target.nodeType == 1)) {\n    throw new Error('target must be an Element');\n  }\n\n  this._registerInstance();\n  this._observationTargets.push({element: target, entry: null});\n  this._monitorIntersections(target.ownerDocument);\n  this._checkForIntersections();\n};\n\n\n/**\n * Stops observing a target element for intersection changes.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.unobserve = function(target) {\n  this._observationTargets =\n      this._observationTargets.filter(function(item) {\n        return item.element != target;\n      });\n  this._unmonitorIntersections(target.ownerDocument);\n  if (this._observationTargets.length == 0) {\n    this._unregisterInstance();\n  }\n};\n\n\n/**\n * Stops observing all target elements for intersection changes.\n */\nIntersectionObserver.prototype.disconnect = function() {\n  this._observationTargets = [];\n  this._unmonitorAllIntersections();\n  this._unregisterInstance();\n};\n\n\n/**\n * Returns any queue entries that have not yet been reported to the\n * callback and clears the queue. This can be used in conjunction with the\n * callback to obtain the absolute most up-to-date intersection information.\n * @return {Array} The currently queued entries.\n */\nIntersectionObserver.prototype.takeRecords = function() {\n  var records = this._queuedEntries.slice();\n  this._queuedEntries = [];\n  return records;\n};\n\n\n/**\n * Accepts the threshold value from the user configuration object and\n * returns a sorted array of unique threshold values. If a value is not\n * between 0 and 1 and error is thrown.\n * @private\n * @param {Array|number=} opt_threshold An optional threshold value or\n *     a list of threshold values, defaulting to [0].\n * @return {Array} A sorted list of unique and valid threshold values.\n */\nIntersectionObserver.prototype._initThresholds = function(opt_threshold) {\n  var threshold = opt_threshold || [0];\n  if (!Array.isArray(threshold)) threshold = [threshold];\n\n  return threshold.sort().filter(function(t, i, a) {\n    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n      throw new Error('threshold must be a number between 0 and 1 inclusively');\n    }\n    return t !== a[i - 1];\n  });\n};\n\n\n/**\n * Accepts the rootMargin value from the user configuration object\n * and returns an array of the four margin values as an object containing\n * the value and unit properties. If any of the values are not properly\n * formatted or use a unit other than px or %, and error is thrown.\n * @private\n * @param {string=} opt_rootMargin An optional rootMargin value,\n *     defaulting to '0px'.\n * @return {Array<Object>} An array of margin objects with the keys\n *     value and unit.\n */\nIntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {\n  var marginString = opt_rootMargin || '0px';\n  var margins = marginString.split(/\\s+/).map(function(margin) {\n    var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n    if (!parts) {\n      throw new Error('rootMargin must be specified in pixels or percent');\n    }\n    return {value: parseFloat(parts[1]), unit: parts[2]};\n  });\n\n  // Handles shorthand.\n  margins[1] = margins[1] || margins[0];\n  margins[2] = margins[2] || margins[0];\n  margins[3] = margins[3] || margins[1];\n\n  return margins;\n};\n\n\n/**\n * Starts polling for intersection changes if the polling is not already\n * happening, and if the page's visibility state is visible.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._monitorIntersections = function(doc) {\n  var win = doc.defaultView;\n  if (!win) {\n    // Already destroyed.\n    return;\n  }\n  if (this._monitoringDocuments.indexOf(doc) != -1) {\n    // Already monitoring.\n    return;\n  }\n\n  // Private state for monitoring.\n  var callback = this._checkForIntersections;\n  var monitoringInterval = null;\n  var domObserver = null;\n\n  // If a poll interval is set, use polling instead of listening to\n  // resize and scroll events or DOM mutations.\n  if (this.POLL_INTERVAL) {\n    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n  } else {\n    addEvent(win, 'resize', callback, true);\n    addEvent(doc, 'scroll', callback, true);\n    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n      domObserver = new win.MutationObserver(callback);\n      domObserver.observe(doc, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    }\n  }\n\n  this._monitoringDocuments.push(doc);\n  this._monitoringUnsubscribes.push(function() {\n    // Get the window object again. When a friendly iframe is destroyed, it\n    // will be null.\n    var win = doc.defaultView;\n\n    if (win) {\n      if (monitoringInterval) {\n        win.clearInterval(monitoringInterval);\n      }\n      removeEvent(win, 'resize', callback, true);\n    }\n\n    removeEvent(doc, 'scroll', callback, true);\n    if (domObserver) {\n      domObserver.disconnect();\n    }\n  });\n\n  // Also monitor the parent.\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._monitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorIntersections = function(doc) {\n  var index = this._monitoringDocuments.indexOf(doc);\n  if (index == -1) {\n    return;\n  }\n\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n\n  // Check if any dependent targets are still remaining.\n  var hasDependentTargets =\n      this._observationTargets.some(function(item) {\n        var itemDoc = item.element.ownerDocument;\n        // Target is in this context.\n        if (itemDoc == doc) {\n          return true;\n        }\n        // Target is nested in this context.\n        while (itemDoc && itemDoc != rootDoc) {\n          var frame = getFrameElement(itemDoc);\n          itemDoc = frame && frame.ownerDocument;\n          if (itemDoc == doc) {\n            return true;\n          }\n        }\n        return false;\n      });\n  if (hasDependentTargets) {\n    return;\n  }\n\n  // Unsubscribe.\n  var unsubscribe = this._monitoringUnsubscribes[index];\n  this._monitoringDocuments.splice(index, 1);\n  this._monitoringUnsubscribes.splice(index, 1);\n  unsubscribe();\n\n  // Also unmonitor the parent.\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._unmonitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorAllIntersections = function() {\n  var unsubscribes = this._monitoringUnsubscribes.slice(0);\n  this._monitoringDocuments.length = 0;\n  this._monitoringUnsubscribes.length = 0;\n  for (var i = 0; i < unsubscribes.length; i++) {\n    unsubscribes[i]();\n  }\n};\n\n\n/**\n * Scans each observation target for intersection changes and adds them\n * to the internal entries queue. If new entries are found, it\n * schedules the callback to be invoked.\n * @private\n */\nIntersectionObserver.prototype._checkForIntersections = function() {\n  if (!this.root && crossOriginUpdater && !crossOriginRect) {\n    // Cross origin monitoring, but no initial data available yet.\n    return;\n  }\n\n  var rootIsInDom = this._rootIsInDom();\n  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n  this._observationTargets.forEach(function(item) {\n    var target = item.element;\n    var targetRect = getBoundingClientRect(target);\n    var rootContainsTarget = this._rootContainsTarget(target);\n    var oldEntry = item.entry;\n    var intersectionRect = rootIsInDom && rootContainsTarget &&\n        this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n    var rootBounds = null;\n    if (!this._rootContainsTarget(target)) {\n      rootBounds = getEmptyRect();\n    } else if (!crossOriginUpdater || this.root) {\n      rootBounds = rootRect;\n    }\n\n    var newEntry = item.entry = new IntersectionObserverEntry({\n      time: now(),\n      target: target,\n      boundingClientRect: targetRect,\n      rootBounds: rootBounds,\n      intersectionRect: intersectionRect\n    });\n\n    if (!oldEntry) {\n      this._queuedEntries.push(newEntry);\n    } else if (rootIsInDom && rootContainsTarget) {\n      // If the new entry intersection ratio has crossed any of the\n      // thresholds, add a new entry.\n      if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n        this._queuedEntries.push(newEntry);\n      }\n    } else {\n      // If the root is not in the DOM or target is not contained within\n      // root but the previous entry for this target had an intersection,\n      // add a new record indicating removal.\n      if (oldEntry && oldEntry.isIntersecting) {\n        this._queuedEntries.push(newEntry);\n      }\n    }\n  }, this);\n\n  if (this._queuedEntries.length) {\n    this._callback(this.takeRecords(), this);\n  }\n};\n\n\n/**\n * Accepts a target and root rect computes the intersection between then\n * following the algorithm in the spec.\n * TODO(philipwalton): at this time clip-path is not considered.\n * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n * @param {Element} target The target DOM element\n * @param {Object} targetRect The bounding rect of the target.\n * @param {Object} rootRect The bounding rect of the root after being\n *     expanded by the rootMargin value.\n * @return {?Object} The final intersection rect object or undefined if no\n *     intersection is found.\n * @private\n */\nIntersectionObserver.prototype._computeTargetAndRootIntersection =\n    function(target, targetRect, rootRect) {\n  // If the element isn't displayed, an intersection can't happen.\n  if (window.getComputedStyle(target).display == 'none') return;\n\n  var intersectionRect = targetRect;\n  var parent = getParentNode(target);\n  var atRoot = false;\n\n  while (!atRoot && parent) {\n    var parentRect = null;\n    var parentComputedStyle = parent.nodeType == 1 ?\n        window.getComputedStyle(parent) : {};\n\n    // If the parent isn't displayed, an intersection can't happen.\n    if (parentComputedStyle.display == 'none') return null;\n\n    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {\n      atRoot = true;\n      if (parent == this.root || parent == document) {\n        if (crossOriginUpdater && !this.root) {\n          if (!crossOriginRect ||\n              crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n            // A 0-size cross-origin intersection means no-intersection.\n            parent = null;\n            parentRect = null;\n            intersectionRect = null;\n          } else {\n            parentRect = crossOriginRect;\n          }\n        } else {\n          parentRect = rootRect;\n        }\n      } else {\n        // Check if there's a frame that can be navigated to.\n        var frame = getParentNode(parent);\n        var frameRect = frame && getBoundingClientRect(frame);\n        var frameIntersect =\n            frame &&\n            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n        if (frameRect && frameIntersect) {\n          parent = frame;\n          parentRect = convertFromParentRect(frameRect, frameIntersect);\n        } else {\n          parent = null;\n          intersectionRect = null;\n        }\n      }\n    } else {\n      // If the element has a non-visible overflow, and it's not the <body>\n      // or <html> element, update the intersection rect.\n      // Note: <body> and <html> cannot be clipped to a rect that's not also\n      // the document rect, so no need to compute a new intersection.\n      var doc = parent.ownerDocument;\n      if (parent != doc.body &&\n          parent != doc.documentElement &&\n          parentComputedStyle.overflow != 'visible') {\n        parentRect = getBoundingClientRect(parent);\n      }\n    }\n\n    // If either of the above conditionals set a new parentRect,\n    // calculate new intersection data.\n    if (parentRect) {\n      intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n    }\n    if (!intersectionRect) break;\n    parent = parent && getParentNode(parent);\n  }\n  return intersectionRect;\n};\n\n\n/**\n * Returns the root rect after being expanded by the rootMargin value.\n * @return {ClientRect} The expanded root rect.\n * @private\n */\nIntersectionObserver.prototype._getRootRect = function() {\n  var rootRect;\n  if (this.root && !isDoc(this.root)) {\n    rootRect = getBoundingClientRect(this.root);\n  } else {\n    // Use <html>/<body> instead of window since scroll bars affect size.\n    var doc = isDoc(this.root) ? this.root : document;\n    var html = doc.documentElement;\n    var body = doc.body;\n    rootRect = {\n      top: 0,\n      left: 0,\n      right: html.clientWidth || body.clientWidth,\n      width: html.clientWidth || body.clientWidth,\n      bottom: html.clientHeight || body.clientHeight,\n      height: html.clientHeight || body.clientHeight\n    };\n  }\n  return this._expandRectByRootMargin(rootRect);\n};\n\n\n/**\n * Accepts a rect and expands it by the rootMargin value.\n * @param {DOMRect|ClientRect} rect The rect object to expand.\n * @return {ClientRect} The expanded rect.\n * @private\n */\nIntersectionObserver.prototype._expandRectByRootMargin = function(rect) {\n  var margins = this._rootMarginValues.map(function(margin, i) {\n    return margin.unit == 'px' ? margin.value :\n        margin.value * (i % 2 ? rect.width : rect.height) / 100;\n  });\n  var newRect = {\n    top: rect.top - margins[0],\n    right: rect.right + margins[1],\n    bottom: rect.bottom + margins[2],\n    left: rect.left - margins[3]\n  };\n  newRect.width = newRect.right - newRect.left;\n  newRect.height = newRect.bottom - newRect.top;\n\n  return newRect;\n};\n\n\n/**\n * Accepts an old and new entry and returns true if at least one of the\n * threshold values has been crossed.\n * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n *    particular target element or null if no previous entry exists.\n * @param {IntersectionObserverEntry} newEntry The current entry for a\n *    particular target element.\n * @return {boolean} Returns true if a any threshold has been crossed.\n * @private\n */\nIntersectionObserver.prototype._hasCrossedThreshold =\n    function(oldEntry, newEntry) {\n\n  // To make comparing easier, an entry that has a ratio of 0\n  // but does not actually intersect is given a value of -1\n  var oldRatio = oldEntry && oldEntry.isIntersecting ?\n      oldEntry.intersectionRatio || 0 : -1;\n  var newRatio = newEntry.isIntersecting ?\n      newEntry.intersectionRatio || 0 : -1;\n\n  // Ignore unchanged ratios\n  if (oldRatio === newRatio) return;\n\n  for (var i = 0; i < this.thresholds.length; i++) {\n    var threshold = this.thresholds[i];\n\n    // Return true if an entry matches a threshold or if the new ratio\n    // and the old ratio are on the opposite sides of a threshold.\n    if (threshold == oldRatio || threshold == newRatio ||\n        threshold < oldRatio !== threshold < newRatio) {\n      return true;\n    }\n  }\n};\n\n\n/**\n * Returns whether or not the root element is an element and is in the DOM.\n * @return {boolean} True if the root element is an element and is in the DOM.\n * @private\n */\nIntersectionObserver.prototype._rootIsInDom = function() {\n  return !this.root || containsDeep(document, this.root);\n};\n\n\n/**\n * Returns whether or not the target element is a child of root.\n * @param {Element} target The target element to check.\n * @return {boolean} True if the target element is a child of root.\n * @private\n */\nIntersectionObserver.prototype._rootContainsTarget = function(target) {\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  return (\n    containsDeep(rootDoc, target) &&\n    (!this.root || rootDoc == target.ownerDocument)\n  );\n};\n\n\n/**\n * Adds the instance to the global IntersectionObserver registry if it isn't\n * already present.\n * @private\n */\nIntersectionObserver.prototype._registerInstance = function() {\n  if (registry.indexOf(this) < 0) {\n    registry.push(this);\n  }\n};\n\n\n/**\n * Removes the instance from the global IntersectionObserver registry.\n * @private\n */\nIntersectionObserver.prototype._unregisterInstance = function() {\n  var index = registry.indexOf(this);\n  if (index != -1) registry.splice(index, 1);\n};\n\n\n/**\n * Returns the result of the performance.now() method or null in browsers\n * that don't support the API.\n * @return {number} The elapsed time since the page was requested.\n */\nfunction now() {\n  return window.performance && performance.now && performance.now();\n}\n\n\n/**\n * Throttles a function and delays its execution, so it's only called at most\n * once within a given time period.\n * @param {Function} fn The function to throttle.\n * @param {number} timeout The amount of time that must pass before the\n *     function can be called again.\n * @return {Function} The throttled function.\n */\nfunction throttle(fn, timeout) {\n  var timer = null;\n  return function () {\n    if (!timer) {\n      timer = setTimeout(function() {\n        fn();\n        timer = null;\n      }, timeout);\n    }\n  };\n}\n\n\n/**\n * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n * @param {Node} node The DOM node to add the event handler to.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to add.\n * @param {boolean} opt_useCapture Optionally adds the even to the capture\n *     phase. Note: this only works in modern browsers.\n */\nfunction addEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.addEventListener == 'function') {\n    node.addEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.attachEvent == 'function') {\n    node.attachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Removes a previously added event handler from a DOM node.\n * @param {Node} node The DOM node to remove the event handler from.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to remove.\n * @param {boolean} opt_useCapture If the event handler was added with this\n *     flag set to true, it should be set to true here in order to remove it.\n */\nfunction removeEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.removeEventListener == 'function') {\n    node.removeEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.detachEvent == 'function') {\n    node.detachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Returns the intersection between two rect objects.\n * @param {Object} rect1 The first rect.\n * @param {Object} rect2 The second rect.\n * @return {?Object|?ClientRect} The intersection rect or undefined if no\n *     intersection is found.\n */\nfunction computeRectIntersection(rect1, rect2) {\n  var top = Math.max(rect1.top, rect2.top);\n  var bottom = Math.min(rect1.bottom, rect2.bottom);\n  var left = Math.max(rect1.left, rect2.left);\n  var right = Math.min(rect1.right, rect2.right);\n  var width = right - left;\n  var height = bottom - top;\n\n  return (width >= 0 && height >= 0) && {\n    top: top,\n    bottom: bottom,\n    left: left,\n    right: right,\n    width: width,\n    height: height\n  } || null;\n}\n\n\n/**\n * Shims the native getBoundingClientRect for compatibility with older IE.\n * @param {Element} el The element whose bounding rect to get.\n * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n */\nfunction getBoundingClientRect(el) {\n  var rect;\n\n  try {\n    rect = el.getBoundingClientRect();\n  } catch (err) {\n    // Ignore Windows 7 IE11 \"Unspecified error\"\n    // https://github.com/w3c/IntersectionObserver/pull/205\n  }\n\n  if (!rect) return getEmptyRect();\n\n  // Older IE\n  if (!(rect.width && rect.height)) {\n    rect = {\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      left: rect.left,\n      width: rect.right - rect.left,\n      height: rect.bottom - rect.top\n    };\n  }\n  return rect;\n}\n\n\n/**\n * Returns an empty rect object. An empty rect is returned when an element\n * is not in the DOM.\n * @return {ClientRect} The empty rect.\n */\nfunction getEmptyRect() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    width: 0,\n    height: 0\n  };\n}\n\n\n/**\n * Ensure that the result has all of the necessary fields of the DOMRect.\n * Specifically this ensures that `x` and `y` fields are set.\n *\n * @param {?DOMRect|?ClientRect} rect\n * @return {?DOMRect}\n */\nfunction ensureDOMRect(rect) {\n  // A `DOMRect` object has `x` and `y` fields.\n  if (!rect || 'x' in rect) {\n    return rect;\n  }\n  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n  // for internally calculated Rect objects. For the purposes of\n  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n  // for these fields.\n  return {\n    top: rect.top,\n    y: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    right: rect.right,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n\n/**\n * Inverts the intersection and bounding rect from the parent (frame) BCR to\n * the local BCR space.\n * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n * @return {ClientRect} The local root bounding rect for the parent's children.\n */\nfunction convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n  var top = parentIntersectionRect.top - parentBoundingRect.top;\n  var left = parentIntersectionRect.left - parentBoundingRect.left;\n  return {\n    top: top,\n    left: left,\n    height: parentIntersectionRect.height,\n    width: parentIntersectionRect.width,\n    bottom: top + parentIntersectionRect.height,\n    right: left + parentIntersectionRect.width\n  };\n}\n\n\n/**\n * Checks to see if a parent element contains a child element (including inside\n * shadow DOM).\n * @param {Node} parent The parent element.\n * @param {Node} child The child element.\n * @return {boolean} True if the parent node contains the child node.\n */\nfunction containsDeep(parent, child) {\n  var node = child;\n  while (node) {\n    if (node == parent) return true;\n\n    node = getParentNode(node);\n  }\n  return false;\n}\n\n\n/**\n * Gets the parent node of an element or its host element if the parent node\n * is a shadow root.\n * @param {Node} node The node whose parent to get.\n * @return {Node|null} The parent node or null if no parent exists.\n */\nfunction getParentNode(node) {\n  var parent = node.parentNode;\n\n  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {\n    // If this node is a document node, look for the embedding frame.\n    return getFrameElement(node);\n  }\n\n  // If the parent has element that is assigned through shadow root slot\n  if (parent && parent.assignedSlot) {\n    parent = parent.assignedSlot.parentNode\n  }\n\n  if (parent && parent.nodeType == 11 && parent.host) {\n    // If the parent is a shadow root, return the host element.\n    return parent.host;\n  }\n\n  return parent;\n}\n\n/**\n * Returns true if `node` is a Document.\n * @param {!Node} node\n * @returns {boolean}\n */\nfunction isDoc(node) {\n  return node && node.nodeType === 9;\n}\n\n\n// Exposes the constructors globally.\nwindow.IntersectionObserver = IntersectionObserver;\nwindow.IntersectionObserverEntry = IntersectionObserverEntry;\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACC,aAAW;EACZ,YAAY;;EAEZ;EACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B;EACF;;EAEA;EACA;EACA,IAAI,sBAAsB,IAAIA,MAAM,IAChC,2BAA2B,IAAIA,MAAM,IACrC,mBAAmB,IAAIA,MAAM,CAACC,yBAAyB,CAACC,SAAS,EAAE;IAErE;IACA;IACA,IAAI,EAAE,gBAAgB,IAAIF,MAAM,CAACC,yBAAyB,CAACC,SAAS,CAAC,EAAE;MACrEC,MAAM,CAACC,cAAc,CAACJ,MAAM,CAACC,yBAAyB,CAACC,SAAS,EAC9D,gBAAgB,EAAE;QAClBG,GAAG,EAAE,SAAAA,CAAA,EAAY;UACf,OAAO,IAAI,CAACC,iBAAiB,GAAG,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IACA;EACF;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASC,eAAeA,CAACC,GAAG,EAAE;IAC5B,IAAI;MACF,OAAOA,GAAG,CAACC,WAAW,IAAID,GAAG,CAACC,WAAW,CAACC,YAAY,IAAI,IAAI;IAChE,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;MACA,OAAO,IAAI;IACb;EACF;;EAEA;AACA;AACA;EACA,IAAIC,QAAQ,GAAI,UAASC,QAAQ,EAAE;IACjC,IAAIL,GAAG,GAAGK,QAAQ;IAClB,IAAIC,KAAK,GAAGP,eAAe,CAACC,GAAG,CAAC;IAChC,OAAOM,KAAK,EAAE;MACZN,GAAG,GAAGM,KAAK,CAACC,aAAa;MACzBD,KAAK,GAAGP,eAAe,CAACC,GAAG,CAAC;IAC9B;IACA,OAAOA,GAAG;EACZ,CAAC,CAAER,MAAM,CAACY,QAAQ,CAAC;;EAEnB;AACA;AACA;AACA;AACA;AACA;EACA,IAAII,QAAQ,GAAG,EAAE;;EAEjB;AACA;AACA;AACA;AACA;EACA,IAAIC,kBAAkB,GAAG,IAAI;;EAE7B;AACA;AACA;AACA;EACA,IAAIC,eAAe,GAAG,IAAI;;EAG1B;AACA;AACA;AACA;AACA;AACA;EACA,SAASjB,yBAAyBA,CAACkB,KAAK,EAAE;IACxC,IAAI,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACtB,IAAI,CAACC,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC1B,IAAI,CAACC,UAAU,GAAGC,aAAa,CAACJ,KAAK,CAACG,UAAU,CAAC;IACjD,IAAI,CAACE,kBAAkB,GAAGD,aAAa,CAACJ,KAAK,CAACK,kBAAkB,CAAC;IACjE,IAAI,CAACC,gBAAgB,GAAGF,aAAa,CAACJ,KAAK,CAACM,gBAAgB,IAAIC,YAAY,CAAC,CAAC,CAAC;IAC/E,IAAI,CAACC,cAAc,GAAG,CAAC,CAACR,KAAK,CAACM,gBAAgB;;IAE9C;IACA,IAAIG,UAAU,GAAG,IAAI,CAACJ,kBAAkB;IACxC,IAAIK,UAAU,GAAGD,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACG,MAAM;IACrD,IAAIN,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC5C,IAAIO,gBAAgB,GAAGP,gBAAgB,CAACK,KAAK,GAAGL,gBAAgB,CAACM,MAAM;;IAEvE;IACA,IAAIF,UAAU,EAAE;MACd;MACA;MACA,IAAI,CAACvB,iBAAiB,GAAG2B,MAAM,CAAC,CAACD,gBAAgB,GAAGH,UAAU,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC,MAAM;MACL;MACA,IAAI,CAAC5B,iBAAiB,GAAG,IAAI,CAACqB,cAAc,GAAG,CAAC,GAAG,CAAC;IACtD;EACF;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASQ,oBAAoBA,CAACC,QAAQ,EAAEC,WAAW,EAAE;IAEnD,IAAIC,OAAO,GAAGD,WAAW,IAAI,CAAC,CAAC;IAE/B,IAAI,OAAOD,QAAQ,IAAI,UAAU,EAAE;MACjC,MAAM,IAAIG,KAAK,CAAC,6BAA6B,CAAC;IAChD;IAEA,IACED,OAAO,CAACE,IAAI,IACZF,OAAO,CAACE,IAAI,CAACC,QAAQ,IAAI,CAAC,IAC1BH,OAAO,CAACE,IAAI,CAACC,QAAQ,IAAI,CAAC,EAC1B;MACA,MAAM,IAAIF,KAAK,CAAC,oCAAoC,CAAC;IACvD;;IAEA;IACA,IAAI,CAACG,sBAAsB,GAAGC,QAAQ,CAClC,IAAI,CAACD,sBAAsB,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAACC,gBAAgB,CAAC;;IAElE;IACA,IAAI,CAACC,SAAS,GAAGV,QAAQ;IACzB,IAAI,CAACW,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,gBAAgB,CAACZ,OAAO,CAACa,UAAU,CAAC;;IAElE;IACA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,eAAe,CAACf,OAAO,CAACgB,SAAS,CAAC;IACzD,IAAI,CAACd,IAAI,GAAGF,OAAO,CAACE,IAAI,IAAI,IAAI;IAChC,IAAI,CAACW,UAAU,GAAG,IAAI,CAACF,iBAAiB,CAACM,GAAG,CAAC,UAASC,MAAM,EAAE;MAC5D,OAAOA,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,IAAI;IACnC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;IAEZ;IACA,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,uBAAuB,GAAG,EAAE;EACnC;;EAGA;AACA;AACA;AACA;EACA1B,oBAAoB,CAACjC,SAAS,CAAC2C,gBAAgB,GAAG,GAAG;;EAGrD;AACA;AACA;AACA;AACA;EACAV,oBAAoB,CAACjC,SAAS,CAAC4D,aAAa,GAAG,IAAI;;EAEnD;AACA;AACA;AACA;EACA3B,oBAAoB,CAACjC,SAAS,CAAC6D,qBAAqB,GAAG,IAAI;;EAG3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA5B,oBAAoB,CAAC6B,wBAAwB,GAAG,YAAW;IACzD,IAAI,CAAC/C,kBAAkB,EAAE;MACvB;AACJ;AACA;AACA;MACIA,kBAAkB,GAAG,SAAAA,CAASO,kBAAkB,EAAEC,gBAAgB,EAAE;QAClE,IAAI,CAACD,kBAAkB,IAAI,CAACC,gBAAgB,EAAE;UAC5CP,eAAe,GAAGQ,YAAY,CAAC,CAAC;QAClC,CAAC,MAAM;UACLR,eAAe,GAAG+C,qBAAqB,CAACzC,kBAAkB,EAAEC,gBAAgB,CAAC;QAC/E;QACAT,QAAQ,CAACkD,OAAO,CAAC,UAASC,QAAQ,EAAE;UAClCA,QAAQ,CAACzB,sBAAsB,CAAC,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC;IACH;IACA,OAAOzB,kBAAkB;EAC3B,CAAC;;EAGD;AACA;AACA;EACAkB,oBAAoB,CAACiC,wBAAwB,GAAG,YAAW;IACzDnD,kBAAkB,GAAG,IAAI;IACzBC,eAAe,GAAG,IAAI;EACxB,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAiB,oBAAoB,CAACjC,SAAS,CAACmE,OAAO,GAAG,UAAShD,MAAM,EAAE;IACxD,IAAIiD,uBAAuB,GAAG,IAAI,CAACvB,mBAAmB,CAACwB,IAAI,CAAC,UAASC,IAAI,EAAE;MACzE,OAAOA,IAAI,CAACC,OAAO,IAAIpD,MAAM;IAC/B,CAAC,CAAC;IAEF,IAAIiD,uBAAuB,EAAE;MAC3B;IACF;IAEA,IAAI,EAAEjD,MAAM,IAAIA,MAAM,CAACoB,QAAQ,IAAI,CAAC,CAAC,EAAE;MACrC,MAAM,IAAIF,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAEA,IAAI,CAACmC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC3B,mBAAmB,CAAC4B,IAAI,CAAC;MAACF,OAAO,EAAEpD,MAAM;MAAEF,KAAK,EAAE;IAAI,CAAC,CAAC;IAC7D,IAAI,CAACyD,qBAAqB,CAACvD,MAAM,CAACN,aAAa,CAAC;IAChD,IAAI,CAAC2B,sBAAsB,CAAC,CAAC;EAC/B,CAAC;;EAGD;AACA;AACA;AACA;EACAP,oBAAoB,CAACjC,SAAS,CAAC2E,SAAS,GAAG,UAASxD,MAAM,EAAE;IAC1D,IAAI,CAAC0B,mBAAmB,GACpB,IAAI,CAACA,mBAAmB,CAAC+B,MAAM,CAAC,UAASN,IAAI,EAAE;MAC7C,OAAOA,IAAI,CAACC,OAAO,IAAIpD,MAAM;IAC/B,CAAC,CAAC;IACN,IAAI,CAAC0D,uBAAuB,CAAC1D,MAAM,CAACN,aAAa,CAAC;IAClD,IAAI,IAAI,CAACgC,mBAAmB,CAACiC,MAAM,IAAI,CAAC,EAAE;MACxC,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC5B;EACF,CAAC;;EAGD;AACA;AACA;EACA9C,oBAAoB,CAACjC,SAAS,CAACgF,UAAU,GAAG,YAAW;IACrD,IAAI,CAACnC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACoC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACF,mBAAmB,CAAC,CAAC;EAC5B,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;EACA9C,oBAAoB,CAACjC,SAAS,CAACkF,WAAW,GAAG,YAAW;IACtD,IAAIC,OAAO,GAAG,IAAI,CAACrC,cAAc,CAACsC,KAAK,CAAC,CAAC;IACzC,IAAI,CAACtC,cAAc,GAAG,EAAE;IACxB,OAAOqC,OAAO;EAChB,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAlD,oBAAoB,CAACjC,SAAS,CAACmD,eAAe,GAAG,UAASkC,aAAa,EAAE;IACvE,IAAIjC,SAAS,GAAGiC,aAAa,IAAI,CAAC,CAAC,CAAC;IACpC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACnC,SAAS,CAAC,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;IAEtD,OAAOA,SAAS,CAACoC,IAAI,CAAC,CAAC,CAACZ,MAAM,CAAC,UAASa,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;MAC/C,IAAI,OAAOF,CAAC,IAAI,QAAQ,IAAIG,KAAK,CAACH,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QACtD,MAAM,IAAIpD,KAAK,CAAC,wDAAwD,CAAC;MAC3E;MACA,OAAOoD,CAAC,KAAKE,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAzD,oBAAoB,CAACjC,SAAS,CAACgD,gBAAgB,GAAG,UAAS6C,cAAc,EAAE;IACzE,IAAIC,YAAY,GAAGD,cAAc,IAAI,KAAK;IAC1C,IAAIE,OAAO,GAAGD,YAAY,CAACE,KAAK,CAAC,KAAK,CAAC,CAAC3C,GAAG,CAAC,UAASC,MAAM,EAAE;MAC3D,IAAI2C,KAAK,GAAG,uBAAuB,CAACC,IAAI,CAAC5C,MAAM,CAAC;MAChD,IAAI,CAAC2C,KAAK,EAAE;QACV,MAAM,IAAI5D,KAAK,CAAC,mDAAmD,CAAC;MACtE;MACA,OAAO;QAACkB,KAAK,EAAE4C,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;QAAEzC,IAAI,EAAEyC,KAAK,CAAC,CAAC;MAAC,CAAC;IACtD,CAAC,CAAC;;IAEF;IACAF,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC;IACrCA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC;IACrCA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC;IAErC,OAAOA,OAAO;EAChB,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;EACA9D,oBAAoB,CAACjC,SAAS,CAAC0E,qBAAqB,GAAG,UAASpE,GAAG,EAAE;IACnE,IAAI8F,GAAG,GAAG9F,GAAG,CAACC,WAAW;IACzB,IAAI,CAAC6F,GAAG,EAAE;MACR;MACA;IACF;IACA,IAAI,IAAI,CAAC1C,oBAAoB,CAAC2C,OAAO,CAAC/F,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;MAChD;MACA;IACF;;IAEA;IACA,IAAI4B,QAAQ,GAAG,IAAI,CAACM,sBAAsB;IAC1C,IAAI8D,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,WAAW,GAAG,IAAI;;IAEtB;IACA;IACA,IAAI,IAAI,CAAC3C,aAAa,EAAE;MACtB0C,kBAAkB,GAAGF,GAAG,CAACI,WAAW,CAACtE,QAAQ,EAAE,IAAI,CAAC0B,aAAa,CAAC;IACpE,CAAC,MAAM;MACL6C,QAAQ,CAACL,GAAG,EAAE,QAAQ,EAAElE,QAAQ,EAAE,IAAI,CAAC;MACvCuE,QAAQ,CAACnG,GAAG,EAAE,QAAQ,EAAE4B,QAAQ,EAAE,IAAI,CAAC;MACvC,IAAI,IAAI,CAAC2B,qBAAqB,IAAI,kBAAkB,IAAIuC,GAAG,EAAE;QAC3DG,WAAW,GAAG,IAAIH,GAAG,CAACM,gBAAgB,CAACxE,QAAQ,CAAC;QAChDqE,WAAW,CAACpC,OAAO,CAAC7D,GAAG,EAAE;UACvBqG,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAE,IAAI;UACfC,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACpD,oBAAoB,CAACe,IAAI,CAACnE,GAAG,CAAC;IACnC,IAAI,CAACqD,uBAAuB,CAACc,IAAI,CAAC,YAAW;MAC3C;MACA;MACA,IAAI2B,GAAG,GAAG9F,GAAG,CAACC,WAAW;MAEzB,IAAI6F,GAAG,EAAE;QACP,IAAIE,kBAAkB,EAAE;UACtBF,GAAG,CAACW,aAAa,CAACT,kBAAkB,CAAC;QACvC;QACAU,WAAW,CAACZ,GAAG,EAAE,QAAQ,EAAElE,QAAQ,EAAE,IAAI,CAAC;MAC5C;MAEA8E,WAAW,CAAC1G,GAAG,EAAE,QAAQ,EAAE4B,QAAQ,EAAE,IAAI,CAAC;MAC1C,IAAIqE,WAAW,EAAE;QACfA,WAAW,CAACvB,UAAU,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIiC,OAAO,GACR,IAAI,CAAC3E,IAAI,KAAK,IAAI,CAACA,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACyB,IAAI,CAAC,IAAK5B,QAAQ;IACnE,IAAIJ,GAAG,IAAI2G,OAAO,EAAE;MAClB,IAAIrG,KAAK,GAAGP,eAAe,CAACC,GAAG,CAAC;MAChC,IAAIM,KAAK,EAAE;QACT,IAAI,CAAC8D,qBAAqB,CAAC9D,KAAK,CAACC,aAAa,CAAC;MACjD;IACF;EACF,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAoB,oBAAoB,CAACjC,SAAS,CAAC6E,uBAAuB,GAAG,UAASvE,GAAG,EAAE;IACrE,IAAI4G,KAAK,GAAG,IAAI,CAACxD,oBAAoB,CAAC2C,OAAO,CAAC/F,GAAG,CAAC;IAClD,IAAI4G,KAAK,IAAI,CAAC,CAAC,EAAE;MACf;IACF;IAEA,IAAID,OAAO,GACR,IAAI,CAAC3E,IAAI,KAAK,IAAI,CAACA,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACyB,IAAI,CAAC,IAAK5B,QAAQ;;IAEnE;IACA,IAAIyG,mBAAmB,GACnB,IAAI,CAACtE,mBAAmB,CAACwB,IAAI,CAAC,UAASC,IAAI,EAAE;MAC3C,IAAI8C,OAAO,GAAG9C,IAAI,CAACC,OAAO,CAAC1D,aAAa;MACxC;MACA,IAAIuG,OAAO,IAAI9G,GAAG,EAAE;QAClB,OAAO,IAAI;MACb;MACA;MACA,OAAO8G,OAAO,IAAIA,OAAO,IAAIH,OAAO,EAAE;QACpC,IAAIrG,KAAK,GAAGP,eAAe,CAAC+G,OAAO,CAAC;QACpCA,OAAO,GAAGxG,KAAK,IAAIA,KAAK,CAACC,aAAa;QACtC,IAAIuG,OAAO,IAAI9G,GAAG,EAAE;UAClB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd,CAAC,CAAC;IACN,IAAI6G,mBAAmB,EAAE;MACvB;IACF;;IAEA;IACA,IAAIE,WAAW,GAAG,IAAI,CAAC1D,uBAAuB,CAACuD,KAAK,CAAC;IACrD,IAAI,CAACxD,oBAAoB,CAAC4D,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IAC1C,IAAI,CAACvD,uBAAuB,CAAC2D,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;IAC7CG,WAAW,CAAC,CAAC;;IAEb;IACA,IAAI/G,GAAG,IAAI2G,OAAO,EAAE;MAClB,IAAIrG,KAAK,GAAGP,eAAe,CAACC,GAAG,CAAC;MAChC,IAAIM,KAAK,EAAE;QACT,IAAI,CAACiE,uBAAuB,CAACjE,KAAK,CAACC,aAAa,CAAC;MACnD;IACF;EACF,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAoB,oBAAoB,CAACjC,SAAS,CAACiF,0BAA0B,GAAG,YAAW;IACrE,IAAIsC,YAAY,GAAG,IAAI,CAAC5D,uBAAuB,CAACyB,KAAK,CAAC,CAAC,CAAC;IACxD,IAAI,CAAC1B,oBAAoB,CAACoB,MAAM,GAAG,CAAC;IACpC,IAAI,CAACnB,uBAAuB,CAACmB,MAAM,GAAG,CAAC;IACvC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,YAAY,CAACzC,MAAM,EAAEY,CAAC,EAAE,EAAE;MAC5C6B,YAAY,CAAC7B,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;EACAzD,oBAAoB,CAACjC,SAAS,CAACwC,sBAAsB,GAAG,YAAW;IACjE,IAAI,CAAC,IAAI,CAACF,IAAI,IAAIvB,kBAAkB,IAAI,CAACC,eAAe,EAAE;MACxD;MACA;IACF;IAEA,IAAIwG,WAAW,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACrC,IAAIC,QAAQ,GAAGF,WAAW,GAAG,IAAI,CAACG,YAAY,CAAC,CAAC,GAAGnG,YAAY,CAAC,CAAC;IAEjE,IAAI,CAACqB,mBAAmB,CAACmB,OAAO,CAAC,UAASM,IAAI,EAAE;MAC9C,IAAInD,MAAM,GAAGmD,IAAI,CAACC,OAAO;MACzB,IAAI7C,UAAU,GAAGkG,qBAAqB,CAACzG,MAAM,CAAC;MAC9C,IAAI0G,kBAAkB,GAAG,IAAI,CAACC,mBAAmB,CAAC3G,MAAM,CAAC;MACzD,IAAI4G,QAAQ,GAAGzD,IAAI,CAACrD,KAAK;MACzB,IAAIM,gBAAgB,GAAGiG,WAAW,IAAIK,kBAAkB,IACpD,IAAI,CAACG,iCAAiC,CAAC7G,MAAM,EAAEO,UAAU,EAAEgG,QAAQ,CAAC;MAExE,IAAItG,UAAU,GAAG,IAAI;MACrB,IAAI,CAAC,IAAI,CAAC0G,mBAAmB,CAAC3G,MAAM,CAAC,EAAE;QACrCC,UAAU,GAAGI,YAAY,CAAC,CAAC;MAC7B,CAAC,MAAM,IAAI,CAACT,kBAAkB,IAAI,IAAI,CAACuB,IAAI,EAAE;QAC3ClB,UAAU,GAAGsG,QAAQ;MACvB;MAEA,IAAIO,QAAQ,GAAG3D,IAAI,CAACrD,KAAK,GAAG,IAAIlB,yBAAyB,CAAC;QACxDmB,IAAI,EAAEgH,GAAG,CAAC,CAAC;QACX/G,MAAM,EAAEA,MAAM;QACdG,kBAAkB,EAAEI,UAAU;QAC9BN,UAAU,EAAEA,UAAU;QACtBG,gBAAgB,EAAEA;MACpB,CAAC,CAAC;MAEF,IAAI,CAACwG,QAAQ,EAAE;QACb,IAAI,CAACjF,cAAc,CAAC2B,IAAI,CAACwD,QAAQ,CAAC;MACpC,CAAC,MAAM,IAAIT,WAAW,IAAIK,kBAAkB,EAAE;QAC5C;QACA;QACA,IAAI,IAAI,CAACM,oBAAoB,CAACJ,QAAQ,EAAEE,QAAQ,CAAC,EAAE;UACjD,IAAI,CAACnF,cAAc,CAAC2B,IAAI,CAACwD,QAAQ,CAAC;QACpC;MACF,CAAC,MAAM;QACL;QACA;QACA;QACA,IAAIF,QAAQ,IAAIA,QAAQ,CAACtG,cAAc,EAAE;UACvC,IAAI,CAACqB,cAAc,CAAC2B,IAAI,CAACwD,QAAQ,CAAC;QACpC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IAER,IAAI,IAAI,CAACnF,cAAc,CAACgC,MAAM,EAAE;MAC9B,IAAI,CAAClC,SAAS,CAAC,IAAI,CAACsC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;IAC1C;EACF,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAjD,oBAAoB,CAACjC,SAAS,CAACgI,iCAAiC,GAC5D,UAAS7G,MAAM,EAAEO,UAAU,EAAEgG,QAAQ,EAAE;IACzC;IACA,IAAI5H,MAAM,CAACsI,gBAAgB,CAACjH,MAAM,CAAC,CAACkH,OAAO,IAAI,MAAM,EAAE;IAEvD,IAAI9G,gBAAgB,GAAGG,UAAU;IACjC,IAAI4G,MAAM,GAAGC,aAAa,CAACpH,MAAM,CAAC;IAClC,IAAIqH,MAAM,GAAG,KAAK;IAElB,OAAO,CAACA,MAAM,IAAIF,MAAM,EAAE;MACxB,IAAIG,UAAU,GAAG,IAAI;MACrB,IAAIC,mBAAmB,GAAGJ,MAAM,CAAC/F,QAAQ,IAAI,CAAC,GAC1CzC,MAAM,CAACsI,gBAAgB,CAACE,MAAM,CAAC,GAAG,CAAC,CAAC;;MAExC;MACA,IAAII,mBAAmB,CAACL,OAAO,IAAI,MAAM,EAAE,OAAO,IAAI;MAEtD,IAAIC,MAAM,IAAI,IAAI,CAAChG,IAAI,IAAIgG,MAAM,CAAC/F,QAAQ,IAAI,cAAe,CAAC,EAAE;QAC9DiG,MAAM,GAAG,IAAI;QACb,IAAIF,MAAM,IAAI,IAAI,CAAChG,IAAI,IAAIgG,MAAM,IAAI5H,QAAQ,EAAE;UAC7C,IAAIK,kBAAkB,IAAI,CAAC,IAAI,CAACuB,IAAI,EAAE;YACpC,IAAI,CAACtB,eAAe,IAChBA,eAAe,CAACY,KAAK,IAAI,CAAC,IAAIZ,eAAe,CAACa,MAAM,IAAI,CAAC,EAAE;cAC7D;cACAyG,MAAM,GAAG,IAAI;cACbG,UAAU,GAAG,IAAI;cACjBlH,gBAAgB,GAAG,IAAI;YACzB,CAAC,MAAM;cACLkH,UAAU,GAAGzH,eAAe;YAC9B;UACF,CAAC,MAAM;YACLyH,UAAU,GAAGf,QAAQ;UACvB;QACF,CAAC,MAAM;UACL;UACA,IAAI9G,KAAK,GAAG2H,aAAa,CAACD,MAAM,CAAC;UACjC,IAAIK,SAAS,GAAG/H,KAAK,IAAIgH,qBAAqB,CAAChH,KAAK,CAAC;UACrD,IAAIgI,cAAc,GACdhI,KAAK,IACL,IAAI,CAACoH,iCAAiC,CAACpH,KAAK,EAAE+H,SAAS,EAAEjB,QAAQ,CAAC;UACtE,IAAIiB,SAAS,IAAIC,cAAc,EAAE;YAC/BN,MAAM,GAAG1H,KAAK;YACd6H,UAAU,GAAG1E,qBAAqB,CAAC4E,SAAS,EAAEC,cAAc,CAAC;UAC/D,CAAC,MAAM;YACLN,MAAM,GAAG,IAAI;YACb/G,gBAAgB,GAAG,IAAI;UACzB;QACF;MACF,CAAC,MAAM;QACL;QACA;QACA;QACA;QACA,IAAIjB,GAAG,GAAGgI,MAAM,CAACzH,aAAa;QAC9B,IAAIyH,MAAM,IAAIhI,GAAG,CAACuI,IAAI,IAClBP,MAAM,IAAIhI,GAAG,CAACwI,eAAe,IAC7BJ,mBAAmB,CAACK,QAAQ,IAAI,SAAS,EAAE;UAC7CN,UAAU,GAAGb,qBAAqB,CAACU,MAAM,CAAC;QAC5C;MACF;;MAEA;MACA;MACA,IAAIG,UAAU,EAAE;QACdlH,gBAAgB,GAAGyH,uBAAuB,CAACP,UAAU,EAAElH,gBAAgB,CAAC;MAC1E;MACA,IAAI,CAACA,gBAAgB,EAAE;MACvB+G,MAAM,GAAGA,MAAM,IAAIC,aAAa,CAACD,MAAM,CAAC;IAC1C;IACA,OAAO/G,gBAAgB;EACzB,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAU,oBAAoB,CAACjC,SAAS,CAAC2H,YAAY,GAAG,YAAW;IACvD,IAAID,QAAQ;IACZ,IAAI,IAAI,CAACpF,IAAI,IAAI,CAAC2G,KAAK,CAAC,IAAI,CAAC3G,IAAI,CAAC,EAAE;MAClCoF,QAAQ,GAAGE,qBAAqB,CAAC,IAAI,CAACtF,IAAI,CAAC;IAC7C,CAAC,MAAM;MACL;MACA,IAAIhC,GAAG,GAAG2I,KAAK,CAAC,IAAI,CAAC3G,IAAI,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG5B,QAAQ;MACjD,IAAIwI,IAAI,GAAG5I,GAAG,CAACwI,eAAe;MAC9B,IAAID,IAAI,GAAGvI,GAAG,CAACuI,IAAI;MACnBnB,QAAQ,GAAG;QACTyB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAEH,IAAI,CAACI,WAAW,IAAIT,IAAI,CAACS,WAAW;QAC3C1H,KAAK,EAAEsH,IAAI,CAACI,WAAW,IAAIT,IAAI,CAACS,WAAW;QAC3CC,MAAM,EAAEL,IAAI,CAACM,YAAY,IAAIX,IAAI,CAACW,YAAY;QAC9C3H,MAAM,EAAEqH,IAAI,CAACM,YAAY,IAAIX,IAAI,CAACW;MACpC,CAAC;IACH;IACA,OAAO,IAAI,CAACC,uBAAuB,CAAC/B,QAAQ,CAAC;EAC/C,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;EACAzF,oBAAoB,CAACjC,SAAS,CAACyJ,uBAAuB,GAAG,UAASC,IAAI,EAAE;IACtE,IAAI3D,OAAO,GAAG,IAAI,CAAChD,iBAAiB,CAACM,GAAG,CAAC,UAASC,MAAM,EAAEoC,CAAC,EAAE;MAC3D,OAAOpC,MAAM,CAACE,IAAI,IAAI,IAAI,GAAGF,MAAM,CAACC,KAAK,GACrCD,MAAM,CAACC,KAAK,IAAImC,CAAC,GAAG,CAAC,GAAGgE,IAAI,CAAC9H,KAAK,GAAG8H,IAAI,CAAC7H,MAAM,CAAC,GAAG,GAAG;IAC7D,CAAC,CAAC;IACF,IAAI8H,OAAO,GAAG;MACZR,GAAG,EAAEO,IAAI,CAACP,GAAG,GAAGpD,OAAO,CAAC,CAAC,CAAC;MAC1BsD,KAAK,EAAEK,IAAI,CAACL,KAAK,GAAGtD,OAAO,CAAC,CAAC,CAAC;MAC9BwD,MAAM,EAAEG,IAAI,CAACH,MAAM,GAAGxD,OAAO,CAAC,CAAC,CAAC;MAChCqD,IAAI,EAAEM,IAAI,CAACN,IAAI,GAAGrD,OAAO,CAAC,CAAC;IAC7B,CAAC;IACD4D,OAAO,CAAC/H,KAAK,GAAG+H,OAAO,CAACN,KAAK,GAAGM,OAAO,CAACP,IAAI;IAC5CO,OAAO,CAAC9H,MAAM,GAAG8H,OAAO,CAACJ,MAAM,GAAGI,OAAO,CAACR,GAAG;IAE7C,OAAOQ,OAAO;EAChB,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA1H,oBAAoB,CAACjC,SAAS,CAACmI,oBAAoB,GAC/C,UAASJ,QAAQ,EAAEE,QAAQ,EAAE;IAE/B;IACA;IACA,IAAI2B,QAAQ,GAAG7B,QAAQ,IAAIA,QAAQ,CAACtG,cAAc,GAC9CsG,QAAQ,CAAC3H,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,IAAIyJ,QAAQ,GAAG5B,QAAQ,CAACxG,cAAc,GAClCwG,QAAQ,CAAC7H,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;;IAExC;IACA,IAAIwJ,QAAQ,KAAKC,QAAQ,EAAE;IAE3B,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACxC,UAAU,CAAC4B,MAAM,EAAEY,CAAC,EAAE,EAAE;MAC/C,IAAItC,SAAS,GAAG,IAAI,CAACF,UAAU,CAACwC,CAAC,CAAC;;MAElC;MACA;MACA,IAAItC,SAAS,IAAIwG,QAAQ,IAAIxG,SAAS,IAAIyG,QAAQ,IAC9CzG,SAAS,GAAGwG,QAAQ,KAAKxG,SAAS,GAAGyG,QAAQ,EAAE;QACjD,OAAO,IAAI;MACb;IACF;EACF,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACA5H,oBAAoB,CAACjC,SAAS,CAACyH,YAAY,GAAG,YAAW;IACvD,OAAO,CAAC,IAAI,CAACnF,IAAI,IAAIwH,YAAY,CAACpJ,QAAQ,EAAE,IAAI,CAAC4B,IAAI,CAAC;EACxD,CAAC;;EAGD;AACA;AACA;AACA;AACA;AACA;EACAL,oBAAoB,CAACjC,SAAS,CAAC8H,mBAAmB,GAAG,UAAS3G,MAAM,EAAE;IACpE,IAAI8F,OAAO,GACR,IAAI,CAAC3E,IAAI,KAAK,IAAI,CAACA,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACyB,IAAI,CAAC,IAAK5B,QAAQ;IACnE,OACEoJ,YAAY,CAAC7C,OAAO,EAAE9F,MAAM,CAAC,KAC5B,CAAC,IAAI,CAACmB,IAAI,IAAI2E,OAAO,IAAI9F,MAAM,CAACN,aAAa,CAAC;EAEnD,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACAoB,oBAAoB,CAACjC,SAAS,CAACwE,iBAAiB,GAAG,YAAW;IAC5D,IAAI1D,QAAQ,CAACuF,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;MAC9BvF,QAAQ,CAAC2D,IAAI,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAGD;AACA;AACA;AACA;EACAxC,oBAAoB,CAACjC,SAAS,CAAC+E,mBAAmB,GAAG,YAAW;IAC9D,IAAImC,KAAK,GAAGpG,QAAQ,CAACuF,OAAO,CAAC,IAAI,CAAC;IAClC,IAAIa,KAAK,IAAI,CAAC,CAAC,EAAEpG,QAAQ,CAACwG,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;EAC5C,CAAC;;EAGD;AACA;AACA;AACA;AACA;EACA,SAASgB,GAAGA,CAAA,EAAG;IACb,OAAOpI,MAAM,CAACiK,WAAW,IAAIA,WAAW,CAAC7B,GAAG,IAAI6B,WAAW,CAAC7B,GAAG,CAAC,CAAC;EACnE;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASzF,QAAQA,CAACuH,EAAE,EAAEC,OAAO,EAAE;IAC7B,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAO,YAAY;MACjB,IAAI,CAACA,KAAK,EAAE;QACVA,KAAK,GAAGC,UAAU,CAAC,YAAW;UAC5BH,EAAE,CAAC,CAAC;UACJE,KAAK,GAAG,IAAI;QACd,CAAC,EAAED,OAAO,CAAC;MACb;IACF,CAAC;EACH;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASxD,QAAQA,CAAC2D,IAAI,EAAEC,KAAK,EAAEL,EAAE,EAAEM,cAAc,EAAE;IACjD,IAAI,OAAOF,IAAI,CAACG,gBAAgB,IAAI,UAAU,EAAE;MAC9CH,IAAI,CAACG,gBAAgB,CAACF,KAAK,EAAEL,EAAE,EAAEM,cAAc,IAAI,KAAK,CAAC;IAC3D,CAAC,MACI,IAAI,OAAOF,IAAI,CAACI,WAAW,IAAI,UAAU,EAAE;MAC9CJ,IAAI,CAACI,WAAW,CAAC,IAAI,GAAGH,KAAK,EAAEL,EAAE,CAAC;IACpC;EACF;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAAShD,WAAWA,CAACoD,IAAI,EAAEC,KAAK,EAAEL,EAAE,EAAEM,cAAc,EAAE;IACpD,IAAI,OAAOF,IAAI,CAACK,mBAAmB,IAAI,UAAU,EAAE;MACjDL,IAAI,CAACK,mBAAmB,CAACJ,KAAK,EAAEL,EAAE,EAAEM,cAAc,IAAI,KAAK,CAAC;IAC9D,CAAC,MACI,IAAI,OAAOF,IAAI,CAACM,WAAW,IAAI,UAAU,EAAE;MAC9CN,IAAI,CAACM,WAAW,CAAC,IAAI,GAAGL,KAAK,EAAEL,EAAE,CAAC;IACpC;EACF;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAAShB,uBAAuBA,CAAC2B,KAAK,EAAEC,KAAK,EAAE;IAC7C,IAAIzB,GAAG,GAAG0B,IAAI,CAACC,GAAG,CAACH,KAAK,CAACxB,GAAG,EAAEyB,KAAK,CAACzB,GAAG,CAAC;IACxC,IAAII,MAAM,GAAGsB,IAAI,CAACE,GAAG,CAACJ,KAAK,CAACpB,MAAM,EAAEqB,KAAK,CAACrB,MAAM,CAAC;IACjD,IAAIH,IAAI,GAAGyB,IAAI,CAACC,GAAG,CAACH,KAAK,CAACvB,IAAI,EAAEwB,KAAK,CAACxB,IAAI,CAAC;IAC3C,IAAIC,KAAK,GAAGwB,IAAI,CAACE,GAAG,CAACJ,KAAK,CAACtB,KAAK,EAAEuB,KAAK,CAACvB,KAAK,CAAC;IAC9C,IAAIzH,KAAK,GAAGyH,KAAK,GAAGD,IAAI;IACxB,IAAIvH,MAAM,GAAG0H,MAAM,GAAGJ,GAAG;IAEzB,OAAQvH,KAAK,IAAI,CAAC,IAAIC,MAAM,IAAI,CAAC,IAAK;MACpCsH,GAAG,EAAEA,GAAG;MACRI,MAAM,EAAEA,MAAM;MACdH,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAEA,KAAK;MACZzH,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV,CAAC,IAAI,IAAI;EACX;;EAGA;AACA;AACA;AACA;AACA;EACA,SAAS+F,qBAAqBA,CAACoD,EAAE,EAAE;IACjC,IAAItB,IAAI;IAER,IAAI;MACFA,IAAI,GAAGsB,EAAE,CAACpD,qBAAqB,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOqD,GAAG,EAAE;MACZ;MACA;IAAA;IAGF,IAAI,CAACvB,IAAI,EAAE,OAAOlI,YAAY,CAAC,CAAC;;IAEhC;IACA,IAAI,EAAEkI,IAAI,CAAC9H,KAAK,IAAI8H,IAAI,CAAC7H,MAAM,CAAC,EAAE;MAChC6H,IAAI,GAAG;QACLP,GAAG,EAAEO,IAAI,CAACP,GAAG;QACbE,KAAK,EAAEK,IAAI,CAACL,KAAK;QACjBE,MAAM,EAAEG,IAAI,CAACH,MAAM;QACnBH,IAAI,EAAEM,IAAI,CAACN,IAAI;QACfxH,KAAK,EAAE8H,IAAI,CAACL,KAAK,GAAGK,IAAI,CAACN,IAAI;QAC7BvH,MAAM,EAAE6H,IAAI,CAACH,MAAM,GAAGG,IAAI,CAACP;MAC7B,CAAC;IACH;IACA,OAAOO,IAAI;EACb;;EAGA;AACA;AACA;AACA;AACA;EACA,SAASlI,YAAYA,CAAA,EAAG;IACtB,OAAO;MACL2H,GAAG,EAAE,CAAC;MACNI,MAAM,EAAE,CAAC;MACTH,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRzH,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASR,aAAaA,CAACqI,IAAI,EAAE;IAC3B;IACA,IAAI,CAACA,IAAI,IAAI,GAAG,IAAIA,IAAI,EAAE;MACxB,OAAOA,IAAI;IACb;IACA;IACA;IACA;IACA;IACA,OAAO;MACLP,GAAG,EAAEO,IAAI,CAACP,GAAG;MACb+B,CAAC,EAAExB,IAAI,CAACP,GAAG;MACXI,MAAM,EAAEG,IAAI,CAACH,MAAM;MACnBH,IAAI,EAAEM,IAAI,CAACN,IAAI;MACf+B,CAAC,EAAEzB,IAAI,CAACN,IAAI;MACZC,KAAK,EAAEK,IAAI,CAACL,KAAK;MACjBzH,KAAK,EAAE8H,IAAI,CAAC9H,KAAK;MACjBC,MAAM,EAAE6H,IAAI,CAAC7H;IACf,CAAC;EACH;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASkC,qBAAqBA,CAACqH,kBAAkB,EAAEC,sBAAsB,EAAE;IACzE,IAAIlC,GAAG,GAAGkC,sBAAsB,CAAClC,GAAG,GAAGiC,kBAAkB,CAACjC,GAAG;IAC7D,IAAIC,IAAI,GAAGiC,sBAAsB,CAACjC,IAAI,GAAGgC,kBAAkB,CAAChC,IAAI;IAChE,OAAO;MACLD,GAAG,EAAEA,GAAG;MACRC,IAAI,EAAEA,IAAI;MACVvH,MAAM,EAAEwJ,sBAAsB,CAACxJ,MAAM;MACrCD,KAAK,EAAEyJ,sBAAsB,CAACzJ,KAAK;MACnC2H,MAAM,EAAEJ,GAAG,GAAGkC,sBAAsB,CAACxJ,MAAM;MAC3CwH,KAAK,EAAED,IAAI,GAAGiC,sBAAsB,CAACzJ;IACvC,CAAC;EACH;;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASkI,YAAYA,CAACxB,MAAM,EAAEgD,KAAK,EAAE;IACnC,IAAIlB,IAAI,GAAGkB,KAAK;IAChB,OAAOlB,IAAI,EAAE;MACX,IAAIA,IAAI,IAAI9B,MAAM,EAAE,OAAO,IAAI;MAE/B8B,IAAI,GAAG7B,aAAa,CAAC6B,IAAI,CAAC;IAC5B;IACA,OAAO,KAAK;EACd;;EAGA;AACA;AACA;AACA;AACA;AACA;EACA,SAAS7B,aAAaA,CAAC6B,IAAI,EAAE;IAC3B,IAAI9B,MAAM,GAAG8B,IAAI,CAACmB,UAAU;IAE5B,IAAInB,IAAI,CAAC7H,QAAQ,IAAI,cAAe,CAAC,IAAI6H,IAAI,IAAI1J,QAAQ,EAAE;MACzD;MACA,OAAOL,eAAe,CAAC+J,IAAI,CAAC;IAC9B;;IAEA;IACA,IAAI9B,MAAM,IAAIA,MAAM,CAACkD,YAAY,EAAE;MACjClD,MAAM,GAAGA,MAAM,CAACkD,YAAY,CAACD,UAAU;IACzC;IAEA,IAAIjD,MAAM,IAAIA,MAAM,CAAC/F,QAAQ,IAAI,EAAE,IAAI+F,MAAM,CAACmD,IAAI,EAAE;MAClD;MACA,OAAOnD,MAAM,CAACmD,IAAI;IACpB;IAEA,OAAOnD,MAAM;EACf;;EAEA;AACA;AACA;AACA;AACA;EACA,SAASW,KAAKA,CAACmB,IAAI,EAAE;IACnB,OAAOA,IAAI,IAAIA,IAAI,CAAC7H,QAAQ,KAAK,CAAC;EACpC;;EAGA;EACAzC,MAAM,CAACmC,oBAAoB,GAAGA,oBAAoB;EAClDnC,MAAM,CAACC,yBAAyB,GAAGA,yBAAyB;AAE5D,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}