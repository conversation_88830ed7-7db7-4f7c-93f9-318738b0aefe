{"ast": null, "code": "import { __assign, __read, __rest } from \"tslib\";\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = __read(useState(function () {\n      var cookieValue = Cookies.get(cookieKey);\n      if (isString(cookieValue)) return cookieValue;\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n    var _a = __assign(__assign({}, options), newOptions),\n      defaultValue = _a.defaultValue,\n      restOptions = __rest(_a, [\"defaultValue\"]);\n    var value = isFunction(newValue) ? newValue(state) : newValue;\n    setState(value);\n    if (value === undefined) {\n      Cookies.remove(cookieKey);\n    } else {\n      Cookies.set(cookieKey, value, restOptions);\n    }\n  });\n  return [state, updateState];\n}\nexport default useCookieState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}