{"ast": null, "code": "import React, { useEffect, useRef } from 'react';\nimport { useInViewport } from 'ahooks';\nexport const LazyDetector = props => {\n  const ref = useRef(null);\n  const [inViewport] = useInViewport(ref);\n  useEffect(() => {\n    if (inViewport) {\n      props.onActive();\n    }\n  }, [inViewport]);\n  return React.createElement(\"div\", {\n    ref: ref\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}