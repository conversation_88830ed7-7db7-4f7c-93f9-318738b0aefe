{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\nvar checkIfAllInShadow = function (targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) return false;\n    if (targetElement.getRootNode() instanceof ShadowRoot) return true;\n    return false;\n  });\n};\nvar getShadow = function (node) {\n  if (!node) {\n    return document;\n  }\n  return node.getRootNode();\n};\nvar getDocumentOrShadow = function (target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n  var targets = Array.isArray(target) ? target : [target];\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n  return document;\n};\nexport default getDocumentOrShadow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}