{"ast": null, "code": "import \"./infinite-scroll.css\";\nimport { InfiniteScroll } from './infinite-scroll';\nexport default InfiniteScroll;", "map": {"version": 3, "names": ["InfiniteScroll"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/infinite-scroll/index.js"], "sourcesContent": ["import \"./infinite-scroll.css\";\nimport { InfiniteScroll } from './infinite-scroll';\nexport default InfiniteScroll;"], "mappings": "AAAA,OAAO,uBAAuB;AAC9B,SAASA,cAAc,QAAQ,mBAAmB;AAClD,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}