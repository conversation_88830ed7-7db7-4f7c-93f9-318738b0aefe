{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider`;\nconst Ticks = ({\n  points,\n  max,\n  min,\n  upperBound,\n  lowerBound\n}) => {\n  const range = max - min;\n  const elements = points.map(point => {\n    const offset = `${Math.abs(point - min) / range * 100}%`;\n    const isActived = point <= upperBound && point >= lowerBound;\n    const style = {\n      left: offset\n    };\n    const pointClassName = classNames({\n      [`${classPrefix}-tick`]: true,\n      [`${classPrefix}-tick-active`]: isActived\n    });\n    return React.createElement(\"span\", {\n      className: pointClassName,\n      style: style,\n      key: point\n    });\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-ticks`\n  }, elements);\n};\nexport default Ticks;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}