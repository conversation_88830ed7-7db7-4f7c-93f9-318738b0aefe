{"ast": null, "code": "import dayjs from 'dayjs';\nimport quarterOfYear from 'dayjs/plugin/quarterOfYear';\ndayjs.extend(quarterOfYear);\nconst precisionRankRecord = {\n  year: 0,\n  quarter: 1\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minQuarter = minDay.quarter();\n  const maxQuarter = maxDay.quarter();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.quarter) {\n    const lower = isInMinYear ? minQuarter : 1;\n    const upper = isInMaxYear ? maxQuarter : 4;\n    const quarters = generateColumn(lower, upper, 'quarter');\n    ret.push(quarters.map(v => ({\n      label: renderLabel('quarter', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.year().toString(), day.quarter().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const quarterString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const day = dayjs().year(parseInt(yearString)).quarter(parseInt(quarterString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}