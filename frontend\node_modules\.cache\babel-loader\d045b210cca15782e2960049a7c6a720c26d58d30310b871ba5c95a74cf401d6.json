{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { STEP_ACTIVATED, STEP_ACTIVE, STEP_NONE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useNextFrame from \"./useNextFrame\";\nvar FULL_STEP_QUEUE = [STEP_PREPARE, STEP_START, STEP_ACTIVE, STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [STEP_PREPARE, STEP_PREPARED];\n\n/** Skip current step */\nexport var SkipStep = false;\n/** Current step should be update in */\nexport var DoStep = true;\nexport function isActive(step) {\n  return step === STEP_ACTIVE || step === STEP_ACTIVATED;\n}\nexport default (function (status, prepareOnly, callback) {\n  var _useState = useState(STEP_NONE),\n    _useState2 = _slicedToArray(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = useNextFrame(),\n    _useNextFrame2 = _slicedToArray(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  useIsomorphicLayoutEffect(function () {\n    if (step !== STEP_NONE && step !== STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}