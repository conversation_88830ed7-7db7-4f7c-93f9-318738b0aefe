{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useInterval = function (fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (options.immediate) {\n      timerCallback();\n    }\n    timerRef.current = setInterval(timerCallback, delay);\n    return clear;\n  }, [delay, options.immediate]);\n  return clear;\n};\nexport default useInterval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}