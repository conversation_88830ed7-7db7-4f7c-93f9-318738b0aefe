{"ast": null, "code": "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport useMount from '../useMount';\nimport { isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar useDrag = function (data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  var imageElementRef = useRef();\n  var dragImage = optionsRef.current.dragImage;\n  useMount(function () {\n    if (dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) {\n      var image = dragImage.image;\n      if (isString(image)) {\n        var imageElement = new Image();\n        imageElement.src = image;\n        imageElementRef.current = imageElement;\n      } else {\n        imageElementRef.current = image;\n      }\n    }\n  });\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onDragStart = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n      if ((dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) && imageElementRef.current) {\n        var _c = dragImage.offsetX,\n          offsetX = _c === void 0 ? 0 : _c,\n          _d = dragImage.offsetY,\n          offsetY = _d === void 0 ? 0 : _d;\n        event.dataTransfer.setDragImage(imageElementRef.current, offsetX, offsetY);\n      }\n    };\n    var onDragEnd = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\nexport default useDrag;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}