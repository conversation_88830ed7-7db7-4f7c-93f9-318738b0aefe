{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\nvar useDrop = function (target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  // https://stackoverflow.com/a/26459269\n  var dragEnterTarget = useRef();\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onData = function (dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n        try {\n          data = JSON.parse(dom);\n        } catch (e) {\n          data = dom;\n        }\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n    var onDragEnter = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragOver = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragLeave = function (event) {\n      var _a, _b;\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n    var onDrop = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onPaste = function (event) {\n      var _a, _b;\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\nexport default useDrop;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}