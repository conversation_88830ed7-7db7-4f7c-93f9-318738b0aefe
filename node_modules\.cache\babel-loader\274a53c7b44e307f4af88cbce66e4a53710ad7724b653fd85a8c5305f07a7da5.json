{"ast": null, "code": "import * as React from \"react\";\nfunction CloseCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleOutline-CloseCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M18.3750018,16.1171551 L23.9998449,21.7419127 L23.9998449,21.7419127 L29.624688,16.1171551 C29.6997023,16.0421419 29.801443,16 29.9075285,16 L33.0200651,16 C33.240979,16 33.4200651,16.1790861 33.4200651,16.4 C33.4200651,16.5060962 33.3779147,16.6078461 33.3028882,16.6828623 L26.1208449,23.8639127 L26.1208449,23.8639127 L33.5750057,31.3171396 C33.7312252,31.4733395 33.731241,31.7266055 33.5750411,31.882825 C33.5000251,31.9578504 33.398276,32 33.2921807,32 L30.1796562,32 C30.0735583,32 29.9718069,31.9578483 29.8967904,31.8828197 L23.9998449,25.9849127 L23.9998449,25.9849127 L18.1011057,31.8828617 C18.0260927,31.9578646 17.9243593,32 17.8182819,32 L14.7076208,32 C14.4867069,32 14.3076208,31.8209139 14.3076208,31.6 C14.3076208,31.4939151 14.3497622,31.3921749 14.4247747,31.3171607 L21.8778449,23.8639127 L21.8778449,23.8639127 L14.6966968,16.6828443 C14.5404862,16.5266354 14.5404848,16.2733694 14.6966936,16.1171589 C14.7717083,16.0421433 14.8734505,16 14.9795379,16 L18.0921612,16 C18.1982468,16 18.2999874,16.0421419 18.3750018,16.1171551 Z\",\n    id: \"CloseCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CloseCircleOutline;", "map": {"version": 3, "names": ["React", "CloseCircleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/CloseCircleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CloseCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleOutline-CloseCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CloseCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CloseCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M18.3750018,16.1171551 L23.9998449,21.7419127 L23.9998449,21.7419127 L29.624688,16.1171551 C29.6997023,16.0421419 29.801443,16 29.9075285,16 L33.0200651,16 C33.240979,16 33.4200651,16.1790861 33.4200651,16.4 C33.4200651,16.5060962 33.3779147,16.6078461 33.3028882,16.6828623 L26.1208449,23.8639127 L26.1208449,23.8639127 L33.5750057,31.3171396 C33.7312252,31.4733395 33.731241,31.7266055 33.5750411,31.882825 C33.5000251,31.9578504 33.398276,32 33.2921807,32 L30.1796562,32 C30.0735583,32 29.9718069,31.9578483 29.8967904,31.8828197 L23.9998449,25.9849127 L23.9998449,25.9849127 L18.1011057,31.8828617 C18.0260927,31.9578646 17.9243593,32 17.8182819,32 L14.7076208,32 C14.4867069,32 14.3076208,31.8209139 14.3076208,31.6 C14.3076208,31.4939151 14.3497622,31.3921749 14.4247747,31.3171607 L21.8778449,23.8639127 L21.8778449,23.8639127 L14.6966968,16.6828443 C14.5404862,16.5266354 14.5404848,16.2733694 14.6966936,16.1171589 C14.7717083,16.0421433 14.8734505,16 14.9795379,16 L18.0921612,16 C18.1982468,16 18.2999874,16.0421419 18.3750018,16.1171551 Z\",\n    id: \"CloseCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CloseCircleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uCAAuC;IAC3CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,wzCAAwzC;IAC3zCR,EAAE,EAAE,iCAAiC;IACrCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}