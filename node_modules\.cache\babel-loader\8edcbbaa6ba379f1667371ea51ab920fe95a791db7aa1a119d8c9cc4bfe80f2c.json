{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { useIsomorphicLayoutEffect, useSize, useUnmount } from 'ahooks';\nimport { AddOutline, CloseOutline } from 'antd-mobile-icons';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { measureCSSLength } from '../../utils/measure-css-length';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Grid from '../grid';\nimport ImageViewer from '../image-viewer';\nimport Space from '../space';\nimport PreviewItem from './preview-item';\nconst classPrefix = `adm-image-uploader`;\nconst defaultProps = {\n  disableUpload: false,\n  deletable: true,\n  deleteIcon: React.createElement(CloseOutline, {\n    className: `${classPrefix}-cell-delete-icon`\n  }),\n  showUpload: true,\n  multiple: false,\n  maxCount: 0,\n  defaultValue: [],\n  accept: 'image/*',\n  preview: true,\n  showFailed: true,\n  imageFit: 'cover'\n};\nexport const ImageUploader = forwardRef((p, ref) => {\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, p);\n  const {\n    columns\n  } = props;\n  const [value, setValue] = usePropsValue(props);\n  const [tasks, setTasks] = useState([]);\n  const containerRef = useRef(null);\n  const containerSize = useSize(containerRef);\n  const gapMeasureRef = useRef(null);\n  const [cellSize, setCellSize] = useState(80);\n  const inputRef = useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const gapMeasure = gapMeasureRef.current;\n    if (columns && containerSize && gapMeasure) {\n      const width = containerSize.width;\n      const gap = measureCSSLength(window.getComputedStyle(gapMeasure).getPropertyValue('height'));\n      setCellSize((width - gap * (columns - 1)) / columns);\n    }\n  }, [containerSize === null || containerSize === void 0 ? void 0 : containerSize.width]);\n  const style = {\n    '--cell-size': cellSize + 'px'\n  };\n  useIsomorphicLayoutEffect(() => {\n    setTasks(prev => prev.filter(task => {\n      if (task.url === undefined) return true;\n      return !value.some(fileItem => fileItem.url === task.url);\n    }));\n  }, [value]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    (_a = props.onUploadQueueChange) === null || _a === void 0 ? void 0 : _a.call(props, tasks.map(item => ({\n      id: item.id,\n      status: item.status\n    })));\n  }, [tasks]);\n  const idCountRef = useRef(0);\n  const {\n    maxCount,\n    onPreview,\n    renderItem\n  } = props;\n  function processFile(file, fileList) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const {\n        beforeUpload\n      } = props;\n      let transformedFile = file;\n      transformedFile = yield beforeUpload === null || beforeUpload === void 0 ? void 0 : beforeUpload(file, fileList);\n      return transformedFile;\n    });\n  }\n  function getFinalTasks(tasks) {\n    return props.showFailed ? tasks : tasks.filter(task => task.status !== 'fail');\n  }\n  function onChange(e) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      e.persist();\n      const {\n        files: rawFiles\n      } = e.target;\n      if (!rawFiles) return;\n      let files = [].slice.call(rawFiles);\n      e.target.value = ''; // HACK: fix the same file doesn't trigger onChange\n      if (props.beforeUpload) {\n        const postFiles = files.map(file => processFile(file, files));\n        yield Promise.all(postFiles).then(filesList => {\n          files = filesList.filter(Boolean);\n        });\n      }\n      if (files.length === 0) {\n        return;\n      }\n      if (maxCount > 0) {\n        const exceed = value.length + files.length - maxCount;\n        if (exceed > 0) {\n          files = files.slice(0, files.length - exceed);\n          (_a = props.onCountExceed) === null || _a === void 0 ? void 0 : _a.call(props, exceed);\n        }\n      }\n      const newTasks = files.map(file => ({\n        id: idCountRef.current++,\n        status: 'pending',\n        file\n      }));\n      setTasks(prev => [...getFinalTasks(prev), ...newTasks]);\n      const newVal = [];\n      yield Promise.all(newTasks.map((currentTask, index) => __awaiter(this, void 0, void 0, function* () {\n        try {\n          const result = yield props.upload(currentTask.file);\n          newVal[index] = result;\n          setTasks(prev => {\n            return prev.map(task => {\n              if (task.id === currentTask.id) {\n                return Object.assign(Object.assign({}, task), {\n                  status: 'success',\n                  url: result.url\n                });\n              }\n              return task;\n            });\n          });\n        } catch (e) {\n          setTasks(prev => {\n            return prev.map(task => {\n              if (task.id === currentTask.id) {\n                return Object.assign(Object.assign({}, task), {\n                  status: 'fail'\n                });\n              }\n              return task;\n            });\n          });\n          console.error(e);\n        }\n      })));\n      setValue(prev => prev.concat(newVal).filter(Boolean));\n    });\n  }\n  const imageViewerHandlerRef = useRef(null);\n  function previewImage(index) {\n    imageViewerHandlerRef.current = ImageViewer.Multi.show({\n      images: value.map(fileItem => fileItem.url),\n      defaultIndex: index,\n      onClose: () => {\n        imageViewerHandlerRef.current = null;\n      }\n    });\n  }\n  useUnmount(() => {\n    var _a;\n    (_a = imageViewerHandlerRef.current) === null || _a === void 0 ? void 0 : _a.close();\n  });\n  const finalTasks = getFinalTasks(tasks);\n  const showUpload = props.showUpload && (maxCount === 0 || value.length + finalTasks.length < maxCount);\n  const renderImages = () => {\n    return value.map((fileItem, index) => {\n      var _a, _b;\n      const originNode = React.createElement(PreviewItem, {\n        key: (_a = fileItem.key) !== null && _a !== void 0 ? _a : index,\n        url: (_b = fileItem.thumbnailUrl) !== null && _b !== void 0 ? _b : fileItem.url,\n        deletable: props.deletable,\n        deleteIcon: props.deleteIcon,\n        imageFit: props.imageFit,\n        onClick: () => {\n          if (props.preview) {\n            previewImage(index);\n          }\n          onPreview && onPreview(index, fileItem);\n        },\n        onDelete: () => __awaiter(void 0, void 0, void 0, function* () {\n          var _c;\n          const canDelete = yield (_c = props.onDelete) === null || _c === void 0 ? void 0 : _c.call(props, fileItem);\n          if (canDelete === false) return;\n          setValue(value.filter((x, i) => i !== index));\n        })\n      });\n      return renderItem ? renderItem(originNode, fileItem, value) : originNode;\n    });\n  };\n  const contentNode = React.createElement(React.Fragment, null, renderImages(), tasks.map(task => {\n    if (!props.showFailed && task.status === 'fail') {\n      return null;\n    }\n    return React.createElement(PreviewItem, {\n      key: task.id,\n      file: task.file,\n      deletable: task.status !== 'pending',\n      deleteIcon: props.deleteIcon,\n      status: task.status,\n      imageFit: props.imageFit,\n      onDelete: () => {\n        setTasks(tasks.filter(x => x.id !== task.id));\n      }\n    });\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-upload-button-wrap`,\n    style: showUpload ? undefined : {\n      display: 'none'\n    }\n  }, props.children || React.createElement(\"span\", {\n    className: `${classPrefix}-cell ${classPrefix}-upload-button`,\n    role: 'button',\n    \"aria-label\": locale.ImageUploader.upload\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-upload-button-icon`\n  }, React.createElement(AddOutline, null))), !props.disableUpload && React.createElement(\"input\", {\n    \"aria-label\": locale.ImageUploader.upload,\n    ref: inputRef,\n    capture: props.capture,\n    accept: props.accept,\n    multiple: props.multiple,\n    type: 'file',\n    className: `${classPrefix}-input`,\n    onChange: onChange\n  })));\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return inputRef.current;\n    }\n  }));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: containerRef\n  }, columns ? React.createElement(Grid, {\n    className: `${classPrefix}-grid`,\n    columns: columns,\n    style: style\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-gap-measure`,\n    ref: gapMeasureRef\n  }), contentNode.props.children) : React.createElement(Space, {\n    className: `${classPrefix}-space`,\n    wrap: true,\n    block: true\n  }, contentNode.props.children)));\n});", "map": {"version": 3, "names": ["__awaiter", "useIsomorphicLayoutEffect", "useSize", "useUnmount", "AddOutline", "CloseOutline", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "measureCSSLength", "withNativeProps", "usePropsValue", "mergeProps", "useConfig", "Grid", "ImageViewer", "Space", "PreviewItem", "classPrefix", "defaultProps", "disableUpload", "deletable", "deleteIcon", "createElement", "className", "showUpload", "multiple", "maxCount", "defaultValue", "accept", "preview", "showFailed", "imageFit", "ImageUploader", "p", "ref", "locale", "props", "columns", "value", "setValue", "tasks", "setTasks", "containerRef", "containerSize", "gapMeasureRef", "cellSize", "setCellSize", "inputRef", "gapMeasure", "current", "width", "gap", "window", "getComputedStyle", "getPropertyValue", "style", "prev", "filter", "task", "url", "undefined", "some", "fileItem", "_a", "onUploadQueueChange", "call", "map", "item", "id", "status", "idCountRef", "onPreview", "renderItem", "processFile", "file", "fileList", "beforeUpload", "transformedFile", "getFinalTasks", "onChange", "e", "persist", "files", "rawFiles", "target", "slice", "postFiles", "Promise", "all", "then", "filesList", "Boolean", "length", "exceed", "onCountExceed", "newTasks", "newVal", "currentTask", "index", "result", "upload", "Object", "assign", "console", "error", "concat", "imageViewerHandlerRef", "previewImage", "Multi", "show", "images", "defaultIndex", "onClose", "close", "finalTasks", "renderImages", "_b", "originNode", "key", "thumbnailUrl", "onClick", "onDelete", "_c", "canDelete", "x", "i", "contentNode", "Fragment", "display", "children", "role", "capture", "type", "nativeElement", "wrap", "block"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/image-uploader/image-uploader.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport { useIsomorphicLayoutEffect, useSize, useUnmount } from 'ahooks';\nimport { AddOutline, CloseOutline } from 'antd-mobile-icons';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { measureCSSLength } from '../../utils/measure-css-length';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport Grid from '../grid';\nimport ImageViewer from '../image-viewer';\nimport Space from '../space';\nimport PreviewItem from './preview-item';\nconst classPrefix = `adm-image-uploader`;\nconst defaultProps = {\n  disableUpload: false,\n  deletable: true,\n  deleteIcon: React.createElement(CloseOutline, {\n    className: `${classPrefix}-cell-delete-icon`\n  }),\n  showUpload: true,\n  multiple: false,\n  maxCount: 0,\n  defaultValue: [],\n  accept: 'image/*',\n  preview: true,\n  showFailed: true,\n  imageFit: 'cover'\n};\nexport const ImageUploader = forwardRef((p, ref) => {\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, p);\n  const {\n    columns\n  } = props;\n  const [value, setValue] = usePropsValue(props);\n  const [tasks, setTasks] = useState([]);\n  const containerRef = useRef(null);\n  const containerSize = useSize(containerRef);\n  const gapMeasureRef = useRef(null);\n  const [cellSize, setCellSize] = useState(80);\n  const inputRef = useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const gapMeasure = gapMeasureRef.current;\n    if (columns && containerSize && gapMeasure) {\n      const width = containerSize.width;\n      const gap = measureCSSLength(window.getComputedStyle(gapMeasure).getPropertyValue('height'));\n      setCellSize((width - gap * (columns - 1)) / columns);\n    }\n  }, [containerSize === null || containerSize === void 0 ? void 0 : containerSize.width]);\n  const style = {\n    '--cell-size': cellSize + 'px'\n  };\n  useIsomorphicLayoutEffect(() => {\n    setTasks(prev => prev.filter(task => {\n      if (task.url === undefined) return true;\n      return !value.some(fileItem => fileItem.url === task.url);\n    }));\n  }, [value]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    (_a = props.onUploadQueueChange) === null || _a === void 0 ? void 0 : _a.call(props, tasks.map(item => ({\n      id: item.id,\n      status: item.status\n    })));\n  }, [tasks]);\n  const idCountRef = useRef(0);\n  const {\n    maxCount,\n    onPreview,\n    renderItem\n  } = props;\n  function processFile(file, fileList) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const {\n        beforeUpload\n      } = props;\n      let transformedFile = file;\n      transformedFile = yield beforeUpload === null || beforeUpload === void 0 ? void 0 : beforeUpload(file, fileList);\n      return transformedFile;\n    });\n  }\n  function getFinalTasks(tasks) {\n    return props.showFailed ? tasks : tasks.filter(task => task.status !== 'fail');\n  }\n  function onChange(e) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      e.persist();\n      const {\n        files: rawFiles\n      } = e.target;\n      if (!rawFiles) return;\n      let files = [].slice.call(rawFiles);\n      e.target.value = ''; // HACK: fix the same file doesn't trigger onChange\n      if (props.beforeUpload) {\n        const postFiles = files.map(file => processFile(file, files));\n        yield Promise.all(postFiles).then(filesList => {\n          files = filesList.filter(Boolean);\n        });\n      }\n      if (files.length === 0) {\n        return;\n      }\n      if (maxCount > 0) {\n        const exceed = value.length + files.length - maxCount;\n        if (exceed > 0) {\n          files = files.slice(0, files.length - exceed);\n          (_a = props.onCountExceed) === null || _a === void 0 ? void 0 : _a.call(props, exceed);\n        }\n      }\n      const newTasks = files.map(file => ({\n        id: idCountRef.current++,\n        status: 'pending',\n        file\n      }));\n      setTasks(prev => [...getFinalTasks(prev), ...newTasks]);\n      const newVal = [];\n      yield Promise.all(newTasks.map((currentTask, index) => __awaiter(this, void 0, void 0, function* () {\n        try {\n          const result = yield props.upload(currentTask.file);\n          newVal[index] = result;\n          setTasks(prev => {\n            return prev.map(task => {\n              if (task.id === currentTask.id) {\n                return Object.assign(Object.assign({}, task), {\n                  status: 'success',\n                  url: result.url\n                });\n              }\n              return task;\n            });\n          });\n        } catch (e) {\n          setTasks(prev => {\n            return prev.map(task => {\n              if (task.id === currentTask.id) {\n                return Object.assign(Object.assign({}, task), {\n                  status: 'fail'\n                });\n              }\n              return task;\n            });\n          });\n          console.error(e);\n        }\n      })));\n      setValue(prev => prev.concat(newVal).filter(Boolean));\n    });\n  }\n  const imageViewerHandlerRef = useRef(null);\n  function previewImage(index) {\n    imageViewerHandlerRef.current = ImageViewer.Multi.show({\n      images: value.map(fileItem => fileItem.url),\n      defaultIndex: index,\n      onClose: () => {\n        imageViewerHandlerRef.current = null;\n      }\n    });\n  }\n  useUnmount(() => {\n    var _a;\n    (_a = imageViewerHandlerRef.current) === null || _a === void 0 ? void 0 : _a.close();\n  });\n  const finalTasks = getFinalTasks(tasks);\n  const showUpload = props.showUpload && (maxCount === 0 || value.length + finalTasks.length < maxCount);\n  const renderImages = () => {\n    return value.map((fileItem, index) => {\n      var _a, _b;\n      const originNode = React.createElement(PreviewItem, {\n        key: (_a = fileItem.key) !== null && _a !== void 0 ? _a : index,\n        url: (_b = fileItem.thumbnailUrl) !== null && _b !== void 0 ? _b : fileItem.url,\n        deletable: props.deletable,\n        deleteIcon: props.deleteIcon,\n        imageFit: props.imageFit,\n        onClick: () => {\n          if (props.preview) {\n            previewImage(index);\n          }\n          onPreview && onPreview(index, fileItem);\n        },\n        onDelete: () => __awaiter(void 0, void 0, void 0, function* () {\n          var _c;\n          const canDelete = yield (_c = props.onDelete) === null || _c === void 0 ? void 0 : _c.call(props, fileItem);\n          if (canDelete === false) return;\n          setValue(value.filter((x, i) => i !== index));\n        })\n      });\n      return renderItem ? renderItem(originNode, fileItem, value) : originNode;\n    });\n  };\n  const contentNode = React.createElement(React.Fragment, null, renderImages(), tasks.map(task => {\n    if (!props.showFailed && task.status === 'fail') {\n      return null;\n    }\n    return React.createElement(PreviewItem, {\n      key: task.id,\n      file: task.file,\n      deletable: task.status !== 'pending',\n      deleteIcon: props.deleteIcon,\n      status: task.status,\n      imageFit: props.imageFit,\n      onDelete: () => {\n        setTasks(tasks.filter(x => x.id !== task.id));\n      }\n    });\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-upload-button-wrap`,\n    style: showUpload ? undefined : {\n      display: 'none'\n    }\n  }, props.children || React.createElement(\"span\", {\n    className: `${classPrefix}-cell ${classPrefix}-upload-button`,\n    role: 'button',\n    \"aria-label\": locale.ImageUploader.upload\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-upload-button-icon`\n  }, React.createElement(AddOutline, null))), !props.disableUpload && React.createElement(\"input\", {\n    \"aria-label\": locale.ImageUploader.upload,\n    ref: inputRef,\n    capture: props.capture,\n    accept: props.accept,\n    multiple: props.multiple,\n    type: 'file',\n    className: `${classPrefix}-input`,\n    onChange: onChange\n  })));\n  useImperativeHandle(ref, () => ({\n    get nativeElement() {\n      return inputRef.current;\n    }\n  }));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: containerRef\n  }, columns ? React.createElement(Grid, {\n    className: `${classPrefix}-grid`,\n    columns: columns,\n    style: style\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-gap-measure`,\n    ref: gapMeasureRef\n  }), contentNode.props.children) : React.createElement(Space, {\n    className: `${classPrefix}-space`,\n    wrap: true,\n    block: true\n  }, contentNode.props.children)));\n});"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,yBAAyB,EAAEC,OAAO,EAAEC,UAAU,QAAQ,QAAQ;AACvE,SAASC,UAAU,EAAEC,YAAY,QAAQ,mBAAmB;AAC5D,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,YAAY,GAAG;EACnBC,aAAa,EAAE,KAAK;EACpBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAElB,KAAK,CAACmB,aAAa,CAACpB,YAAY,EAAE;IAC5CqB,SAAS,EAAE,GAAGN,WAAW;EAC3B,CAAC,CAAC;EACFO,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,EAAE;EAChBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,aAAa,GAAG5B,UAAU,CAAC,CAAC6B,CAAC,EAAEC,GAAG,KAAK;EAClD,MAAM;IACJC;EACF,CAAC,GAAGvB,SAAS,CAAC,CAAC;EACf,MAAMwB,KAAK,GAAGzB,UAAU,CAACO,YAAY,EAAEe,CAAC,CAAC;EACzC,MAAM;IACJI;EACF,CAAC,GAAGD,KAAK;EACT,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,aAAa,CAAC0B,KAAK,CAAC;EAC9C,MAAM,CAACI,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMmC,YAAY,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMqC,aAAa,GAAG5C,OAAO,CAAC2C,YAAY,CAAC;EAC3C,MAAME,aAAa,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMwC,QAAQ,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAC7BR,yBAAyB,CAAC,MAAM;IAC9B,MAAMkD,UAAU,GAAGJ,aAAa,CAACK,OAAO;IACxC,IAAIZ,OAAO,IAAIM,aAAa,IAAIK,UAAU,EAAE;MAC1C,MAAME,KAAK,GAAGP,aAAa,CAACO,KAAK;MACjC,MAAMC,GAAG,GAAG3C,gBAAgB,CAAC4C,MAAM,CAACC,gBAAgB,CAACL,UAAU,CAAC,CAACM,gBAAgB,CAAC,QAAQ,CAAC,CAAC;MAC5FR,WAAW,CAAC,CAACI,KAAK,GAAGC,GAAG,IAAId,OAAO,GAAG,CAAC,CAAC,IAAIA,OAAO,CAAC;IACtD;EACF,CAAC,EAAE,CAACM,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,KAAK,CAAC,CAAC;EACvF,MAAMK,KAAK,GAAG;IACZ,aAAa,EAAEV,QAAQ,GAAG;EAC5B,CAAC;EACD/C,yBAAyB,CAAC,MAAM;IAC9B2C,QAAQ,CAACe,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,IAAI,IAAI;MACnC,IAAIA,IAAI,CAACC,GAAG,KAAKC,SAAS,EAAE,OAAO,IAAI;MACvC,OAAO,CAACtB,KAAK,CAACuB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACH,GAAG,KAAKD,IAAI,CAACC,GAAG,CAAC;IAC3D,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACrB,KAAK,CAAC,CAAC;EACXxC,yBAAyB,CAAC,MAAM;IAC9B,IAAIiE,EAAE;IACN,CAACA,EAAE,GAAG3B,KAAK,CAAC4B,mBAAmB,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC7B,KAAK,EAAEI,KAAK,CAAC0B,GAAG,CAACC,IAAI,KAAK;MACtGC,EAAE,EAAED,IAAI,CAACC,EAAE;MACXC,MAAM,EAAEF,IAAI,CAACE;IACf,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,CAAC7B,KAAK,CAAC,CAAC;EACX,MAAM8B,UAAU,GAAGhE,MAAM,CAAC,CAAC,CAAC;EAC5B,MAAM;IACJoB,QAAQ;IACR6C,SAAS;IACTC;EACF,CAAC,GAAGpC,KAAK;EACT,SAASqC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IACnC,OAAO9E,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClD,MAAM;QACJ+E;MACF,CAAC,GAAGxC,KAAK;MACT,IAAIyC,eAAe,GAAGH,IAAI;MAC1BG,eAAe,GAAG,MAAMD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACF,IAAI,EAAEC,QAAQ,CAAC;MAChH,OAAOE,eAAe;IACxB,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAACtC,KAAK,EAAE;IAC5B,OAAOJ,KAAK,CAACN,UAAU,GAAGU,KAAK,GAAGA,KAAK,CAACiB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACW,MAAM,KAAK,MAAM,CAAC;EAChF;EACA,SAASU,QAAQA,CAACC,CAAC,EAAE;IACnB,IAAIjB,EAAE;IACN,OAAOlE,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClDmF,CAAC,CAACC,OAAO,CAAC,CAAC;MACX,MAAM;QACJC,KAAK,EAAEC;MACT,CAAC,GAAGH,CAAC,CAACI,MAAM;MACZ,IAAI,CAACD,QAAQ,EAAE;MACf,IAAID,KAAK,GAAG,EAAE,CAACG,KAAK,CAACpB,IAAI,CAACkB,QAAQ,CAAC;MACnCH,CAAC,CAACI,MAAM,CAAC9C,KAAK,GAAG,EAAE,CAAC,CAAC;MACrB,IAAIF,KAAK,CAACwC,YAAY,EAAE;QACtB,MAAMU,SAAS,GAAGJ,KAAK,CAAChB,GAAG,CAACQ,IAAI,IAAID,WAAW,CAACC,IAAI,EAAEQ,KAAK,CAAC,CAAC;QAC7D,MAAMK,OAAO,CAACC,GAAG,CAACF,SAAS,CAAC,CAACG,IAAI,CAACC,SAAS,IAAI;UAC7CR,KAAK,GAAGQ,SAAS,CAACjC,MAAM,CAACkC,OAAO,CAAC;QACnC,CAAC,CAAC;MACJ;MACA,IAAIT,KAAK,CAACU,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAIlE,QAAQ,GAAG,CAAC,EAAE;QAChB,MAAMmE,MAAM,GAAGvD,KAAK,CAACsD,MAAM,GAAGV,KAAK,CAACU,MAAM,GAAGlE,QAAQ;QACrD,IAAImE,MAAM,GAAG,CAAC,EAAE;UACdX,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC,EAAEH,KAAK,CAACU,MAAM,GAAGC,MAAM,CAAC;UAC7C,CAAC9B,EAAE,GAAG3B,KAAK,CAAC0D,aAAa,MAAM,IAAI,IAAI/B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC7B,KAAK,EAAEyD,MAAM,CAAC;QACxF;MACF;MACA,MAAME,QAAQ,GAAGb,KAAK,CAAChB,GAAG,CAACQ,IAAI,KAAK;QAClCN,EAAE,EAAEE,UAAU,CAACrB,OAAO,EAAE;QACxBoB,MAAM,EAAE,SAAS;QACjBK;MACF,CAAC,CAAC,CAAC;MACHjC,QAAQ,CAACe,IAAI,IAAI,CAAC,GAAGsB,aAAa,CAACtB,IAAI,CAAC,EAAE,GAAGuC,QAAQ,CAAC,CAAC;MACvD,MAAMC,MAAM,GAAG,EAAE;MACjB,MAAMT,OAAO,CAACC,GAAG,CAACO,QAAQ,CAAC7B,GAAG,CAAC,CAAC+B,WAAW,EAAEC,KAAK,KAAKrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;QAClG,IAAI;UACF,MAAMsG,MAAM,GAAG,MAAM/D,KAAK,CAACgE,MAAM,CAACH,WAAW,CAACvB,IAAI,CAAC;UACnDsB,MAAM,CAACE,KAAK,CAAC,GAAGC,MAAM;UACtB1D,QAAQ,CAACe,IAAI,IAAI;YACf,OAAOA,IAAI,CAACU,GAAG,CAACR,IAAI,IAAI;cACtB,IAAIA,IAAI,CAACU,EAAE,KAAK6B,WAAW,CAAC7B,EAAE,EAAE;gBAC9B,OAAOiC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5C,IAAI,CAAC,EAAE;kBAC5CW,MAAM,EAAE,SAAS;kBACjBV,GAAG,EAAEwC,MAAM,CAACxC;gBACd,CAAC,CAAC;cACJ;cACA,OAAOD,IAAI;YACb,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOsB,CAAC,EAAE;UACVvC,QAAQ,CAACe,IAAI,IAAI;YACf,OAAOA,IAAI,CAACU,GAAG,CAACR,IAAI,IAAI;cACtB,IAAIA,IAAI,CAACU,EAAE,KAAK6B,WAAW,CAAC7B,EAAE,EAAE;gBAC9B,OAAOiC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5C,IAAI,CAAC,EAAE;kBAC5CW,MAAM,EAAE;gBACV,CAAC,CAAC;cACJ;cACA,OAAOX,IAAI;YACb,CAAC,CAAC;UACJ,CAAC,CAAC;UACF6C,OAAO,CAACC,KAAK,CAACxB,CAAC,CAAC;QAClB;MACF,CAAC,CAAC,CAAC,CAAC;MACJzC,QAAQ,CAACiB,IAAI,IAAIA,IAAI,CAACiD,MAAM,CAACT,MAAM,CAAC,CAACvC,MAAM,CAACkC,OAAO,CAAC,CAAC;IACvD,CAAC,CAAC;EACJ;EACA,MAAMe,qBAAqB,GAAGpG,MAAM,CAAC,IAAI,CAAC;EAC1C,SAASqG,YAAYA,CAACT,KAAK,EAAE;IAC3BQ,qBAAqB,CAACzD,OAAO,GAAGnC,WAAW,CAAC8F,KAAK,CAACC,IAAI,CAAC;MACrDC,MAAM,EAAExE,KAAK,CAAC4B,GAAG,CAACJ,QAAQ,IAAIA,QAAQ,CAACH,GAAG,CAAC;MAC3CoD,YAAY,EAAEb,KAAK;MACnBc,OAAO,EAAEA,CAAA,KAAM;QACbN,qBAAqB,CAACzD,OAAO,GAAG,IAAI;MACtC;IACF,CAAC,CAAC;EACJ;EACAjD,UAAU,CAAC,MAAM;IACf,IAAI+D,EAAE;IACN,CAACA,EAAE,GAAG2C,qBAAqB,CAACzD,OAAO,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkD,KAAK,CAAC,CAAC;EACtF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGpC,aAAa,CAACtC,KAAK,CAAC;EACvC,MAAMhB,UAAU,GAAGY,KAAK,CAACZ,UAAU,KAAKE,QAAQ,KAAK,CAAC,IAAIY,KAAK,CAACsD,MAAM,GAAGsB,UAAU,CAACtB,MAAM,GAAGlE,QAAQ,CAAC;EACtG,MAAMyF,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO7E,KAAK,CAAC4B,GAAG,CAAC,CAACJ,QAAQ,EAAEoC,KAAK,KAAK;MACpC,IAAInC,EAAE,EAAEqD,EAAE;MACV,MAAMC,UAAU,GAAGlH,KAAK,CAACmB,aAAa,CAACN,WAAW,EAAE;QAClDsG,GAAG,EAAE,CAACvD,EAAE,GAAGD,QAAQ,CAACwD,GAAG,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGmC,KAAK;QAC/DvC,GAAG,EAAE,CAACyD,EAAE,GAAGtD,QAAQ,CAACyD,YAAY,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGtD,QAAQ,CAACH,GAAG;QAC/EvC,SAAS,EAAEgB,KAAK,CAAChB,SAAS;QAC1BC,UAAU,EAAEe,KAAK,CAACf,UAAU;QAC5BU,QAAQ,EAAEK,KAAK,CAACL,QAAQ;QACxByF,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIpF,KAAK,CAACP,OAAO,EAAE;YACjB8E,YAAY,CAACT,KAAK,CAAC;UACrB;UACA3B,SAAS,IAAIA,SAAS,CAAC2B,KAAK,EAAEpC,QAAQ,CAAC;QACzC,CAAC;QACD2D,QAAQ,EAAEA,CAAA,KAAM5H,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;UAC7D,IAAI6H,EAAE;UACN,MAAMC,SAAS,GAAG,MAAM,CAACD,EAAE,GAAGtF,KAAK,CAACqF,QAAQ,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzD,IAAI,CAAC7B,KAAK,EAAE0B,QAAQ,CAAC;UAC3G,IAAI6D,SAAS,KAAK,KAAK,EAAE;UACzBpF,QAAQ,CAACD,KAAK,CAACmB,MAAM,CAAC,CAACmE,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAK3B,KAAK,CAAC,CAAC;QAC/C,CAAC;MACH,CAAC,CAAC;MACF,OAAO1B,UAAU,GAAGA,UAAU,CAAC6C,UAAU,EAAEvD,QAAQ,EAAExB,KAAK,CAAC,GAAG+E,UAAU;IAC1E,CAAC,CAAC;EACJ,CAAC;EACD,MAAMS,WAAW,GAAG3H,KAAK,CAACmB,aAAa,CAACnB,KAAK,CAAC4H,QAAQ,EAAE,IAAI,EAAEZ,YAAY,CAAC,CAAC,EAAE3E,KAAK,CAAC0B,GAAG,CAACR,IAAI,IAAI;IAC9F,IAAI,CAACtB,KAAK,CAACN,UAAU,IAAI4B,IAAI,CAACW,MAAM,KAAK,MAAM,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAOlE,KAAK,CAACmB,aAAa,CAACN,WAAW,EAAE;MACtCsG,GAAG,EAAE5D,IAAI,CAACU,EAAE;MACZM,IAAI,EAAEhB,IAAI,CAACgB,IAAI;MACftD,SAAS,EAAEsC,IAAI,CAACW,MAAM,KAAK,SAAS;MACpChD,UAAU,EAAEe,KAAK,CAACf,UAAU;MAC5BgD,MAAM,EAAEX,IAAI,CAACW,MAAM;MACnBtC,QAAQ,EAAEK,KAAK,CAACL,QAAQ;MACxB0F,QAAQ,EAAEA,CAAA,KAAM;QACdhF,QAAQ,CAACD,KAAK,CAACiB,MAAM,CAACmE,CAAC,IAAIA,CAAC,CAACxD,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EAAEjE,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC7BC,SAAS,EAAE,GAAGN,WAAW,qBAAqB;IAC9CsC,KAAK,EAAE/B,UAAU,GAAGoC,SAAS,GAAG;MAC9BoE,OAAO,EAAE;IACX;EACF,CAAC,EAAE5F,KAAK,CAAC6F,QAAQ,IAAI9H,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IAC/CC,SAAS,EAAE,GAAGN,WAAW,SAASA,WAAW,gBAAgB;IAC7DiH,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE/F,MAAM,CAACH,aAAa,CAACoE;EACrC,CAAC,EAAEjG,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IAC7BC,SAAS,EAAE,GAAGN,WAAW;EAC3B,CAAC,EAAEd,KAAK,CAACmB,aAAa,CAACrB,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAACmC,KAAK,CAACjB,aAAa,IAAIhB,KAAK,CAACmB,aAAa,CAAC,OAAO,EAAE;IAC/F,YAAY,EAAEa,MAAM,CAACH,aAAa,CAACoE,MAAM;IACzClE,GAAG,EAAEa,QAAQ;IACboF,OAAO,EAAE/F,KAAK,CAAC+F,OAAO;IACtBvG,MAAM,EAAEQ,KAAK,CAACR,MAAM;IACpBH,QAAQ,EAAEW,KAAK,CAACX,QAAQ;IACxB2G,IAAI,EAAE,MAAM;IACZ7G,SAAS,EAAE,GAAGN,WAAW,QAAQ;IACjC8D,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC;EACJ1E,mBAAmB,CAAC6B,GAAG,EAAE,OAAO;IAC9B,IAAImG,aAAaA,CAAA,EAAG;MAClB,OAAOtF,QAAQ,CAACE,OAAO;IACzB;EACF,CAAC,CAAC,CAAC;EACH,OAAOxC,eAAe,CAAC2B,KAAK,EAAEjC,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEN,WAAW;IACtBiB,GAAG,EAAEQ;EACP,CAAC,EAAEL,OAAO,GAAGlC,KAAK,CAACmB,aAAa,CAACT,IAAI,EAAE;IACrCU,SAAS,EAAE,GAAGN,WAAW,OAAO;IAChCoB,OAAO,EAAEA,OAAO;IAChBkB,KAAK,EAAEA;EACT,CAAC,EAAEpD,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGN,WAAW,cAAc;IACvCiB,GAAG,EAAEU;EACP,CAAC,CAAC,EAAEkF,WAAW,CAAC1F,KAAK,CAAC6F,QAAQ,CAAC,GAAG9H,KAAK,CAACmB,aAAa,CAACP,KAAK,EAAE;IAC3DQ,SAAS,EAAE,GAAGN,WAAW,QAAQ;IACjCqH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EAAET,WAAW,CAAC1F,KAAK,CAAC6F,QAAQ,CAAC,CAAC,CAAC;AAClC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}