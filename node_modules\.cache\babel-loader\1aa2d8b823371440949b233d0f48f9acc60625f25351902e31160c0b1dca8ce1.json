{"ast": null, "code": "import * as React from \"react\";\nfunction ScanningFaceOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningFaceOutline-ScanningFaceOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningFaceOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ScanningFaceOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7,32.4 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L15.6,41 C15.8209139,41 16,41.1790861 16,41.4 L16,43.6 C16,43.8209139 15.8209139,44 15.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,32.4 C4,32.1790861 4.1790861,32 4.4,32 L6.6,32 C6.8209139,32 7,32.1790861 7,32.4 Z M44,32.4 L44,38 C44,41.3137085 41.3137085,44 38,44 L32.4,44 C32.1790861,44 32,43.8209139 32,43.6 L32,41.4 C32,41.1790861 32.1790861,41 32.4,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,32.4 C41,32.1790861 41.1790861,32 41.4,32 L43.6,32 C43.8209139,32 44,32.1790861 44,32.4 Z M15.7615358,29.3202264 C15.7808454,29.4144131 15.7979106,29.4923276 15.8127313,29.5539699 C16.6926497,33.2137174 19.9226157,35.8784137 23.7011502,35.9961789 L23.9617542,36.0002438 L24.2187578,35.9972116 C28.0459556,35.8995354 31.2890832,33.2355019 32.1808385,29.580277 C32.1973709,29.5125119 32.2164728,29.4259189 32.2381441,29.3204982 C32.2762562,29.1344863 32.4399392,29.0009825 32.6298152,29.000918 C32.7121051,29.0008901 32.7812451,29.0008666 32.8372353,29.0008476 C33.3139529,29.0006857 33.9955223,29.0004543 34.8819434,29.0001533 C34.8920714,29.0001498 34.9027902,29.0001462 34.9140999,29.0001424 C35.1144266,29.0000588 35.2768787,29.1624005 35.2769468,29.3627273 C35.2769534,29.3823289 35.2753712,29.4018988 35.2722155,29.4212447 C35.2722155,29.4212447 35.2722155,29.4212447 35.2722155,29.4212447 C34.389869,34.8305711 29.7782392,38.8563016 24.2952977,38.9962351 L24,39 C18.3740495,39 13.602127,34.907028 12.7211223,29.3799307 C12.6949239,29.1981929 12.8210135,29.0296274 13.0027513,29.003429 C13.0184914,29.0011599 13.034374,29.0000232 13.0502768,29.000042 C13.1140676,29.0000591 13.1690901,29.0000815 13.2153442,29.000109 C13.6771075,29.0002007 14.33203,29.0003307 15.1801118,29.0004991 C15.2303931,29.0005091 15.2936507,29.0005217 15.3698846,29.0005368 C15.5597453,29.0006962 15.7234047,29.1342341 15.7615358,29.3202264 Z M4,25.6 L4,23.4 C4,23.1790861 4.1790861,23 4.4,23 L11.866,23 L11.8502921,22.8771906 C10.9556123,16.1670922 15.6699405,10.0022014 22.3800389,9.10752164 C22.9170563,9.03591932 23.4582302,9 24,9 C30.769481,9 36.2572296,14.4877486 36.2572296,21.2572296 C36.2572296,21.6635569 36.2370249,22.0695491 36.1967154,22.4737039 L36.133,23 L43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L4.4,26 C4.1790861,26 4,25.8209139 4,25.6 Z M24,12 C23.5908302,12 23.1821106,12.0271279 22.7765301,12.0812053 C17.7976603,12.7450547 14.2736651,17.2507775 14.7923092,22.2143738 L14.8239758,22.4806994 L14.892,23 L33.107,23 L33.1760242,22.4806994 C33.2301016,22.075119 33.2572296,21.6663993 33.2572296,21.2572296 C33.2572296,16.1446028 29.1126267,12 24,12 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,15.6 C44,15.8209139 43.8209139,16 43.6,16 L41.4,16 C41.1790861,16 41,15.8209139 41,15.6 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L32.4,7 C32.1790861,7 32,6.8209139 32,6.6 L32,4.4 C32,4.1790861 32.1790861,4 32.4,4 L38,4 Z M16,4.4 L16,6.6 C16,6.8209139 15.8209139,7 15.6,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,15.6 C7,15.8209139 6.8209139,16 6.6,16 L4.4,16 C4.1790861,16 4,15.8209139 4,15.6 L4,10 C4,6.6862915 6.6862915,4 10,4 L15.6,4 C15.8209139,4 16,4.1790861 16,4.4 Z\",\n    id: \"ScanningFaceOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ScanningFaceOutline;", "map": {"version": 3, "names": ["React", "ScanningFaceOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ScanningFaceOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ScanningFaceOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningFaceOutline-ScanningFaceOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningFaceOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ScanningFaceOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7,32.4 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L15.6,41 C15.8209139,41 16,41.1790861 16,41.4 L16,43.6 C16,43.8209139 15.8209139,44 15.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,32.4 C4,32.1790861 4.1790861,32 4.4,32 L6.6,32 C6.8209139,32 7,32.1790861 7,32.4 Z M44,32.4 L44,38 C44,41.3137085 41.3137085,44 38,44 L32.4,44 C32.1790861,44 32,43.8209139 32,43.6 L32,41.4 C32,41.1790861 32.1790861,41 32.4,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,32.4 C41,32.1790861 41.1790861,32 41.4,32 L43.6,32 C43.8209139,32 44,32.1790861 44,32.4 Z M15.7615358,29.3202264 C15.7808454,29.4144131 15.7979106,29.4923276 15.8127313,29.5539699 C16.6926497,33.2137174 19.9226157,35.8784137 23.7011502,35.9961789 L23.9617542,36.0002438 L24.2187578,35.9972116 C28.0459556,35.8995354 31.2890832,33.2355019 32.1808385,29.580277 C32.1973709,29.5125119 32.2164728,29.4259189 32.2381441,29.3204982 C32.2762562,29.1344863 32.4399392,29.0009825 32.6298152,29.000918 C32.7121051,29.0008901 32.7812451,29.0008666 32.8372353,29.0008476 C33.3139529,29.0006857 33.9955223,29.0004543 34.8819434,29.0001533 C34.8920714,29.0001498 34.9027902,29.0001462 34.9140999,29.0001424 C35.1144266,29.0000588 35.2768787,29.1624005 35.2769468,29.3627273 C35.2769534,29.3823289 35.2753712,29.4018988 35.2722155,29.4212447 C35.2722155,29.4212447 35.2722155,29.4212447 35.2722155,29.4212447 C34.389869,34.8305711 29.7782392,38.8563016 24.2952977,38.9962351 L24,39 C18.3740495,39 13.602127,34.907028 12.7211223,29.3799307 C12.6949239,29.1981929 12.8210135,29.0296274 13.0027513,29.003429 C13.0184914,29.0011599 13.034374,29.0000232 13.0502768,29.000042 C13.1140676,29.0000591 13.1690901,29.0000815 13.2153442,29.000109 C13.6771075,29.0002007 14.33203,29.0003307 15.1801118,29.0004991 C15.2303931,29.0005091 15.2936507,29.0005217 15.3698846,29.0005368 C15.5597453,29.0006962 15.7234047,29.1342341 15.7615358,29.3202264 Z M4,25.6 L4,23.4 C4,23.1790861 4.1790861,23 4.4,23 L11.866,23 L11.8502921,22.8771906 C10.9556123,16.1670922 15.6699405,10.0022014 22.3800389,9.10752164 C22.9170563,9.03591932 23.4582302,9 24,9 C30.769481,9 36.2572296,14.4877486 36.2572296,21.2572296 C36.2572296,21.6635569 36.2370249,22.0695491 36.1967154,22.4737039 L36.133,23 L43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L4.4,26 C4.1790861,26 4,25.8209139 4,25.6 Z M24,12 C23.5908302,12 23.1821106,12.0271279 22.7765301,12.0812053 C17.7976603,12.7450547 14.2736651,17.2507775 14.7923092,22.2143738 L14.8239758,22.4806994 L14.892,23 L33.107,23 L33.1760242,22.4806994 C33.2301016,22.075119 33.2572296,21.6663993 33.2572296,21.2572296 C33.2572296,16.1446028 29.1126267,12 24,12 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,15.6 C44,15.8209139 43.8209139,16 43.6,16 L41.4,16 C41.1790861,16 41,15.8209139 41,15.6 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L32.4,7 C32.1790861,7 32,6.8209139 32,6.6 L32,4.4 C32,4.1790861 32.1790861,4 32.4,4 L38,4 Z M16,4.4 L16,6.6 C16,6.8209139 15.8209139,7 15.6,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,15.6 C7,15.8209139 6.8209139,16 6.6,16 L4.4,16 C4.1790861,16 4,15.8209139 4,15.6 L4,10 C4,6.6862915 6.6862915,4 10,4 L15.6,4 C15.8209139,4 16,4.1790861 16,4.4 Z\",\n    id: \"ScanningFaceOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ScanningFaceOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yCAAyC;IAC7CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,ytGAAytG;IAC5tGR,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}