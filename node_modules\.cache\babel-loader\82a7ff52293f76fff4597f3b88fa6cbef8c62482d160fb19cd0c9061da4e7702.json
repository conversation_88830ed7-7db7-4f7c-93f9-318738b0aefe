{"ast": null, "code": "import { useTouch } from './use-touch';\nimport { useEffect } from 'react';\nimport { getScrollParent } from './get-scroll-parent';\nimport { supportsPassive } from './supports-passive';\nlet totalLockCount = 0;\nconst BODY_LOCK_CLASS = 'adm-overflow-hidden';\nfunction getScrollableElement(el) {\n  let current = el === null || el === void 0 ? void 0 : el.parentElement;\n  while (current) {\n    if (current.clientHeight < current.scrollHeight) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n// 移植自vant：https://github.com/youzan/vant/blob/HEAD/src/composables/use-lock-scroll.ts\nexport function useLockScroll(rootRef, shouldLock) {\n  const touch = useTouch();\n  const onTouchMove = event => {\n    touch.move(event);\n    const direction = touch.deltaY.current > 0 ? '10' : '01';\n    const el = getScrollParent(event.target, rootRef.current);\n    if (!el) return;\n    // This has perf cost but we have to compatible with iOS 12\n    if (shouldLock === 'strict') {\n      const scrollableParent = getScrollableElement(event.target);\n      if (scrollableParent === document.body || scrollableParent === document.documentElement) {\n        event.preventDefault();\n        return;\n      }\n    }\n    const {\n      scrollHeight,\n      offsetHeight,\n      scrollTop\n    } = el;\n    const {\n      height\n    } = el.getBoundingClientRect();\n    let status = '11';\n    if (scrollTop === 0) {\n      status = offsetHeight >= scrollHeight ? '00' : '01';\n    } else if (scrollHeight <= Math.round(height + scrollTop)) {\n      status = '10';\n    }\n    if (status !== '11' && touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {\n      if (event.cancelable && supportsPassive) {\n        // https://github.com/ant-design/ant-design-mobile/issues/6282\n        event.preventDefault();\n      }\n    }\n  };\n  const lock = () => {\n    document.addEventListener('touchstart', touch.start);\n    document.addEventListener('touchmove', onTouchMove, supportsPassive ? {\n      passive: false\n    } : false);\n    if (!totalLockCount) {\n      document.body.classList.add(BODY_LOCK_CLASS);\n    }\n    totalLockCount++;\n  };\n  const unlock = () => {\n    if (totalLockCount) {\n      document.removeEventListener('touchstart', touch.start);\n      document.removeEventListener('touchmove', onTouchMove);\n      totalLockCount--;\n      if (!totalLockCount) {\n        document.body.classList.remove(BODY_LOCK_CLASS);\n      }\n    }\n  };\n  useEffect(() => {\n    if (shouldLock) {\n      lock();\n      return () => {\n        unlock();\n      };\n    }\n  }, [shouldLock]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}