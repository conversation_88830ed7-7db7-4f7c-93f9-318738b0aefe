{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    listeners.splice(index, 1);\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n}\nexport default subscribe;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "listeners", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice", "revalidate", "i", "length", "window", "addEventListener"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    listeners.splice(index, 1);\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n}\nexport default subscribe;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,IAAIC,SAAS,GAAG,EAAE;AAClB,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC3BF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EACxB,OAAO,SAASE,WAAWA,CAAA,EAAG;IAC5B,IAAIC,KAAK,GAAGL,SAAS,CAACM,OAAO,CAACJ,QAAQ,CAAC;IACvCF,SAAS,CAACO,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC5B,CAAC;AACH;AACA,IAAIP,SAAS,EAAE;EACb,IAAIU,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,IAAI,CAACT,iBAAiB,CAAC,CAAC,EAAE;IAC1B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIP,QAAQ,GAAGF,SAAS,CAACS,CAAC,CAAC;MAC3BP,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDS,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,UAAU,EAAE,KAAK,CAAC;AAChE;AACA,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}