{"ast": null, "code": "import * as dateUtils from './date-picker-date-utils';\nimport * as quarterUtils from './date-picker-quarter-utils';\nimport * as weekUtils from './date-picker-week-utils';\nimport { TILL_NOW } from './util';\nconst precisionLengthRecord = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hour: 4,\n  minute: 5,\n  second: 6\n};\nexport const convertDateToStringArray = (date, precision) => {\n  if (precision.includes('week')) {\n    return weekUtils.convertDateToStringArray(date);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertDateToStringArray(date);\n  } else {\n    const datePrecision = precision;\n    const stringArray = dateUtils.convertDateToStringArray(date);\n    return stringArray.slice(0, precisionLengthRecord[datePrecision]);\n  }\n};\nexport const convertStringArrayToDate = (value, precision) => {\n  // Special case for DATE_NOW\n  if ((value === null || value === void 0 ? void 0 : value[0]) === TILL_NOW) {\n    const now = new Date();\n    now.tillNow = true;\n    return now;\n  }\n  if (precision.includes('week')) {\n    return weekUtils.convertStringArrayToDate(value);\n  } else if (precision.includes('quarter')) {\n    return quarterUtils.convertStringArrayToDate(value);\n  } else {\n    return dateUtils.convertStringArrayToDate(value);\n  }\n};\nexport const generateDatePickerColumns = (selected, min, max, precision, renderLabel, filter, tillNow) => {\n  if (precision.startsWith('week')) {\n    return weekUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else if (precision.startsWith('quarter')) {\n    return quarterUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter);\n  } else {\n    return dateUtils.generateDatePickerColumns(selected, min, max, precision, renderLabel, filter, tillNow);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}