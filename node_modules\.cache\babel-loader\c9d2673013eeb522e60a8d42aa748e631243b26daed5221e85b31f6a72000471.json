{"ast": null, "code": "import { __assign } from \"tslib\";\nexport var fieldAdapter = function (field) {\n  return {\n    getFieldInstance: function (name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function (fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function (result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function (dataIndex, order) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function (filterParams) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};", "map": {"version": 3, "names": ["__assign", "fieldAdapter", "field", "getFieldInstance", "name", "getNames", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON>", "getFieldsValue", "getV<PERSON>ues", "resetFields", "resetToDefault", "validateFields", "fields", "callback", "validate", "resultAdapter", "result", "tableProps", "dataSource", "loading", "onSort", "dataIndex", "order", "_a", "onChange", "current", "pagination", "pageSize", "params", "filters", "onFilter", "filterParams", "sorter", "paginationProps", "changeCurrent", "onPageSizeChange", "changePageSize", "total"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useFusionTable/fusionAdapter.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nexport var fieldAdapter = function (field) {\n  return {\n    getFieldInstance: function (name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function (fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function (result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function (dataIndex, order) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function (filterParams) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACzC,OAAO;IACLC,gBAAgB,EAAE,SAAAA,CAAUC,IAAI,EAAE;MAChC,OAAOF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACF,IAAI,CAAC;IACxC,CAAC;IACDG,cAAc,EAAEL,KAAK,CAACM,SAAS;IAC/BC,cAAc,EAAEP,KAAK,CAACQ,SAAS;IAC/BC,WAAW,EAAET,KAAK,CAACU,cAAc;IACjCC,cAAc,EAAE,SAAAA,CAAUC,MAAM,EAAEC,QAAQ,EAAE;MAC1Cb,KAAK,CAACc,QAAQ,CAACF,MAAM,EAAEC,QAAQ,CAAC;IAClC;EACF,CAAC;AACH,CAAC;AACD,OAAO,IAAIE,aAAa,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAC3C,IAAIC,UAAU,GAAG;IACfC,UAAU,EAAEF,MAAM,CAACC,UAAU,CAACC,UAAU;IACxCC,OAAO,EAAEH,MAAM,CAACC,UAAU,CAACE,OAAO;IAClCC,MAAM,EAAE,SAAAA,CAAUC,SAAS,EAAEC,KAAK,EAAE;MAClC,IAAIC,EAAE;MACNP,MAAM,CAACC,UAAU,CAACO,QAAQ,CAAC;QACzBC,OAAO,EAAET,MAAM,CAACU,UAAU,CAACD,OAAO;QAClCE,QAAQ,EAAEX,MAAM,CAACU,UAAU,CAACC;MAC9B,CAAC,EAAE,CAACJ,EAAE,GAAGP,MAAM,CAACY,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,OAAO,EAAE;QAC1E7B,KAAK,EAAEqB,SAAS;QAChBC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ,CAAC;IACDQ,QAAQ,EAAE,SAAAA,CAAUC,YAAY,EAAE;MAChC,IAAIR,EAAE;MACNP,MAAM,CAACC,UAAU,CAACO,QAAQ,CAAC;QACzBC,OAAO,EAAET,MAAM,CAACU,UAAU,CAACD,OAAO;QAClCE,QAAQ,EAAEX,MAAM,CAACU,UAAU,CAACC;MAC9B,CAAC,EAAEI,YAAY,EAAE,CAACR,EAAE,GAAGP,MAAM,CAACY,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,MAAM,CAAC;IAC1F;EACF,CAAC;EACD,IAAIC,eAAe,GAAG;IACpBT,QAAQ,EAAER,MAAM,CAACU,UAAU,CAACQ,aAAa;IACzCC,gBAAgB,EAAEnB,MAAM,CAACU,UAAU,CAACU,cAAc;IAClDX,OAAO,EAAET,MAAM,CAACU,UAAU,CAACD,OAAO;IAClCE,QAAQ,EAAEX,MAAM,CAACU,UAAU,CAACC,QAAQ;IACpCU,KAAK,EAAErB,MAAM,CAACU,UAAU,CAACW;EAC3B,CAAC;EACD,OAAOvC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkB,MAAM,CAAC,EAAE;IACpCC,UAAU,EAAEA,UAAU;IACtBgB,eAAe,EAAEA;EACnB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}