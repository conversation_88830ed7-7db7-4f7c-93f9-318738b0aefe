{"ast": null, "code": "export const create = () => {\n  return [1, 0, 0, 1, 0, 0];\n};\nexport const getTranslateX = m => {\n  return m[4];\n};\nexport const getTranslateY = m => {\n  return m[5];\n};\nexport const getScaleX = m => {\n  return m[0];\n};\nexport const getScaleY = m => {\n  return m[3];\n};\nexport const translate = (m, x, y) => {\n  return multiply([1, 0, 0, 1, x, y], m);\n};\nexport const scale = (m, scaleX, scaleY = scaleX) => {\n  return multiply([scaleX, 0, 0, scaleY, 0, 0], m);\n};\nexport const apply = (m, [ox, oy]) => {\n  return [m[0] * ox + m[2] * oy + m[4], m[1] * ox + m[3] * oy + m[5]];\n};\nexport const multiply = (m1, m2) => {\n  return [m1[0] * m2[0] + m1[2] * m2[1], m1[1] * m2[0] + m1[3] * m2[1], m1[0] * m2[2] + m1[2] * m2[3], m1[1] * m2[2] + m1[3] * m2[3], m1[0] * m2[4] + m1[2] * m2[5] + m1[4], m1[1] * m2[4] + m1[3] * m2[5] + m1[5]];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}