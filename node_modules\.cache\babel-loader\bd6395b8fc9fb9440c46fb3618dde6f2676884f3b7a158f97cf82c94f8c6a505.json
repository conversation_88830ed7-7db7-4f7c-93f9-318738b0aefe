{"ast": null, "code": "import * as React from \"react\";\nfunction ShopbagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShopbagOutline-ShopbagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShopbagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ShopbagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,4 C40.3137085,4 43,6.6862915 43,10 L43,38 C43,41.3137085 40.3137085,44 37,44 L11,44 C7.6862915,44 5,41.3137085 5,38 L5,10 C5,6.6862915 7.6862915,4 11,4 L37,4 Z M40,36 L8,36 L8,38 C8,39.5976809 9.24891996,40.9036609 10.8237272,40.9949073 L11,41 L37,41 C38.5976809,41 39.9036609,39.75108 39.9949073,38.1762728 L40,38 L40,36 Z M37,7 L11,7 C9.40231912,7 8.09633912,8.24891996 8.00509269,9.82372721 L8,10 L8,33 L40,33 L40,10 C40,8.40231912 38.75108,7.09633912 37.1762728,7.00509269 L37,7 Z M19.3833345,12 C19.5850013,12 19.7500015,12.1650002 19.7500015,12.366667 L19.750042,14.7499849 C19.7500424,17.2035658 21.6826755,19.2220853 24.1341917,19.3287339 L24.3333722,19.3333287 C26.7872173,19.3331869 28.8056509,17.4004374 28.9121275,14.9489212 L28.9167109,14.7500045 L28.9167109,12.366667 C28.9167109,12.1650002 29.0817111,12 29.2833778,12 L31.3000454,12 C31.5017122,12 31.6667124,12.1650002 31.6667124,12.366667 L31.6667124,14.7500045 C31.6667124,18.8000975 28.3834621,22.0833607 24.3333562,22.0833607 C20.2832632,22.0833607 17,18.8001104 17,14.7500045 L17,12.366667 C17,12.1650002 17.1650002,12 17.366667,12 L19.3833345,12 Z\",\n    id: \"ShopbagOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ShopbagOutline;", "map": {"version": 3, "names": ["React", "ShopbagOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ShopbagOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ShopbagOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShopbagOutline-ShopbagOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ShopbagOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ShopbagOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37,4 C40.3137085,4 43,6.6862915 43,10 L43,38 C43,41.3137085 40.3137085,44 37,44 L11,44 C7.6862915,44 5,41.3137085 5,38 L5,10 C5,6.6862915 7.6862915,4 11,4 L37,4 Z M40,36 L8,36 L8,38 C8,39.5976809 9.24891996,40.9036609 10.8237272,40.9949073 L11,41 L37,41 C38.5976809,41 39.9036609,39.75108 39.9949073,38.1762728 L40,38 L40,36 Z M37,7 L11,7 C9.40231912,7 8.09633912,8.24891996 8.00509269,9.82372721 L8,10 L8,33 L40,33 L40,10 C40,8.40231912 38.75108,7.09633912 37.1762728,7.00509269 L37,7 Z M19.3833345,12 C19.5850013,12 19.7500015,12.1650002 19.7500015,12.366667 L19.750042,14.7499849 C19.7500424,17.2035658 21.6826755,19.2220853 24.1341917,19.3287339 L24.3333722,19.3333287 C26.7872173,19.3331869 28.8056509,17.4004374 28.9121275,14.9489212 L28.9167109,14.7500045 L28.9167109,12.366667 C28.9167109,12.1650002 29.0817111,12 29.2833778,12 L31.3000454,12 C31.5017122,12 31.6667124,12.1650002 31.6667124,12.366667 L31.6667124,14.7500045 C31.6667124,18.8000975 28.3834621,22.0833607 24.3333562,22.0833607 C20.2832632,22.0833607 17,18.8001104 17,14.7500045 L17,12.366667 C17,12.1650002 17.1650002,12 17.366667,12 L19.3833345,12 Z\",\n    id: \"ShopbagOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ShopbagOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+BAA+B;IACnCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,qmCAAqmC;IACxmCR,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}