{"ast": null, "code": "import { __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useRef, useCallback } from 'react';\nfunction useLockFn(fn) {\n  var _this = this;\n  var lockRef = useRef(false);\n  return useCallback(function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return __awaiter(_this, void 0, void 0, function () {\n      var ret, e_1;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (lockRef.current) return [2 /*return*/];\n            lockRef.current = true;\n            _a.label = 1;\n          case 1:\n            _a.trys.push([1, 3, 4, 5]);\n            return [4 /*yield*/, fn.apply(void 0, __spreadArray([], __read(args), false))];\n          case 2:\n            ret = _a.sent();\n            return [2 /*return*/, ret];\n          case 3:\n            e_1 = _a.sent();\n            throw e_1;\n          case 4:\n            lockRef.current = false;\n            return [7 /*endfinally*/];\n          case 5:\n            return [2 /*return*/];\n        }\n      });\n    });\n  }, [fn]);\n}\nexport default useLockFn;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}