{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport React, { useEffect, useRef, useState } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { getScrollParent } from '../../utils/get-scroll-parent';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport { convertPx } from '../../utils/convert-px';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { useConfig } from '../config-provider';\nimport { sleep } from '../../utils/sleep';\nconst classPrefix = `adm-pull-to-refresh`;\nexport const defaultProps = {\n  pullingText: '下拉刷新',\n  canReleaseText: '释放立即刷新',\n  refreshingText: '加载中...',\n  completeText: '刷新成功',\n  completeDelay: 500,\n  disabled: false,\n  onRefresh: () => {}\n};\nexport const PullToRefresh = p => {\n  var _a, _b;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    refreshingText: `${locale.common.loading}...`,\n    pullingText: locale.PullToRefresh.pulling,\n    canReleaseText: locale.PullToRefresh.canRelease,\n    completeText: locale.PullToRefresh.complete\n  }, p);\n  const headHeight = (_a = props.headHeight) !== null && _a !== void 0 ? _a : convertPx(40);\n  const threshold = (_b = props.threshold) !== null && _b !== void 0 ? _b : convertPx(60);\n  const [status, setStatus] = useState('pulling');\n  const [springStyles, api] = useSpring(() => ({\n    from: {\n      height: 0\n    },\n    config: {\n      tension: 300,\n      friction: 30,\n      round: true,\n      clamp: true\n    }\n  }));\n  const elementRef = useRef(null);\n  const pullingRef = useRef(false);\n  //防止下拉时抖动\n  useEffect(() => {\n    var _a;\n    (_a = elementRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener('touchmove', () => {});\n  }, []);\n  const reset = () => {\n    return new Promise(resolve => {\n      api.start({\n        to: {\n          height: 0\n        },\n        onResolve() {\n          setStatus('pulling');\n          resolve();\n        }\n      });\n    });\n  };\n  function doRefresh() {\n    return __awaiter(this, void 0, void 0, function* () {\n      api.start({\n        height: headHeight\n      });\n      setStatus('refreshing');\n      try {\n        yield props.onRefresh();\n        setStatus('complete');\n      } catch (e) {\n        reset();\n        throw e;\n      }\n      if (props.completeDelay > 0) {\n        yield sleep(props.completeDelay);\n      }\n      reset();\n    });\n  }\n  useDrag(state => {\n    if (status === 'refreshing' || status === 'complete') return;\n    const {\n      event\n    } = state;\n    if (state.last) {\n      pullingRef.current = false;\n      if (status === 'canRelease') {\n        doRefresh();\n      } else {\n        api.start({\n          height: 0\n        });\n      }\n      return;\n    }\n    const [, y] = state.movement;\n    const parsedY = Math.ceil(y);\n    if (state.first && parsedY > 0) {\n      const target = state.event.target;\n      if (!target || !(target instanceof Element)) return;\n      let scrollParent = getScrollParent(target);\n      while (true) {\n        if (!scrollParent) return;\n        const scrollTop = getScrollTop(scrollParent);\n        if (scrollTop > 0) {\n          return;\n        }\n        if (scrollParent instanceof Window) {\n          break;\n        }\n        scrollParent = getScrollParent(scrollParent.parentNode);\n      }\n      pullingRef.current = true;\n      function getScrollTop(element) {\n        return 'scrollTop' in element ? element.scrollTop : element.scrollY;\n      }\n    }\n    if (!pullingRef.current) return;\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    event.stopPropagation();\n    const height = Math.max(rubberbandIfOutOfBounds(parsedY, 0, 0, headHeight * 5, 0.5), 0);\n    api.start({\n      height\n    });\n    setStatus(height > threshold ? 'canRelease' : 'pulling');\n  }, {\n    pointer: {\n      touch: true\n    },\n    axis: 'y',\n    target: elementRef,\n    enabled: !props.disabled,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  const renderStatusText = () => {\n    var _a;\n    if (props.renderText) {\n      return (_a = props.renderText) === null || _a === void 0 ? void 0 : _a.call(props, status);\n    }\n    if (status === 'pulling') return props.pullingText;\n    if (status === 'canRelease') return props.canReleaseText;\n    if (status === 'refreshing') return props.refreshingText;\n    if (status === 'complete') return props.completeText;\n  };\n  return React.createElement(animated.div, {\n    ref: elementRef,\n    className: classPrefix\n  }, React.createElement(animated.div, {\n    style: springStyles,\n    className: `${classPrefix}-head`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-head-content`,\n    style: {\n      height: headHeight\n    }\n  }, renderStatusText())), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children));\n};", "map": {"version": 3, "names": ["__awaiter", "React", "useEffect", "useRef", "useState", "mergeProps", "animated", "useSpring", "useDrag", "getScrollParent", "supportsPassive", "convertPx", "rubberbandIfOutOfBounds", "useConfig", "sleep", "classPrefix", "defaultProps", "pullingText", "canReleaseText", "refreshingText", "completeText", "completeDelay", "disabled", "onRefresh", "PullToRefresh", "p", "_a", "_b", "locale", "props", "common", "loading", "pulling", "canRelease", "complete", "headHeight", "threshold", "status", "setStatus", "springStyles", "api", "from", "height", "config", "tension", "friction", "round", "clamp", "elementRef", "pullingRef", "current", "addEventListener", "reset", "Promise", "resolve", "start", "to", "onResolve", "doRefresh", "e", "state", "event", "last", "y", "movement", "parsedY", "Math", "ceil", "first", "target", "Element", "scrollParent", "scrollTop", "getScrollTop", "Window", "parentNode", "element", "scrollY", "cancelable", "preventDefault", "stopPropagation", "max", "pointer", "touch", "axis", "enabled", "eventOptions", "passive", "undefined", "renderStatusText", "renderText", "call", "createElement", "div", "ref", "className", "style", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/pull-to-refresh/pull-to-refresh.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport React, { useEffect, useRef, useState } from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { getScrollParent } from '../../utils/get-scroll-parent';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport { convertPx } from '../../utils/convert-px';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { useConfig } from '../config-provider';\nimport { sleep } from '../../utils/sleep';\nconst classPrefix = `adm-pull-to-refresh`;\nexport const defaultProps = {\n  pullingText: '下拉刷新',\n  canReleaseText: '释放立即刷新',\n  refreshingText: '加载中...',\n  completeText: '刷新成功',\n  completeDelay: 500,\n  disabled: false,\n  onRefresh: () => {}\n};\nexport const PullToRefresh = p => {\n  var _a, _b;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    refreshingText: `${locale.common.loading}...`,\n    pullingText: locale.PullToRefresh.pulling,\n    canReleaseText: locale.PullToRefresh.canRelease,\n    completeText: locale.PullToRefresh.complete\n  }, p);\n  const headHeight = (_a = props.headHeight) !== null && _a !== void 0 ? _a : convertPx(40);\n  const threshold = (_b = props.threshold) !== null && _b !== void 0 ? _b : convertPx(60);\n  const [status, setStatus] = useState('pulling');\n  const [springStyles, api] = useSpring(() => ({\n    from: {\n      height: 0\n    },\n    config: {\n      tension: 300,\n      friction: 30,\n      round: true,\n      clamp: true\n    }\n  }));\n  const elementRef = useRef(null);\n  const pullingRef = useRef(false);\n  //防止下拉时抖动\n  useEffect(() => {\n    var _a;\n    (_a = elementRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener('touchmove', () => {});\n  }, []);\n  const reset = () => {\n    return new Promise(resolve => {\n      api.start({\n        to: {\n          height: 0\n        },\n        onResolve() {\n          setStatus('pulling');\n          resolve();\n        }\n      });\n    });\n  };\n  function doRefresh() {\n    return __awaiter(this, void 0, void 0, function* () {\n      api.start({\n        height: headHeight\n      });\n      setStatus('refreshing');\n      try {\n        yield props.onRefresh();\n        setStatus('complete');\n      } catch (e) {\n        reset();\n        throw e;\n      }\n      if (props.completeDelay > 0) {\n        yield sleep(props.completeDelay);\n      }\n      reset();\n    });\n  }\n  useDrag(state => {\n    if (status === 'refreshing' || status === 'complete') return;\n    const {\n      event\n    } = state;\n    if (state.last) {\n      pullingRef.current = false;\n      if (status === 'canRelease') {\n        doRefresh();\n      } else {\n        api.start({\n          height: 0\n        });\n      }\n      return;\n    }\n    const [, y] = state.movement;\n    const parsedY = Math.ceil(y);\n    if (state.first && parsedY > 0) {\n      const target = state.event.target;\n      if (!target || !(target instanceof Element)) return;\n      let scrollParent = getScrollParent(target);\n      while (true) {\n        if (!scrollParent) return;\n        const scrollTop = getScrollTop(scrollParent);\n        if (scrollTop > 0) {\n          return;\n        }\n        if (scrollParent instanceof Window) {\n          break;\n        }\n        scrollParent = getScrollParent(scrollParent.parentNode);\n      }\n      pullingRef.current = true;\n      function getScrollTop(element) {\n        return 'scrollTop' in element ? element.scrollTop : element.scrollY;\n      }\n    }\n    if (!pullingRef.current) return;\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    event.stopPropagation();\n    const height = Math.max(rubberbandIfOutOfBounds(parsedY, 0, 0, headHeight * 5, 0.5), 0);\n    api.start({\n      height\n    });\n    setStatus(height > threshold ? 'canRelease' : 'pulling');\n  }, {\n    pointer: {\n      touch: true\n    },\n    axis: 'y',\n    target: elementRef,\n    enabled: !props.disabled,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  const renderStatusText = () => {\n    var _a;\n    if (props.renderText) {\n      return (_a = props.renderText) === null || _a === void 0 ? void 0 : _a.call(props, status);\n    }\n    if (status === 'pulling') return props.pullingText;\n    if (status === 'canRelease') return props.canReleaseText;\n    if (status === 'refreshing') return props.refreshingText;\n    if (status === 'complete') return props.completeText;\n  };\n  return React.createElement(animated.div, {\n    ref: elementRef,\n    className: classPrefix\n  }, React.createElement(animated.div, {\n    style: springStyles,\n    className: `${classPrefix}-head`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-head-content`,\n    style: {\n      height: headHeight\n    }\n  }, renderStatusText())), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children));\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,KAAK,QAAQ,mBAAmB;AACzC,MAAMC,WAAW,GAAG,qBAAqB;AACzC,OAAO,MAAMC,YAAY,GAAG;EAC1BC,WAAW,EAAE,MAAM;EACnBC,cAAc,EAAE,QAAQ;EACxBC,cAAc,EAAE,QAAQ;EACxBC,YAAY,EAAE,MAAM;EACpBC,aAAa,EAAE,GAAG;EAClBC,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAEA,CAAA,KAAM,CAAC;AACpB,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGC,CAAC,IAAI;EAChC,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJC;EACF,CAAC,GAAGf,SAAS,CAAC,CAAC;EACf,MAAMgB,KAAK,GAAGxB,UAAU,CAACW,YAAY,EAAE;IACrCG,cAAc,EAAE,GAAGS,MAAM,CAACE,MAAM,CAACC,OAAO,KAAK;IAC7Cd,WAAW,EAAEW,MAAM,CAACJ,aAAa,CAACQ,OAAO;IACzCd,cAAc,EAAEU,MAAM,CAACJ,aAAa,CAACS,UAAU;IAC/Cb,YAAY,EAAEQ,MAAM,CAACJ,aAAa,CAACU;EACrC,CAAC,EAAET,CAAC,CAAC;EACL,MAAMU,UAAU,GAAG,CAACT,EAAE,GAAGG,KAAK,CAACM,UAAU,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGf,SAAS,CAAC,EAAE,CAAC;EACzF,MAAMyB,SAAS,GAAG,CAACT,EAAE,GAAGE,KAAK,CAACO,SAAS,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGhB,SAAS,CAAC,EAAE,CAAC;EACvF,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAACmC,YAAY,EAAEC,GAAG,CAAC,GAAGjC,SAAS,CAAC,OAAO;IAC3CkC,IAAI,EAAE;MACJC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,UAAU,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8C,UAAU,GAAG9C,MAAM,CAAC,KAAK,CAAC;EAChC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIwB,EAAE;IACN,CAACA,EAAE,GAAGsB,UAAU,CAACE,OAAO,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EAC3G,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,KAAK,GAAGA,CAAA,KAAM;IAClB,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC5Bd,GAAG,CAACe,KAAK,CAAC;QACRC,EAAE,EAAE;UACFd,MAAM,EAAE;QACV,CAAC;QACDe,SAASA,CAAA,EAAG;UACVnB,SAAS,CAAC,SAAS,CAAC;UACpBgB,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,SAASI,SAASA,CAAA,EAAG;IACnB,OAAO1D,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClDwC,GAAG,CAACe,KAAK,CAAC;QACRb,MAAM,EAAEP;MACV,CAAC,CAAC;MACFG,SAAS,CAAC,YAAY,CAAC;MACvB,IAAI;QACF,MAAMT,KAAK,CAACN,SAAS,CAAC,CAAC;QACvBe,SAAS,CAAC,UAAU,CAAC;MACvB,CAAC,CAAC,OAAOqB,CAAC,EAAE;QACVP,KAAK,CAAC,CAAC;QACP,MAAMO,CAAC;MACT;MACA,IAAI9B,KAAK,CAACR,aAAa,GAAG,CAAC,EAAE;QAC3B,MAAMP,KAAK,CAACe,KAAK,CAACR,aAAa,CAAC;MAClC;MACA+B,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;EACJ;EACA5C,OAAO,CAACoD,KAAK,IAAI;IACf,IAAIvB,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,EAAE;IACtD,MAAM;MACJwB;IACF,CAAC,GAAGD,KAAK;IACT,IAAIA,KAAK,CAACE,IAAI,EAAE;MACdb,UAAU,CAACC,OAAO,GAAG,KAAK;MAC1B,IAAIb,MAAM,KAAK,YAAY,EAAE;QAC3BqB,SAAS,CAAC,CAAC;MACb,CAAC,MAAM;QACLlB,GAAG,CAACe,KAAK,CAAC;UACRb,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;MACA;IACF;IACA,MAAM,GAAGqB,CAAC,CAAC,GAAGH,KAAK,CAACI,QAAQ;IAC5B,MAAMC,OAAO,GAAGC,IAAI,CAACC,IAAI,CAACJ,CAAC,CAAC;IAC5B,IAAIH,KAAK,CAACQ,KAAK,IAAIH,OAAO,GAAG,CAAC,EAAE;MAC9B,MAAMI,MAAM,GAAGT,KAAK,CAACC,KAAK,CAACQ,MAAM;MACjC,IAAI,CAACA,MAAM,IAAI,EAAEA,MAAM,YAAYC,OAAO,CAAC,EAAE;MAC7C,IAAIC,YAAY,GAAG9D,eAAe,CAAC4D,MAAM,CAAC;MAC1C,OAAO,IAAI,EAAE;QACX,IAAI,CAACE,YAAY,EAAE;QACnB,MAAMC,SAAS,GAAGC,YAAY,CAACF,YAAY,CAAC;QAC5C,IAAIC,SAAS,GAAG,CAAC,EAAE;UACjB;QACF;QACA,IAAID,YAAY,YAAYG,MAAM,EAAE;UAClC;QACF;QACAH,YAAY,GAAG9D,eAAe,CAAC8D,YAAY,CAACI,UAAU,CAAC;MACzD;MACA1B,UAAU,CAACC,OAAO,GAAG,IAAI;MACzB,SAASuB,YAAYA,CAACG,OAAO,EAAE;QAC7B,OAAO,WAAW,IAAIA,OAAO,GAAGA,OAAO,CAACJ,SAAS,GAAGI,OAAO,CAACC,OAAO;MACrE;IACF;IACA,IAAI,CAAC5B,UAAU,CAACC,OAAO,EAAE;IACzB,IAAIW,KAAK,CAACiB,UAAU,EAAE;MACpBjB,KAAK,CAACkB,cAAc,CAAC,CAAC;IACxB;IACAlB,KAAK,CAACmB,eAAe,CAAC,CAAC;IACvB,MAAMtC,MAAM,GAAGwB,IAAI,CAACe,GAAG,CAACrE,uBAAuB,CAACqD,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE9B,UAAU,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IACvFK,GAAG,CAACe,KAAK,CAAC;MACRb;IACF,CAAC,CAAC;IACFJ,SAAS,CAACI,MAAM,GAAGN,SAAS,GAAG,YAAY,GAAG,SAAS,CAAC;EAC1D,CAAC,EAAE;IACD8C,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE,GAAG;IACTf,MAAM,EAAErB,UAAU;IAClBqC,OAAO,EAAE,CAACxD,KAAK,CAACP,QAAQ;IACxBgE,YAAY,EAAE5E,eAAe,GAAG;MAC9B6E,OAAO,EAAE;IACX,CAAC,GAAGC;EACN,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/D,EAAE;IACN,IAAIG,KAAK,CAAC6D,UAAU,EAAE;MACpB,OAAO,CAAChE,EAAE,GAAGG,KAAK,CAAC6D,UAAU,MAAM,IAAI,IAAIhE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiE,IAAI,CAAC9D,KAAK,EAAEQ,MAAM,CAAC;IAC5F;IACA,IAAIA,MAAM,KAAK,SAAS,EAAE,OAAOR,KAAK,CAACZ,WAAW;IAClD,IAAIoB,MAAM,KAAK,YAAY,EAAE,OAAOR,KAAK,CAACX,cAAc;IACxD,IAAImB,MAAM,KAAK,YAAY,EAAE,OAAOR,KAAK,CAACV,cAAc;IACxD,IAAIkB,MAAM,KAAK,UAAU,EAAE,OAAOR,KAAK,CAACT,YAAY;EACtD,CAAC;EACD,OAAOnB,KAAK,CAAC2F,aAAa,CAACtF,QAAQ,CAACuF,GAAG,EAAE;IACvCC,GAAG,EAAE9C,UAAU;IACf+C,SAAS,EAAEhF;EACb,CAAC,EAAEd,KAAK,CAAC2F,aAAa,CAACtF,QAAQ,CAACuF,GAAG,EAAE;IACnCG,KAAK,EAAEzD,YAAY;IACnBwD,SAAS,EAAE,GAAGhF,WAAW;EAC3B,CAAC,EAAEd,KAAK,CAAC2F,aAAa,CAAC,KAAK,EAAE;IAC5BG,SAAS,EAAE,GAAGhF,WAAW,eAAe;IACxCiF,KAAK,EAAE;MACLtD,MAAM,EAAEP;IACV;EACF,CAAC,EAAEsD,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAExF,KAAK,CAAC2F,aAAa,CAAC,KAAK,EAAE;IAClDG,SAAS,EAAE,GAAGhF,WAAW;EAC3B,CAAC,EAAEc,KAAK,CAACoE,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}