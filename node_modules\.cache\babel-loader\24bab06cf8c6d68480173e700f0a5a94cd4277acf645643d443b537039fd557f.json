{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\nvar useDebouncePlugin = function (fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n    debounceLeading = _a.debounceLeading,\n    debounceTrailing = _a.debounceTrailing,\n    debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef();\n  var options = useMemo(function () {\n    var ret = {};\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options);\n      // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n  if (!debounceWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useDebouncePlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}