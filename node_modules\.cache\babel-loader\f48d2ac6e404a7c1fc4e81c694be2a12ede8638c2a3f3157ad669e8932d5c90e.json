{"ast": null, "code": "import \"./avatar.css\";\nimport { Avatar } from './avatar';\nexport default Avatar;", "map": {"version": 3, "names": ["Avatar"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/avatar/index.js"], "sourcesContent": ["import \"./avatar.css\";\nimport { Avatar } from './avatar';\nexport default Avatar;"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAASA,MAAM,QAAQ,UAAU;AACjC,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}