{"ast": null, "code": "import * as React from \"react\";\nfunction UserCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserCircleOutline-UserCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24.5,12 C20.7258932,12 17.6514878,14.9957724 17.5250256,18.7390798 L17.5209788,18.9790078 L17.5209788,23.0209922 C17.5209788,25.4528116 18.7728519,27.6635411 20.7856095,28.9310701 L21.0123556,29.0679885 L22.3083607,29.817775 C22.4317831,29.8891793 22.5078628,30.0208861 22.5080524,30.1634751 L22.5125654,33.5568164 C22.512762,33.7046068 22.4314515,33.8404631 22.3011089,33.9101252 C18.6470845,35.8632309 15.9065663,37.3280602 14.0795541,38.3046131 C13.7325118,38.49011 13.2119484,38.7683554 12.5178638,39.1393492 C13.1901424,39.6246071 13.7032785,39.97614 14.057272,40.1939479 C16.9495924,41.9735574 20.3549269,43 24,43 C27.8545718,43 31.4410555,41.8521782 34.4362962,39.8796894 C34.7630477,39.6645102 35.2368715,39.3201316 35.8577676,38.8465537 C35.2025395,38.4934662 34.7111185,38.2286507 34.3835045,38.0521069 C32.6755485,37.1317284 30.1136146,35.7511607 26.6977026,33.9104037 C26.568021,33.8405322 26.4872374,33.7050388 26.4874333,33.557732 L26.4919476,30.1634751 C26.4921372,30.0208861 26.5682169,29.8891793 26.6916393,29.817775 L27.9876444,29.0679885 C30.0560904,27.8713167 31.3818366,25.7046996 31.4738944,23.2902478 L31.4790212,23.0209922 L31.4790212,18.9790078 C31.4790212,15.1246082 28.354407,12 24.5,12 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,28.3991384 6.49505588,32.4490877 9.00478741,35.6694678 C9.21143747,35.9346323 9.52211339,36.3008617 9.93681515,36.768156 C10.059676,36.9064357 10.2613193,36.9425236 10.4244626,36.8553439 C10.9825477,36.5571172 11.4114373,36.3279293 11.7111314,36.1677803 C13.4443214,35.2416069 16.0441065,33.8523468 19.5104865,32 L19.5100406,31.6647285 C16.5275485,29.9392476 14.520973,26.7144498 14.520973,23.0209922 L14.520973,18.9790078 C14.520973,13.467754 18.9887356,9 24.5,9 C30.0112644,9 34.479027,13.467754 34.479027,18.9790078 L34.479027,23.0209922 C34.479027,26.7144498 32.4724515,29.9392476 29.4899594,31.6647285 L29.4895135,32 C32.7497553,33.7427066 35.1949368,35.0497365 36.8250577,35.9210897 C37.0767494,36.0556273 37.4335818,36.2463659 37.8955547,36.4933056 C38.0622353,36.5824462 38.2685533,36.5427142 38.390238,36.3980722 C38.8092897,35.899961 39.1223635,35.509765 39.3294594,35.2274842 C41.6371178,32.082045 43,28.2001816 43,24 C43,13.5065898 34.4934102,5 24,5 Z\",\n    id: \"UserCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UserCircleOutline;", "map": {"version": 3, "names": ["React", "UserCircleOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/UserCircleOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UserCircleOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserCircleOutline-UserCircleOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserCircleOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserCircleOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24.5,12 C20.7258932,12 17.6514878,14.9957724 17.5250256,18.7390798 L17.5209788,18.9790078 L17.5209788,23.0209922 C17.5209788,25.4528116 18.7728519,27.6635411 20.7856095,28.9310701 L21.0123556,29.0679885 L22.3083607,29.817775 C22.4317831,29.8891793 22.5078628,30.0208861 22.5080524,30.1634751 L22.5125654,33.5568164 C22.512762,33.7046068 22.4314515,33.8404631 22.3011089,33.9101252 C18.6470845,35.8632309 15.9065663,37.3280602 14.0795541,38.3046131 C13.7325118,38.49011 13.2119484,38.7683554 12.5178638,39.1393492 C13.1901424,39.6246071 13.7032785,39.97614 14.057272,40.1939479 C16.9495924,41.9735574 20.3549269,43 24,43 C27.8545718,43 31.4410555,41.8521782 34.4362962,39.8796894 C34.7630477,39.6645102 35.2368715,39.3201316 35.8577676,38.8465537 C35.2025395,38.4934662 34.7111185,38.2286507 34.3835045,38.0521069 C32.6755485,37.1317284 30.1136146,35.7511607 26.6977026,33.9104037 C26.568021,33.8405322 26.4872374,33.7050388 26.4874333,33.557732 L26.4919476,30.1634751 C26.4921372,30.0208861 26.5682169,29.8891793 26.6916393,29.817775 L27.9876444,29.0679885 C30.0560904,27.8713167 31.3818366,25.7046996 31.4738944,23.2902478 L31.4790212,23.0209922 L31.4790212,18.9790078 C31.4790212,15.1246082 28.354407,12 24.5,12 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,28.3991384 6.49505588,32.4490877 9.00478741,35.6694678 C9.21143747,35.9346323 9.52211339,36.3008617 9.93681515,36.768156 C10.059676,36.9064357 10.2613193,36.9425236 10.4244626,36.8553439 C10.9825477,36.5571172 11.4114373,36.3279293 11.7111314,36.1677803 C13.4443214,35.2416069 16.0441065,33.8523468 19.5104865,32 L19.5100406,31.6647285 C16.5275485,29.9392476 14.520973,26.7144498 14.520973,23.0209922 L14.520973,18.9790078 C14.520973,13.467754 18.9887356,9 24.5,9 C30.0112644,9 34.479027,13.467754 34.479027,18.9790078 L34.479027,23.0209922 C34.479027,26.7144498 32.4724515,29.9392476 29.4899594,31.6647285 L29.4895135,32 C32.7497553,33.7427066 35.1949368,35.0497365 36.8250577,35.9210897 C37.0767494,36.0556273 37.4335818,36.2463659 37.8955547,36.4933056 C38.0622353,36.5824462 38.2685533,36.5427142 38.390238,36.3980722 C38.8092897,35.899961 39.1223635,35.509765 39.3294594,35.2274842 C41.6371178,32.082045 43,28.2001816 43,24 C43,13.5065898 34.4934102,5 24,5 Z\",\n    id: \"UserCircleOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UserCircleOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,qCAAqC;IACzCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6zEAA6zE;IACh0ER,EAAE,EAAE,gCAAgC;IACpCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}