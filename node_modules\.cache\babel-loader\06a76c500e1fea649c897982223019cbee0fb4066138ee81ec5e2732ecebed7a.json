{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider-mark`;\nconst Marks = ({\n  marks,\n  upperBound,\n  lowerBound,\n  max,\n  min\n}) => {\n  const marksKeys = Object.keys(marks);\n  const range = max - min;\n  const elements = marksKeys.map(parseFloat).sort((a, b) => a - b).filter(point => point >= min && point <= max).map(point => {\n    const markPoint = marks[point];\n    if (!markPoint && markPoint !== 0) {\n      return null;\n    }\n    const isActive = point <= upperBound && point >= lowerBound;\n    const markClassName = classNames({\n      [`${classPrefix}-text`]: true,\n      [`${classPrefix}-text-active`]: isActive\n    });\n    const style = {\n      left: `${(point - min) / range * 100}%`\n    };\n    return React.createElement(\"span\", {\n      className: markClassName,\n      style: style,\n      key: point\n    }, markPoint);\n  });\n  return React.createElement(\"div\", {\n    className: classPrefix\n  }, elements);\n};\nexport default Marks;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}