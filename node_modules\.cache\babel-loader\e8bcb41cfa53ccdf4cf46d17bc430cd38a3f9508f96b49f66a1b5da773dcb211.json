{"ast": null, "code": "import \"./tree-select.css\";\nimport { TreeSelect } from './tree-select';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Multiple } from './multiple';\nexport default attachPropertiesToComponent(TreeSelect, {\n  Multiple\n});", "map": {"version": 3, "names": ["TreeSelect", "attachPropertiesToComponent", "Multiple"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/tree-select/index.js"], "sourcesContent": ["import \"./tree-select.css\";\nimport { TreeSelect } from './tree-select';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Multiple } from './multiple';\nexport default attachPropertiesToComponent(TreeSelect, {\n  Multiple\n});"], "mappings": "AAAA,OAAO,mBAAmB;AAC1B,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,QAAQ,QAAQ,YAAY;AACrC,eAAeD,2BAA2B,CAACD,UAAU,EAAE;EACrDE;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}