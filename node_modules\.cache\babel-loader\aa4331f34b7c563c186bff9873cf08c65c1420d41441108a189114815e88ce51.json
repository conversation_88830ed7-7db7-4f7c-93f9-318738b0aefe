{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport isDev from '../../utils/isDev';\nimport Fetch from './Fetch';\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  var _a = options.manual,\n    manual = _a === void 0 ? false : _a,\n    _b = options.ready,\n    ready = _b === void 0 ? true : _b,\n    rest = __rest(options, [\"manual\", \"ready\"]);\n  if (isDev) {\n    if (options.defaultParams && !Array.isArray(options.defaultParams)) {\n      console.warn(\"expected defaultParams is array, got \".concat(typeof options.defaultParams));\n    }\n  }\n  var fetchOptions = __assign({\n    manual: manual,\n    ready: ready\n  }, rest);\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions;\n  // run all plugins hooks\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual && ready) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || [];\n      // @ts-ignore\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\nexport default useRequestImplement;", "map": {"version": 3, "names": ["__assign", "__read", "__rest", "__spread<PERSON><PERSON>y", "useCreation", "useLatest", "useMemoizedFn", "useMount", "useUnmount", "useUpdate", "isDev", "<PERSON>tch", "useRequestImplement", "service", "options", "plugins", "_a", "manual", "_b", "ready", "rest", "defaultParams", "Array", "isArray", "console", "warn", "concat", "fetchOptions", "serviceRef", "update", "fetchInstance", "initState", "map", "p", "onInit", "call", "filter", "Boolean", "Object", "assign", "apply", "pluginImpls", "params", "state", "run", "cancel", "loading", "data", "error", "bind", "refresh", "refreshAsync", "runAsync", "mutate"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/useRequestImplement.js"], "sourcesContent": ["import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport isDev from '../../utils/isDev';\nimport Fetch from './Fetch';\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  var _a = options.manual,\n    manual = _a === void 0 ? false : _a,\n    _b = options.ready,\n    ready = _b === void 0 ? true : _b,\n    rest = __rest(options, [\"manual\", \"ready\"]);\n  if (isDev) {\n    if (options.defaultParams && !Array.isArray(options.defaultParams)) {\n      console.warn(\"expected defaultParams is array, got \".concat(typeof options.defaultParams));\n    }\n  }\n  var fetchOptions = __assign({\n    manual: manual,\n    ready: ready\n  }, rest);\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions;\n  // run all plugins hooks\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual && ready) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || [];\n      // @ts-ignore\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\nexport default useRequestImplement;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC/D,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtD,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,EAAE;EACd;EACA,IAAIC,EAAE,GAAGF,OAAO,CAACG,MAAM;IACrBA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACnCE,EAAE,GAAGJ,OAAO,CAACK,KAAK;IAClBA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjCE,IAAI,GAAGlB,MAAM,CAACY,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;EAC7C,IAAIJ,KAAK,EAAE;IACT,IAAII,OAAO,CAACO,aAAa,IAAI,CAACC,KAAK,CAACC,OAAO,CAACT,OAAO,CAACO,aAAa,CAAC,EAAE;MAClEG,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAACC,MAAM,CAAC,OAAOZ,OAAO,CAACO,aAAa,CAAC,CAAC;IAC5F;EACF;EACA,IAAIM,YAAY,GAAG3B,QAAQ,CAAC;IAC1BiB,MAAM,EAAEA,MAAM;IACdE,KAAK,EAAEA;EACT,CAAC,EAAEC,IAAI,CAAC;EACR,IAAIQ,UAAU,GAAGvB,SAAS,CAACQ,OAAO,CAAC;EACnC,IAAIgB,MAAM,GAAGpB,SAAS,CAAC,CAAC;EACxB,IAAIqB,aAAa,GAAG1B,WAAW,CAAC,YAAY;IAC1C,IAAI2B,SAAS,GAAGhB,OAAO,CAACiB,GAAG,CAAC,UAAUC,CAAC,EAAE;MACvC,IAAIjB,EAAE;MACN,OAAO,CAACA,EAAE,GAAGiB,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACC,MAAM,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACF,CAAC,EAAEN,YAAY,CAAC;IAC5H,CAAC,CAAC,CAACS,MAAM,CAACC,OAAO,CAAC;IAClB,OAAO,IAAI1B,KAAK,CAACiB,UAAU,EAAED,YAAY,EAAEE,MAAM,EAAES,MAAM,CAACC,MAAM,CAACC,KAAK,CAACF,MAAM,EAAEnC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC8B,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EAChI,CAAC,EAAE,EAAE,CAAC;EACND,aAAa,CAAChB,OAAO,GAAGa,YAAY;EACpC;EACAG,aAAa,CAACW,WAAW,GAAG1B,OAAO,CAACiB,GAAG,CAAC,UAAUC,CAAC,EAAE;IACnD,OAAOA,CAAC,CAACH,aAAa,EAAEH,YAAY,CAAC;EACvC,CAAC,CAAC;EACFpB,QAAQ,CAAC,YAAY;IACnB,IAAI,CAACU,MAAM,IAAIE,KAAK,EAAE;MACpB;MACA,IAAIuB,MAAM,GAAGZ,aAAa,CAACa,KAAK,CAACD,MAAM,IAAI5B,OAAO,CAACO,aAAa,IAAI,EAAE;MACtE;MACAS,aAAa,CAACc,GAAG,CAACJ,KAAK,CAACV,aAAa,EAAE3B,aAAa,CAAC,EAAE,EAAEF,MAAM,CAACyC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IAClF;EACF,CAAC,CAAC;EACFlC,UAAU,CAAC,YAAY;IACrBsB,aAAa,CAACe,MAAM,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,OAAO;IACLC,OAAO,EAAEhB,aAAa,CAACa,KAAK,CAACG,OAAO;IACpCC,IAAI,EAAEjB,aAAa,CAACa,KAAK,CAACI,IAAI;IAC9BC,KAAK,EAAElB,aAAa,CAACa,KAAK,CAACK,KAAK;IAChCN,MAAM,EAAEZ,aAAa,CAACa,KAAK,CAACD,MAAM,IAAI,EAAE;IACxCG,MAAM,EAAEvC,aAAa,CAACwB,aAAa,CAACe,MAAM,CAACI,IAAI,CAACnB,aAAa,CAAC,CAAC;IAC/DoB,OAAO,EAAE5C,aAAa,CAACwB,aAAa,CAACoB,OAAO,CAACD,IAAI,CAACnB,aAAa,CAAC,CAAC;IACjEqB,YAAY,EAAE7C,aAAa,CAACwB,aAAa,CAACqB,YAAY,CAACF,IAAI,CAACnB,aAAa,CAAC,CAAC;IAC3Ec,GAAG,EAAEtC,aAAa,CAACwB,aAAa,CAACc,GAAG,CAACK,IAAI,CAACnB,aAAa,CAAC,CAAC;IACzDsB,QAAQ,EAAE9C,aAAa,CAACwB,aAAa,CAACsB,QAAQ,CAACH,IAAI,CAACnB,aAAa,CAAC,CAAC;IACnEuB,MAAM,EAAE/C,aAAa,CAACwB,aAAa,CAACuB,MAAM,CAACJ,IAAI,CAACnB,aAAa,CAAC;EAChE,CAAC;AACH;AACA,eAAelB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}