{"ast": null, "code": "/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n  'use strict';\n\n  var document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n  var isCommonjs = typeof module !== 'undefined' && module.exports;\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n    // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n    // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n    for (; i < l; i++) {\n      val = fnMap[i];\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n        return ret;\n      }\n    }\n    return false;\n  }();\n  var eventNameMap = {\n    change: fn.fullscreenchange,\n    error: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function (element, options) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function () {\n          this.off('change', onFullScreenEntered);\n          resolve();\n        }.bind(this);\n        this.on('change', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen](options);\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      }.bind(this));\n    },\n    exit: function () {\n      return new Promise(function (resolve, reject) {\n        if (!this.isFullscreen) {\n          resolve();\n          return;\n        }\n        var onFullScreenExit = function () {\n          this.off('change', onFullScreenExit);\n          resolve();\n        }.bind(this);\n        this.on('change', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      }.bind(this));\n    },\n    toggle: function (element, options) {\n      return this.isFullscreen ? this.exit() : this.request(element, options);\n    },\n    onchange: function (callback) {\n      this.on('change', callback);\n    },\n    onerror: function (callback) {\n      this.on('error', callback);\n    },\n    on: function (event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.addEventListener(eventName, callback, false);\n      }\n    },\n    off: function (event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.removeEventListener(eventName, callback, false);\n      }\n    },\n    raw: fn\n  };\n  if (!fn) {\n    if (isCommonjs) {\n      module.exports = {\n        isEnabled: false\n      };\n    } else {\n      window.screenfull = {\n        isEnabled: false\n      };\n    }\n    return;\n  }\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function () {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function () {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function () {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n  if (isCommonjs) {\n    module.exports = screenfull;\n  } else {\n    window.screenfull = screenfull;\n  }\n})();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}