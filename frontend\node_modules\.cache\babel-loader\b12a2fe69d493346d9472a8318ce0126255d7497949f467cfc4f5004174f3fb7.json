{"ast": null, "code": "import { useEffect, useRef } from 'react';\nvar diffTwoDeps = function (deps1, deps2) {\n  //Let's do a reference equality check on 2 dependency list.\n  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  //As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_ele, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_ele, idx) {\n    return idx;\n  }) : [];\n};\nvar useTrackedEffect = function (effect, deps) {\n  var previousDepsRef = useRef();\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\nexport default useTrackedEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}