{"ast": null, "code": "import { isMemo, isFragment } from 'react-is';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction shouldConstruct(Component) {\n  const prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n// https://github.com/facebook/react/blob/ce13860281f833de8a3296b7a3dad9caced102e9/packages/react-reconciler/src/ReactFiber.new.js#L225\nfunction isSimpleFunctionComponent(type) {\n  return typeof type === 'function' && !shouldConstruct(type) && type.defaultProps === undefined;\n}\nexport function isSafeSetRefComponent(component) {\n  if (isFragment(component)) return false;\n  if (isMemo(component)) return isSafeSetRefComponent(component.type);\n  return !isSimpleFunctionComponent(component.type);\n}", "map": {"version": 3, "names": ["isMemo", "isFragment", "toArray", "candidate", "undefined", "Array", "isArray", "shouldConstruct", "Component", "prototype", "isReactComponent", "isSimpleFunctionComponent", "type", "defaultProps", "isSafeSetRefComponent", "component"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/form/utils.js"], "sourcesContent": ["import { isMemo, isFragment } from 'react-is';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction shouldConstruct(Component) {\n  const prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n// https://github.com/facebook/react/blob/ce13860281f833de8a3296b7a3dad9caced102e9/packages/react-reconciler/src/ReactFiber.new.js#L225\nfunction isSimpleFunctionComponent(type) {\n  return typeof type === 'function' && !shouldConstruct(type) && type.defaultProps === undefined;\n}\nexport function isSafeSetRefComponent(component) {\n  if (isFragment(component)) return false;\n  if (isMemo(component)) return isSafeSetRefComponent(component.type);\n  return !isSimpleFunctionComponent(component.type);\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,UAAU,QAAQ,UAAU;AAC7C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjC,IAAIA,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,KAAK,EAAE,OAAO,EAAE;EAC7D,OAAOE,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC3D;AACA;AACA,SAASI,eAAeA,CAACC,SAAS,EAAE;EAClC,MAAMC,SAAS,GAAGD,SAAS,CAACC,SAAS;EACrC,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAACC,gBAAgB,CAAC;AACpD;AACA;AACA,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EACvC,OAAO,OAAOA,IAAI,KAAK,UAAU,IAAI,CAACL,eAAe,CAACK,IAAI,CAAC,IAAIA,IAAI,CAACC,YAAY,KAAKT,SAAS;AAChG;AACA,OAAO,SAASU,qBAAqBA,CAACC,SAAS,EAAE;EAC/C,IAAId,UAAU,CAACc,SAAS,CAAC,EAAE,OAAO,KAAK;EACvC,IAAIf,MAAM,CAACe,SAAS,CAAC,EAAE,OAAOD,qBAAqB,CAACC,SAAS,CAACH,IAAI,CAAC;EACnE,OAAO,CAACD,yBAAyB,CAACI,SAAS,CAACH,IAAI,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}