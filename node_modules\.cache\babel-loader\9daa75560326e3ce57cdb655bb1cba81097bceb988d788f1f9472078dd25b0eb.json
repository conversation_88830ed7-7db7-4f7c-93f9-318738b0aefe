{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nexport default function isDocumentVisible() {\n  if (isBrowser) {\n    return document.visibilityState !== 'hidden';\n  }\n  return true;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "document", "visibilityState"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nexport default function isDocumentVisible() {\n  if (isBrowser) {\n    return document.visibilityState !== 'hidden';\n  }\n  return true;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAC1C,IAAID,SAAS,EAAE;IACb,OAAOE,QAAQ,CAACC,eAAe,KAAK,QAAQ;EAC9C;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}