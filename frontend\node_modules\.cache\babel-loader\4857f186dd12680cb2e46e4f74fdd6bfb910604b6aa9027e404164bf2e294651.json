{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\nexport default useTitle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}