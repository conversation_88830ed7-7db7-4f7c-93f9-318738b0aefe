{"ast": null, "code": "import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\nvar usePollingPlugin = function (fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n    _b = _a.pollingWhenHidden,\n    pollingWhenHidden = _b === void 0 ? true : _b,\n    _c = _a.pollingErrorRetryCount,\n    pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef();\n  var unsubscribeRef = useRef();\n  var countRef = useRef(0);\n  var stopPolling = function () {\n    var _a;\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n  if (!pollingInterval) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      stopPolling();\n    },\n    onError: function () {\n      countRef.current += 1;\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onFinally: function () {\n      if (pollingErrorRetryCount === -1 ||\n      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      stopPolling();\n    }\n  };\n};\nexport default usePollingPlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}