{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useTimeout = function (fn, delay) {\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setTimeout(timerCallback, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n};\nexport default useTimeout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}