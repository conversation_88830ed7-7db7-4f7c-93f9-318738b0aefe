{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { EmptyIcon } from './empty-icon';\nconst classPrefix = `adm-empty`;\n/** @deprecated Empty has been deprecated and will be removed in the next major version. */\nexport const Empty = props => {\n  function renderImageNode() {\n    const {\n      image\n    } = props;\n    if (image === undefined) {\n      return React.createElement(EmptyIcon, {\n        className: `${classPrefix}-image`,\n        style: props.imageStyle\n      });\n    }\n    if (typeof image === 'string') {\n      return React.createElement(\"img\", {\n        className: `${classPrefix}-image`,\n        style: props.imageStyle,\n        src: image,\n        alt: 'empty'\n      });\n    }\n    return image;\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-image-container`\n  }, renderImageNode()), props.description && React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-description`)\n  }, props.description)));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "EmptyIcon", "classPrefix", "Empty", "props", "renderImageNode", "image", "undefined", "createElement", "className", "style", "imageStyle", "src", "alt", "description"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/empty/empty.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { EmptyIcon } from './empty-icon';\nconst classPrefix = `adm-empty`;\n/** @deprecated Empty has been deprecated and will be removed in the next major version. */\nexport const Empty = props => {\n  function renderImageNode() {\n    const {\n      image\n    } = props;\n    if (image === undefined) {\n      return React.createElement(EmptyIcon, {\n        className: `${classPrefix}-image`,\n        style: props.imageStyle\n      });\n    }\n    if (typeof image === 'string') {\n      return React.createElement(\"img\", {\n        className: `${classPrefix}-image`,\n        style: props.imageStyle,\n        src: image,\n        alt: 'empty'\n      });\n    }\n    return image;\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-image-container`\n  }, renderImageNode()), props.description && React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-description`)\n  }, props.description)));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,SAAS,QAAQ,cAAc;AACxC,MAAMC,WAAW,GAAG,WAAW;AAC/B;AACA,OAAO,MAAMC,KAAK,GAAGC,KAAK,IAAI;EAC5B,SAASC,eAAeA,CAAA,EAAG;IACzB,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,IAAIE,KAAK,KAAKC,SAAS,EAAE;MACvB,OAAOT,KAAK,CAACU,aAAa,CAACP,SAAS,EAAE;QACpCQ,SAAS,EAAE,GAAGP,WAAW,QAAQ;QACjCQ,KAAK,EAAEN,KAAK,CAACO;MACf,CAAC,CAAC;IACJ;IACA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOR,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;QAChCC,SAAS,EAAE,GAAGP,WAAW,QAAQ;QACjCQ,KAAK,EAAEN,KAAK,CAACO,UAAU;QACvBC,GAAG,EAAEN,KAAK;QACVO,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;IACA,OAAOP,KAAK;EACd;EACA,OAAON,eAAe,CAACI,KAAK,EAAEN,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEP;EACb,CAAC,EAAEJ,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGP,WAAW;EAC3B,CAAC,EAAEG,eAAe,CAAC,CAAC,CAAC,EAAED,KAAK,CAACU,WAAW,IAAIhB,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACrEC,SAAS,EAAEV,UAAU,CAAC,GAAGG,WAAW,cAAc;EACpD,CAAC,EAAEE,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}