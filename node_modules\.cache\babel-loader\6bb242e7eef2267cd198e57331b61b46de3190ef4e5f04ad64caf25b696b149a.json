{"ast": null, "code": "import { useIsomorphicLayoutEffect } from 'ahooks';\nexport default function useInputHandleKeyDown({\n  onEnterPress,\n  onKeyDown,\n  nativeInputRef,\n  enterKeyHint\n}) {\n  const handleKeydown = e => {\n    if (onEnterPress && (e.code === 'Enter' || e.keyCode === 13)) {\n      onEnterPress(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  useIsomorphicLayoutEffect(() => {\n    const ele = nativeInputRef.current;\n    if (!enterKeyHint || !ele) return;\n    ele.setAttribute('enterkeyhint', enterKeyHint);\n    return () => {\n      ele.removeAttribute('enterkeyhint');\n    };\n  }, [enterKeyHint]);\n  return handleKeydown;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}