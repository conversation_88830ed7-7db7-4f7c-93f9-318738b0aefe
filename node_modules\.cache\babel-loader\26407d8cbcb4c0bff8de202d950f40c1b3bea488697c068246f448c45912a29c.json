{"ast": null, "code": "import { CloseCircleFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { bound } from '../../utils/bound';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { isIOS } from '../../utils/validate';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport useInputHandleKeyDown from './useInputHandleKeyDown';\nconst classPrefix = `adm-input`;\nconst defaultProps = {\n  defaultValue: '',\n  clearIcon: React.createElement(CloseCircleFill, null),\n  onlyShowClearWhenFocus: true\n};\nexport const Input = forwardRef((props, ref) => {\n  const {\n    locale,\n    input: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const [hasFocus, setHasFocus] = useState(false);\n  const compositionStartRef = useRef(false);\n  const nativeInputRef = useRef(null);\n  const handleKeydown = useInputHandleKeyDown({\n    onEnterPress: mergedProps.onEnterPress,\n    onKeyDown: mergedProps.onKeyDown,\n    nativeInputRef,\n    enterKeyHint: mergedProps.enterKeyHint\n  });\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      setValue('');\n    },\n    focus: () => {\n      var _a;\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      return nativeInputRef.current;\n    }\n  }));\n  function checkValue() {\n    let nextValue = value;\n    if (mergedProps.type === 'number') {\n      const boundValue = nextValue && bound(parseFloat(nextValue), mergedProps.min, mergedProps.max).toString();\n      // fix the display issue of numbers starting with 0\n      if (Number(nextValue) !== Number(boundValue)) {\n        nextValue = boundValue;\n      }\n    }\n    if (nextValue !== value) {\n      setValue(nextValue);\n    }\n  }\n  const shouldShowClear = (() => {\n    if (!mergedProps.clearable || !value || mergedProps.readOnly) return false;\n    if (mergedProps.onlyShowClearWhenFocus) {\n      return hasFocus;\n    } else {\n      return true;\n    }\n  })();\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, mergedProps.disabled && `${classPrefix}-disabled`)\n  }, React.createElement(\"input\", {\n    ref: nativeInputRef,\n    className: `${classPrefix}-element`,\n    value: value,\n    onChange: e => {\n      setValue(e.target.value);\n    },\n    onFocus: e => {\n      var _a;\n      setHasFocus(true);\n      (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onBlur: e => {\n      var _a;\n      setHasFocus(false);\n      checkValue();\n      (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onPaste: mergedProps.onPaste,\n    id: mergedProps.id,\n    placeholder: mergedProps.placeholder,\n    disabled: mergedProps.disabled,\n    readOnly: mergedProps.readOnly,\n    maxLength: mergedProps.maxLength,\n    minLength: mergedProps.minLength,\n    max: mergedProps.max,\n    min: mergedProps.min,\n    autoComplete: mergedProps.autoComplete,\n    enterKeyHint: mergedProps.enterKeyHint,\n    autoFocus: mergedProps.autoFocus,\n    pattern: mergedProps.pattern,\n    inputMode: mergedProps.inputMode,\n    type: mergedProps.type,\n    name: mergedProps.name,\n    autoCapitalize: mergedProps.autoCapitalize,\n    autoCorrect: mergedProps.autoCorrect,\n    onKeyDown: handleKeydown,\n    onKeyUp: mergedProps.onKeyUp,\n    onCompositionStart: e => {\n      var _a;\n      compositionStartRef.current = true;\n      (_a = mergedProps.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      compositionStartRef.current = false;\n      (_a = mergedProps.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onClick: mergedProps.onClick,\n    step: mergedProps.step,\n    role: mergedProps.role,\n    \"aria-valuenow\": mergedProps['aria-valuenow'],\n    \"aria-valuemax\": mergedProps['aria-valuemax'],\n    \"aria-valuemin\": mergedProps['aria-valuemin'],\n    \"aria-label\": mergedProps['aria-label']\n  }), shouldShowClear && React.createElement(\"div\", {\n    className: `${classPrefix}-clear`,\n    onMouseDown: e => {\n      e.preventDefault();\n    },\n    onClick: () => {\n      var _a, _b;\n      setValue('');\n      (_a = mergedProps.onClear) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n      // https://github.com/ant-design/ant-design-mobile/issues/5212\n      if (isIOS() && compositionStartRef.current) {\n        compositionStartRef.current = false;\n        (_b = nativeInputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n      }\n    },\n    \"aria-label\": locale.Input.clear\n  }, mergedProps.clearIcon)));\n});", "map": {"version": 3, "names": ["CloseCircleFill", "classNames", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "bound", "withNativeProps", "usePropsValue", "isIOS", "mergeProps", "useConfig", "useInputHandleKeyDown", "classPrefix", "defaultProps", "defaultValue", "clearIcon", "createElement", "onlyShowClearWhenFocus", "Input", "props", "ref", "locale", "input", "componentConfig", "mergedProps", "value", "setValue", "hasFocus", "setHasFocus", "compositionStartRef", "nativeInputRef", "handleKeydown", "onEnterPress", "onKeyDown", "enterKeyHint", "clear", "focus", "_a", "current", "blur", "nativeElement", "checkValue", "nextValue", "type", "boundValue", "parseFloat", "min", "max", "toString", "Number", "shouldShowClear", "clearable", "readOnly", "className", "disabled", "onChange", "e", "target", "onFocus", "call", "onBlur", "onPaste", "id", "placeholder", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "autoComplete", "autoFocus", "pattern", "inputMode", "name", "autoCapitalize", "autoCorrect", "onKeyUp", "onCompositionStart", "onCompositionEnd", "onClick", "step", "role", "onMouseDown", "preventDefault", "_b", "onClear"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/input/input.js"], "sourcesContent": ["import { CloseCircleFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { bound } from '../../utils/bound';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { isIOS } from '../../utils/validate';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport useInputHandleKeyDown from './useInputHandleKeyDown';\nconst classPrefix = `adm-input`;\nconst defaultProps = {\n  defaultValue: '',\n  clearIcon: React.createElement(CloseCircleFill, null),\n  onlyShowClearWhenFocus: true\n};\nexport const Input = forwardRef((props, ref) => {\n  const {\n    locale,\n    input: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const [hasFocus, setHasFocus] = useState(false);\n  const compositionStartRef = useRef(false);\n  const nativeInputRef = useRef(null);\n  const handleKeydown = useInputHandleKeyDown({\n    onEnterPress: mergedProps.onEnterPress,\n    onKeyDown: mergedProps.onKeyDown,\n    nativeInputRef,\n    enterKeyHint: mergedProps.enterKeyHint\n  });\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      setValue('');\n    },\n    focus: () => {\n      var _a;\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = nativeInputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      return nativeInputRef.current;\n    }\n  }));\n  function checkValue() {\n    let nextValue = value;\n    if (mergedProps.type === 'number') {\n      const boundValue = nextValue && bound(parseFloat(nextValue), mergedProps.min, mergedProps.max).toString();\n      // fix the display issue of numbers starting with 0\n      if (Number(nextValue) !== Number(boundValue)) {\n        nextValue = boundValue;\n      }\n    }\n    if (nextValue !== value) {\n      setValue(nextValue);\n    }\n  }\n  const shouldShowClear = (() => {\n    if (!mergedProps.clearable || !value || mergedProps.readOnly) return false;\n    if (mergedProps.onlyShowClearWhenFocus) {\n      return hasFocus;\n    } else {\n      return true;\n    }\n  })();\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, mergedProps.disabled && `${classPrefix}-disabled`)\n  }, React.createElement(\"input\", {\n    ref: nativeInputRef,\n    className: `${classPrefix}-element`,\n    value: value,\n    onChange: e => {\n      setValue(e.target.value);\n    },\n    onFocus: e => {\n      var _a;\n      setHasFocus(true);\n      (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onBlur: e => {\n      var _a;\n      setHasFocus(false);\n      checkValue();\n      (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onPaste: mergedProps.onPaste,\n    id: mergedProps.id,\n    placeholder: mergedProps.placeholder,\n    disabled: mergedProps.disabled,\n    readOnly: mergedProps.readOnly,\n    maxLength: mergedProps.maxLength,\n    minLength: mergedProps.minLength,\n    max: mergedProps.max,\n    min: mergedProps.min,\n    autoComplete: mergedProps.autoComplete,\n    enterKeyHint: mergedProps.enterKeyHint,\n    autoFocus: mergedProps.autoFocus,\n    pattern: mergedProps.pattern,\n    inputMode: mergedProps.inputMode,\n    type: mergedProps.type,\n    name: mergedProps.name,\n    autoCapitalize: mergedProps.autoCapitalize,\n    autoCorrect: mergedProps.autoCorrect,\n    onKeyDown: handleKeydown,\n    onKeyUp: mergedProps.onKeyUp,\n    onCompositionStart: e => {\n      var _a;\n      compositionStartRef.current = true;\n      (_a = mergedProps.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      compositionStartRef.current = false;\n      (_a = mergedProps.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onClick: mergedProps.onClick,\n    step: mergedProps.step,\n    role: mergedProps.role,\n    \"aria-valuenow\": mergedProps['aria-valuenow'],\n    \"aria-valuemax\": mergedProps['aria-valuemax'],\n    \"aria-valuemin\": mergedProps['aria-valuemin'],\n    \"aria-label\": mergedProps['aria-label']\n  }), shouldShowClear && React.createElement(\"div\", {\n    className: `${classPrefix}-clear`,\n    onMouseDown: e => {\n      e.preventDefault();\n    },\n    onClick: () => {\n      var _a, _b;\n      setValue('');\n      (_a = mergedProps.onClear) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n      // https://github.com/ant-design/ant-design-mobile/issues/5212\n      if (isIOS() && compositionStartRef.current) {\n        compositionStartRef.current = false;\n        (_b = nativeInputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n      }\n    },\n    \"aria-label\": locale.Input.clear\n  }, mergedProps.clearIcon)));\n});"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAEf,KAAK,CAACgB,aAAa,CAAClB,eAAe,EAAE,IAAI,CAAC;EACrDmB,sBAAsB,EAAE;AAC1B,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGjB,UAAU,CAAC,CAACkB,KAAK,EAAEC,GAAG,KAAK;EAC9C,MAAM;IACJC,MAAM;IACNC,KAAK,EAAEC,eAAe,GAAG,CAAC;EAC5B,CAAC,GAAGb,SAAS,CAAC,CAAC;EACf,MAAMc,WAAW,GAAGf,UAAU,CAACI,YAAY,EAAEU,eAAe,EAAEJ,KAAK,CAAC;EACpE,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,aAAa,CAACiB,WAAW,CAAC;EACpD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMyB,mBAAmB,GAAG1B,MAAM,CAAC,KAAK,CAAC;EACzC,MAAM2B,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM4B,aAAa,GAAGpB,qBAAqB,CAAC;IAC1CqB,YAAY,EAAER,WAAW,CAACQ,YAAY;IACtCC,SAAS,EAAET,WAAW,CAACS,SAAS;IAChCH,cAAc;IACdI,YAAY,EAAEV,WAAW,CAACU;EAC5B,CAAC,CAAC;EACFhC,mBAAmB,CAACkB,GAAG,EAAE,OAAO;IAC9Be,KAAK,EAAEA,CAAA,KAAM;MACXT,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC;IACDU,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGP,cAAc,CAACQ,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,KAAK,CAAC,CAAC;IAC/E,CAAC;IACDG,IAAI,EAAEA,CAAA,KAAM;MACV,IAAIF,EAAE;MACN,CAACA,EAAE,GAAGP,cAAc,CAACQ,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC,CAAC;IAC9E,CAAC;IACD,IAAIC,aAAaA,CAAA,EAAG;MAClB,OAAOV,cAAc,CAACQ,OAAO;IAC/B;EACF,CAAC,CAAC,CAAC;EACH,SAASG,UAAUA,CAAA,EAAG;IACpB,IAAIC,SAAS,GAAGjB,KAAK;IACrB,IAAID,WAAW,CAACmB,IAAI,KAAK,QAAQ,EAAE;MACjC,MAAMC,UAAU,GAAGF,SAAS,IAAIrC,KAAK,CAACwC,UAAU,CAACH,SAAS,CAAC,EAAElB,WAAW,CAACsB,GAAG,EAAEtB,WAAW,CAACuB,GAAG,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzG;MACA,IAAIC,MAAM,CAACP,SAAS,CAAC,KAAKO,MAAM,CAACL,UAAU,CAAC,EAAE;QAC5CF,SAAS,GAAGE,UAAU;MACxB;IACF;IACA,IAAIF,SAAS,KAAKjB,KAAK,EAAE;MACvBC,QAAQ,CAACgB,SAAS,CAAC;IACrB;EACF;EACA,MAAMQ,eAAe,GAAG,CAAC,MAAM;IAC7B,IAAI,CAAC1B,WAAW,CAAC2B,SAAS,IAAI,CAAC1B,KAAK,IAAID,WAAW,CAAC4B,QAAQ,EAAE,OAAO,KAAK;IAC1E,IAAI5B,WAAW,CAACP,sBAAsB,EAAE;MACtC,OAAOU,QAAQ;IACjB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAC;EACJ,OAAOrB,eAAe,CAACkB,WAAW,EAAExB,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAC7DqC,SAAS,EAAEtD,UAAU,CAAC,GAAGa,WAAW,EAAE,EAAEY,WAAW,CAAC8B,QAAQ,IAAI,GAAG1C,WAAW,WAAW;EAC3F,CAAC,EAAEZ,KAAK,CAACgB,aAAa,CAAC,OAAO,EAAE;IAC9BI,GAAG,EAAEU,cAAc;IACnBuB,SAAS,EAAE,GAAGzC,WAAW,UAAU;IACnCa,KAAK,EAAEA,KAAK;IACZ8B,QAAQ,EAAEC,CAAC,IAAI;MACb9B,QAAQ,CAAC8B,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAC;IAC1B,CAAC;IACDiC,OAAO,EAAEF,CAAC,IAAI;MACZ,IAAInB,EAAE;MACNT,WAAW,CAAC,IAAI,CAAC;MACjB,CAACS,EAAE,GAAGb,WAAW,CAACkC,OAAO,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACnC,WAAW,EAAEgC,CAAC,CAAC;IACzF,CAAC;IACDI,MAAM,EAAEJ,CAAC,IAAI;MACX,IAAInB,EAAE;MACNT,WAAW,CAAC,KAAK,CAAC;MAClBa,UAAU,CAAC,CAAC;MACZ,CAACJ,EAAE,GAAGb,WAAW,CAACoC,MAAM,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACnC,WAAW,EAAEgC,CAAC,CAAC;IACxF,CAAC;IACDK,OAAO,EAAErC,WAAW,CAACqC,OAAO;IAC5BC,EAAE,EAAEtC,WAAW,CAACsC,EAAE;IAClBC,WAAW,EAAEvC,WAAW,CAACuC,WAAW;IACpCT,QAAQ,EAAE9B,WAAW,CAAC8B,QAAQ;IAC9BF,QAAQ,EAAE5B,WAAW,CAAC4B,QAAQ;IAC9BY,SAAS,EAAExC,WAAW,CAACwC,SAAS;IAChCC,SAAS,EAAEzC,WAAW,CAACyC,SAAS;IAChClB,GAAG,EAAEvB,WAAW,CAACuB,GAAG;IACpBD,GAAG,EAAEtB,WAAW,CAACsB,GAAG;IACpBoB,YAAY,EAAE1C,WAAW,CAAC0C,YAAY;IACtChC,YAAY,EAAEV,WAAW,CAACU,YAAY;IACtCiC,SAAS,EAAE3C,WAAW,CAAC2C,SAAS;IAChCC,OAAO,EAAE5C,WAAW,CAAC4C,OAAO;IAC5BC,SAAS,EAAE7C,WAAW,CAAC6C,SAAS;IAChC1B,IAAI,EAAEnB,WAAW,CAACmB,IAAI;IACtB2B,IAAI,EAAE9C,WAAW,CAAC8C,IAAI;IACtBC,cAAc,EAAE/C,WAAW,CAAC+C,cAAc;IAC1CC,WAAW,EAAEhD,WAAW,CAACgD,WAAW;IACpCvC,SAAS,EAAEF,aAAa;IACxB0C,OAAO,EAAEjD,WAAW,CAACiD,OAAO;IAC5BC,kBAAkB,EAAElB,CAAC,IAAI;MACvB,IAAInB,EAAE;MACNR,mBAAmB,CAACS,OAAO,GAAG,IAAI;MAClC,CAACD,EAAE,GAAGb,WAAW,CAACkD,kBAAkB,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACnC,WAAW,EAAEgC,CAAC,CAAC;IACpG,CAAC;IACDmB,gBAAgB,EAAEnB,CAAC,IAAI;MACrB,IAAInB,EAAE;MACNR,mBAAmB,CAACS,OAAO,GAAG,KAAK;MACnC,CAACD,EAAE,GAAGb,WAAW,CAACmD,gBAAgB,MAAM,IAAI,IAAItC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACnC,WAAW,EAAEgC,CAAC,CAAC;IAClG,CAAC;IACDoB,OAAO,EAAEpD,WAAW,CAACoD,OAAO;IAC5BC,IAAI,EAAErD,WAAW,CAACqD,IAAI;IACtBC,IAAI,EAAEtD,WAAW,CAACsD,IAAI;IACtB,eAAe,EAAEtD,WAAW,CAAC,eAAe,CAAC;IAC7C,eAAe,EAAEA,WAAW,CAAC,eAAe,CAAC;IAC7C,eAAe,EAAEA,WAAW,CAAC,eAAe,CAAC;IAC7C,YAAY,EAAEA,WAAW,CAAC,YAAY;EACxC,CAAC,CAAC,EAAE0B,eAAe,IAAIlD,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IAChDqC,SAAS,EAAE,GAAGzC,WAAW,QAAQ;IACjCmE,WAAW,EAAEvB,CAAC,IAAI;MAChBA,CAAC,CAACwB,cAAc,CAAC,CAAC;IACpB,CAAC;IACDJ,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIvC,EAAE,EAAE4C,EAAE;MACVvD,QAAQ,CAAC,EAAE,CAAC;MACZ,CAACW,EAAE,GAAGb,WAAW,CAAC0D,OAAO,MAAM,IAAI,IAAI7C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACnC,WAAW,CAAC;MACpF;MACA,IAAIhB,KAAK,CAAC,CAAC,IAAIqB,mBAAmB,CAACS,OAAO,EAAE;QAC1CT,mBAAmB,CAACS,OAAO,GAAG,KAAK;QACnC,CAAC2C,EAAE,GAAGnD,cAAc,CAACQ,OAAO,MAAM,IAAI,IAAI2C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,IAAI,CAAC,CAAC;MAC9E;IACF,CAAC;IACD,YAAY,EAAElB,MAAM,CAACH,KAAK,CAACiB;EAC7B,CAAC,EAAEX,WAAW,CAACT,SAAS,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}