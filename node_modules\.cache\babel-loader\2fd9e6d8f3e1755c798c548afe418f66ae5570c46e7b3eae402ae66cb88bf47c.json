{"ast": null, "code": "import * as React from \"react\";\nfunction ChatWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatWrongOutline-ChatWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M20.3502105,18 C20.4562955,18 20.5580357,18.0421415 20.6330499,18.117154 L24.2578932,21.7419127 L27.8827365,18.117154 C27.9577508,18.0421415 28.0594909,18 28.1655759,18 L31.2780812,18 C31.4989951,18 31.6780812,18.1790861 31.6780812,18.4 C31.6780812,18.5060994 31.6359283,18.6078522 31.5608977,18.6828689 L26.3788932,23.8639127 L31.8330203,29.3171338 C31.989243,29.4733305 31.9892641,29.7265965 31.8330673,29.8828192 C31.7580509,29.9578481 31.6562992,30 31.5502011,30 L28.437714,30 C28.3316105,30 28.2298541,29.9578439 28.1548368,29.8828082 L24.2578932,25.9849127 L20.3591524,29.8828711 C20.2841402,29.9578683 20.1824108,30 20.0763381,30 L16.9656638,30 C16.7447499,30 16.5656638,29.8209139 16.5656638,29.6 C16.5656638,29.4939156 16.6078048,29.3921759 16.6828166,29.3171618 L22.1358932,23.8639127 L16.9547482,18.6828448 C16.7985373,18.5266363 16.7985354,18.2733703 16.954744,18.1171594 C17.0297587,18.0421436 17.1315012,18 17.2375888,18 L20.3502105,18 Z\",\n    id: \"ChatWrongOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ChatWrongOutline;", "map": {"version": 3, "names": ["React", "ChatWrongOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ChatWrongOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ChatWrongOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatWrongOutline-ChatWrongOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatWrongOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatWrongOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M20.3502105,18 C20.4562955,18 20.5580357,18.0421415 20.6330499,18.117154 L24.2578932,21.7419127 L27.8827365,18.117154 C27.9577508,18.0421415 28.0594909,18 28.1655759,18 L31.2780812,18 C31.4989951,18 31.6780812,18.1790861 31.6780812,18.4 C31.6780812,18.5060994 31.6359283,18.6078522 31.5608977,18.6828689 L26.3788932,23.8639127 L31.8330203,29.3171338 C31.989243,29.4733305 31.9892641,29.7265965 31.8330673,29.8828192 C31.7580509,29.9578481 31.6562992,30 31.5502011,30 L28.437714,30 C28.3316105,30 28.2298541,29.9578439 28.1548368,29.8828082 L24.2578932,25.9849127 L20.3591524,29.8828711 C20.2841402,29.9578683 20.1824108,30 20.0763381,30 L16.9656638,30 C16.7447499,30 16.5656638,29.8209139 16.5656638,29.6 C16.5656638,29.4939156 16.6078048,29.3921759 16.6828166,29.3171618 L22.1358932,23.8639127 L16.9547482,18.6828448 C16.7985373,18.5266363 16.7985354,18.2733703 16.954744,18.1171594 C17.0297587,18.0421436 17.1315012,18 17.2375888,18 L20.3502105,18 Z\",\n    id: \"ChatWrongOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ChatWrongOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,izDAAizD;IACpzDR,EAAE,EAAE,2CAA2C;IAC/CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}