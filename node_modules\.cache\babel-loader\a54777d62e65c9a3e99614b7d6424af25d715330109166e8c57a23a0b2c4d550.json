{"ast": null, "code": "import { isDev } from './is-dev';\nimport { devError } from './dev-log';\nexport function measureCSSLength(raw) {\n  if (raw === null || raw === undefined || raw === '') {\n    if (isDev) {\n      devError('Global', 'Something went wrong when calculating CSS length. Please report an issue at https://github.com/ant-design/ant-design-mobile/issues/new/choose');\n    }\n    return 0;\n  }\n  const withUnit = raw.trim();\n  if (withUnit.endsWith('px')) {\n    return parseFloat(withUnit);\n  } else if (withUnit.endsWith('rem')) {\n    return parseFloat(withUnit) * parseFloat(window.getComputedStyle(document.documentElement).fontSize);\n  } else if (withUnit.endsWith('vw')) {\n    return parseFloat(withUnit) * window.innerWidth / 100;\n  } else {\n    if (isDev) {\n      devError('Global', `You are using a not supported CSS unit in \\`${raw}\\`. Only \\`px\\` \\`rem\\` and \\`vw\\` are supported.`);\n    }\n    return 0;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}