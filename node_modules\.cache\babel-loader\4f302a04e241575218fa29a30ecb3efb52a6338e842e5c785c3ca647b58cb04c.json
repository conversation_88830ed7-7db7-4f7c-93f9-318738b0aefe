{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\n// support refreshDeps & ready\nvar useAutoRunPlugin = function (fetchInstance, _a) {\n  var manual = _a.manual,\n    _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    _c = _a.defaultParams,\n    defaultParams = _c === void 0 ? [] : _c,\n    _d = _a.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function () {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\nexport default useAutoRunPlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}