{"ast": null, "code": "import \"./steps.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Step } from './step';\nimport { Steps } from './steps';\nexport default attachPropertiesToComponent(Steps, {\n  Step\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Step", "Steps"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/steps/index.js"], "sourcesContent": ["import \"./steps.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Step } from './step';\nimport { Steps } from './steps';\nexport default attachPropertiesToComponent(Steps, {\n  Step\n});"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,KAAK,QAAQ,SAAS;AAC/B,eAAeF,2BAA2B,CAACE,KAAK,EAAE;EAChDD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}