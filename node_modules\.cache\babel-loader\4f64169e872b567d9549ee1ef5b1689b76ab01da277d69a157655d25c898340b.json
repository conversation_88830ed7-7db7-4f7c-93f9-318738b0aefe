{"ast": null, "code": "import { closeFnSet } from './show';\nexport function clear() {\n  closeFnSet.forEach(close => {\n    close();\n  });\n}", "map": {"version": 3, "names": ["closeFnSet", "clear", "for<PERSON>ach", "close"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/dialog/clear.js"], "sourcesContent": ["import { closeFnSet } from './show';\nexport function clear() {\n  closeFnSet.forEach(close => {\n    close();\n  });\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,QAAQ;AACnC,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,UAAU,CAACE,OAAO,CAACC,KAAK,IAAI;IAC1BA,KAAK,CAAC,CAAC;EACT,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}