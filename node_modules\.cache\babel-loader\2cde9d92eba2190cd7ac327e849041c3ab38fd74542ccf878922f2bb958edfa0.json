{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isoWeek = t();\n}(this, function () {\n  \"use strict\";\n\n  var e = \"day\";\n  return function (t, i, s) {\n    var a = function (t) {\n        return t.add(4 - t.isoWeekday(), e);\n      },\n      d = i.prototype;\n    d.isoWeekYear = function () {\n      return a(this).year();\n    }, d.isoWeek = function (t) {\n      if (!this.$utils().u(t)) return this.add(7 * (t - this.isoWeek()), e);\n      var i,\n        d,\n        n,\n        o,\n        r = a(this),\n        u = (i = this.isoWeekYear(), d = this.$u, n = (d ? s.utc : s)().year(i).startOf(\"year\"), o = 4 - n.isoWeekday(), n.isoWeekday() > 4 && (o += 7), n.add(o, e));\n      return r.diff(u, \"week\") + 1;\n    }, d.isoWeekday = function (e) {\n      return this.$utils().u(e) ? this.day() || 7 : this.day(this.day() % 7 ? e : e - 7);\n    };\n    var n = d.startOf;\n    d.startOf = function (e, t) {\n      var i = this.$utils(),\n        s = !!i.u(t) || t;\n      return \"isoweek\" === i.p(e) ? s ? this.date(this.date() - (this.isoWeekday() - 1)).startOf(\"day\") : this.date(this.date() - 1 - (this.isoWeekday() - 1) + 7).endOf(\"day\") : n.bind(this)(e, t);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}