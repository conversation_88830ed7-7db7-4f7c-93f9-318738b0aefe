{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport var SYNC_STORAGE_EVENT_NAME = 'AHOOKS_SYNC_STORAGE_EVENT_NAME';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var storage;\n    var _a = options.listenStorageChange,\n      listenStorageChange = _a === void 0 ? false : _a,\n      _b = options.onError,\n      onError = _b === void 0 ? function (e) {\n        console.error(e);\n      } : _b;\n    // https://github.com/alibaba/hooks/issues/800\n    try {\n      storage = getStorage();\n    } catch (err) {\n      onError(err);\n    }\n    var serializer = function (value) {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    };\n    var deserializer = function (value) {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      return JSON.parse(value);\n    };\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        onError(e);\n      }\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }\n    var _c = __read(useState(getStoredValue), 2),\n      state = _c[0],\n      setState = _c[1];\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n    var updateState = function (value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      if (!listenStorageChange) {\n        setState(currentState);\n      }\n      try {\n        var newValue = void 0;\n        var oldValue = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (isUndef(currentState)) {\n          newValue = null;\n          storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n        } else {\n          newValue = serializer(currentState);\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, newValue);\n        }\n        dispatchEvent(\n        // send custom event to communicate within same page\n        // importantly this should not be a StorageEvent since those cannot\n        // be constructed with a non-built-in storage area\n        new CustomEvent(SYNC_STORAGE_EVENT_NAME, {\n          detail: {\n            key: key,\n            newValue: newValue,\n            oldValue: oldValue,\n            storageArea: storage\n          }\n        }));\n      } catch (e) {\n        onError(e);\n      }\n    };\n    var syncState = function (event) {\n      if (event.key !== key || event.storageArea !== storage) {\n        return;\n      }\n      setState(getStoredValue());\n    };\n    var syncStateFromCustomEvent = function (event) {\n      syncState(event.detail);\n    };\n    // from another document\n    useEventListener('storage', syncState, {\n      enable: listenStorageChange\n    });\n    // from the same document but different hooks\n    useEventListener(SYNC_STORAGE_EVENT_NAME, syncStateFromCustomEvent, {\n      enable: listenStorageChange\n    });\n    return [state, useMemoizedFn(updateState)];\n  }\n  return useStorageState;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}