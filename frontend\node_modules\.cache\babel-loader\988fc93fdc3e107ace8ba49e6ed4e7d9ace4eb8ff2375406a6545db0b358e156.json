{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"name\", \"initialValues\", \"fields\", \"form\", \"preserve\", \"children\", \"component\", \"validateMessages\", \"validateTrigger\", \"onValuesChange\", \"onFieldsChange\", \"onFinish\", \"onFinishFailed\"];\nimport * as React from 'react';\nimport useForm from \"./useForm\";\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport FormContext from \"./FormContext\";\nimport { isSimilar } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nvar Form = function Form(_ref, ref) {\n  var name = _ref.name,\n    initialValues = _ref.initialValues,\n    fields = _ref.fields,\n    form = _ref.form,\n    preserve = _ref.preserve,\n    children = _ref.children,\n    _ref$component = _ref.component,\n    Component = _ref$component === void 0 ? 'form' : _ref$component,\n    validateMessages = _ref.validateMessages,\n    _ref$validateTrigger = _ref.validateTrigger,\n    validateTrigger = _ref$validateTrigger === void 0 ? 'onChange' : _ref$validateTrigger,\n    onValuesChange = _ref.onValuesChange,\n    _onFieldsChange = _ref.onFieldsChange,\n    _onFinish = _ref.onFinish,\n    onFinishFailed = _ref.onFinishFailed,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var formContext = React.useContext(FormContext);\n\n  // We customize handle event since Context will makes all the consumer re-render:\n  // https://reactjs.org/docs/context.html#contextprovider\n  var _useForm = useForm(form),\n    _useForm2 = _slicedToArray(_useForm, 1),\n    formInstance = _useForm2[0];\n  var _getInternalHooks = formInstance.getInternalHooks(HOOK_MARK),\n    useSubscribe = _getInternalHooks.useSubscribe,\n    setInitialValues = _getInternalHooks.setInitialValues,\n    setCallbacks = _getInternalHooks.setCallbacks,\n    setValidateMessages = _getInternalHooks.setValidateMessages,\n    setPreserve = _getInternalHooks.setPreserve,\n    destroyForm = _getInternalHooks.destroyForm;\n\n  // Pass ref with form instance\n  React.useImperativeHandle(ref, function () {\n    return formInstance;\n  });\n\n  // Register form into Context\n  React.useEffect(function () {\n    formContext.registerForm(name, formInstance);\n    return function () {\n      formContext.unregisterForm(name);\n    };\n  }, [formContext, formInstance, name]);\n\n  // Pass props to store\n  setValidateMessages(_objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages));\n  setCallbacks({\n    onValuesChange: onValuesChange,\n    onFieldsChange: function onFieldsChange(changedFields) {\n      formContext.triggerFormChange(name, changedFields);\n      if (_onFieldsChange) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n        _onFieldsChange.apply(void 0, [changedFields].concat(rest));\n      }\n    },\n    onFinish: function onFinish(values) {\n      formContext.triggerFormFinish(name, values);\n      if (_onFinish) {\n        _onFinish(values);\n      }\n    },\n    onFinishFailed: onFinishFailed\n  });\n  setPreserve(preserve);\n\n  // Set initial value, init store value when first mount\n  var mountRef = React.useRef(null);\n  setInitialValues(initialValues, !mountRef.current);\n  if (!mountRef.current) {\n    mountRef.current = true;\n  }\n  React.useEffect(function () {\n    return destroyForm;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // Prepare children by `children` type\n  var childrenNode;\n  var childrenRenderProps = typeof children === 'function';\n  if (childrenRenderProps) {\n    var _values = formInstance.getFieldsValue(true);\n    childrenNode = children(_values, formInstance);\n  } else {\n    childrenNode = children;\n  }\n\n  // Not use subscribe when using render props\n  useSubscribe(!childrenRenderProps);\n\n  // Listen if fields provided. We use ref to save prev data here to avoid additional render\n  var prevFieldsRef = React.useRef();\n  React.useEffect(function () {\n    if (!isSimilar(prevFieldsRef.current || [], fields || [])) {\n      formInstance.setFields(fields || []);\n    }\n    prevFieldsRef.current = fields;\n  }, [fields, formInstance]);\n  var formContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      validateTrigger: validateTrigger\n    });\n  }, [formInstance, validateTrigger]);\n  var wrapperNode = /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: formContextValue\n  }, childrenNode));\n  if (Component === false) {\n    return wrapperNode;\n  }\n  return /*#__PURE__*/React.createElement(Component, _extends({}, restProps, {\n    onSubmit: function onSubmit(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      formInstance.submit();\n    },\n    onReset: function onReset(event) {\n      var _restProps$onReset;\n      event.preventDefault();\n      formInstance.resetFields();\n      (_restProps$onReset = restProps.onReset) === null || _restProps$onReset === void 0 || _restProps$onReset.call(restProps, event);\n    }\n  }), wrapperNode);\n};\nexport default Form;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}