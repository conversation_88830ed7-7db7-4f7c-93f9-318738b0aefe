{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v3\\\\src\\\\components\\\\MobileCameraModal.js\",\n  _s = $RefreshSig$();\n/**\n * 移動端全屏相機組件\n * 提供優化的移動端拍照體驗\n */\n\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { Button } from 'antd-mobile';\nimport { CameraOutline, CloseOutline, RedoOutline, CheckOutline, LoopOutline } from 'antd-mobile-icons';\nimport './MobileCameraModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileCameraModal = ({\n  visible,\n  onClose,\n  onPhotoTaken,\n  cameraManager,\n  target = 'back'\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [isReady, setIsReady] = useState(false);\n  const [supportsCameraSwitch, setSupportsCameraSwitch] = useState(false);\n  const [currentFacingMode, setCurrentFacingMode] = useState('environment');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [showGrid, setShowGrid] = useState(false);\n  const [focusPoint, setFocusPoint] = useState(null);\n\n  // 相機啟動成功回調\n  const handleCameraStart = useCallback(data => {\n    setIsReady(true);\n    if (data.facingMode) {\n      setCurrentFacingMode(data.facingMode);\n    }\n  }, []);\n\n  // 相機錯誤回調\n  const handleCameraError = useCallback(error => {\n    console.error('相機錯誤:', error);\n    setIsReady(false);\n  }, []);\n\n  // 攝像頭切換回調\n  const handleCameraSwitch = useCallback(data => {\n    if (data.facingMode) {\n      setCurrentFacingMode(data.facingMode);\n    }\n  }, []);\n\n  // 關閉相機\n  const handleClose = useCallback(() => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n    }\n    setIsReady(false);\n    setIsCapturing(false);\n    setFocusPoint(null);\n    if (onClose) {\n      onClose();\n    }\n  }, [cameraManager, onClose]);\n\n  // 拍照完成回調\n  const handlePhotoTaken = useCallback(data => {\n    setIsCapturing(false);\n    if (onPhotoTaken) {\n      onPhotoTaken(data);\n    }\n    handleClose();\n  }, [onPhotoTaken, handleClose]);\n\n  // 手動對焦功能\n  const handleFocus = useCallback(event => {\n    if (!isReady || !videoRef.current) return;\n\n    // 獲取點擊位置\n    const rect = videoRef.current.getBoundingClientRect();\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // 計算相對位置\n    const relativeX = x / rect.width * 100;\n    const relativeY = y / rect.height * 100;\n    console.log('手動對焦位置:', {\n      x: relativeX,\n      y: relativeY\n    });\n\n    // 顯示對焦指示器\n    setFocusPoint({\n      x: relativeX,\n      y: relativeY\n    });\n\n    // 嘗試調用瀏覽器對焦API（如果支持）\n    try {\n      var _videoRef$current$src;\n      const videoTrack = (_videoRef$current$src = videoRef.current.srcObject) === null || _videoRef$current$src === void 0 ? void 0 : _videoRef$current$src.getVideoTracks()[0];\n      if (videoTrack && videoTrack.getCapabilities) {\n        const capabilities = videoTrack.getCapabilities();\n        if (capabilities.focusMode) {\n          // 設置對焦模式為手動或連續\n          videoTrack.applyConstraints({\n            advanced: [{\n              focusMode: 'continuous',\n              pointsOfInterest: [{\n                x: relativeX / 100,\n                y: relativeY / 100\n              }]\n            }]\n          }).catch(err => {\n            console.log('對焦約束設置失敗:', err);\n          });\n        }\n      }\n    } catch (error) {\n      console.log('對焦功能不支持:', error);\n    }\n\n    // 清除對焦指示器\n    setTimeout(() => {\n      setFocusPoint(null);\n    }, 1500);\n  }, [isReady]);\n\n  // 切換網格線\n  const toggleGrid = useCallback(() => {\n    setShowGrid(prev => !prev);\n  }, []);\n\n  // 初始化相機\n  const initializeCamera = useCallback(async () => {\n    try {\n      setIsReady(false);\n      console.log('移動端相機初始化開始...', {\n        target,\n        videoElement: !!videoRef.current,\n        canvasElement: !!canvasRef.current\n      });\n\n      // 設置相機管理器回調\n      cameraManager.setCallbacks({\n        cameraStart: handleCameraStart,\n        cameraError: handleCameraError,\n        cameraSwitch: handleCameraSwitch,\n        photoTaken: handlePhotoTaken\n      });\n\n      // 等待DOM元素準備就緒\n      if (!videoRef.current || !canvasRef.current) {\n        await new Promise(resolve => setTimeout(resolve, 100));\n      }\n\n      // 啟動相機\n      await cameraManager.startCamera(target, {\n        videoElement: videoRef.current,\n        canvasElement: canvasRef.current\n      });\n\n      // 檢查是否支持攝像頭切換\n      setSupportsCameraSwitch(cameraManager.supportsCameraSwitch());\n      console.log('移動端相機初始化完成');\n    } catch (error) {\n      console.error('初始化相機失敗:', error);\n      handleCameraError(error);\n    }\n  }, [cameraManager, target, handleCameraStart, handleCameraError, handleCameraSwitch, handlePhotoTaken]);\n\n  // 初始化相機\n  useEffect(() => {\n    if (visible && cameraManager) {\n      initializeCamera();\n    }\n    return () => {\n      if (cameraManager) {\n        cameraManager.stopCamera();\n      }\n    };\n  }, [visible, cameraManager, target, initializeCamera]);\n\n  // 拍照\n  const handleTakePhoto = useCallback(async () => {\n    if (!isReady || isCapturing) {\n      console.log('拍照條件不滿足', {\n        isReady,\n        isCapturing\n      });\n      return;\n    }\n    if (!cameraManager) {\n      console.error('相機管理器未初始化');\n      return;\n    }\n    try {\n      setIsCapturing(true);\n      console.log('移動端開始拍照...');\n\n      // 拍照前短暫延遲，確保對焦穩定\n      await new Promise(resolve => setTimeout(resolve, 200));\n      const result = await cameraManager.takePhoto();\n      if (result && result.file) {\n        console.log('移動端拍照成功', {\n          fileSize: result.file.size,\n          facingMode: result.facingMode\n        });\n      }\n    } catch (error) {\n      console.error('移動端拍照失敗:', error);\n      setIsCapturing(false);\n    }\n  }, [isReady, isCapturing, cameraManager]);\n\n  // 切換攝像頭\n  const handleSwitchCamera = useCallback(async () => {\n    if (!supportsCameraSwitch) return;\n    try {\n      await cameraManager.switchCamera();\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n    }\n  }, [supportsCameraSwitch, cameraManager]);\n  if (!visible) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mobile-camera-modal\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"camera-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"video\", {\n        ref: videoRef,\n        autoPlay: true,\n        playsInline: true,\n        muted: true,\n        className: \"camera-video\",\n        onClick: handleFocus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `camera-grid ${!showGrid ? 'hidden' : ''}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'none'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), focusPoint && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"focus-indicator active\",\n        style: {\n          left: `${focusPoint.x}%`,\n          top: `${focusPoint.y}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), !isReady && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"camera-loading\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), isReady && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"camera-guides\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"guide-corner top-left\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"guide-corner top-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"guide-corner bottom-left\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"guide-corner bottom-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"capture-hint\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hint-text\",\n            children: \"\\u9AD8\\u6E05\\u62CD\\u651D\\u5340\\u57DF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hint-subtext\",\n            children: \"\\u5C07\\u6587\\u4EF6\\u5C0D\\u6E96\\u6B64\\u5340\\u57DF\\u4EE5\\u7372\\u5F97\\u6700\\u4F73OCR\\u6548\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"camera-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-top\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            fill: \"none\",\n            onClick: handleClose,\n            className: \"control-button close-button\",\n            children: /*#__PURE__*/_jsxDEV(CloseOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              color: \"primary\",\n              fill: \"none\",\n              onClick: toggleGrid,\n              className: \"control-button grid-button\",\n              disabled: !isReady,\n              children: /*#__PURE__*/_jsxDEV(LoopOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), supportsCameraSwitch && /*#__PURE__*/_jsxDEV(Button, {\n              color: \"primary\",\n              fill: \"none\",\n              onClick: handleSwitchCamera,\n              className: \"control-button switch-button\",\n              disabled: !isReady,\n              children: /*#__PURE__*/_jsxDEV(RedoOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-bottom\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"capture-area\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              color: \"primary\",\n              size: \"large\",\n              onClick: handleTakePhoto,\n              disabled: !isReady || isCapturing,\n              className: \"capture-button\",\n              loading: isCapturing,\n              children: isCapturing ? /*#__PURE__*/_jsxDEV(CheckOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 32\n              }, this) : /*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 229,\n    columnNumber: 5\n  }, this);\n};\n_s(MobileCameraModal, \"R3OEbNc+4PPe4s1dGhRO726XadY=\");\n_c = MobileCameraModal;\nexport default MobileCameraModal;\nvar _c;\n$RefreshReg$(_c, \"MobileCameraModal\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "useCallback", "<PERSON><PERSON>", "CameraOutline", "CloseOutline", "RedoOutline", "CheckOutline", "LoopOutline", "jsxDEV", "_jsxDEV", "MobileCameraModal", "visible", "onClose", "onPhotoTaken", "cameraManager", "target", "_s", "videoRef", "canvasRef", "isReady", "setIsReady", "supportsCameraSwitch", "setSupportsCameraSwitch", "currentFacingMode", "setCurrentFacingMode", "isCapturing", "setIsCapturing", "showGrid", "setShowGrid", "focusPoint", "setFocusPoint", "handleCameraStart", "data", "facingMode", "handleCameraError", "error", "console", "handleCameraSwitch", "handleClose", "stopCamera", "handlePhotoTaken", "handleFocus", "event", "current", "rect", "getBoundingClientRect", "x", "clientX", "left", "y", "clientY", "top", "relativeX", "width", "relativeY", "height", "log", "_videoRef$current$src", "videoTrack", "srcObject", "getVideoTracks", "getCapabilities", "capabilities", "focusMode", "applyConstraints", "advanced", "pointsOfInterest", "catch", "err", "setTimeout", "to<PERSON><PERSON><PERSON>", "prev", "initializeCamera", "videoElement", "canvasElement", "setCallbacks", "cameraStart", "cameraError", "cameraSwitch", "photoTaken", "Promise", "resolve", "startCamera", "handleTakePhoto", "result", "<PERSON><PERSON><PERSON><PERSON>", "file", "fileSize", "size", "handleSwitchCamera", "switchCamera", "className", "children", "ref", "autoPlay", "playsInline", "muted", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "color", "fill", "gap", "disabled", "loading", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v3/src/components/MobileCameraModal.js"], "sourcesContent": ["/**\n * 移動端全屏相機組件\n * 提供優化的移動端拍照體驗\n */\n\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { Button } from 'antd-mobile';\nimport {\n  CameraOutline,\n  CloseOutline,\n  RedoOutline,\n  CheckOutline,\n  LoopOutline\n} from 'antd-mobile-icons';\nimport './MobileCameraModal.css';\n\nconst MobileCameraModal = ({ \n  visible, \n  onClose, \n  onPhotoTaken, \n  cameraManager,\n  target = 'back'\n}) => {\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [isReady, setIsReady] = useState(false);\n  const [supportsCameraSwitch, setSupportsCameraSwitch] = useState(false);\n  const [currentFacingMode, setCurrentFacingMode] = useState('environment');\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [showGrid, setShowGrid] = useState(false);\n  const [focusPoint, setFocusPoint] = useState(null);\n\n  // 相機啟動成功回調\n  const handleCameraStart = useCallback((data) => {\n    setIsReady(true);\n    if (data.facingMode) {\n      setCurrentFacingMode(data.facingMode);\n    }\n  }, []);\n\n  // 相機錯誤回調\n  const handleCameraError = useCallback((error) => {\n    console.error('相機錯誤:', error);\n    setIsReady(false);\n  }, []);\n\n  // 攝像頭切換回調\n  const handleCameraSwitch = useCallback((data) => {\n    if (data.facingMode) {\n      setCurrentFacingMode(data.facingMode);\n    }\n  }, []);\n\n  // 關閉相機\n  const handleClose = useCallback(() => {\n    if (cameraManager) {\n      cameraManager.stopCamera();\n    }\n    setIsReady(false);\n    setIsCapturing(false);\n    setFocusPoint(null);\n    if (onClose) {\n      onClose();\n    }\n  }, [cameraManager, onClose]);\n\n  // 拍照完成回調\n  const handlePhotoTaken = useCallback((data) => {\n    setIsCapturing(false);\n    if (onPhotoTaken) {\n      onPhotoTaken(data);\n    }\n    handleClose();\n  }, [onPhotoTaken, handleClose]);\n\n  // 手動對焦功能\n  const handleFocus = useCallback((event) => {\n    if (!isReady || !videoRef.current) return;\n\n    // 獲取點擊位置\n    const rect = videoRef.current.getBoundingClientRect();\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // 計算相對位置\n    const relativeX = (x / rect.width) * 100;\n    const relativeY = (y / rect.height) * 100;\n\n    console.log('手動對焦位置:', { x: relativeX, y: relativeY });\n\n    // 顯示對焦指示器\n    setFocusPoint({ x: relativeX, y: relativeY });\n\n    // 嘗試調用瀏覽器對焦API（如果支持）\n    try {\n      const videoTrack = videoRef.current.srcObject?.getVideoTracks()[0];\n      if (videoTrack && videoTrack.getCapabilities) {\n        const capabilities = videoTrack.getCapabilities();\n        if (capabilities.focusMode) {\n          // 設置對焦模式為手動或連續\n          videoTrack.applyConstraints({\n            advanced: [{\n              focusMode: 'continuous',\n              pointsOfInterest: [{ x: relativeX / 100, y: relativeY / 100 }]\n            }]\n          }).catch(err => {\n            console.log('對焦約束設置失敗:', err);\n          });\n        }\n      }\n    } catch (error) {\n      console.log('對焦功能不支持:', error);\n    }\n\n    // 清除對焦指示器\n    setTimeout(() => {\n      setFocusPoint(null);\n    }, 1500);\n  }, [isReady]);\n\n  // 切換網格線\n  const toggleGrid = useCallback(() => {\n    setShowGrid(prev => !prev);\n  }, []);\n\n  // 初始化相機\n  const initializeCamera = useCallback(async () => {\n    try {\n      setIsReady(false);\n\n      console.log('移動端相機初始化開始...', {\n        target,\n        videoElement: !!videoRef.current,\n        canvasElement: !!canvasRef.current\n      });\n\n      // 設置相機管理器回調\n      cameraManager.setCallbacks({\n        cameraStart: handleCameraStart,\n        cameraError: handleCameraError,\n        cameraSwitch: handleCameraSwitch,\n        photoTaken: handlePhotoTaken\n      });\n\n      // 等待DOM元素準備就緒\n      if (!videoRef.current || !canvasRef.current) {\n        await new Promise(resolve => setTimeout(resolve, 100));\n      }\n\n      // 啟動相機\n      await cameraManager.startCamera(target, {\n        videoElement: videoRef.current,\n        canvasElement: canvasRef.current\n      });\n\n      // 檢查是否支持攝像頭切換\n      setSupportsCameraSwitch(cameraManager.supportsCameraSwitch());\n\n      console.log('移動端相機初始化完成');\n\n    } catch (error) {\n      console.error('初始化相機失敗:', error);\n      handleCameraError(error);\n    }\n  }, [cameraManager, target, handleCameraStart, handleCameraError, handleCameraSwitch, handlePhotoTaken]);\n\n  // 初始化相機\n  useEffect(() => {\n    if (visible && cameraManager) {\n      initializeCamera();\n    }\n\n    return () => {\n      if (cameraManager) {\n        cameraManager.stopCamera();\n      }\n    };\n  }, [visible, cameraManager, target, initializeCamera]);\n\n  // 拍照\n  const handleTakePhoto = useCallback(async () => {\n    if (!isReady || isCapturing) {\n      console.log('拍照條件不滿足', { isReady, isCapturing });\n      return;\n    }\n\n    if (!cameraManager) {\n      console.error('相機管理器未初始化');\n      return;\n    }\n\n    try {\n      setIsCapturing(true);\n      console.log('移動端開始拍照...');\n\n      // 拍照前短暫延遲，確保對焦穩定\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      const result = await cameraManager.takePhoto();\n\n      if (result && result.file) {\n        console.log('移動端拍照成功', {\n          fileSize: result.file.size,\n          facingMode: result.facingMode\n        });\n      }\n    } catch (error) {\n      console.error('移動端拍照失敗:', error);\n      setIsCapturing(false);\n    }\n  }, [isReady, isCapturing, cameraManager]);\n\n  // 切換攝像頭\n  const handleSwitchCamera = useCallback(async () => {\n    if (!supportsCameraSwitch) return;\n\n    try {\n      await cameraManager.switchCamera();\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n    }\n  }, [supportsCameraSwitch, cameraManager]);\n\n  if (!visible) {\n    return null;\n  }\n\n  return (\n    <div className=\"mobile-camera-modal\">\n      <div className=\"camera-container\">\n        {/* 視頻預覽 */}\n        <video\n          ref={videoRef}\n          autoPlay\n          playsInline\n          muted\n          className=\"camera-video\"\n          onClick={handleFocus}\n        />\n        \n        {/* 網格線輔助 */}\n        <div className={`camera-grid ${!showGrid ? 'hidden' : ''}`}></div>\n        \n        {/* 隱藏的畫布用於拍照 */}\n        <canvas ref={canvasRef} style={{ display: 'none' }} />\n        \n        {/* 對焦指示器 */}\n        {focusPoint && (\n          <div \n            className=\"focus-indicator active\"\n            style={{\n              left: `${focusPoint.x}%`,\n              top: `${focusPoint.y}%`\n            }}\n          />\n        )}\n        \n        {/* 相機未準備就緒時的加載提示 */}\n        {!isReady && (\n          <div className=\"camera-loading\">\n            <div className=\"loading-spinner\"></div>\n            {/* 移除加載文字，只保留視覺指示器 */}\n          </div>\n        )}\n        \n        {/* 拍照指引線 - 更新版本 */}\n        {isReady && (\n          <div className=\"camera-guides\">\n            <div className=\"guide-corner top-left\"></div>\n            <div className=\"guide-corner top-right\"></div>\n            <div className=\"guide-corner bottom-left\"></div>\n            <div className=\"guide-corner bottom-right\"></div>\n\n            {/* 拍攝範圍提示 */}\n            <div className=\"capture-hint\">\n              <div className=\"hint-text\">高清拍攝區域</div>\n              <div className=\"hint-subtext\">將文件對準此區域以獲得最佳OCR效果</div>\n            </div>\n          </div>\n        )}\n        \n        {/* 控制按鈕 */}\n        <div className=\"camera-controls\">\n          <div className=\"controls-top\">\n            <Button\n              color=\"primary\"\n              fill=\"none\"\n              onClick={handleClose}\n              className=\"control-button close-button\"\n            >\n              <CloseOutline />\n            </Button>\n            \n            <div style={{ display: 'flex', gap: '12px' }}>\n              <Button\n                color=\"primary\"\n                fill=\"none\"\n                onClick={toggleGrid}\n                className=\"control-button grid-button\"\n                disabled={!isReady}\n              >\n                <LoopOutline />\n              </Button>\n              \n              {supportsCameraSwitch && (\n                <Button\n                  color=\"primary\"\n                  fill=\"none\"\n                  onClick={handleSwitchCamera}\n                  className=\"control-button switch-button\"\n                  disabled={!isReady}\n                >\n                  <RedoOutline />\n                </Button>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"controls-bottom\">\n            <div className=\"capture-area\">\n              <Button\n                color=\"primary\"\n                size=\"large\"\n                onClick={handleTakePhoto}\n                disabled={!isReady || isCapturing}\n                className=\"capture-button\"\n                loading={isCapturing}\n              >\n                {isCapturing ? <CheckOutline /> : <CameraOutline />}\n              </Button>\n            </div>\n            \n            {/* 移除攝像頭狀態指示文字，減少視覺干擾 */}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobileCameraModal;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,MAAM,QAAQ,aAAa;AACpC,SACEC,aAAa,EACbC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,WAAW,QACN,mBAAmB;AAC1B,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,OAAO;EACPC,OAAO;EACPC,YAAY;EACZC,aAAa;EACbC,MAAM,GAAG;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMoB,SAAS,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM+B,iBAAiB,GAAG9B,WAAW,CAAE+B,IAAI,IAAK;IAC9CZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIY,IAAI,CAACC,UAAU,EAAE;MACnBT,oBAAoB,CAACQ,IAAI,CAACC,UAAU,CAAC;IACvC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAGjC,WAAW,CAAEkC,KAAK,IAAK;IAC/CC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC7Bf,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiB,kBAAkB,GAAGpC,WAAW,CAAE+B,IAAI,IAAK;IAC/C,IAAIA,IAAI,CAACC,UAAU,EAAE;MACnBT,oBAAoB,CAACQ,IAAI,CAACC,UAAU,CAAC;IACvC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,WAAW,GAAGrC,WAAW,CAAC,MAAM;IACpC,IAAIa,aAAa,EAAE;MACjBA,aAAa,CAACyB,UAAU,CAAC,CAAC;IAC5B;IACAnB,UAAU,CAAC,KAAK,CAAC;IACjBM,cAAc,CAAC,KAAK,CAAC;IACrBI,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIlB,OAAO,EAAE;MACXA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACE,aAAa,EAAEF,OAAO,CAAC,CAAC;;EAE5B;EACA,MAAM4B,gBAAgB,GAAGvC,WAAW,CAAE+B,IAAI,IAAK;IAC7CN,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIb,YAAY,EAAE;MAChBA,YAAY,CAACmB,IAAI,CAAC;IACpB;IACAM,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACzB,YAAY,EAAEyB,WAAW,CAAC,CAAC;;EAE/B;EACA,MAAMG,WAAW,GAAGxC,WAAW,CAAEyC,KAAK,IAAK;IACzC,IAAI,CAACvB,OAAO,IAAI,CAACF,QAAQ,CAAC0B,OAAO,EAAE;;IAEnC;IACA,MAAMC,IAAI,GAAG3B,QAAQ,CAAC0B,OAAO,CAACE,qBAAqB,CAAC,CAAC;IACrD,MAAMC,CAAC,GAAGJ,KAAK,CAACK,OAAO,GAAGH,IAAI,CAACI,IAAI;IACnC,MAAMC,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGN,IAAI,CAACO,GAAG;;IAElC;IACA,MAAMC,SAAS,GAAIN,CAAC,GAAGF,IAAI,CAACS,KAAK,GAAI,GAAG;IACxC,MAAMC,SAAS,GAAIL,CAAC,GAAGL,IAAI,CAACW,MAAM,GAAI,GAAG;IAEzCnB,OAAO,CAACoB,GAAG,CAAC,SAAS,EAAE;MAAEV,CAAC,EAAEM,SAAS;MAAEH,CAAC,EAAEK;IAAU,CAAC,CAAC;;IAEtD;IACAxB,aAAa,CAAC;MAAEgB,CAAC,EAAEM,SAAS;MAAEH,CAAC,EAAEK;IAAU,CAAC,CAAC;;IAE7C;IACA,IAAI;MAAA,IAAAG,qBAAA;MACF,MAAMC,UAAU,IAAAD,qBAAA,GAAGxC,QAAQ,CAAC0B,OAAO,CAACgB,SAAS,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,IAAIF,UAAU,IAAIA,UAAU,CAACG,eAAe,EAAE;QAC5C,MAAMC,YAAY,GAAGJ,UAAU,CAACG,eAAe,CAAC,CAAC;QACjD,IAAIC,YAAY,CAACC,SAAS,EAAE;UAC1B;UACAL,UAAU,CAACM,gBAAgB,CAAC;YAC1BC,QAAQ,EAAE,CAAC;cACTF,SAAS,EAAE,YAAY;cACvBG,gBAAgB,EAAE,CAAC;gBAAEpB,CAAC,EAAEM,SAAS,GAAG,GAAG;gBAAEH,CAAC,EAAEK,SAAS,GAAG;cAAI,CAAC;YAC/D,CAAC;UACH,CAAC,CAAC,CAACa,KAAK,CAACC,GAAG,IAAI;YACdhC,OAAO,CAACoB,GAAG,CAAC,WAAW,EAAEY,GAAG,CAAC;UAC/B,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACoB,GAAG,CAAC,UAAU,EAAErB,KAAK,CAAC;IAChC;;IAEA;IACAkC,UAAU,CAAC,MAAM;MACfvC,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMmD,UAAU,GAAGrE,WAAW,CAAC,MAAM;IACnC2B,WAAW,CAAC2C,IAAI,IAAI,CAACA,IAAI,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAGvE,WAAW,CAAC,YAAY;IAC/C,IAAI;MACFmB,UAAU,CAAC,KAAK,CAAC;MAEjBgB,OAAO,CAACoB,GAAG,CAAC,eAAe,EAAE;QAC3BzC,MAAM;QACN0D,YAAY,EAAE,CAAC,CAACxD,QAAQ,CAAC0B,OAAO;QAChC+B,aAAa,EAAE,CAAC,CAACxD,SAAS,CAACyB;MAC7B,CAAC,CAAC;;MAEF;MACA7B,aAAa,CAAC6D,YAAY,CAAC;QACzBC,WAAW,EAAE7C,iBAAiB;QAC9B8C,WAAW,EAAE3C,iBAAiB;QAC9B4C,YAAY,EAAEzC,kBAAkB;QAChC0C,UAAU,EAAEvC;MACd,CAAC,CAAC;;MAEF;MACA,IAAI,CAACvB,QAAQ,CAAC0B,OAAO,IAAI,CAACzB,SAAS,CAACyB,OAAO,EAAE;QAC3C,MAAM,IAAIqC,OAAO,CAACC,OAAO,IAAIZ,UAAU,CAACY,OAAO,EAAE,GAAG,CAAC,CAAC;MACxD;;MAEA;MACA,MAAMnE,aAAa,CAACoE,WAAW,CAACnE,MAAM,EAAE;QACtC0D,YAAY,EAAExD,QAAQ,CAAC0B,OAAO;QAC9B+B,aAAa,EAAExD,SAAS,CAACyB;MAC3B,CAAC,CAAC;;MAEF;MACArB,uBAAuB,CAACR,aAAa,CAACO,oBAAoB,CAAC,CAAC,CAAC;MAE7De,OAAO,CAACoB,GAAG,CAAC,YAAY,CAAC;IAE3B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCD,iBAAiB,CAACC,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAACrB,aAAa,EAAEC,MAAM,EAAEgB,iBAAiB,EAAEG,iBAAiB,EAAEG,kBAAkB,EAAEG,gBAAgB,CAAC,CAAC;;EAEvG;EACAzC,SAAS,CAAC,MAAM;IACd,IAAIY,OAAO,IAAIG,aAAa,EAAE;MAC5B0D,gBAAgB,CAAC,CAAC;IACpB;IAEA,OAAO,MAAM;MACX,IAAI1D,aAAa,EAAE;QACjBA,aAAa,CAACyB,UAAU,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAAC5B,OAAO,EAAEG,aAAa,EAAEC,MAAM,EAAEyD,gBAAgB,CAAC,CAAC;;EAEtD;EACA,MAAMW,eAAe,GAAGlF,WAAW,CAAC,YAAY;IAC9C,IAAI,CAACkB,OAAO,IAAIM,WAAW,EAAE;MAC3BW,OAAO,CAACoB,GAAG,CAAC,SAAS,EAAE;QAAErC,OAAO;QAAEM;MAAY,CAAC,CAAC;MAChD;IACF;IAEA,IAAI,CAACX,aAAa,EAAE;MAClBsB,OAAO,CAACD,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAI;MACFT,cAAc,CAAC,IAAI,CAAC;MACpBU,OAAO,CAACoB,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACA,MAAM,IAAIwB,OAAO,CAACC,OAAO,IAAIZ,UAAU,CAACY,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,MAAMG,MAAM,GAAG,MAAMtE,aAAa,CAACuE,SAAS,CAAC,CAAC;MAE9C,IAAID,MAAM,IAAIA,MAAM,CAACE,IAAI,EAAE;QACzBlD,OAAO,CAACoB,GAAG,CAAC,SAAS,EAAE;UACrB+B,QAAQ,EAAEH,MAAM,CAACE,IAAI,CAACE,IAAI;UAC1BvD,UAAU,EAAEmD,MAAM,CAACnD;QACrB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCT,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACP,OAAO,EAAEM,WAAW,EAAEX,aAAa,CAAC,CAAC;;EAEzC;EACA,MAAM2E,kBAAkB,GAAGxF,WAAW,CAAC,YAAY;IACjD,IAAI,CAACoB,oBAAoB,EAAE;IAE3B,IAAI;MACF,MAAMP,aAAa,CAAC4E,YAAY,CAAC,CAAC;IACpC,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACd,oBAAoB,EAAEP,aAAa,CAAC,CAAC;EAEzC,IAAI,CAACH,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,oBACEF,OAAA;IAAKkF,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCnF,OAAA;MAAKkF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BnF,OAAA;QACEoF,GAAG,EAAE5E,QAAS;QACd6E,QAAQ;QACRC,WAAW;QACXC,KAAK;QACLL,SAAS,EAAC,cAAc;QACxBM,OAAO,EAAExD;MAAY;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGF5F,OAAA;QAAKkF,SAAS,EAAE,eAAe,CAAChE,QAAQ,GAAG,QAAQ,GAAG,EAAE;MAAG;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGlE5F,OAAA;QAAQoF,GAAG,EAAE3E,SAAU;QAACoF,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGrDxE,UAAU,iBACTpB,OAAA;QACEkF,SAAS,EAAC,wBAAwB;QAClCW,KAAK,EAAE;UACLtD,IAAI,EAAE,GAAGnB,UAAU,CAACiB,CAAC,GAAG;UACxBK,GAAG,EAAE,GAAGtB,UAAU,CAACoB,CAAC;QACtB;MAAE;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EAGA,CAAClF,OAAO,iBACPV,OAAA;QAAKkF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BnF,OAAA;UAAKkF,SAAS,EAAC;QAAiB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpC,CACN,EAGAlF,OAAO,iBACNV,OAAA;QAAKkF,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnF,OAAA;UAAKkF,SAAS,EAAC;QAAuB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7C5F,OAAA;UAAKkF,SAAS,EAAC;QAAwB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9C5F,OAAA;UAAKkF,SAAS,EAAC;QAA0B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChD5F,OAAA;UAAKkF,SAAS,EAAC;QAA2B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGjD5F,OAAA;UAAKkF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnF,OAAA;YAAKkF,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC5F,OAAA;YAAKkF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5F,OAAA;QAAKkF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnF,OAAA;UAAKkF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnF,OAAA,CAACP,MAAM;YACLsG,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,MAAM;YACXR,OAAO,EAAE3D,WAAY;YACrBqD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAEvCnF,OAAA,CAACL,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAET5F,OAAA;YAAK6F,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC3CnF,OAAA,CAACP,MAAM;cACLsG,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,MAAM;cACXR,OAAO,EAAE3B,UAAW;cACpBqB,SAAS,EAAC,4BAA4B;cACtCgB,QAAQ,EAAE,CAACxF,OAAQ;cAAAyE,QAAA,eAEnBnF,OAAA,CAACF,WAAW;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EAERhF,oBAAoB,iBACnBZ,OAAA,CAACP,MAAM;cACLsG,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,MAAM;cACXR,OAAO,EAAER,kBAAmB;cAC5BE,SAAS,EAAC,8BAA8B;cACxCgB,QAAQ,EAAE,CAACxF,OAAQ;cAAAyE,QAAA,eAEnBnF,OAAA,CAACJ,WAAW;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5F,OAAA;UAAKkF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BnF,OAAA;YAAKkF,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BnF,OAAA,CAACP,MAAM;cACLsG,KAAK,EAAC,SAAS;cACfhB,IAAI,EAAC,OAAO;cACZS,OAAO,EAAEd,eAAgB;cACzBwB,QAAQ,EAAE,CAACxF,OAAO,IAAIM,WAAY;cAClCkE,SAAS,EAAC,gBAAgB;cAC1BiB,OAAO,EAAEnF,WAAY;cAAAmE,QAAA,EAEpBnE,WAAW,gBAAGhB,OAAA,CAACH,YAAY;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5F,OAAA,CAACN,aAAa;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrF,EAAA,CAlUIN,iBAAiB;AAAAmG,EAAA,GAAjBnG,iBAAiB;AAoUvB,eAAeA,iBAAiB;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}