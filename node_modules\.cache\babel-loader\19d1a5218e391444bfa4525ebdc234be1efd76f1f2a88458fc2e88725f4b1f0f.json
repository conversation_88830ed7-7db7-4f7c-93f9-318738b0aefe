{"ast": null, "code": "import React, { isValidElement } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Badge from '../badge';\nimport SafeArea from '../safe-area';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\n/* istanbul ignore next */\nexport const TabBarItem = () => {\n  return null;\n};\nconst classPrefix = `adm-tab-bar`;\nconst defaultProps = {\n  safeArea: false\n};\nexport const TabBar = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  let firstActiveKey = null;\n  const items = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    items.push(child);\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-wrap`\n  }, items.map(item => {\n    const active = item.key === activeKey;\n    function renderContent() {\n      const iconElement = item.props.icon && React.createElement(\"div\", {\n        className: `${classPrefix}-item-icon`\n      }, typeof item.props.icon === 'function' ? item.props.icon(active) : item.props.icon);\n      const titleElement = item.props.title && React.createElement(\"div\", {\n        className: classNames(`${classPrefix}-item-title`, Boolean(iconElement) && `${classPrefix}-item-title-with-icon`)\n      }, typeof item.props.title === 'function' ? item.props.title(active) : item.props.title);\n      if (iconElement) {\n        return React.createElement(React.Fragment, null, React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-icon-badge`\n        }, iconElement), titleElement);\n      } else if (titleElement) {\n        return React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-title-badge`\n        }, titleElement);\n      }\n      return null;\n    }\n    return withNativeProps(item.props, React.createElement(\"div\", {\n      key: item.key,\n      onClick: () => {\n        var _a, _b;\n        const {\n          key\n        } = item;\n        if (key === undefined || key === null) return;\n        setActiveKey(key.toString());\n        (_b = (_a = item.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a);\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-active`]: active\n      })\n    }, renderContent()));\n  })), props.safeArea && React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}