{"ast": null, "code": "import \"./action-sheet.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { ActionSheet, showActionSheet } from './action-sheet';\nexport default attachPropertiesToComponent(ActionSheet, {\n  show: showActionSheet\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "ActionSheet", "showActionSheet", "show"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/action-sheet/index.js"], "sourcesContent": ["import \"./action-sheet.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { ActionSheet, showActionSheet } from './action-sheet';\nexport default attachPropertiesToComponent(ActionSheet, {\n  show: showActionSheet\n});"], "mappings": "AAAA,OAAO,oBAAoB;AAC3B,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,WAAW,EAAEC,eAAe,QAAQ,gBAAgB;AAC7D,eAAeF,2BAA2B,CAACC,WAAW,EAAE;EACtDE,IAAI,EAAED;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}