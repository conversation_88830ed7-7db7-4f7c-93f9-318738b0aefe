{"ast": null, "code": "/**\n * Check if the `node` is visible Node (not null, undefined, or false)\n */\nexport function isNodeWithContent(node) {\n  return node !== undefined && node !== null && node !== false;\n}", "map": {"version": 3, "names": ["isNodeWithContent", "node", "undefined"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/is-node-with-content.js"], "sourcesContent": ["/**\n * Check if the `node` is visible Node (not null, undefined, or false)\n */\nexport function isNodeWithContent(node) {\n  return node !== undefined && node !== null && node !== false;\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAOA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}