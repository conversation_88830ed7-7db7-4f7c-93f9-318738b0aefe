{"ast": null, "code": "import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { SwiperItem } from './swiper-item';\nimport { devWarning } from '../../utils/dev-log';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport PageIndicator from '../page-indicator';\nimport { staged } from 'staged-components';\nimport { useRefState } from '../../utils/use-ref-state';\nimport { bound } from '../../utils/bound';\nimport { useIsomorphicLayoutEffect, useGetState } from 'ahooks';\nimport { mergeFuncProps } from '../../utils/with-func-props';\nconst classPrefix = `adm-swiper`;\nconst eventToPropRecord = {\n  'mousedown': 'onMouseDown',\n  'mousemove': 'onMouseMove',\n  'mouseup': 'onMouseUp'\n};\nconst defaultProps = {\n  defaultIndex: 0,\n  allowTouchMove: true,\n  autoplay: false,\n  autoplayInterval: 3000,\n  loop: false,\n  direction: 'horizontal',\n  slideSize: 100,\n  trackOffset: 0,\n  stuckAtBoundary: true,\n  rubberband: true,\n  stopPropagation: []\n};\nlet currentUid;\nexport const Swiper = forwardRef(staged((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    total,\n    children,\n    indicator\n  } = props;\n  const [uid] = useState({});\n  const timeoutRef = useRef(null);\n  const isVertical = direction === 'vertical';\n  const slideRatio = props.slideSize / 100;\n  const offsetRatio = props.trackOffset / 100;\n  const {\n    validChildren,\n    count,\n    renderChildren\n  } = useMemo(() => {\n    let count = 0;\n    let renderChildren = undefined;\n    let validChildren = undefined;\n    if (typeof children === 'function') {\n      renderChildren = children;\n    } else {\n      validChildren = React.Children.map(children, child => {\n        if (!React.isValidElement(child)) return null;\n        if (child.type !== SwiperItem) {\n          devWarning('Swiper', 'The children of `Swiper` must be `Swiper.Item` components.');\n          return null;\n        }\n        count++;\n        return child;\n      });\n    }\n    return {\n      renderChildren,\n      validChildren,\n      count\n    };\n  }, [children]);\n  const mergedTotal = total !== null && total !== void 0 ? total : count;\n  if (mergedTotal === 0 || !validChildren && !renderChildren) {\n    devWarning('Swiper', '`Swiper` needs at least one child.');\n    return null;\n  }\n  return () => {\n    let loop = props.loop;\n    if (slideRatio * (mergedTotal - 1) < 1) {\n      loop = false;\n    }\n    const trackRef = useRef(null);\n    function getSlidePixels() {\n      const track = trackRef.current;\n      if (!track) return 0;\n      const trackPixels = isVertical ? track.offsetHeight : track.offsetWidth;\n      return trackPixels * props.slideSize / 100;\n    }\n    const [current, setCurrent, getCurrent] = useGetState(props.defaultIndex);\n    const [dragging, setDragging, draggingRef] = useRefState(false);\n    function boundIndex(current) {\n      let min = 0;\n      let max = mergedTotal - 1;\n      if (props.stuckAtBoundary) {\n        min += offsetRatio / slideRatio;\n        max -= (1 - slideRatio - offsetRatio) / slideRatio;\n      }\n      return bound(current, min, max);\n    }\n    const [{\n      position\n    }, api] = useSpring(() => ({\n      position: boundIndex(current) * 100,\n      config: {\n        tension: 200,\n        friction: 30\n      },\n      onRest: () => {\n        if (draggingRef.current) return;\n        if (!loop) return;\n        const rawX = position.get();\n        const totalWidth = 100 * mergedTotal;\n        const standardPosition = modulus(rawX, totalWidth);\n        if (standardPosition === rawX) return;\n        api.start({\n          position: standardPosition,\n          immediate: true\n        });\n      }\n    }), [mergedTotal]);\n    const dragCancelRef = useRef(null);\n    function forceCancelDrag() {\n      var _a;\n      (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n      draggingRef.current = false;\n    }\n    const bind = useDrag(state => {\n      dragCancelRef.current = state.cancel;\n      if (!state.intentional) return;\n      if (state.first && !currentUid) {\n        currentUid = uid;\n      }\n      if (currentUid !== uid) return;\n      currentUid = state.last ? undefined : uid;\n      const slidePixels = getSlidePixels();\n      if (!slidePixels) return;\n      const paramIndex = isVertical ? 1 : 0;\n      const offset = state.offset[paramIndex];\n      const direction = state.direction[paramIndex];\n      const velocity = state.velocity[paramIndex];\n      setDragging(true);\n      if (!state.last) {\n        api.start({\n          position: offset * 100 / slidePixels,\n          immediate: true\n        });\n      } else {\n        const minIndex = Math.floor(offset / slidePixels);\n        const maxIndex = minIndex + 1;\n        const index = Math.round((offset + velocity * 2000 * direction) / slidePixels);\n        swipeTo(bound(index, minIndex, maxIndex));\n        window.setTimeout(() => {\n          setDragging(false);\n        });\n      }\n    }, {\n      transform: ([x, y]) => [-x, -y],\n      from: () => {\n        const slidePixels = getSlidePixels();\n        return [position.get() / 100 * slidePixels, position.get() / 100 * slidePixels];\n      },\n      triggerAllEvents: true,\n      bounds: () => {\n        if (loop) return {};\n        const slidePixels = getSlidePixels();\n        const lowerBound = boundIndex(0) * slidePixels;\n        const upperBound = boundIndex(mergedTotal - 1) * slidePixels;\n        return isVertical ? {\n          top: lowerBound,\n          bottom: upperBound\n        } : {\n          left: lowerBound,\n          right: upperBound\n        };\n      },\n      rubberband: props.rubberband,\n      axis: isVertical ? 'y' : 'x',\n      preventScroll: !isVertical,\n      pointer: {\n        touch: true\n      }\n    });\n    function swipeTo(index, immediate = false) {\n      var _a;\n      const roundedIndex = Math.round(index);\n      const targetIndex = loop ? modulus(roundedIndex, mergedTotal) : bound(roundedIndex, 0, mergedTotal - 1);\n      if (targetIndex !== getCurrent()) {\n        (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, targetIndex);\n      }\n      setCurrent(targetIndex);\n      api.start({\n        position: (loop ? roundedIndex : boundIndex(roundedIndex)) * 100,\n        immediate\n      });\n    }\n    function swipeNext() {\n      swipeTo(Math.round(position.get() / 100) + 1);\n    }\n    function swipePrev() {\n      swipeTo(Math.round(position.get() / 100) - 1);\n    }\n    useImperativeHandle(ref, () => ({\n      swipeTo,\n      swipeNext,\n      swipePrev\n    }));\n    useIsomorphicLayoutEffect(() => {\n      const maxIndex = mergedTotal - 1;\n      if (current > maxIndex) {\n        swipeTo(maxIndex, true);\n      }\n    });\n    const {\n      autoplay,\n      autoplayInterval\n    } = props;\n    const runTimeSwiper = () => {\n      timeoutRef.current = window.setTimeout(() => {\n        if (autoplay === 'reverse') {\n          swipePrev();\n        } else {\n          swipeNext();\n        }\n        runTimeSwiper();\n      }, autoplayInterval);\n    };\n    useEffect(() => {\n      if (!autoplay || dragging) return;\n      runTimeSwiper();\n      return () => {\n        if (timeoutRef.current) window.clearTimeout(timeoutRef.current);\n      };\n    }, [autoplay, autoplayInterval, dragging, mergedTotal]);\n    // ============================== Render ==============================\n    // Render Item\n    function renderItem(index, child) {\n      let itemStyle = {};\n      if (loop) {\n        itemStyle = {\n          [isVertical ? 'y' : 'x']: position.to(position => {\n            let finalPosition = -position + index * 100;\n            const totalWidth = mergedTotal * 100;\n            const flagWidth = totalWidth / 2;\n            finalPosition = modulus(finalPosition + flagWidth, totalWidth) - flagWidth;\n            return `${finalPosition}%`;\n          }),\n          [isVertical ? 'top' : 'left']: `-${index * 100}%`\n        };\n      }\n      return React.createElement(animated.div, {\n        className: classNames(`${classPrefix}-slide`, {\n          [`${classPrefix}-slide-active`]: current === index\n        }),\n        style: itemStyle,\n        key: index\n      }, child);\n    }\n    function renderItems() {\n      if (renderChildren && total) {\n        const offsetCount = 2;\n        const startIndex = Math.max(current - offsetCount, 0);\n        const endIndex = Math.min(current + offsetCount, total - 1);\n        const items = [];\n        for (let index = startIndex; index <= endIndex; index += 1) {\n          items.push(renderItem(index, renderChildren(index)));\n        }\n        return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n          className: `${classPrefix}-slide-placeholder`,\n          style: {\n            width: `${startIndex * 100}%`\n          }\n        }), items);\n      }\n      return React.Children.map(validChildren, (child, index) => {\n        return renderItem(index, child);\n      });\n    }\n    // Render Track Inner\n    function renderTrackInner() {\n      if (loop) {\n        return React.createElement(\"div\", {\n          className: `${classPrefix}-track-inner`\n        }, renderItems());\n      } else {\n        return React.createElement(animated.div, {\n          className: `${classPrefix}-track-inner`,\n          style: {\n            [isVertical ? 'y' : 'x']: position.to(position => `${-position}%`)\n          }\n        }, renderItems());\n      }\n    }\n    // Render\n    const style = {\n      '--slide-size': `${props.slideSize}%`,\n      '--track-offset': `${props.trackOffset}%`\n    };\n    const dragProps = Object.assign({}, props.allowTouchMove ? bind() : {});\n    const stopPropagationProps = {};\n    for (const key of props.stopPropagation) {\n      const prop = eventToPropRecord[key];\n      stopPropagationProps[prop] = function (e) {\n        e.stopPropagation();\n      };\n    }\n    const mergedProps = mergeFuncProps(dragProps, stopPropagationProps);\n    let indicatorNode = null;\n    if (typeof indicator === 'function') {\n      indicatorNode = indicator(mergedTotal, current);\n    } else if (indicator !== false) {\n      indicatorNode = React.createElement(\"div\", {\n        className: `${classPrefix}-indicator`\n      }, React.createElement(PageIndicator, Object.assign({}, props.indicatorProps, {\n        total: mergedTotal,\n        current: current,\n        direction: direction\n      })));\n    }\n    return withNativeProps(props, React.createElement(\"div\", {\n      className: classNames(classPrefix, `${classPrefix}-${direction}`),\n      style: style\n    }, React.createElement(\"div\", Object.assign({\n      ref: trackRef,\n      className: classNames(`${classPrefix}-track`, {\n        [`${classPrefix}-track-allow-touch-move`]: props.allowTouchMove\n      }),\n      onClickCapture: e => {\n        if (draggingRef.current) {\n          e.stopPropagation();\n        }\n        forceCancelDrag();\n      }\n    }, mergedProps), renderTrackInner()), indicatorNode));\n  };\n}));\nfunction modulus(value, division) {\n  const remainder = value % division;\n  return remainder < 0 ? remainder + division : remainder;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}