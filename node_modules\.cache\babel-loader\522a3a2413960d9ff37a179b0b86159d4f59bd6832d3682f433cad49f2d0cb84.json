{"ast": null, "code": "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n  'use strict';\n\n  var hasOwn = {}.hasOwnProperty;\n  function classNames() {\n    var classes = '';\n    for (var i = 0; i < arguments.length; i++) {\n      var arg = arguments[i];\n      if (arg) {\n        classes = appendClass(classes, parseValue(arg));\n      }\n    }\n    return classes;\n  }\n  function parseValue(arg) {\n    if (typeof arg === 'string' || typeof arg === 'number') {\n      return arg;\n    }\n    if (typeof arg !== 'object') {\n      return '';\n    }\n    if (Array.isArray(arg)) {\n      return classNames.apply(null, arg);\n    }\n    if (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n      return arg.toString();\n    }\n    var classes = '';\n    for (var key in arg) {\n      if (hasOwn.call(arg, key) && arg[key]) {\n        classes = appendClass(classes, key);\n      }\n    }\n    return classes;\n  }\n  function appendClass(value, newClass) {\n    if (!newClass) {\n      return value;\n    }\n    if (value) {\n      return value + ' ' + newClass;\n    }\n    return value + newClass;\n  }\n  if (typeof module !== 'undefined' && module.exports) {\n    classNames.default = classNames;\n    module.exports = classNames;\n  } else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n    // register as 'classnames', consistent with npm package name\n    define('classnames', [], function () {\n      return classNames;\n    });\n  } else {\n    window.classNames = classNames;\n  }\n})();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}