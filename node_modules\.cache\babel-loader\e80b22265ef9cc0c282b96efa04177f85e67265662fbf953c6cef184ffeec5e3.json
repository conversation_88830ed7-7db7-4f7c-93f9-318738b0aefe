{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useCreation from '../useCreation';\nvar useResetState = function (initialState) {\n  var initialStateRef = useRef(initialState);\n  var initialStateMemo = useCreation(function () {\n    return isFunction(initialStateRef.current) ? initialStateRef.current() : initialStateRef.current;\n  }, []);\n  var _a = __read(useState(initialStateMemo), 2),\n    state = _a[0],\n    setState = _a[1];\n  var resetState = useMemoizedFn(function () {\n    setState(initialStateMemo);\n  });\n  return [state, setState, resetState];\n};\nexport default useResetState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}