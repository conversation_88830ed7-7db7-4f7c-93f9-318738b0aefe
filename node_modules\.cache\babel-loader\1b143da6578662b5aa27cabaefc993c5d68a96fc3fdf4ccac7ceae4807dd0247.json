{"ast": null, "code": "import \"./radio.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Group } from './group';\nimport { Radio } from './radio';\nexport default attachPropertiesToComponent(Radio, {\n  Group\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Group", "Radio"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/radio/index.js"], "sourcesContent": ["import \"./radio.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Group } from './group';\nimport { Radio } from './radio';\nexport default attachPropertiesToComponent(Radio, {\n  Group\n});"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,SAAS;AAC/B,eAAeF,2BAA2B,CAACE,KAAK,EAAE;EAChDD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}