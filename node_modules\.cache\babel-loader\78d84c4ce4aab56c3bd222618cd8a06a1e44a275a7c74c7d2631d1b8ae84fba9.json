{"ast": null, "code": "import { SearchOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport Button from '../button';\nimport { useConfig } from '../config-provider';\nimport Input from '../input';\nconst classPrefix = `adm-search-bar`;\nconst defaultProps = {\n  clearable: true,\n  onlyShowClearWhenFocus: false,\n  showCancelButton: false,\n  defaultValue: '',\n  clearOnCancel: true\n};\nexport const SearchBar = forwardRef((props, ref) => {\n  const {\n    locale,\n    searchBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, {\n    cancelText: locale.common.cancel\n  }, props);\n  const searchIcon = mergeProp(React.createElement(SearchOutline, null), componentConfig.searchIcon, props.icon, props.searchIcon);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const [hasFocus, setHasFocus] = useState(false);\n  const inputRef = useRef(null);\n  const composingRef = useRef(false);\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n    },\n    focus: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      var _a, _b;\n      return (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement) !== null && _b !== void 0 ? _b : null;\n    }\n  }));\n  const renderCancelButton = () => {\n    let isShowCancel;\n    if (typeof mergedProps.showCancelButton === 'function') {\n      isShowCancel = mergedProps.showCancelButton(hasFocus, value);\n    } else {\n      isShowCancel = mergedProps.showCancelButton && hasFocus;\n    }\n    return isShowCancel && React.createElement(\"div\", {\n      className: `${classPrefix}-suffix`\n    }, React.createElement(Button, {\n      fill: 'none',\n      className: `${classPrefix}-cancel-button`,\n      onClick: () => {\n        var _a, _b, _c;\n        if (mergedProps.clearOnCancel) {\n          (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n        }\n        (_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n        (_c = mergedProps.onCancel) === null || _c === void 0 ? void 0 : _c.call(mergedProps);\n      },\n      onMouseDown: e => {\n        e.preventDefault();\n      }\n    }, mergedProps.cancelText));\n  };\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-active`]: hasFocus\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-input-box`\n  }, searchIcon && React.createElement(\"div\", {\n    className: `${classPrefix}-input-box-icon`\n  }, searchIcon), React.createElement(Input, {\n    ref: inputRef,\n    className: classNames(`${classPrefix}-input`, {\n      [`${classPrefix}-input-without-icon`]: !searchIcon\n    }),\n    value: value,\n    onChange: setValue,\n    maxLength: mergedProps.maxLength,\n    autoFocus: mergedProps.autoFocus,\n    placeholder: mergedProps.placeholder,\n    clearable: mergedProps.clearable,\n    onlyShowClearWhenFocus: mergedProps.onlyShowClearWhenFocus,\n    onFocus: e => {\n      var _a;\n      setHasFocus(true);\n      (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onBlur: e => {\n      var _a;\n      setHasFocus(false);\n      (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onClear: mergedProps.onClear,\n    type: 'search',\n    enterKeyHint: 'search',\n    onEnterPress: () => {\n      var _a, _b;\n      if (!composingRef.current) {\n        (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n        (_b = mergedProps.onSearch) === null || _b === void 0 ? void 0 : _b.call(mergedProps, value);\n      }\n    },\n    \"aria-label\": locale.SearchBar.name,\n    onCompositionStart: e => {\n      var _a;\n      composingRef.current = true;\n      (_a = mergedProps.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      composingRef.current = false;\n      (_a = mergedProps.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    }\n  })), renderCancelButton()));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}