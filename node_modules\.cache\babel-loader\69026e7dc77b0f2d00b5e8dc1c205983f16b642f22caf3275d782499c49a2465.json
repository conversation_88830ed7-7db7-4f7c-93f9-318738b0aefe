{"ast": null, "code": "import \"./picker.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Picker } from './picker';\nimport { prompt } from './prompt';\nexport default attachPropertiesToComponent(Picker, {\n  prompt\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Picker", "prompt"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/picker/index.js"], "sourcesContent": ["import \"./picker.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Picker } from './picker';\nimport { prompt } from './prompt';\nexport default attachPropertiesToComponent(Picker, {\n  prompt\n});"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,MAAM,QAAQ,UAAU;AACjC,eAAeF,2BAA2B,CAACC,MAAM,EAAE;EACjDC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}