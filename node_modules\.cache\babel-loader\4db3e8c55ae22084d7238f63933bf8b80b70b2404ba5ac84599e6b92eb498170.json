{"ast": null, "code": "import \"./number-keyboard.css\";\nimport { NumberKeyboard } from './number-keyboard';\nexport default NumberKeyboard;", "map": {"version": 3, "names": ["NumberKeyboard"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/number-keyboard/index.js"], "sourcesContent": ["import \"./number-keyboard.css\";\nimport { NumberKeyboard } from './number-keyboard';\nexport default NumberKeyboard;"], "mappings": "AAAA,OAAO,uBAAuB;AAC9B,SAASA,cAAc,QAAQ,mBAAmB;AAClD,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}