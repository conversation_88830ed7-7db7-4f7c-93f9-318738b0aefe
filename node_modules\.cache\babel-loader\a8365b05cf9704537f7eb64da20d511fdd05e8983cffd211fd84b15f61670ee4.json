{"ast": null, "code": "import classNames from 'classnames';\nimport React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Mask from '../mask';\nimport SafeArea from '../safe-area';\nimport { Slide } from './slide';\nimport { Slides } from './slides';\nconst classPrefix = `adm-image-viewer`;\nconst defaultProps = {\n  maxZoom: 3,\n  getContainer: null,\n  visible: false\n};\nexport const ImageViewer = p => {\n  var _a, _b, _c;\n  const props = mergeProps(defaultProps, p);\n  const node = React.createElement(Mask, {\n    visible: props.visible,\n    disableBodyScroll: false,\n    opacity: 'thick',\n    afterClose: props.afterClose,\n    destroyOnClose: true,\n    className: (_a = props === null || props === void 0 ? void 0 : props.classNames) === null || _a === void 0 ? void 0 : _a.mask\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-content`, (_b = props === null || props === void 0 ? void 0 : props.classNames) === null || _b === void 0 ? void 0 : _b.body)\n  }, (props.image || typeof props.imageRender === 'function') && React.createElement(Slide, {\n    image: props.image,\n    onTap: props.onClose,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender\n  })), props.image && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, (_c = props.renderFooter) === null || _c === void 0 ? void 0 : _c.call(props, props.image), React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n  return renderToContainer(props.getContainer, node);\n};\nconst multiDefaultProps = Object.assign(Object.assign({}, defaultProps), {\n  defaultIndex: 0\n});\nexport const MultiImageViewer = forwardRef((p, ref) => {\n  var _a, _b, _c;\n  const props = mergeProps(multiDefaultProps, p);\n  const [index, setIndex] = useState(props.defaultIndex);\n  const slidesRef = useRef(null);\n  useImperativeHandle(ref, () => ({\n    swipeTo: (index, immediate) => {\n      var _a;\n      setIndex(index);\n      (_a = slidesRef.current) === null || _a === void 0 ? void 0 : _a.swipeTo(index, immediate);\n    }\n  }));\n  const onSlideChange = useCallback(newIndex => {\n    var _a;\n    if (newIndex === index) return;\n    setIndex(newIndex);\n    (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, newIndex);\n  }, [props.onIndexChange, index]);\n  const node = React.createElement(Mask, {\n    visible: props.visible,\n    disableBodyScroll: false,\n    opacity: 'thick',\n    afterClose: props.afterClose,\n    destroyOnClose: true,\n    className: (_a = props === null || props === void 0 ? void 0 : props.classNames) === null || _a === void 0 ? void 0 : _a.mask\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-content`, (_b = props === null || props === void 0 ? void 0 : props.classNames) === null || _b === void 0 ? void 0 : _b.body)\n  }, props.images && React.createElement(Slides, {\n    ref: slidesRef,\n    defaultIndex: index,\n    onIndexChange: onSlideChange,\n    images: props.images,\n    onTap: props.onClose,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender\n  })), props.images && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, (_c = props.renderFooter) === null || _c === void 0 ? void 0 : _c.call(props, props.images[index], index), React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n  return renderToContainer(props.getContainer, node);\n});", "map": {"version": 3, "names": ["classNames", "React", "forwardRef", "useCallback", "useImperativeHandle", "useRef", "useState", "renderToContainer", "mergeProps", "Mask", "SafeArea", "Slide", "Slides", "classPrefix", "defaultProps", "max<PERSON><PERSON>", "getContainer", "visible", "ImageViewer", "p", "_a", "_b", "_c", "props", "node", "createElement", "disableBodyScroll", "opacity", "afterClose", "destroyOnClose", "className", "mask", "body", "image", "imageRender", "onTap", "onClose", "renderFooter", "call", "position", "multiDefaultProps", "Object", "assign", "defaultIndex", "MultiImageViewer", "ref", "index", "setIndex", "slidesRef", "swipeTo", "immediate", "current", "onSlideChange", "newIndex", "onIndexChange", "images"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/image-viewer/image-viewer.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Mask from '../mask';\nimport SafeArea from '../safe-area';\nimport { Slide } from './slide';\nimport { Slides } from './slides';\nconst classPrefix = `adm-image-viewer`;\nconst defaultProps = {\n  maxZoom: 3,\n  getContainer: null,\n  visible: false\n};\nexport const ImageViewer = p => {\n  var _a, _b, _c;\n  const props = mergeProps(defaultProps, p);\n  const node = React.createElement(Mask, {\n    visible: props.visible,\n    disableBodyScroll: false,\n    opacity: 'thick',\n    afterClose: props.afterClose,\n    destroyOnClose: true,\n    className: (_a = props === null || props === void 0 ? void 0 : props.classNames) === null || _a === void 0 ? void 0 : _a.mask\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-content`, (_b = props === null || props === void 0 ? void 0 : props.classNames) === null || _b === void 0 ? void 0 : _b.body)\n  }, (props.image || typeof props.imageRender === 'function') && React.createElement(Slide, {\n    image: props.image,\n    onTap: props.onClose,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender\n  })), props.image && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, (_c = props.renderFooter) === null || _c === void 0 ? void 0 : _c.call(props, props.image), React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n  return renderToContainer(props.getContainer, node);\n};\nconst multiDefaultProps = Object.assign(Object.assign({}, defaultProps), {\n  defaultIndex: 0\n});\nexport const MultiImageViewer = forwardRef((p, ref) => {\n  var _a, _b, _c;\n  const props = mergeProps(multiDefaultProps, p);\n  const [index, setIndex] = useState(props.defaultIndex);\n  const slidesRef = useRef(null);\n  useImperativeHandle(ref, () => ({\n    swipeTo: (index, immediate) => {\n      var _a;\n      setIndex(index);\n      (_a = slidesRef.current) === null || _a === void 0 ? void 0 : _a.swipeTo(index, immediate);\n    }\n  }));\n  const onSlideChange = useCallback(newIndex => {\n    var _a;\n    if (newIndex === index) return;\n    setIndex(newIndex);\n    (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, newIndex);\n  }, [props.onIndexChange, index]);\n  const node = React.createElement(Mask, {\n    visible: props.visible,\n    disableBodyScroll: false,\n    opacity: 'thick',\n    afterClose: props.afterClose,\n    destroyOnClose: true,\n    className: (_a = props === null || props === void 0 ? void 0 : props.classNames) === null || _a === void 0 ? void 0 : _a.mask\n  }, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-content`, (_b = props === null || props === void 0 ? void 0 : props.classNames) === null || _b === void 0 ? void 0 : _b.body)\n  }, props.images && React.createElement(Slides, {\n    ref: slidesRef,\n    defaultIndex: index,\n    onIndexChange: onSlideChange,\n    images: props.images,\n    onTap: props.onClose,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender\n  })), props.images && React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, (_c = props.renderFooter) === null || _c === void 0 ? void 0 : _c.call(props, props.images[index], index), React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n  return renderToContainer(props.getContainer, node);\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7F,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,MAAM,QAAQ,UAAU;AACjC,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE,IAAI;EAClBC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGC,CAAC,IAAI;EAC9B,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,KAAK,GAAGf,UAAU,CAACM,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAMK,IAAI,GAAGvB,KAAK,CAACwB,aAAa,CAAChB,IAAI,EAAE;IACrCQ,OAAO,EAAEM,KAAK,CAACN,OAAO;IACtBS,iBAAiB,EAAE,KAAK;IACxBC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAEL,KAAK,CAACK,UAAU;IAC5BC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,CAACV,EAAE,GAAGG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACvB,UAAU,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW;EAC3H,CAAC,EAAE9B,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC5BK,SAAS,EAAE9B,UAAU,CAAC,GAAGa,WAAW,UAAU,EAAE,CAACQ,EAAE,GAAGE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACvB,UAAU,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,IAAI;EACpK,CAAC,EAAE,CAACT,KAAK,CAACU,KAAK,IAAI,OAAOV,KAAK,CAACW,WAAW,KAAK,UAAU,KAAKjC,KAAK,CAACwB,aAAa,CAACd,KAAK,EAAE;IACxFsB,KAAK,EAAEV,KAAK,CAACU,KAAK;IAClBE,KAAK,EAAEZ,KAAK,CAACa,OAAO;IACpBrB,OAAO,EAAEQ,KAAK,CAACR,OAAO;IACtBmB,WAAW,EAAEX,KAAK,CAACW;EACrB,CAAC,CAAC,CAAC,EAAEX,KAAK,CAACU,KAAK,IAAIhC,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC7CK,SAAS,EAAE,GAAGjB,WAAW;EAC3B,CAAC,EAAE,CAACS,EAAE,GAAGC,KAAK,CAACc,YAAY,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAACf,KAAK,EAAEA,KAAK,CAACU,KAAK,CAAC,EAAEhC,KAAK,CAACwB,aAAa,CAACf,QAAQ,EAAE;IAC3H6B,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOhC,iBAAiB,CAACgB,KAAK,CAACP,YAAY,EAAEQ,IAAI,CAAC;AACpD,CAAC;AACD,MAAMgB,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE5B,YAAY,CAAC,EAAE;EACvE6B,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,OAAO,MAAMC,gBAAgB,GAAG1C,UAAU,CAAC,CAACiB,CAAC,EAAE0B,GAAG,KAAK;EACrD,IAAIzB,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,KAAK,GAAGf,UAAU,CAACgC,iBAAiB,EAAErB,CAAC,CAAC;EAC9C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAACiB,KAAK,CAACoB,YAAY,CAAC;EACtD,MAAMK,SAAS,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC9BD,mBAAmB,CAACyC,GAAG,EAAE,OAAO;IAC9BI,OAAO,EAAEA,CAACH,KAAK,EAAEI,SAAS,KAAK;MAC7B,IAAI9B,EAAE;MACN2B,QAAQ,CAACD,KAAK,CAAC;MACf,CAAC1B,EAAE,GAAG4B,SAAS,CAACG,OAAO,MAAM,IAAI,IAAI/B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,OAAO,CAACH,KAAK,EAAEI,SAAS,CAAC;IAC5F;EACF,CAAC,CAAC,CAAC;EACH,MAAME,aAAa,GAAGjD,WAAW,CAACkD,QAAQ,IAAI;IAC5C,IAAIjC,EAAE;IACN,IAAIiC,QAAQ,KAAKP,KAAK,EAAE;IACxBC,QAAQ,CAACM,QAAQ,CAAC;IAClB,CAACjC,EAAE,GAAGG,KAAK,CAAC+B,aAAa,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,IAAI,CAACf,KAAK,EAAE8B,QAAQ,CAAC;EAC1F,CAAC,EAAE,CAAC9B,KAAK,CAAC+B,aAAa,EAAER,KAAK,CAAC,CAAC;EAChC,MAAMtB,IAAI,GAAGvB,KAAK,CAACwB,aAAa,CAAChB,IAAI,EAAE;IACrCQ,OAAO,EAAEM,KAAK,CAACN,OAAO;IACtBS,iBAAiB,EAAE,KAAK;IACxBC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAEL,KAAK,CAACK,UAAU;IAC5BC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,CAACV,EAAE,GAAGG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACvB,UAAU,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW;EAC3H,CAAC,EAAE9B,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC5BK,SAAS,EAAE9B,UAAU,CAAC,GAAGa,WAAW,UAAU,EAAE,CAACQ,EAAE,GAAGE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACvB,UAAU,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,IAAI;EACpK,CAAC,EAAET,KAAK,CAACgC,MAAM,IAAItD,KAAK,CAACwB,aAAa,CAACb,MAAM,EAAE;IAC7CiC,GAAG,EAAEG,SAAS;IACdL,YAAY,EAAEG,KAAK;IACnBQ,aAAa,EAAEF,aAAa;IAC5BG,MAAM,EAAEhC,KAAK,CAACgC,MAAM;IACpBpB,KAAK,EAAEZ,KAAK,CAACa,OAAO;IACpBrB,OAAO,EAAEQ,KAAK,CAACR,OAAO;IACtBmB,WAAW,EAAEX,KAAK,CAACW;EACrB,CAAC,CAAC,CAAC,EAAEX,KAAK,CAACgC,MAAM,IAAItD,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE;IAC9CK,SAAS,EAAE,GAAGjB,WAAW;EAC3B,CAAC,EAAE,CAACS,EAAE,GAAGC,KAAK,CAACc,YAAY,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAACf,KAAK,EAAEA,KAAK,CAACgC,MAAM,CAACT,KAAK,CAAC,EAAEA,KAAK,CAAC,EAAE7C,KAAK,CAACwB,aAAa,CAACf,QAAQ,EAAE;IAC1I6B,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOhC,iBAAiB,CAACgB,KAAK,CAACP,YAAY,EAAEQ,IAAI,CAAC;AACpD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}