{"ast": null, "code": "import * as React from \"react\";\nfunction StopOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StopOutline-StopOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StopOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StopOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M5,24 C5,34.4934102 13.5065898,43 24,43 C28.3881152,43 32.4287892,41.5124272 35.6452438,39.0140597 C35.8010004,38.8930764 36.0302257,38.7063293 36.3329196,38.4538182 C36.0423945,38.1632931 35.8250183,37.9459169 35.680791,37.8016896 C24.4857058,26.6066044 16.0626081,18.1835068 10.4114979,12.5323965 C10.2192054,12.340104 9.93076673,12.0516653 9.54618179,11.6670804 C9.19276878,12.0948211 8.93297966,12.4199447 8.76681444,12.6424511 C6.40083552,15.8106567 5,19.741737 5,24 Z M24,5 C19.6974062,5 15.7288428,6.43015424 12.5437515,8.84102107 C12.3444748,8.99185806 12.052584,9.22662733 11.6680791,9.54532886 C12.0451541,9.92242994 12.3279604,10.2052558 12.5164979,10.3938063 C18.174104,16.0518035 26.608371,24.4866535 37.8192988,35.6983563 C37.9596503,35.8387176 38.1711568,36.0502387 38.4538182,36.3329196 C38.7029135,36.0344168 38.8871714,35.8083941 39.006592,35.6548516 C41.5094626,32.4368374 43,28.3924855 43,24 C43,13.5065898 34.4934102,5 24,5 Z\",\n    id: \"StopOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default StopOutline;", "map": {"version": 3, "names": ["React", "StopOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/StopOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction StopOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StopOutline-StopOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StopOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StopOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M5,24 C5,34.4934102 13.5065898,43 24,43 C28.3881152,43 32.4287892,41.5124272 35.6452438,39.0140597 C35.8010004,38.8930764 36.0302257,38.7063293 36.3329196,38.4538182 C36.0423945,38.1632931 35.8250183,37.9459169 35.680791,37.8016896 C24.4857058,26.6066044 16.0626081,18.1835068 10.4114979,12.5323965 C10.2192054,12.340104 9.93076673,12.0516653 9.54618179,11.6670804 C9.19276878,12.0948211 8.93297966,12.4199447 8.76681444,12.6424511 C6.40083552,15.8106567 5,19.741737 5,24 Z M24,5 C19.6974062,5 15.7288428,6.43015424 12.5437515,8.84102107 C12.3444748,8.99185806 12.052584,9.22662733 11.6680791,9.54532886 C12.0451541,9.92242994 12.3279604,10.2052558 12.5164979,10.3938063 C18.174104,16.0518035 26.608371,24.4866535 37.8192988,35.6983563 C37.9596503,35.8387176 38.1711568,36.0502387 38.4538182,36.3329196 C38.7029135,36.0344168 38.8871714,35.8083941 39.006592,35.6548516 C41.5094626,32.4368374 43,28.3924855 43,24 C43,13.5065898 34.4934102,5 24,5 Z\",\n    id: \"StopOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default StopOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,kkCAAkkC;IACrkCR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}