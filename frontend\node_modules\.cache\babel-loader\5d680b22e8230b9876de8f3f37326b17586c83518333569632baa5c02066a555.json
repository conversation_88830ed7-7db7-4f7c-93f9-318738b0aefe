{"ast": null, "code": "import * as React from 'react';\nexport default function useEvent(callback) {\n  var fnRef = React.useRef();\n  fnRef.current = callback;\n  var memoFn = React.useCallback(function () {\n    var _fnRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_fnRef$current = fnRef.current) === null || _fnRef$current === void 0 ? void 0 : _fnRef$current.call.apply(_fnRef$current, [fnRef].concat(args));\n  }, []);\n  return memoFn;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}