{"ast": null, "code": "import \"./toast.css\";\nimport { clear, show, config } from './methods';\nconst Toast = {\n  show,\n  clear,\n  config\n};\nexport default Toast;", "map": {"version": 3, "names": ["clear", "show", "config", "Toast"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/toast/index.js"], "sourcesContent": ["import \"./toast.css\";\nimport { clear, show, config } from './methods';\nconst Toast = {\n  show,\n  clear,\n  config\n};\nexport default Toast;"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,KAAK,EAAEC,IAAI,EAAEC,MAAM,QAAQ,WAAW;AAC/C,MAAMC,KAAK,GAAG;EACZF,IAAI;EACJD,KAAK;EACLE;AACF,CAAC;AACD,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}