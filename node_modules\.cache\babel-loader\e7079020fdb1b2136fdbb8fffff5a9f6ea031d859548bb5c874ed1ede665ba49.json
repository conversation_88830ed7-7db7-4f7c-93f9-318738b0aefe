{"ast": null, "code": "import { isDev } from './is-dev';\nimport { devError } from './dev-log';\nexport function measureCSSLength(raw) {\n  if (raw === null || raw === undefined || raw === '') {\n    if (isDev) {\n      devError('Global', 'Something went wrong when calculating CSS length. Please report an issue at https://github.com/ant-design/ant-design-mobile/issues/new/choose');\n    }\n    return 0;\n  }\n  const withUnit = raw.trim();\n  if (withUnit.endsWith('px')) {\n    return parseFloat(withUnit);\n  } else if (withUnit.endsWith('rem')) {\n    return parseFloat(withUnit) * parseFloat(window.getComputedStyle(document.documentElement).fontSize);\n  } else if (withUnit.endsWith('vw')) {\n    return parseFloat(withUnit) * window.innerWidth / 100;\n  } else {\n    if (isDev) {\n      devError('Global', `You are using a not supported CSS unit in \\`${raw}\\`. Only \\`px\\` \\`rem\\` and \\`vw\\` are supported.`);\n    }\n    return 0;\n  }\n}", "map": {"version": 3, "names": ["isDev", "dev<PERSON><PERSON><PERSON>", "measureCSSLength", "raw", "undefined", "with<PERSON><PERSON><PERSON>", "trim", "endsWith", "parseFloat", "window", "getComputedStyle", "document", "documentElement", "fontSize", "innerWidth"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/measure-css-length.js"], "sourcesContent": ["import { isDev } from './is-dev';\nimport { devError } from './dev-log';\nexport function measureCSSLength(raw) {\n  if (raw === null || raw === undefined || raw === '') {\n    if (isDev) {\n      devError('Global', 'Something went wrong when calculating CSS length. Please report an issue at https://github.com/ant-design/ant-design-mobile/issues/new/choose');\n    }\n    return 0;\n  }\n  const withUnit = raw.trim();\n  if (withUnit.endsWith('px')) {\n    return parseFloat(withUnit);\n  } else if (withUnit.endsWith('rem')) {\n    return parseFloat(withUnit) * parseFloat(window.getComputedStyle(document.documentElement).fontSize);\n  } else if (withUnit.endsWith('vw')) {\n    return parseFloat(withUnit) * window.innerWidth / 100;\n  } else {\n    if (isDev) {\n      devError('Global', `You are using a not supported CSS unit in \\`${raw}\\`. Only \\`px\\` \\`rem\\` and \\`vw\\` are supported.`);\n    }\n    return 0;\n  }\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,UAAU;AAChC,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAO,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EACpC,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,EAAE;IACnD,IAAIH,KAAK,EAAE;MACTC,QAAQ,CAAC,QAAQ,EAAE,+IAA+I,CAAC;IACrK;IACA,OAAO,CAAC;EACV;EACA,MAAMI,QAAQ,GAAGF,GAAG,CAACG,IAAI,CAAC,CAAC;EAC3B,IAAID,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC3B,OAAOC,UAAU,CAACH,QAAQ,CAAC;EAC7B,CAAC,MAAM,IAAIA,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;IACnC,OAAOC,UAAU,CAACH,QAAQ,CAAC,GAAGG,UAAU,CAACC,MAAM,CAACC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC,CAACC,QAAQ,CAAC;EACtG,CAAC,MAAM,IAAIR,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;IAClC,OAAOC,UAAU,CAACH,QAAQ,CAAC,GAAGI,MAAM,CAACK,UAAU,GAAG,GAAG;EACvD,CAAC,MAAM;IACL,IAAId,KAAK,EAAE;MACTC,QAAQ,CAAC,QAAQ,EAAE,+CAA+CE,GAAG,mDAAmD,CAAC;IAC3H;IACA,OAAO,CAAC;EACV;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}