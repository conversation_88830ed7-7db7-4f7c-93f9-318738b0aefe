{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement';\n// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\nexport default useRequest;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useAutoRunPlugin", "useCachePlugin", "useDebouncePlugin", "useLoadingDelayPlugin", "usePollingPlugin", "useRefreshOnWindowFocusPlugin", "useRetryPlugin", "useThrottlePlugin", "useRequestImplement", "useRequest", "service", "options", "plugins"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/useRequest.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement';\n// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\nexport default useRequest;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,OAAOC,qBAAqB,MAAM,iCAAiC;AACnE,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,6BAA6B,MAAM,yCAAyC;AACnF,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC7C,OAAOJ,mBAAmB,CAACE,OAAO,EAAEC,OAAO,EAAEZ,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACc,OAAO,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAACV,iBAAiB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,6BAA6B,EAAEE,iBAAiB,EAAEP,gBAAgB,EAAEC,cAAc,EAAEK,cAAc,CAAC,EAAE,KAAK,CAAC,CAAC;AACvR;AACA,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}