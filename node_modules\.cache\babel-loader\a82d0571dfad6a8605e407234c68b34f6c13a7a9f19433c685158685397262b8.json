{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nexport function withNativeProps(props, element) {\n  const p = Object.assign({}, element.props);\n  if (props.className) {\n    p.className = classNames(element.props.className, props.className);\n  }\n  if (props.style) {\n    p.style = Object.assign(Object.assign({}, p.style), props.style);\n  }\n  if (props.tabIndex !== undefined) {\n    p.tabIndex = props.tabIndex;\n  }\n  for (const key in props) {\n    if (!props.hasOwnProperty(key)) continue;\n    if (key.startsWith('data-') || key.startsWith('aria-')) {\n      p[key] = props[key];\n    }\n  }\n  return React.cloneElement(element, p);\n}", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "props", "element", "p", "Object", "assign", "className", "style", "tabIndex", "undefined", "key", "hasOwnProperty", "startsWith", "cloneElement"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/native-props.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nexport function withNativeProps(props, element) {\n  const p = Object.assign({}, element.props);\n  if (props.className) {\n    p.className = classNames(element.props.className, props.className);\n  }\n  if (props.style) {\n    p.style = Object.assign(Object.assign({}, p.style), props.style);\n  }\n  if (props.tabIndex !== undefined) {\n    p.tabIndex = props.tabIndex;\n  }\n  for (const key in props) {\n    if (!props.hasOwnProperty(key)) continue;\n    if (key.startsWith('data-') || key.startsWith('aria-')) {\n      p[key] = props[key];\n    }\n  }\n  return React.cloneElement(element, p);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC9C,MAAMC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACD,KAAK,CAAC;EAC1C,IAAIA,KAAK,CAACK,SAAS,EAAE;IACnBH,CAAC,CAACG,SAAS,GAAGP,UAAU,CAACG,OAAO,CAACD,KAAK,CAACK,SAAS,EAAEL,KAAK,CAACK,SAAS,CAAC;EACpE;EACA,IAAIL,KAAK,CAACM,KAAK,EAAE;IACfJ,CAAC,CAACI,KAAK,GAAGH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACI,KAAK,CAAC,EAAEN,KAAK,CAACM,KAAK,CAAC;EAClE;EACA,IAAIN,KAAK,CAACO,QAAQ,KAAKC,SAAS,EAAE;IAChCN,CAAC,CAACK,QAAQ,GAAGP,KAAK,CAACO,QAAQ;EAC7B;EACA,KAAK,MAAME,GAAG,IAAIT,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,CAACU,cAAc,CAACD,GAAG,CAAC,EAAE;IAChC,IAAIA,GAAG,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,GAAG,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;MACtDT,CAAC,CAACO,GAAG,CAAC,GAAGT,KAAK,CAACS,GAAG,CAAC;IACrB;EACF;EACA,OAAOZ,KAAK,CAACe,YAAY,CAACX,OAAO,EAAEC,CAAC,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}