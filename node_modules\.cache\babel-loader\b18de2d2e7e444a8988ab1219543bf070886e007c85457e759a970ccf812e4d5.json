{"ast": null, "code": "import { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { prompt } from './prompt';\nimport { CascadePicker } from './cascade-picker';\nexport default attachPropertiesToComponent(CascadePicker, {\n  prompt\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "prompt", "CascadePicker"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascade-picker/index.js"], "sourcesContent": ["import { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { prompt } from './prompt';\nimport { CascadePicker } from './cascade-picker';\nexport default attachPropertiesToComponent(CascadePicker, {\n  prompt\n});"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,eAAeF,2BAA2B,CAACE,aAAa,EAAE;EACxDD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}