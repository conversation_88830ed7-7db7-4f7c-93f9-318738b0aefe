{"ast": null, "code": "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport isBrowser from '../utils/isBrowser';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar touchSupported = isBrowser && (\n// @ts-ignore\n'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.delay,\n    delay = _c === void 0 ? 300 : _c,\n    moveThreshold = _b.moveThreshold,\n    onClick = _b.onClick,\n    onLongPressEnd = _b.onLongPressEnd;\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef();\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var overThreshold = function (event) {\n      var _a = getClientPosition(event),\n        clientX = _a.clientX,\n        clientY = _a.clientY;\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n    function getClientPosition(event) {\n      if ('TouchEvent' in window && event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n      console.warn('Unsupported event type');\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n    var onStart = function (event) {\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n    var onMove = function (event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n    var onEnd = function (event, shouldTriggerClick) {\n      var _a;\n      if (shouldTriggerClick === void 0) {\n        shouldTriggerClick = false;\n      }\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      }\n      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onEndWithClick = function (event) {\n      return onEnd(event, true);\n    };\n    if (!touchSupported) {\n      targetElement.addEventListener('mousedown', onStart);\n      targetElement.addEventListener('mouseup', onEndWithClick);\n      targetElement.addEventListener('mouseleave', onEnd);\n      if (hasMoveThreshold) targetElement.addEventListener('mousemove', onMove);\n    } else {\n      targetElement.addEventListener('touchstart', onStart);\n      targetElement.addEventListener('touchend', onEndWithClick);\n      if (hasMoveThreshold) targetElement.addEventListener('touchmove', onMove);\n    }\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n      if (!touchSupported) {\n        targetElement.removeEventListener('mousedown', onStart);\n        targetElement.removeEventListener('mouseup', onEndWithClick);\n        targetElement.removeEventListener('mouseleave', onEnd);\n        if (hasMoveThreshold) targetElement.removeEventListener('mousemove', onMove);\n      } else {\n        targetElement.removeEventListener('touchstart', onStart);\n        targetElement.removeEventListener('touchend', onEndWithClick);\n        if (hasMoveThreshold) targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\nexport default useLongPress;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}