{"ast": null, "code": "import { useCallback } from 'react';\nimport { useConfig } from '../config-provider';\nexport default function useRenderLabel(renderLabel) {\n  const {\n    locale\n  } = useConfig();\n  return useCallback((type, data) => {\n    if (renderLabel) {\n      return renderLabel(type, data);\n    }\n    // Default render\n    switch (type) {\n      case 'minute':\n      case 'second':\n      case 'hour':\n        return ('0' + data.toString()).slice(-2);\n      case 'now':\n        return locale.DatePicker.tillNow;\n      default:\n        return data.toString();\n    }\n  }, [renderLabel]);\n}", "map": {"version": 3, "names": ["useCallback", "useConfig", "useRenderLabel", "renderLabel", "locale", "type", "data", "toString", "slice", "DatePicker", "tillNow"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/date-picker-view/useRenderLabel.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport { useConfig } from '../config-provider';\nexport default function useRenderLabel(renderLabel) {\n  const {\n    locale\n  } = useConfig();\n  return useCallback((type, data) => {\n    if (renderLabel) {\n      return renderLabel(type, data);\n    }\n    // Default render\n    switch (type) {\n      case 'minute':\n      case 'second':\n      case 'hour':\n        return ('0' + data.toString()).slice(-2);\n      case 'now':\n        return locale.DatePicker.tillNow;\n      default:\n        return data.toString();\n    }\n  }, [renderLabel]);\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,eAAe,SAASC,cAAcA,CAACC,WAAW,EAAE;EAClD,MAAM;IACJC;EACF,CAAC,GAAGH,SAAS,CAAC,CAAC;EACf,OAAOD,WAAW,CAAC,CAACK,IAAI,EAAEC,IAAI,KAAK;IACjC,IAAIH,WAAW,EAAE;MACf,OAAOA,WAAW,CAACE,IAAI,EAAEC,IAAI,CAAC;IAChC;IACA;IACA,QAAQD,IAAI;MACV,KAAK,QAAQ;MACb,KAAK,QAAQ;MACb,KAAK,MAAM;QACT,OAAO,CAAC,GAAG,GAAGC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1C,KAAK,KAAK;QACR,OAAOJ,MAAM,CAACK,UAAU,CAACC,OAAO;MAClC;QACE,OAAOJ,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACJ,WAAW,CAAC,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}