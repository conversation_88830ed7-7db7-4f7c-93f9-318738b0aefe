{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n    isFocusWithin = _a[0],\n    setIsFocusWithin = _a[1];\n  var _b = options || {},\n    onFocus = _b.onFocus,\n    onBlur = _b.onBlur,\n    onChange = _b.onChange;\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}