{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ThumbIcon = props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 24 24',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor',\n    fillRule: 'evenodd'\n  }, React.createElement(\"rect\", {\n    x: 10,\n    width: 4,\n    height: 24,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    x: 20,\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }))));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}