{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nexport default function useSelections(items, options) {\n  var _a, _b;\n  var defaultSelected = [];\n  var itemKey;\n  if (Array.isArray(options)) {\n    defaultSelected = options;\n  } else if (isPlainObject(options)) {\n    defaultSelected = (_a = options === null || options === void 0 ? void 0 : options.defaultSelected) !== null && _a !== void 0 ? _a : defaultSelected;\n    itemKey = (_b = options === null || options === void 0 ? void 0 : options.itemKey) !== null && _b !== void 0 ? _b : itemKey;\n  }\n  var getKey = function (item) {\n    if (isFunction(itemKey)) {\n      return itemKey(item);\n    }\n    if (isString(itemKey) && isPlainObject(item)) {\n      return item[itemKey];\n    }\n    return item;\n  };\n  var _c = __read(useState(defaultSelected), 2),\n    selected = _c[0],\n    setSelected = _c[1];\n  var selectedMap = useMemo(function () {\n    var keyToItemMap = new Map();\n    if (!Array.isArray(selected)) {\n      return keyToItemMap;\n    }\n    selected.forEach(function (item) {\n      keyToItemMap.set(getKey(item), item);\n    });\n    return keyToItemMap;\n  }, [selected]);\n  var isSelected = function (item) {\n    return selectedMap.has(getKey(item));\n  };\n  var select = function (item) {\n    selectedMap.set(getKey(item), item);\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelect = function (item) {\n    selectedMap.delete(getKey(item));\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var toggle = function (item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n  var selectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.set(getKey(item), item);\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.delete(getKey(item));\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var noneSelected = useMemo(function () {\n    return items.every(function (item) {\n      return !selectedMap.has(getKey(item));\n    });\n  }, [items, selectedMap]);\n  var allSelected = useMemo(function () {\n    return items.every(function (item) {\n      return selectedMap.has(getKey(item));\n    }) && !noneSelected;\n  }, [items, selectedMap, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n  var toggleAll = function () {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n  var clearAll = function () {\n    selectedMap.clear();\n    setSelected([]);\n  };\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    clearAll: useMemoizedFn(clearAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}