{"ast": null, "code": "import React, { useRef, useEffect, useState } from 'react';\nimport { useSpring, animated, to } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-floating-bubble`;\nconst defaultProps = {\n  axis: 'y',\n  defaultOffset: {\n    x: 0,\n    y: 0\n  }\n};\nexport const FloatingBubble = p => {\n  const props = mergeProps(defaultProps, p);\n  const boundaryRef = useRef(null);\n  const buttonRef = useRef(null);\n  const [innerValue, setInnerValue] = useState(props.offset === undefined ? props.defaultOffset : props.offset);\n  useEffect(() => {\n    if (props.offset === undefined) return;\n    api.start({\n      x: props.offset.x,\n      y: props.offset.y\n    });\n  }, [props.offset]);\n  /**\n   * memoize the `to` function\n   * inside a component that renders frequently\n   * to prevent an unintended restart\n   */\n  const [{\n    x,\n    y,\n    opacity\n  }, api] = useSpring(() => ({\n    x: innerValue.x,\n    y: innerValue.y,\n    opacity: 1\n  }));\n  const bind = useDrag(state => {\n    var _a;\n    let nextX = state.offset[0];\n    let nextY = state.offset[1];\n    if (state.last && props.magnetic) {\n      const boundary = boundaryRef.current;\n      const button = buttonRef.current;\n      if (!boundary || !button) return;\n      const boundaryRect = boundary.getBoundingClientRect();\n      const buttonRect = button.getBoundingClientRect();\n      if (props.magnetic === 'x') {\n        const compensation = x.goal - x.get();\n        const leftDistance = buttonRect.left + compensation - boundaryRect.left;\n        const rightDistance = boundaryRect.right - (buttonRect.right + compensation);\n        if (rightDistance <= leftDistance) {\n          nextX += rightDistance;\n        } else {\n          nextX -= leftDistance;\n        }\n      } else if (props.magnetic === 'y') {\n        const compensation = y.goal - y.get();\n        const topDistance = buttonRect.top + compensation - boundaryRect.top;\n        const bottomDistance = boundaryRect.bottom - (buttonRect.bottom + compensation);\n        if (bottomDistance <= topDistance) {\n          nextY += bottomDistance;\n        } else {\n          nextY -= topDistance;\n        }\n      }\n    }\n    const nextOffest = {\n      x: nextX,\n      y: nextY\n    };\n    if (props.offset === undefined) {\n      // Uncontrolled mode\n      api.start(nextOffest);\n    } else {\n      setInnerValue(nextOffest);\n    }\n    (_a = props.onOffsetChange) === null || _a === void 0 ? void 0 : _a.call(props, nextOffest);\n    // active status\n    api.start({\n      opacity: state.active ? 0.8 : 1\n    });\n  }, {\n    axis: props.axis === 'xy' ? undefined : props.axis,\n    pointer: {\n      touch: true\n    },\n    // the component won't trigger drag logic if the user just clicked on the component.\n    filterTaps: true,\n    // set constraints to the user gesture\n    bounds: boundaryRef,\n    from: () => [x.get(), y.get()]\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-boundary-outer`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-boundary`,\n    ref: boundaryRef\n  })), React.createElement(animated.div, Object.assign({}, bind(), {\n    style: {\n      opacity,\n      transform: to([x, y], (x, y) => `translate(${x}px, ${y}px)`)\n    },\n    onClick: props.onClick,\n    className: `${classPrefix}-button`,\n    ref: buttonRef\n  }), props.children)));\n};", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "useSpring", "animated", "to", "useDrag", "mergeProps", "withNativeProps", "classPrefix", "defaultProps", "axis", "defaultOffset", "x", "y", "FloatingBubble", "p", "props", "boundaryRef", "buttonRef", "innerValue", "setInnerValue", "offset", "undefined", "api", "start", "opacity", "bind", "state", "_a", "nextX", "nextY", "last", "magnetic", "boundary", "current", "button", "boundaryRect", "getBoundingClientRect", "buttonRect", "compensation", "goal", "get", "leftDistance", "left", "rightDistance", "right", "topDistance", "top", "bottomDistance", "bottom", "nextOffest", "onOffsetChange", "call", "active", "pointer", "touch", "filterTaps", "bounds", "from", "createElement", "className", "ref", "div", "Object", "assign", "style", "transform", "onClick", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/floating-bubble/floating-bubble.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport { useSpring, animated, to } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-floating-bubble`;\nconst defaultProps = {\n  axis: 'y',\n  defaultOffset: {\n    x: 0,\n    y: 0\n  }\n};\nexport const FloatingBubble = p => {\n  const props = mergeProps(defaultProps, p);\n  const boundaryRef = useRef(null);\n  const buttonRef = useRef(null);\n  const [innerValue, setInnerValue] = useState(props.offset === undefined ? props.defaultOffset : props.offset);\n  useEffect(() => {\n    if (props.offset === undefined) return;\n    api.start({\n      x: props.offset.x,\n      y: props.offset.y\n    });\n  }, [props.offset]);\n  /**\n   * memoize the `to` function\n   * inside a component that renders frequently\n   * to prevent an unintended restart\n   */\n  const [{\n    x,\n    y,\n    opacity\n  }, api] = useSpring(() => ({\n    x: innerValue.x,\n    y: innerValue.y,\n    opacity: 1\n  }));\n  const bind = useDrag(state => {\n    var _a;\n    let nextX = state.offset[0];\n    let nextY = state.offset[1];\n    if (state.last && props.magnetic) {\n      const boundary = boundaryRef.current;\n      const button = buttonRef.current;\n      if (!boundary || !button) return;\n      const boundaryRect = boundary.getBoundingClientRect();\n      const buttonRect = button.getBoundingClientRect();\n      if (props.magnetic === 'x') {\n        const compensation = x.goal - x.get();\n        const leftDistance = buttonRect.left + compensation - boundaryRect.left;\n        const rightDistance = boundaryRect.right - (buttonRect.right + compensation);\n        if (rightDistance <= leftDistance) {\n          nextX += rightDistance;\n        } else {\n          nextX -= leftDistance;\n        }\n      } else if (props.magnetic === 'y') {\n        const compensation = y.goal - y.get();\n        const topDistance = buttonRect.top + compensation - boundaryRect.top;\n        const bottomDistance = boundaryRect.bottom - (buttonRect.bottom + compensation);\n        if (bottomDistance <= topDistance) {\n          nextY += bottomDistance;\n        } else {\n          nextY -= topDistance;\n        }\n      }\n    }\n    const nextOffest = {\n      x: nextX,\n      y: nextY\n    };\n    if (props.offset === undefined) {\n      // Uncontrolled mode\n      api.start(nextOffest);\n    } else {\n      setInnerValue(nextOffest);\n    }\n    (_a = props.onOffsetChange) === null || _a === void 0 ? void 0 : _a.call(props, nextOffest);\n    // active status\n    api.start({\n      opacity: state.active ? 0.8 : 1\n    });\n  }, {\n    axis: props.axis === 'xy' ? undefined : props.axis,\n    pointer: {\n      touch: true\n    },\n    // the component won't trigger drag logic if the user just clicked on the component.\n    filterTaps: true,\n    // set constraints to the user gesture\n    bounds: boundaryRef,\n    from: () => [x.get(), y.get()]\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-boundary-outer`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-boundary`,\n    ref: boundaryRef\n  })), React.createElement(animated.div, Object.assign({}, bind(), {\n    style: {\n      opacity,\n      transform: to([x, y], (x, y) => `translate(${x}px, ${y}px)`)\n    },\n    onClick: props.onClick,\n    className: `${classPrefix}-button`,\n    ref: buttonRef\n  }), props.children)));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,QAAQ,EAAEC,EAAE,QAAQ,mBAAmB;AAC3D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,qBAAqB;AACzC,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,GAAG;EACTC,aAAa,EAAE;IACbC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL;AACF,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGC,CAAC,IAAI;EACjC,MAAMC,KAAK,GAAGV,UAAU,CAACG,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAME,WAAW,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMmB,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAACe,KAAK,CAACK,MAAM,KAAKC,SAAS,GAAGN,KAAK,CAACL,aAAa,GAAGK,KAAK,CAACK,MAAM,CAAC;EAC7GrB,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,CAACK,MAAM,KAAKC,SAAS,EAAE;IAChCC,GAAG,CAACC,KAAK,CAAC;MACRZ,CAAC,EAAEI,KAAK,CAACK,MAAM,CAACT,CAAC;MACjBC,CAAC,EAAEG,KAAK,CAACK,MAAM,CAACR;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,KAAK,CAACK,MAAM,CAAC,CAAC;EAClB;AACF;AACA;AACA;AACA;EACE,MAAM,CAAC;IACLT,CAAC;IACDC,CAAC;IACDY;EACF,CAAC,EAAEF,GAAG,CAAC,GAAGrB,SAAS,CAAC,OAAO;IACzBU,CAAC,EAAEO,UAAU,CAACP,CAAC;IACfC,CAAC,EAAEM,UAAU,CAACN,CAAC;IACfY,OAAO,EAAE;EACX,CAAC,CAAC,CAAC;EACH,MAAMC,IAAI,GAAGrB,OAAO,CAACsB,KAAK,IAAI;IAC5B,IAAIC,EAAE;IACN,IAAIC,KAAK,GAAGF,KAAK,CAACN,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIS,KAAK,GAAGH,KAAK,CAACN,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIM,KAAK,CAACI,IAAI,IAAIf,KAAK,CAACgB,QAAQ,EAAE;MAChC,MAAMC,QAAQ,GAAGhB,WAAW,CAACiB,OAAO;MACpC,MAAMC,MAAM,GAAGjB,SAAS,CAACgB,OAAO;MAChC,IAAI,CAACD,QAAQ,IAAI,CAACE,MAAM,EAAE;MAC1B,MAAMC,YAAY,GAAGH,QAAQ,CAACI,qBAAqB,CAAC,CAAC;MACrD,MAAMC,UAAU,GAAGH,MAAM,CAACE,qBAAqB,CAAC,CAAC;MACjD,IAAIrB,KAAK,CAACgB,QAAQ,KAAK,GAAG,EAAE;QAC1B,MAAMO,YAAY,GAAG3B,CAAC,CAAC4B,IAAI,GAAG5B,CAAC,CAAC6B,GAAG,CAAC,CAAC;QACrC,MAAMC,YAAY,GAAGJ,UAAU,CAACK,IAAI,GAAGJ,YAAY,GAAGH,YAAY,CAACO,IAAI;QACvE,MAAMC,aAAa,GAAGR,YAAY,CAACS,KAAK,IAAIP,UAAU,CAACO,KAAK,GAAGN,YAAY,CAAC;QAC5E,IAAIK,aAAa,IAAIF,YAAY,EAAE;UACjCb,KAAK,IAAIe,aAAa;QACxB,CAAC,MAAM;UACLf,KAAK,IAAIa,YAAY;QACvB;MACF,CAAC,MAAM,IAAI1B,KAAK,CAACgB,QAAQ,KAAK,GAAG,EAAE;QACjC,MAAMO,YAAY,GAAG1B,CAAC,CAAC2B,IAAI,GAAG3B,CAAC,CAAC4B,GAAG,CAAC,CAAC;QACrC,MAAMK,WAAW,GAAGR,UAAU,CAACS,GAAG,GAAGR,YAAY,GAAGH,YAAY,CAACW,GAAG;QACpE,MAAMC,cAAc,GAAGZ,YAAY,CAACa,MAAM,IAAIX,UAAU,CAACW,MAAM,GAAGV,YAAY,CAAC;QAC/E,IAAIS,cAAc,IAAIF,WAAW,EAAE;UACjChB,KAAK,IAAIkB,cAAc;QACzB,CAAC,MAAM;UACLlB,KAAK,IAAIgB,WAAW;QACtB;MACF;IACF;IACA,MAAMI,UAAU,GAAG;MACjBtC,CAAC,EAAEiB,KAAK;MACRhB,CAAC,EAAEiB;IACL,CAAC;IACD,IAAId,KAAK,CAACK,MAAM,KAAKC,SAAS,EAAE;MAC9B;MACAC,GAAG,CAACC,KAAK,CAAC0B,UAAU,CAAC;IACvB,CAAC,MAAM;MACL9B,aAAa,CAAC8B,UAAU,CAAC;IAC3B;IACA,CAACtB,EAAE,GAAGZ,KAAK,CAACmC,cAAc,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,IAAI,CAACpC,KAAK,EAAEkC,UAAU,CAAC;IAC3F;IACA3B,GAAG,CAACC,KAAK,CAAC;MACRC,OAAO,EAAEE,KAAK,CAAC0B,MAAM,GAAG,GAAG,GAAG;IAChC,CAAC,CAAC;EACJ,CAAC,EAAE;IACD3C,IAAI,EAAEM,KAAK,CAACN,IAAI,KAAK,IAAI,GAAGY,SAAS,GAAGN,KAAK,CAACN,IAAI;IAClD4C,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACD;IACAC,UAAU,EAAE,IAAI;IAChB;IACAC,MAAM,EAAExC,WAAW;IACnByC,IAAI,EAAEA,CAAA,KAAM,CAAC9C,CAAC,CAAC6B,GAAG,CAAC,CAAC,EAAE5B,CAAC,CAAC4B,GAAG,CAAC,CAAC;EAC/B,CAAC,CAAC;EACF,OAAOlC,eAAe,CAACS,KAAK,EAAElB,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEpD;EACb,CAAC,EAAEV,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpD,WAAW;EAC3B,CAAC,EAAEV,KAAK,CAAC6D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGpD,WAAW,WAAW;IACpCqD,GAAG,EAAE5C;EACP,CAAC,CAAC,CAAC,EAAEnB,KAAK,CAAC6D,aAAa,CAACxD,QAAQ,CAAC2D,GAAG,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtC,IAAI,CAAC,CAAC,EAAE;IAC/DuC,KAAK,EAAE;MACLxC,OAAO;MACPyC,SAAS,EAAE9D,EAAE,CAAC,CAACQ,CAAC,EAAEC,CAAC,CAAC,EAAE,CAACD,CAAC,EAAEC,CAAC,KAAK,aAAaD,CAAC,OAAOC,CAAC,KAAK;IAC7D,CAAC;IACDsD,OAAO,EAAEnD,KAAK,CAACmD,OAAO;IACtBP,SAAS,EAAE,GAAGpD,WAAW,SAAS;IAClCqD,GAAG,EAAE3C;EACP,CAAC,CAAC,EAAEF,KAAK,CAACoD,QAAQ,CAAC,CAAC,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}