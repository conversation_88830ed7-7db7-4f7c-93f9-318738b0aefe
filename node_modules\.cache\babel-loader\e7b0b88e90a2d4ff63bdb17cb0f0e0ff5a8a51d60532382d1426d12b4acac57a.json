{"ast": null, "code": "import { useEvent } from 'rc-util';\nimport { useEffect, useRef } from 'react';\nexport default function useSyncScroll(current, visible, bodyRef) {\n  const rafRef = useRef();\n  const clean = () => {\n    if (rafRef.current) {\n      cancelAnimationFrame(rafRef.current);\n    }\n  };\n  const scrollTo = useEvent(date => {\n    clean();\n    rafRef.current = requestAnimationFrame(() => {\n      if (bodyRef.current) {\n        const yearMonth = date.format('YYYY-M');\n        const target = bodyRef.current.querySelector(`[data-year-month=\"${yearMonth}\"]`);\n        if (target) {\n          // Scroll to the top of view\n          target.scrollIntoView({\n            block: 'start',\n            inline: 'nearest'\n          });\n        }\n      }\n    });\n  });\n  useEffect(() => {\n    if (visible && current) {\n      scrollTo(current);\n      return clean;\n    }\n  }, [current, visible]);\n  return scrollTo;\n}", "map": {"version": 3, "names": ["useEvent", "useEffect", "useRef", "useSyncScroll", "current", "visible", "bodyRef", "rafRef", "clean", "cancelAnimationFrame", "scrollTo", "date", "requestAnimationFrame", "yearMonth", "format", "target", "querySelector", "scrollIntoView", "block", "inline"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/calendar-picker-view/useSyncScroll.js"], "sourcesContent": ["import { useEvent } from 'rc-util';\nimport { useEffect, useRef } from 'react';\nexport default function useSyncScroll(current, visible, bodyRef) {\n  const rafRef = useRef();\n  const clean = () => {\n    if (rafRef.current) {\n      cancelAnimationFrame(rafRef.current);\n    }\n  };\n  const scrollTo = useEvent(date => {\n    clean();\n    rafRef.current = requestAnimationFrame(() => {\n      if (bodyRef.current) {\n        const yearMonth = date.format('YYYY-M');\n        const target = bodyRef.current.querySelector(`[data-year-month=\"${yearMonth}\"]`);\n        if (target) {\n          // Scroll to the top of view\n          target.scrollIntoView({\n            block: 'start',\n            inline: 'nearest'\n          });\n        }\n      }\n    });\n  });\n  useEffect(() => {\n    if (visible && current) {\n      scrollTo(current);\n      return clean;\n    }\n  }, [current, visible]);\n  return scrollTo;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAClC,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/D,MAAMC,MAAM,GAAGL,MAAM,CAAC,CAAC;EACvB,MAAMM,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAID,MAAM,CAACH,OAAO,EAAE;MAClBK,oBAAoB,CAACF,MAAM,CAACH,OAAO,CAAC;IACtC;EACF,CAAC;EACD,MAAMM,QAAQ,GAAGV,QAAQ,CAACW,IAAI,IAAI;IAChCH,KAAK,CAAC,CAAC;IACPD,MAAM,CAACH,OAAO,GAAGQ,qBAAqB,CAAC,MAAM;MAC3C,IAAIN,OAAO,CAACF,OAAO,EAAE;QACnB,MAAMS,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,QAAQ,CAAC;QACvC,MAAMC,MAAM,GAAGT,OAAO,CAACF,OAAO,CAACY,aAAa,CAAC,qBAAqBH,SAAS,IAAI,CAAC;QAChF,IAAIE,MAAM,EAAE;UACV;UACAA,MAAM,CAACE,cAAc,CAAC;YACpBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFlB,SAAS,CAAC,MAAM;IACd,IAAII,OAAO,IAAID,OAAO,EAAE;MACtBM,QAAQ,CAACN,OAAO,CAAC;MACjB,OAAOI,KAAK;IACd;EACF,CAAC,EAAE,CAACJ,OAAO,EAAEC,OAAO,CAAC,CAAC;EACtB,OAAOK,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}