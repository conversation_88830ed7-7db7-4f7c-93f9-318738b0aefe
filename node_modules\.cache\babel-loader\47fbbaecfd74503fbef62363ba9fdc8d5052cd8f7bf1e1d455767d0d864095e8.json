{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport { convertPx } from '../../utils/convert-px';\nimport { Slide } from './slide';\nconst classPrefix = `adm-image-viewer`;\nexport const Slides = forwardRef((props, ref) => {\n  const slideWidth = window.innerWidth + convertPx(16);\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: props.defaultIndex * slideWidth,\n    config: {\n      tension: 250,\n      clamp: true\n    }\n  }));\n  const count = props.images.length;\n  function swipeTo(index, immediate = false) {\n    var _a;\n    const i = bound(index, 0, count - 1);\n    (_a = props.onIndexChange) === null || _a === void 0 ? void 0 : _a.call(props, i);\n    api.start({\n      x: i * slideWidth,\n      immediate\n    });\n  }\n  useImperativeHandle(ref, () => ({\n    swipeTo\n  }));\n  const dragLockRef = useRef(false);\n  const bind = useDrag(state => {\n    if (dragLockRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const minIndex = Math.floor(offsetX / slideWidth);\n      const maxIndex = minIndex + 1;\n      const velocityOffset = Math.min(state.velocity[0] * 2000, slideWidth) * state.direction[0];\n      swipeTo(bound(Math.round((offsetX + velocityOffset) / slideWidth), minIndex, maxIndex));\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    transform: ([x, y]) => [-x, y],\n    from: () => [x.get(), 0],\n    bounds: () => ({\n      left: 0,\n      right: (count - 1) * slideWidth\n    }),\n    rubberband: true,\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-slides`\n  }, bind()), React.createElement(animated.div, {\n    className: `${classPrefix}-indicator`\n  }, x.to(v => {\n    const index = bound(Math.round(v / slideWidth), 0, count - 1);\n    return `${index + 1} / ${count}`;\n  })), React.createElement(animated.div, {\n    className: `${classPrefix}-slides-inner`,\n    style: {\n      x: x.to(x => -x)\n    }\n  }, props.images.map((image, index) => React.createElement(Slide, {\n    key: index,\n    image: image,\n    onTap: props.onTap,\n    maxZoom: props.maxZoom,\n    imageRender: props.imageRender,\n    index: index,\n    onZoomChange: zoom => {\n      if (zoom !== 1) {\n        const index = Math.round(x.get() / slideWidth);\n        api.start({\n          x: index * slideWidth\n        });\n      }\n    },\n    dragLockRef: dragLockRef\n  }))));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}