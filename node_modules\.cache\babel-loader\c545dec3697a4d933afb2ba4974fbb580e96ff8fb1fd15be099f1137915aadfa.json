{"ast": null, "code": "import * as React from \"react\";\nfunction FaceRecognitionOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FaceRecognitionOutline-FaceRecognitionOutline-\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"currentColor\",\n    fillRule: \"nonzero\",\n    id: \"FaceRecognitionOutline-48_48\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(4.000000, 4.000000)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2.6,22 C2.8209139,22 3,22.1790861 3,22.4 L3,33 C3,35.1421954 4.68396847,36.8910789 6.8003597,36.9951047 L7,37 L17.6,37 C17.8209139,37 18,37.1790861 18,37.4 L18,39.6 C18,39.8209139 17.8209139,40 17.6,40 L6,40 C2.6862915,40 0,37.3137085 0,34 L0,22.4 C0,22.1790861 0.1790861,22 0.4,22 L2.6,22 Z M39.6,22 C39.8209139,22 40,22.1790861 40,22.4 L40,34 C40,37.3137085 37.3137085,40 34,40 L22.4,40 C22.1790861,40 22,39.8209139 22,39.6 L22,37.4 C22,37.1790861 22.1790861,37 22.4,37 L33,37 C35.1421954,37 36.8910789,35.3160315 36.9951047,33.1996403 L37,33 L37,22.4 C37,22.1790861 37.1790861,22 37.4,22 L39.6,22 Z M12.5001683,21 C12.7127179,21 12.8881623,21.1662303 12.8996101,21.3784714 C12.9141715,21.6484375 12.9324496,21.8608443 12.9544443,22.0156918 C13.4467712,25.4817667 16.4220362,28.1428571 20.0188321,28.1428571 C23.6240244,28.1428571 26.6047805,25.4693282 27.0866272,21.9967435 C27.1030702,21.8782417 27.1194807,21.6719204 27.1358588,21.3777797 C27.147643,21.1658235 27.3229566,21 27.5352401,21 L29.5870434,21 L29.5870434,21 C29.8079373,21 29.9870071,21.1791062 29.9870071,21.4 C29.9870071,21.4053491 29.9870071,21.4106976 29.9866853,21.4160423 C29.9766364,21.6663757 29.9641715,21.8654775 29.9492905,22.0133479 C29.4413903,27.0602709 25.1807817,31 20,31 C14.8142523,31 10.5504425,27.0527143 10.0492593,21.9988316 C10.0349153,21.8541881 10.0228828,21.659856 10.0131619,21.4158355 C10.0044568,21.1951409 10.1762353,21.0091085 10.3969263,21.0003169 L12.5001683,21 Z M17.6,0 C17.8209139,0 18,0.1790861 18,0.4 L18,2.6 C18,2.8209139 17.8209139,3 17.6,3 L7,3 C4.85780461,3 3.10892112,4.68396847 3.00489531,6.8003597 L3,7 L3,17.6 C3,17.8209139 2.8209139,18 2.6,18 L0.4,18 C0.1790861,18 0,17.8209139 0,17.6 L0,6 C0,2.6862915 2.6862915,0 6,0 L17.6,0 Z M34,0 C37.3137085,0 40,2.6862915 40,6 L40,17.6 C40,17.8209139 39.8209139,18 39.6,18 L37.4,18 C37.1790861,18 37,17.8209139 37,17.6 L37,7 C37,4.790861 35.209139,3 33,3 L22.4,3 C22.1790861,3 22,2.8209139 22,2.6 L22,0.4 C22,0.1790861 22.1790861,0 22.4,0 L34,0 Z M16,10 L16,15 C16,16.1045695 15.1045695,17 14,17 C12.8954305,17 12,16.1045695 12,15 L12,12 C12,10.8954305 12.8954305,10 14,10 L16,10 Z M28,10 L28,15 C28,16.1045695 27.1045695,17 26,17 C24.8954305,17 24,16.1045695 24,15 L24,12 C24,10.8954305 24.8954305,10 26,10 L28,10 Z\",\n    id: \"FaceRecognitionOutline-\\u5F62\\u72B6\\u7ED3\\u5408\"\n  })))));\n}\nexport default FaceRecognitionOutline;", "map": {"version": 3, "names": ["React", "FaceRecognitionOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "transform", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/FaceRecognitionOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FaceRecognitionOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FaceRecognitionOutline-FaceRecognitionOutline-\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"currentColor\",\n    fillRule: \"nonzero\",\n    id: \"FaceRecognitionOutline-48_48\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    transform: \"translate(4.000000, 4.000000)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M2.6,22 C2.8209139,22 3,22.1790861 3,22.4 L3,33 C3,35.1421954 4.68396847,36.8910789 6.8003597,36.9951047 L7,37 L17.6,37 C17.8209139,37 18,37.1790861 18,37.4 L18,39.6 C18,39.8209139 17.8209139,40 17.6,40 L6,40 C2.6862915,40 0,37.3137085 0,34 L0,22.4 C0,22.1790861 0.1790861,22 0.4,22 L2.6,22 Z M39.6,22 C39.8209139,22 40,22.1790861 40,22.4 L40,34 C40,37.3137085 37.3137085,40 34,40 L22.4,40 C22.1790861,40 22,39.8209139 22,39.6 L22,37.4 C22,37.1790861 22.1790861,37 22.4,37 L33,37 C35.1421954,37 36.8910789,35.3160315 36.9951047,33.1996403 L37,33 L37,22.4 C37,22.1790861 37.1790861,22 37.4,22 L39.6,22 Z M12.5001683,21 C12.7127179,21 12.8881623,21.1662303 12.8996101,21.3784714 C12.9141715,21.6484375 12.9324496,21.8608443 12.9544443,22.0156918 C13.4467712,25.4817667 16.4220362,28.1428571 20.0188321,28.1428571 C23.6240244,28.1428571 26.6047805,25.4693282 27.0866272,21.9967435 C27.1030702,21.8782417 27.1194807,21.6719204 27.1358588,21.3777797 C27.147643,21.1658235 27.3229566,21 27.5352401,21 L29.5870434,21 L29.5870434,21 C29.8079373,21 29.9870071,21.1791062 29.9870071,21.4 C29.9870071,21.4053491 29.9870071,21.4106976 29.9866853,21.4160423 C29.9766364,21.6663757 29.9641715,21.8654775 29.9492905,22.0133479 C29.4413903,27.0602709 25.1807817,31 20,31 C14.8142523,31 10.5504425,27.0527143 10.0492593,21.9988316 C10.0349153,21.8541881 10.0228828,21.659856 10.0131619,21.4158355 C10.0044568,21.1951409 10.1762353,21.0091085 10.3969263,21.0003169 L12.5001683,21 Z M17.6,0 C17.8209139,0 18,0.1790861 18,0.4 L18,2.6 C18,2.8209139 17.8209139,3 17.6,3 L7,3 C4.85780461,3 3.10892112,4.68396847 3.00489531,6.8003597 L3,7 L3,17.6 C3,17.8209139 2.8209139,18 2.6,18 L0.4,18 C0.1790861,18 0,17.8209139 0,17.6 L0,6 C0,2.6862915 2.6862915,0 6,0 L17.6,0 Z M34,0 C37.3137085,0 40,2.6862915 40,6 L40,17.6 C40,17.8209139 39.8209139,18 39.6,18 L37.4,18 C37.1790861,18 37,17.8209139 37,17.6 L37,7 C37,4.790861 35.209139,3 33,3 L22.4,3 C22.1790861,3 22,2.8209139 22,2.6 L22,0.4 C22,0.1790861 22.1790861,0 22.4,0 L34,0 Z M16,10 L16,15 C16,16.1045695 15.1045695,17 14,17 C12.8954305,17 12,16.1045695 12,15 L12,12 C12,10.8954305 12.8954305,10 14,10 L16,10 Z M28,10 L28,15 C28,16.1045695 27.1045695,17 26,17 C24.8954305,17 24,16.1045695 24,15 L24,12 C24,10.8954305 24.8954305,10 26,10 L28,10 Z\",\n    id: \"FaceRecognitionOutline-\\u5F62\\u72B6\\u7ED3\\u5408\"\n  })))));\n}\n\nexport default FaceRecognitionOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACrC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,gDAAgD;IACpDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCiB,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,SAAS;IACnBJ,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCmB,SAAS,EAAE;EACb,CAAC,EAAE,aAAatB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1CoB,CAAC,EAAE,guEAAguE;IACnuEN,EAAE,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR;AAEA,eAAehB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}