{"ast": null, "code": "import { useRef } from 'react';\nexport var createUpdateEffect = function (hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false);\n    // for react-refresh\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}