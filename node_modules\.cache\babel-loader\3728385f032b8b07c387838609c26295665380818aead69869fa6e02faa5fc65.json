{"ast": null, "code": "import \"./text-area.css\";\nimport { TextArea } from './text-area';\nexport default TextArea;", "map": {"version": 3, "names": ["TextArea"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/text-area/index.js"], "sourcesContent": ["import \"./text-area.css\";\nimport { TextArea } from './text-area';\nexport default TextArea;"], "mappings": "AAAA,OAAO,iBAAiB;AACxB,SAASA,QAAQ,QAAQ,aAAa;AACtC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}