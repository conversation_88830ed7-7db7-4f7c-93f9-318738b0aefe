{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n/**\n * Same as React.useState but `setState` accept `ignoreDestroy` param to not to setState after destroyed.\n * We do not make this auto is to avoid real memory leak.\n * Dev<PERSON>per should confirm it's safe to ignore themselves.\n */\nexport default function useSafeState(defaultValue) {\n  var destroyRef = React.useRef(false);\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    value = _React$useState2[0],\n    setValue = _React$useState2[1];\n  React.useEffect(function () {\n    destroyRef.current = false;\n    return function () {\n      destroyRef.current = true;\n    };\n  }, []);\n  function safeSetState(updater, ignoreDestroy) {\n    if (ignoreDestroy && destroyRef.current) {\n      return;\n    }\n    setValue(updater);\n  }\n  return [value, safeSetState];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}