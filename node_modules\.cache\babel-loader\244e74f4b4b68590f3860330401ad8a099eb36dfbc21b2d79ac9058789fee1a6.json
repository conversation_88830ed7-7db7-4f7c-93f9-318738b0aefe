{"ast": null, "code": "var overArg = require('./_overArg');\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\nmodule.exports = getPrototype;", "map": {"version": 3, "names": ["overArg", "require", "getPrototype", "Object", "getPrototypeOf", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/lodash/_getPrototype.js"], "sourcesContent": ["var overArg = require('./_overArg');\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\nmodule.exports = getPrototype;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA,IAAIC,YAAY,GAAGF,OAAO,CAACG,MAAM,CAACC,cAAc,EAAED,MAAM,CAAC;AAEzDE,MAAM,CAACC,OAAO,GAAGJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}