{"ast": null, "code": "export function mergeFuncProps(p1, p2) {\n  const p1Keys = Object.keys(p1);\n  const p2Keys = Object.keys(p2);\n  const keys = new Set([...p1Keys, ...p2Keys]);\n  const res = {};\n  keys.forEach(key => {\n    const p1Value = p1[key];\n    const p2Value = p2[key];\n    if (typeof p1Value === 'function' && typeof p2Value === 'function') {\n      res[key] = function (...args) {\n        p1Value(...args);\n        p2Value(...args);\n      };\n    } else {\n      res[key] = p1Value || p2Value;\n    }\n  });\n  return res;\n}", "map": {"version": 3, "names": ["mergeFuncProps", "p1", "p2", "p1Keys", "Object", "keys", "p2Keys", "Set", "res", "for<PERSON>ach", "key", "p1Value", "p2Value", "args"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/with-func-props.js"], "sourcesContent": ["export function mergeFuncProps(p1, p2) {\n  const p1Keys = Object.keys(p1);\n  const p2Keys = Object.keys(p2);\n  const keys = new Set([...p1Keys, ...p2Keys]);\n  const res = {};\n  keys.forEach(key => {\n    const p1Value = p1[key];\n    const p2Value = p2[key];\n    if (typeof p1Value === 'function' && typeof p2Value === 'function') {\n      res[key] = function (...args) {\n        p1Value(...args);\n        p2Value(...args);\n      };\n    } else {\n      res[key] = p1Value || p2Value;\n    }\n  });\n  return res;\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACrC,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACJ,EAAE,CAAC;EAC9B,MAAMK,MAAM,GAAGF,MAAM,CAACC,IAAI,CAACH,EAAE,CAAC;EAC9B,MAAMG,IAAI,GAAG,IAAIE,GAAG,CAAC,CAAC,GAAGJ,MAAM,EAAE,GAAGG,MAAM,CAAC,CAAC;EAC5C,MAAME,GAAG,GAAG,CAAC,CAAC;EACdH,IAAI,CAACI,OAAO,CAACC,GAAG,IAAI;IAClB,MAAMC,OAAO,GAAGV,EAAE,CAACS,GAAG,CAAC;IACvB,MAAME,OAAO,GAAGV,EAAE,CAACQ,GAAG,CAAC;IACvB,IAAI,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;MAClEJ,GAAG,CAACE,GAAG,CAAC,GAAG,UAAU,GAAGG,IAAI,EAAE;QAC5BF,OAAO,CAAC,GAAGE,IAAI,CAAC;QAChBD,OAAO,CAAC,GAAGC,IAAI,CAAC;MAClB,CAAC;IACH,CAAC,MAAM;MACLL,GAAG,CAACE,GAAG,CAAC,GAAGC,OAAO,IAAIC,OAAO;IAC/B;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}