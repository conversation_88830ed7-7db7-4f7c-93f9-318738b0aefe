{"ast": null, "code": "import * as React from \"react\";\nfunction ReceivePaymentOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceivePaymentOutline-ReceivePaymentOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceivePaymentOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ReceivePaymentOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M4.68284271,18.6828427 L8.88284271,22.8828427 C8.95785726,22.9578573 9,23.0595988 9,23.1656854 L9,26.0343146 C9,26.2552285 8.8209139,26.4343146 8.6,26.4343146 C8.4939134,26.4343146 8.39217184,26.3921718 8.31715729,26.3171573 L7,25 L7,25 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31.9656854 C41,31.7447715 41.1790861,31.5656854 41.4,31.5656854 C41.5060866,31.5656854 41.6078282,31.6078282 41.6828427,31.6828427 L43.8828427,33.8828427 C43.9578573,33.9578573 44,34.0595988 44,34.1656854 L44,38 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,18.9656854 C4,18.7447715 4.1790861,18.5656854 4.4,18.5656854 C4.5060866,18.5656854 4.60782816,18.6078282 4.68284271,18.6828427 Z M29.2490706,12.0202394 L29.3228635,12.0533496 L31.2197061,13.1446148 L31.2854064,13.1920292 C31.4046122,13.2979594 31.4494756,13.4661033 31.3988855,13.6173378 L31.3655382,13.6911796 L26.322,22.404 L30.8673866,22.4047287 C31.0883005,22.4047287 31.2673866,22.5838148 31.2673866,22.8047287 L31.2673866,24.9970927 C31.2673866,25.2180066 31.0883005,25.3970927 30.8673866,25.3970927 L25.267,25.397 L25.267,27.392 L30.8673866,27.3920021 C31.0883005,27.3920021 31.2673866,27.5710882 31.2673866,27.7920021 L31.2673866,29.9843661 C31.2673866,30.20528 31.0883005,30.3843661 30.8673866,30.3843661 L25.267,30.384 L25.2673866,34.9665489 C25.2673866,35.1874628 25.0883005,35.3665489 24.8673866,35.3665489 L22.6673866,35.3665489 C22.4464727,35.3665489 22.2673866,35.1874628 22.2673866,34.9665489 L22.267,30.384 L16.6673866,30.3843661 C16.4464727,30.3843661 16.2673866,30.20528 16.2673866,29.9843661 L16.2673866,27.7920021 C16.2673866,27.5710882 16.4464727,27.3920021 16.6673866,27.3920021 L22.267,27.392 L22.267,25.397 L16.6673866,25.3970927 C16.4464727,25.3970927 16.2673866,25.2180066 16.2673866,24.9970927 L16.2673866,22.8047287 C16.2673866,22.5838148 16.4464727,22.4047287 16.6673866,22.4047287 L21.096,22.404 L16.053868,13.6911796 C15.9570411,13.5238795 15.9949264,13.3156144 16.1339997,13.1920292 L16.2001491,13.1443554 L18.0965426,12.0533496 C18.287678,11.9433884 18.5317526,12.0088504 18.6422091,12.1997 L23.709,20.955 L28.7771971,12.1997 C28.8738465,12.0327066 29.0727973,11.9617131 29.2490706,12.0202394 Z M38,4 C41.3137085,4 44,7.6862915 44,11 L44,29.0343146 C44,29.2552285 43.8209139,29.4343146 43.6,29.4343146 C43.4939134,29.4343146 43.3921718,29.3921718 43.3171573,29.3171573 L39.1171573,25.1171573 C39.0421427,25.0421427 39,24.9404012 39,24.8343146 L39,21.9656854 C39,21.7447715 39.1790861,21.5656854 39.4,21.5656854 C39.5060866,21.5656854 39.6078282,21.6078282 39.6828427,21.6828427 L41,23 L41,23 L41,11 C41,9.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L10,7 C8.40231912,7 7.09633912,9.24891996 7.00509269,10.8237272 L7,11 L7,16.0343146 C7,16.2552285 6.8209139,16.4343146 6.6,16.4343146 C6.4939134,16.4343146 6.39217184,16.3921718 6.31715729,16.3171573 L4.11715729,14.1171573 C4.04214274,14.0421427 4,13.9404012 4,13.8343146 L4,11 L4,11 C4,7.6862915 6.6862915,4 10,4 L38,4 Z\",\n    id: \"ReceivePaymentOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ReceivePaymentOutline;", "map": {"version": 3, "names": ["React", "ReceivePaymentOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ReceivePaymentOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ReceivePaymentOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceivePaymentOutline-ReceivePaymentOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ReceivePaymentOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ReceivePaymentOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M4.68284271,18.6828427 L8.88284271,22.8828427 C8.95785726,22.9578573 9,23.0595988 9,23.1656854 L9,26.0343146 C9,26.2552285 8.8209139,26.4343146 8.6,26.4343146 C8.4939134,26.4343146 8.39217184,26.3921718 8.31715729,26.3171573 L7,25 L7,25 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,31.9656854 C41,31.7447715 41.1790861,31.5656854 41.4,31.5656854 C41.5060866,31.5656854 41.6078282,31.6078282 41.6828427,31.6828427 L43.8828427,33.8828427 C43.9578573,33.9578573 44,34.0595988 44,34.1656854 L44,38 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,18.9656854 C4,18.7447715 4.1790861,18.5656854 4.4,18.5656854 C4.5060866,18.5656854 4.60782816,18.6078282 4.68284271,18.6828427 Z M29.2490706,12.0202394 L29.3228635,12.0533496 L31.2197061,13.1446148 L31.2854064,13.1920292 C31.4046122,13.2979594 31.4494756,13.4661033 31.3988855,13.6173378 L31.3655382,13.6911796 L26.322,22.404 L30.8673866,22.4047287 C31.0883005,22.4047287 31.2673866,22.5838148 31.2673866,22.8047287 L31.2673866,24.9970927 C31.2673866,25.2180066 31.0883005,25.3970927 30.8673866,25.3970927 L25.267,25.397 L25.267,27.392 L30.8673866,27.3920021 C31.0883005,27.3920021 31.2673866,27.5710882 31.2673866,27.7920021 L31.2673866,29.9843661 C31.2673866,30.20528 31.0883005,30.3843661 30.8673866,30.3843661 L25.267,30.384 L25.2673866,34.9665489 C25.2673866,35.1874628 25.0883005,35.3665489 24.8673866,35.3665489 L22.6673866,35.3665489 C22.4464727,35.3665489 22.2673866,35.1874628 22.2673866,34.9665489 L22.267,30.384 L16.6673866,30.3843661 C16.4464727,30.3843661 16.2673866,30.20528 16.2673866,29.9843661 L16.2673866,27.7920021 C16.2673866,27.5710882 16.4464727,27.3920021 16.6673866,27.3920021 L22.267,27.392 L22.267,25.397 L16.6673866,25.3970927 C16.4464727,25.3970927 16.2673866,25.2180066 16.2673866,24.9970927 L16.2673866,22.8047287 C16.2673866,22.5838148 16.4464727,22.4047287 16.6673866,22.4047287 L21.096,22.404 L16.053868,13.6911796 C15.9570411,13.5238795 15.9949264,13.3156144 16.1339997,13.1920292 L16.2001491,13.1443554 L18.0965426,12.0533496 C18.287678,11.9433884 18.5317526,12.0088504 18.6422091,12.1997 L23.709,20.955 L28.7771971,12.1997 C28.8738465,12.0327066 29.0727973,11.9617131 29.2490706,12.0202394 Z M38,4 C41.3137085,4 44,7.6862915 44,11 L44,29.0343146 C44,29.2552285 43.8209139,29.4343146 43.6,29.4343146 C43.4939134,29.4343146 43.3921718,29.3921718 43.3171573,29.3171573 L39.1171573,25.1171573 C39.0421427,25.0421427 39,24.9404012 39,24.8343146 L39,21.9656854 C39,21.7447715 39.1790861,21.5656854 39.4,21.5656854 C39.5060866,21.5656854 39.6078282,21.6078282 39.6828427,21.6828427 L41,23 L41,23 L41,11 C41,9.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L10,7 C8.40231912,7 7.09633912,9.24891996 7.00509269,10.8237272 L7,11 L7,16.0343146 C7,16.2552285 6.8209139,16.4343146 6.6,16.4343146 C6.4939134,16.4343146 6.39217184,16.3921718 6.31715729,16.3171573 L4.11715729,14.1171573 C4.04214274,14.0421427 4,13.9404012 4,13.8343146 L4,11 L4,11 C4,7.6862915 6.6862915,4 10,4 L38,4 Z\",\n    id: \"ReceivePaymentOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ReceivePaymentOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6CAA6C;IACjDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,oCAAoC;IACxCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0hGAA0hG;IAC7hGR,EAAE,EAAE,oCAAoC;IACxCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}