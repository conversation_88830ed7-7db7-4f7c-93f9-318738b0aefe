{"ast": null, "code": "import isFragment from \"../React/isFragment\";\nimport React from 'react';\nexport default function toArray(children) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var ret = [];\n  React.Children.forEach(children, function (child) {\n    if ((child === undefined || child === null) && !option.keepEmpty) {\n      return;\n    }\n    if (Array.isArray(child)) {\n      ret = ret.concat(toArray(child));\n    } else if (isFragment(child) && child.props) {\n      ret = ret.concat(toArray(child.props.children, option));\n    } else {\n      ret.push(child);\n    }\n  });\n  return ret;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}