{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-progress-circle`;\nexport const ProgressCircle = p => {\n  const props = mergeProps({\n    percent: 0\n  }, p);\n  const style = {\n    '--percent': props.percent.toString()\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}`,\n    style: style\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, React.createElement(\"svg\", {\n    className: `${classPrefix}-svg`\n  }, React.createElement(\"circle\", {\n    className: `${classPrefix}-track`,\n    fill: 'transparent'\n  }), React.createElement(\"circle\", {\n    className: `${classPrefix}-fill`,\n    fill: 'transparent'\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-info`\n  }, props.children))));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "mergeProps", "classPrefix", "ProgressCircle", "p", "props", "percent", "style", "toString", "createElement", "className", "fill", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/progress-circle/progress-circle.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-progress-circle`;\nexport const ProgressCircle = p => {\n  const props = mergeProps({\n    percent: 0\n  }, p);\n  const style = {\n    '--percent': props.percent.toString()\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}`,\n    style: style\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, React.createElement(\"svg\", {\n    className: `${classPrefix}-svg`\n  }, React.createElement(\"circle\", {\n    className: `${classPrefix}-track`,\n    fill: 'transparent'\n  }), React.createElement(\"circle\", {\n    className: `${classPrefix}-fill`,\n    fill: 'transparent'\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-info`\n  }, props.children))));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,qBAAqB;AACzC,OAAO,MAAMC,cAAc,GAAGC,CAAC,IAAI;EACjC,MAAMC,KAAK,GAAGJ,UAAU,CAAC;IACvBK,OAAO,EAAE;EACX,CAAC,EAAEF,CAAC,CAAC;EACL,MAAMG,KAAK,GAAG;IACZ,WAAW,EAAEF,KAAK,CAACC,OAAO,CAACE,QAAQ,CAAC;EACtC,CAAC;EACD,OAAOR,eAAe,CAACK,KAAK,EAAEN,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,GAAGR,WAAW,EAAE;IAC3BK,KAAK,EAAEA;EACT,CAAC,EAAER,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEH,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEH,KAAK,CAACU,aAAa,CAAC,QAAQ,EAAE;IAC/BC,SAAS,EAAE,GAAGR,WAAW,QAAQ;IACjCS,IAAI,EAAE;EACR,CAAC,CAAC,EAAEZ,KAAK,CAACU,aAAa,CAAC,QAAQ,EAAE;IAChCC,SAAS,EAAE,GAAGR,WAAW,OAAO;IAChCS,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,EAAEZ,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC9BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEG,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}