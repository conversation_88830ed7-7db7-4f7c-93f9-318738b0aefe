{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Arrow = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 30 16'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor'\n  }, React.createElement(\"path\", {\n    d: 'M0,0 L30,0 L18.07289,14.312538 C16.65863,16.009645 14.13637,16.238942 12.43926,14.824685 C12.25341,14.669808 12.08199,14.49839 11.92711,14.312538 L0,0 L0,0 Z'\n  }))));\n});", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "Arrow", "props", "createElement", "viewBox", "fill", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/popover/arrow.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Arrow = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 30 16'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor'\n  }, React.createElement(\"path\", {\n    d: 'M0,0 L30,0 L18.07289,14.312538 C16.65863,16.009645 14.13637,16.238942 12.43926,14.824685 C12.25341,14.669808 12.08199,14.49839 11.92711,14.312538 L0,0 L0,0 Z'\n  }))));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,KAAK,GAAGF,IAAI,CAACG,KAAK,IAAI;EACjC,OAAOF,eAAe,CAACE,KAAK,EAAEJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IACvDC,OAAO,EAAE;EACX,CAAC,EAAEN,KAAK,CAACK,aAAa,CAAC,GAAG,EAAE;IAC1BE,IAAI,EAAE;EACR,CAAC,EAAEP,KAAK,CAACK,aAAa,CAAC,MAAM,EAAE;IAC7BG,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}