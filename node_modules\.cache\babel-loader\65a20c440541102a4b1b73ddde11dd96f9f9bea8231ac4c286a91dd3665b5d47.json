{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Fallback } from './fallback';\nimport Image from '../image';\nconst classPrefix = 'adm-avatar';\nconst defaultProps = {\n  fallback: React.createElement(Fallback, null),\n  fit: 'cover'\n};\nexport const Avatar = p => {\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(Image, {\n    className: classPrefix,\n    src: props.src,\n    fallback: props.fallback,\n    placeholder: props.fallback,\n    alt: props.alt,\n    lazy: props.lazy,\n    fit: props.fit,\n    onClick: props.onClick,\n    onError: props.onError,\n    onLoad: props.onLoad\n  }));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "mergeProps", "Fallback", "Image", "classPrefix", "defaultProps", "fallback", "createElement", "fit", "Avatar", "p", "props", "className", "src", "placeholder", "alt", "lazy", "onClick", "onError", "onLoad"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/avatar/avatar.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { Fallback } from './fallback';\nimport Image from '../image';\nconst classPrefix = 'adm-avatar';\nconst defaultProps = {\n  fallback: React.createElement(Fallback, null),\n  fit: 'cover'\n};\nexport const Avatar = p => {\n  const props = mergeProps(defaultProps, p);\n  return withNativeProps(props, React.createElement(Image, {\n    className: classPrefix,\n    src: props.src,\n    fallback: props.fallback,\n    placeholder: props.fallback,\n    alt: props.alt,\n    lazy: props.lazy,\n    fit: props.fit,\n    onClick: props.onClick,\n    onError: props.onError,\n    onLoad: props.onLoad\n  }));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAOC,KAAK,MAAM,UAAU;AAC5B,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAEP,KAAK,CAACQ,aAAa,CAACL,QAAQ,EAAE,IAAI,CAAC;EAC7CM,GAAG,EAAE;AACP,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,MAAMC,KAAK,GAAGV,UAAU,CAACI,YAAY,EAAEK,CAAC,CAAC;EACzC,OAAOV,eAAe,CAACW,KAAK,EAAEZ,KAAK,CAACQ,aAAa,CAACJ,KAAK,EAAE;IACvDS,SAAS,EAAER,WAAW;IACtBS,GAAG,EAAEF,KAAK,CAACE,GAAG;IACdP,QAAQ,EAAEK,KAAK,CAACL,QAAQ;IACxBQ,WAAW,EAAEH,KAAK,CAACL,QAAQ;IAC3BS,GAAG,EAAEJ,KAAK,CAACI,GAAG;IACdC,IAAI,EAAEL,KAAK,CAACK,IAAI;IAChBR,GAAG,EAAEG,KAAK,CAACH,GAAG;IACdS,OAAO,EAAEN,KAAK,CAACM,OAAO;IACtBC,OAAO,EAAEP,KAAK,CAACO,OAAO;IACtBC,MAAM,EAAER,KAAK,CAACQ;EAChB,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}