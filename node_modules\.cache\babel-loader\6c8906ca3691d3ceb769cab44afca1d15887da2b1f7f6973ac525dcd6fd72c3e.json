{"ast": null, "code": "!function (t, n) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = n() : \"function\" == typeof define && define.amd ? define(n) : (t = \"undefined\" != typeof globalThis ? globalThis : t || self).dayjs_plugin_quarterOfYear = n();\n}(this, function () {\n  \"use strict\";\n\n  var t = \"month\",\n    n = \"quarter\";\n  return function (e, i) {\n    var r = i.prototype;\n    r.quarter = function (t) {\n      return this.$utils().u(t) ? Math.ceil((this.month() + 1) / 3) : this.month(this.month() % 3 + 3 * (t - 1));\n    };\n    var s = r.add;\n    r.add = function (e, i) {\n      return e = Number(e), this.$utils().p(i) === n ? this.add(3 * e, t) : s.bind(this)(e, i);\n    };\n    var u = r.startOf;\n    r.startOf = function (e, i) {\n      var r = this.$utils(),\n        s = !!r.u(i) || i;\n      if (r.p(e) === n) {\n        var o = this.quarter() - 1;\n        return s ? this.month(3 * o).startOf(t).startOf(\"day\") : this.month(3 * o + 2).endOf(t).endOf(\"day\");\n      }\n      return u.bind(this)(e, i);\n    };\n  };\n});", "map": {"version": 3, "names": ["t", "n", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_quarterOfYear", "e", "i", "r", "prototype", "quarter", "$utils", "u", "Math", "ceil", "month", "s", "add", "Number", "p", "bind", "startOf", "o", "endOf"], "sources": ["C:/Users/<USER>/node_modules/dayjs/plugin/quarterOfYear.js"], "sourcesContent": ["!function(t,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs_plugin_quarterOfYear=n()}(this,(function(){\"use strict\";var t=\"month\",n=\"quarter\";return function(e,i){var r=i.prototype;r.quarter=function(t){return this.$utils().u(t)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(t-1))};var s=r.add;r.add=function(e,i){return e=Number(e),this.$utils().p(i)===n?this.add(3*e,t):s.bind(this)(e,i)};var u=r.startOf;r.startOf=function(e,i){var r=this.$utils(),s=!!r.u(i)||i;if(r.p(e)===n){var o=this.quarter()-1;return s?this.month(3*o).startOf(t).startOf(\"day\"):this.month(3*o+2).endOf(t).endOf(\"day\")}return u.bind(this)(e,i)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,0BAA0B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,IAAID,CAAC,GAAC,OAAO;IAACC,CAAC,GAAC,SAAS;EAAC,OAAO,UAASQ,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,SAAS;IAACD,CAAC,CAACE,OAAO,GAAC,UAASb,CAAC,EAAC;MAAC,OAAO,IAAI,CAACc,MAAM,CAAC,CAAC,CAACC,CAAC,CAACf,CAAC,CAAC,GAACgB,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,GAAC,CAAC,IAAE,CAAC,CAAC,GAAC,IAAI,CAACA,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC,CAAC,GAAC,CAAC,GAAC,CAAC,IAAElB,CAAC,GAAC,CAAC,CAAC,CAAC;IAAA,CAAC;IAAC,IAAImB,CAAC,GAACR,CAAC,CAACS,GAAG;IAACT,CAAC,CAACS,GAAG,GAAC,UAASX,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOD,CAAC,GAACY,MAAM,CAACZ,CAAC,CAAC,EAAC,IAAI,CAACK,MAAM,CAAC,CAAC,CAACQ,CAAC,CAACZ,CAAC,CAAC,KAAGT,CAAC,GAAC,IAAI,CAACmB,GAAG,CAAC,CAAC,GAACX,CAAC,EAACT,CAAC,CAAC,GAACmB,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAACd,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC;IAAC,IAAIK,CAAC,GAACJ,CAAC,CAACa,OAAO;IAACb,CAAC,CAACa,OAAO,GAAC,UAASf,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIC,CAAC,GAAC,IAAI,CAACG,MAAM,CAAC,CAAC;QAACK,CAAC,GAAC,CAAC,CAACR,CAAC,CAACI,CAAC,CAACL,CAAC,CAAC,IAAEA,CAAC;MAAC,IAAGC,CAAC,CAACW,CAAC,CAACb,CAAC,CAAC,KAAGR,CAAC,EAAC;QAAC,IAAIwB,CAAC,GAAC,IAAI,CAACZ,OAAO,CAAC,CAAC,GAAC,CAAC;QAAC,OAAOM,CAAC,GAAC,IAAI,CAACD,KAAK,CAAC,CAAC,GAACO,CAAC,CAAC,CAACD,OAAO,CAACxB,CAAC,CAAC,CAACwB,OAAO,CAAC,KAAK,CAAC,GAAC,IAAI,CAACN,KAAK,CAAC,CAAC,GAACO,CAAC,GAAC,CAAC,CAAC,CAACC,KAAK,CAAC1B,CAAC,CAAC,CAAC0B,KAAK,CAAC,KAAK,CAAC;MAAA;MAAC,OAAOX,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,CAACd,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}