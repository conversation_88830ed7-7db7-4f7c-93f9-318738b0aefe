{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Retrieves a DOM node via a ref, and does not invoke `findDOMNode`.\n */\nexport function getDOM(node) {\n  if (node && _typeof(node) === 'object' && isDOM(node.nativeElement)) {\n    return node.nativeElement;\n  }\n  if (isDOM(node)) {\n    return node;\n  }\n  return null;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  var domNode = getDOM(node);\n  if (domNode) {\n    return domNode;\n  }\n  if (node instanceof React.Component) {\n    var _ReactDOM$findDOMNode;\n    return (_ReactDOM$findDOMNode = ReactDOM.findDOMNode) === null || _ReactDOM$findDOMNode === void 0 ? void 0 : _ReactDOM$findDOMNode.call(ReactDOM, node);\n  }\n  return null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}