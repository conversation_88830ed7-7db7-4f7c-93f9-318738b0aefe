{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\nvar getVisibility = function () {\n  if (!isBrowser) {\n    return 'visible';\n  }\n  return document.visibilityState;\n};\nfunction useDocumentVisibility() {\n  var _a = __read(useState(getVisibility), 2),\n    documentVisibility = _a[0],\n    setDocumentVisibility = _a[1];\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\nexport default useDocumentVisibility;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}