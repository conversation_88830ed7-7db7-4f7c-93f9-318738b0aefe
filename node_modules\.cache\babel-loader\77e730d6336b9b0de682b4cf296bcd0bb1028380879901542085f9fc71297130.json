{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useIsomorphicLayoutEffect, useUnmountedRef } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { ShouldRender } from '../../utils/should-render';\nimport { useInnerVisible } from '../../utils/use-inner-visible';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { useConfig } from '../config-provider';\nimport Mask from '../mask';\nimport { defaultPopupBaseProps } from '../popup/popup-base-props';\nconst classPrefix = 'adm-center-popup';\nconst defaultProps = Object.assign(Object.assign({}, defaultPopupBaseProps), {\n  getContainer: null\n});\nexport const CenterPopup = props => {\n  const {\n    popup: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const unmountedRef = useUnmountedRef();\n  const style = useSpring({\n    scale: mergedProps.visible ? 1 : 0.8,\n    opacity: mergedProps.visible ? 1 : 0,\n    config: {\n      mass: 1.2,\n      tension: 200,\n      friction: 25,\n      clamp: true\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(mergedProps.visible);\n      if (mergedProps.visible) {\n        (_a = mergedProps.afterShow) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n      } else {\n        (_b = mergedProps.afterClose) === null || _b === void 0 ? void 0 : _b.call(mergedProps);\n      }\n    }\n  });\n  const [active, setActive] = useState(mergedProps.visible);\n  useIsomorphicLayoutEffect(() => {\n    if (mergedProps.visible) {\n      setActive(true);\n    }\n  }, [mergedProps.visible]);\n  const ref = useRef(null);\n  useLockScroll(ref, mergedProps.disableBodyScroll && active);\n  const maskVisible = useInnerVisible(active && mergedProps.visible);\n  const body = React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-body`, mergedProps.bodyClassName),\n    style: mergedProps.bodyStyle\n  }, mergedProps.children);\n  const node = withStopPropagation(mergedProps.stopPropagation, withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classPrefix,\n    style: {\n      display: active ? undefined : 'none',\n      pointerEvents: active ? undefined : 'none'\n    }\n  }, mergedProps.mask && React.createElement(Mask, {\n    visible: maskVisible,\n    forceRender: mergedProps.forceRender,\n    destroyOnClose: mergedProps.destroyOnClose,\n    onMaskClick: e => {\n      var _a, _b;\n      (_a = mergedProps.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n      if (mergedProps.closeOnMaskClick) {\n        (_b = mergedProps.onClose) === null || _b === void 0 ? void 0 : _b.call(mergedProps);\n      }\n    },\n    style: mergedProps.maskStyle,\n    className: classNames(`${classPrefix}-mask`, mergedProps.maskClassName),\n    disableBodyScroll: false,\n    stopPropagation: mergedProps.stopPropagation\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-wrap`,\n    role: mergedProps.role,\n    \"aria-label\": mergedProps['aria-label']\n  }, React.createElement(animated.div, {\n    style: Object.assign(Object.assign({}, style), {\n      pointerEvents: style.opacity.to(v => v === 1 ? 'unset' : 'none')\n    }),\n    ref: ref\n  }, mergedProps.showCloseButton && React.createElement(\"a\", {\n    className: classNames(`${classPrefix}-close`, 'adm-plain-anchor'),\n    onClick: () => {\n      var _a;\n      (_a = mergedProps.onClose) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    }\n  }, mergedProps.closeIcon), body)))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: mergedProps.forceRender,\n    destroyOnClose: mergedProps.destroyOnClose\n  }, renderToContainer(mergedProps.getContainer, node));\n};", "map": {"version": 3, "names": ["animated", "useSpring", "useIsomorphicLayoutEffect", "useUnmountedRef", "classNames", "React", "useRef", "useState", "withNativeProps", "renderToContainer", "ShouldRender", "useInnerVisible", "useLockScroll", "mergeProps", "withStopPropagation", "useConfig", "Mask", "defaultPopupBaseProps", "classPrefix", "defaultProps", "Object", "assign", "getContainer", "CenterPopup", "props", "popup", "componentConfig", "mergedProps", "unmountedRef", "style", "scale", "visible", "opacity", "config", "mass", "tension", "friction", "clamp", "onRest", "_a", "_b", "current", "setActive", "afterShow", "call", "afterClose", "active", "ref", "disableBodyScroll", "maskVisible", "body", "createElement", "className", "bodyClassName", "bodyStyle", "children", "node", "stopPropagation", "display", "undefined", "pointerEvents", "mask", "forceRender", "destroyOnClose", "onMaskClick", "e", "closeOnMaskClick", "onClose", "maskStyle", "maskClassName", "role", "div", "to", "v", "showCloseButton", "onClick", "closeIcon"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/center-popup/center-popup.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useIsomorphicLayoutEffect, useUnmountedRef } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { ShouldRender } from '../../utils/should-render';\nimport { useInnerVisible } from '../../utils/use-inner-visible';\nimport { useLockScroll } from '../../utils/use-lock-scroll';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { useConfig } from '../config-provider';\nimport Mask from '../mask';\nimport { defaultPopupBaseProps } from '../popup/popup-base-props';\nconst classPrefix = 'adm-center-popup';\nconst defaultProps = Object.assign(Object.assign({}, defaultPopupBaseProps), {\n  getContainer: null\n});\nexport const CenterPopup = props => {\n  const {\n    popup: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const unmountedRef = useUnmountedRef();\n  const style = useSpring({\n    scale: mergedProps.visible ? 1 : 0.8,\n    opacity: mergedProps.visible ? 1 : 0,\n    config: {\n      mass: 1.2,\n      tension: 200,\n      friction: 25,\n      clamp: true\n    },\n    onRest: () => {\n      var _a, _b;\n      if (unmountedRef.current) return;\n      setActive(mergedProps.visible);\n      if (mergedProps.visible) {\n        (_a = mergedProps.afterShow) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n      } else {\n        (_b = mergedProps.afterClose) === null || _b === void 0 ? void 0 : _b.call(mergedProps);\n      }\n    }\n  });\n  const [active, setActive] = useState(mergedProps.visible);\n  useIsomorphicLayoutEffect(() => {\n    if (mergedProps.visible) {\n      setActive(true);\n    }\n  }, [mergedProps.visible]);\n  const ref = useRef(null);\n  useLockScroll(ref, mergedProps.disableBodyScroll && active);\n  const maskVisible = useInnerVisible(active && mergedProps.visible);\n  const body = React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-body`, mergedProps.bodyClassName),\n    style: mergedProps.bodyStyle\n  }, mergedProps.children);\n  const node = withStopPropagation(mergedProps.stopPropagation, withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classPrefix,\n    style: {\n      display: active ? undefined : 'none',\n      pointerEvents: active ? undefined : 'none'\n    }\n  }, mergedProps.mask && React.createElement(Mask, {\n    visible: maskVisible,\n    forceRender: mergedProps.forceRender,\n    destroyOnClose: mergedProps.destroyOnClose,\n    onMaskClick: e => {\n      var _a, _b;\n      (_a = mergedProps.onMaskClick) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n      if (mergedProps.closeOnMaskClick) {\n        (_b = mergedProps.onClose) === null || _b === void 0 ? void 0 : _b.call(mergedProps);\n      }\n    },\n    style: mergedProps.maskStyle,\n    className: classNames(`${classPrefix}-mask`, mergedProps.maskClassName),\n    disableBodyScroll: false,\n    stopPropagation: mergedProps.stopPropagation\n  }), React.createElement(\"div\", {\n    className: `${classPrefix}-wrap`,\n    role: mergedProps.role,\n    \"aria-label\": mergedProps['aria-label']\n  }, React.createElement(animated.div, {\n    style: Object.assign(Object.assign({}, style), {\n      pointerEvents: style.opacity.to(v => v === 1 ? 'unset' : 'none')\n    }),\n    ref: ref\n  }, mergedProps.showCloseButton && React.createElement(\"a\", {\n    className: classNames(`${classPrefix}-close`, 'adm-plain-anchor'),\n    onClick: () => {\n      var _a;\n      (_a = mergedProps.onClose) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    }\n  }, mergedProps.closeIcon), body)))));\n  return React.createElement(ShouldRender, {\n    active: active,\n    forceRender: mergedProps.forceRender,\n    destroyOnClose: mergedProps.destroyOnClose\n  }, renderToContainer(mergedProps.getContainer, node));\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,yBAAyB,EAAEC,eAAe,QAAQ,QAAQ;AACnE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,MAAMC,WAAW,GAAG,kBAAkB;AACtC,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,qBAAqB,CAAC,EAAE;EAC3EK,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,OAAO,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,KAAK,EAAEC,eAAe,GAAG,CAAC;EAC5B,CAAC,GAAGX,SAAS,CAAC,CAAC;EACf,MAAMY,WAAW,GAAGd,UAAU,CAACM,YAAY,EAAEO,eAAe,EAAEF,KAAK,CAAC;EACpE,MAAMI,YAAY,GAAGzB,eAAe,CAAC,CAAC;EACtC,MAAM0B,KAAK,GAAG5B,SAAS,CAAC;IACtB6B,KAAK,EAAEH,WAAW,CAACI,OAAO,GAAG,CAAC,GAAG,GAAG;IACpCC,OAAO,EAAEL,WAAW,CAACI,OAAO,GAAG,CAAC,GAAG,CAAC;IACpCE,MAAM,EAAE;MACNC,IAAI,EAAE,GAAG;MACTC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIZ,YAAY,CAACa,OAAO,EAAE;MAC1BC,SAAS,CAACf,WAAW,CAACI,OAAO,CAAC;MAC9B,IAAIJ,WAAW,CAACI,OAAO,EAAE;QACvB,CAACQ,EAAE,GAAGZ,WAAW,CAACgB,SAAS,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAACjB,WAAW,CAAC;MACxF,CAAC,MAAM;QACL,CAACa,EAAE,GAAGb,WAAW,CAACkB,UAAU,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACjB,WAAW,CAAC;MACzF;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACmB,MAAM,EAAEJ,SAAS,CAAC,GAAGnC,QAAQ,CAACoB,WAAW,CAACI,OAAO,CAAC;EACzD7B,yBAAyB,CAAC,MAAM;IAC9B,IAAIyB,WAAW,CAACI,OAAO,EAAE;MACvBW,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC,EAAE,CAACf,WAAW,CAACI,OAAO,CAAC,CAAC;EACzB,MAAMgB,GAAG,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACxBM,aAAa,CAACmC,GAAG,EAAEpB,WAAW,CAACqB,iBAAiB,IAAIF,MAAM,CAAC;EAC3D,MAAMG,WAAW,GAAGtC,eAAe,CAACmC,MAAM,IAAInB,WAAW,CAACI,OAAO,CAAC;EAClE,MAAMmB,IAAI,GAAG7C,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAEhD,UAAU,CAAC,GAAGc,WAAW,OAAO,EAAES,WAAW,CAAC0B,aAAa,CAAC;IACvExB,KAAK,EAAEF,WAAW,CAAC2B;EACrB,CAAC,EAAE3B,WAAW,CAAC4B,QAAQ,CAAC;EACxB,MAAMC,IAAI,GAAG1C,mBAAmB,CAACa,WAAW,CAAC8B,eAAe,EAAEjD,eAAe,CAACmB,WAAW,EAAEtB,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;IACpHC,SAAS,EAAElC,WAAW;IACtBW,KAAK,EAAE;MACL6B,OAAO,EAAEZ,MAAM,GAAGa,SAAS,GAAG,MAAM;MACpCC,aAAa,EAAEd,MAAM,GAAGa,SAAS,GAAG;IACtC;EACF,CAAC,EAAEhC,WAAW,CAACkC,IAAI,IAAIxD,KAAK,CAAC8C,aAAa,CAACnC,IAAI,EAAE;IAC/Ce,OAAO,EAAEkB,WAAW;IACpBa,WAAW,EAAEnC,WAAW,CAACmC,WAAW;IACpCC,cAAc,EAAEpC,WAAW,CAACoC,cAAc;IAC1CC,WAAW,EAAEC,CAAC,IAAI;MAChB,IAAI1B,EAAE,EAAEC,EAAE;MACV,CAACD,EAAE,GAAGZ,WAAW,CAACqC,WAAW,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAACjB,WAAW,EAAEsC,CAAC,CAAC;MAC3F,IAAItC,WAAW,CAACuC,gBAAgB,EAAE;QAChC,CAAC1B,EAAE,GAAGb,WAAW,CAACwC,OAAO,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAACjB,WAAW,CAAC;MACtF;IACF,CAAC;IACDE,KAAK,EAAEF,WAAW,CAACyC,SAAS;IAC5BhB,SAAS,EAAEhD,UAAU,CAAC,GAAGc,WAAW,OAAO,EAAES,WAAW,CAAC0C,aAAa,CAAC;IACvErB,iBAAiB,EAAE,KAAK;IACxBS,eAAe,EAAE9B,WAAW,CAAC8B;EAC/B,CAAC,CAAC,EAAEpD,KAAK,CAAC8C,aAAa,CAAC,KAAK,EAAE;IAC7BC,SAAS,EAAE,GAAGlC,WAAW,OAAO;IAChCoD,IAAI,EAAE3C,WAAW,CAAC2C,IAAI;IACtB,YAAY,EAAE3C,WAAW,CAAC,YAAY;EACxC,CAAC,EAAEtB,KAAK,CAAC8C,aAAa,CAACnD,QAAQ,CAACuE,GAAG,EAAE;IACnC1C,KAAK,EAAET,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAAC,EAAE;MAC7C+B,aAAa,EAAE/B,KAAK,CAACG,OAAO,CAACwC,EAAE,CAACC,CAAC,IAAIA,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM;IACjE,CAAC,CAAC;IACF1B,GAAG,EAAEA;EACP,CAAC,EAAEpB,WAAW,CAAC+C,eAAe,IAAIrE,KAAK,CAAC8C,aAAa,CAAC,GAAG,EAAE;IACzDC,SAAS,EAAEhD,UAAU,CAAC,GAAGc,WAAW,QAAQ,EAAE,kBAAkB,CAAC;IACjEyD,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIpC,EAAE;MACN,CAACA,EAAE,GAAGZ,WAAW,CAACwC,OAAO,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAACjB,WAAW,CAAC;IACtF;EACF,CAAC,EAAEA,WAAW,CAACiD,SAAS,CAAC,EAAE1B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,OAAO7C,KAAK,CAAC8C,aAAa,CAACzC,YAAY,EAAE;IACvCoC,MAAM,EAAEA,MAAM;IACdgB,WAAW,EAAEnC,WAAW,CAACmC,WAAW;IACpCC,cAAc,EAAEpC,WAAW,CAACoC;EAC9B,CAAC,EAAEtD,iBAAiB,CAACkB,WAAW,CAACL,YAAY,EAAEkC,IAAI,CAAC,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}