{"ast": null, "code": "import { LeftOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-nav-bar`;\nconst defaultBackIcon = React.createElement(LeftOutline, null);\nexport const NavBar = props => {\n  const {\n    navBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const {\n    back,\n    backIcon,\n    backArrow\n  } = mergedProps;\n  const mergedDefaultBackIcon = componentConfig.backIcon || defaultBackIcon;\n  const mergedBackIcon = mergeProp(defaultBackIcon, componentConfig.backIcon, backArrow === true ? mergedDefaultBackIcon : backArrow, backIcon === true ? mergedDefaultBackIcon : backIcon);\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-left`,\n    role: 'button'\n  }, back !== null && React.createElement(\"div\", {\n    className: `${classPrefix}-back`,\n    onClick: mergedProps.onBack\n  }, mergedBackIcon && React.createElement(\"span\", {\n    className: `${classPrefix}-back-arrow`\n  }, mergedBackIcon), React.createElement(\"span\", {\n    \"aria-hidden\": 'true'\n  }, back)), mergedProps.left), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, mergedProps.children), React.createElement(\"div\", {\n    className: `${classPrefix}-right`\n  }, mergedProps.right)));\n};", "map": {"version": 3, "names": ["LeftOutline", "classNames", "React", "withNativeProps", "mergeProp", "mergeProps", "useConfig", "classPrefix", "defaultBackIcon", "createElement", "NavBar", "props", "navBar", "componentConfig", "mergedProps", "back", "backIcon", "backArrow", "mergedDefaultBackIcon", "mergedBackIcon", "className", "role", "onClick", "onBack", "left", "children", "right"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/nav-bar/nav-bar.js"], "sourcesContent": ["import { LeftOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-nav-bar`;\nconst defaultBackIcon = React.createElement(LeftOutline, null);\nexport const NavBar = props => {\n  const {\n    navBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const {\n    back,\n    backIcon,\n    backArrow\n  } = mergedProps;\n  const mergedDefaultBackIcon = componentConfig.backIcon || defaultBackIcon;\n  const mergedBackIcon = mergeProp(defaultBackIcon, componentConfig.backIcon, backArrow === true ? mergedDefaultBackIcon : backArrow, backIcon === true ? mergedDefaultBackIcon : backIcon);\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-left`,\n    role: 'button'\n  }, back !== null && React.createElement(\"div\", {\n    className: `${classPrefix}-back`,\n    onClick: mergedProps.onBack\n  }, mergedBackIcon && React.createElement(\"span\", {\n    className: `${classPrefix}-back-arrow`\n  }, mergedBackIcon), React.createElement(\"span\", {\n    \"aria-hidden\": 'true'\n  }, back)), mergedProps.left), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, mergedProps.children), React.createElement(\"div\", {\n    className: `${classPrefix}-right`\n  }, mergedProps.right)));\n};"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,eAAe,GAAGN,KAAK,CAACO,aAAa,CAACT,WAAW,EAAE,IAAI,CAAC;AAC9D,OAAO,MAAMU,MAAM,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,MAAM,EAAEC,eAAe,GAAG,CAAC;EAC7B,CAAC,GAAGP,SAAS,CAAC,CAAC;EACf,MAAMQ,WAAW,GAAGT,UAAU,CAACQ,eAAe,EAAEF,KAAK,CAAC;EACtD,MAAM;IACJI,IAAI;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGH,WAAW;EACf,MAAMI,qBAAqB,GAAGL,eAAe,CAACG,QAAQ,IAAIR,eAAe;EACzE,MAAMW,cAAc,GAAGf,SAAS,CAACI,eAAe,EAAEK,eAAe,CAACG,QAAQ,EAAEC,SAAS,KAAK,IAAI,GAAGC,qBAAqB,GAAGD,SAAS,EAAED,QAAQ,KAAK,IAAI,GAAGE,qBAAqB,GAAGF,QAAQ,CAAC;EACzL,OAAOb,eAAe,CAACW,WAAW,EAAEZ,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7DW,SAAS,EAAEnB,UAAU,CAACM,WAAW;EACnC,CAAC,EAAEL,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC5BW,SAAS,EAAE,GAAGb,WAAW,OAAO;IAChCc,IAAI,EAAE;EACR,CAAC,EAAEN,IAAI,KAAK,IAAI,IAAIb,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IAC7CW,SAAS,EAAE,GAAGb,WAAW,OAAO;IAChCe,OAAO,EAAER,WAAW,CAACS;EACvB,CAAC,EAAEJ,cAAc,IAAIjB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC/CW,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEY,cAAc,CAAC,EAAEjB,KAAK,CAACO,aAAa,CAAC,MAAM,EAAE;IAC9C,aAAa,EAAE;EACjB,CAAC,EAAEM,IAAI,CAAC,CAAC,EAAED,WAAW,CAACU,IAAI,CAAC,EAAEtB,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IACvDW,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEO,WAAW,CAACW,QAAQ,CAAC,EAAEvB,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;IACnDW,SAAS,EAAE,GAAGb,WAAW;EAC3B,CAAC,EAAEO,WAAW,CAACY,KAAK,CAAC,CAAC,CAAC;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}