{"ast": null, "code": "import \"./space.css\";\nimport { Space } from './space';\nexport default Space;", "map": {"version": 3, "names": ["Space"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/space/index.js"], "sourcesContent": ["import \"./space.css\";\nimport { Space } from './space';\nexport default Space;"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,KAAK,QAAQ,SAAS;AAC/B,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}