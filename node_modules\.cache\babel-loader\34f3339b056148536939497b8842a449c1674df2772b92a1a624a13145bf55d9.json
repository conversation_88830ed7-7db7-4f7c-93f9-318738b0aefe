{"ast": null, "code": "import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useMount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\nexport default useMount;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}