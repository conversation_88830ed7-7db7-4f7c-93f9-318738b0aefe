{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction } from '../utils';\nvar useSetState = function (initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setMergeState = useMemoizedFn(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  });\n  return [state, setMergeState];\n};\nexport default useSetState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}