{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const SwiperItem = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: 'adm-swiper-item',\n    onClick: props.onClick\n  }, props.children));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "SwiperItem", "props", "createElement", "className", "onClick", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/swiper/swiper-item.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const SwiperItem = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: 'adm-swiper-item',\n    onClick: props.onClick\n  }, props.children));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,UAAU,GAAGC,KAAK,IAAI;EACjC,OAAOF,eAAe,CAACE,KAAK,EAAEH,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAEH,KAAK,CAACG;EACjB,CAAC,EAAEH,KAAK,CAACI,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}