{"ast": null, "code": "import * as React from \"react\";\nfunction PhonebookFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookFill-PhonebookFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhonebookFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.8333501,4 L37.8333499,4 C40.9892606,4 43.5476356,6.55837054 43.5476356,9.71428571 L43.5476356,38.2857143 L43.5476356,38.2857143 C43.5476356,41.441625 40.9892606,44 37.8333499,44 L13.0714302,44 L13.07143,44 C9.91551925,44 7.35714425,41.441625 7.35714425,38.2857143 C7.35714425,38.2857143 7.35714425,38.2857143 7.35714425,38.2857143 L7.35714425,36.3809509 L4.88095228,36.3809509 L4.88095227,36.3809509 C4.67055807,36.3809509 4.5,36.2103929 4.5,36 L4.5,33.9047621 C4.5,33.6952384 4.6714286,33.5238098 4.88095227,33.5238098 L7.35714423,33.5238098 L7.35714423,24.9523812 L4.88095227,24.9523812 L4.88095225,24.9523812 C4.67055805,24.9523812 4.5,24.7818232 4.5,24.571429 L4.5,22.4761924 C4.5,22.2666687 4.67142859,22.0952402 4.88095225,22.0952402 L7.35714421,22.0952402 L7.35714421,13.5238116 L4.88095225,13.5238116 L4.88095223,13.5238116 C4.67055804,13.5238116 4.5,13.3532536 4.5,13.1428594 C4.5,13.1428594 4.5,13.1428594 4.5,13.1428594 L4.5,11.0476228 C4.5,10.8380991 4.67142857,10.6666705 4.88095223,10.6666705 L7.3571442,10.6666705 L7.3571442,9.71429107 L7.3571442,9.71429193 C7.35714372,6.55838122 9.91551473,4 13.0714299,4 L37.8333496,4 L37.8333501,4 Z M19.5742876,14 C19.0352385,14 18.5238099,14.2333335 18.102859,14.6380937 L16.4495242,16.283808 L16.4495242,16.2838081 C15.8822161,16.8361027 15.5346626,17.5755402 15.4714304,18.364759 C15.3876211,19.4180938 15.5876211,20.5257099 16.0847652,21.8466652 C16.8561938,23.9171429 18.019051,25.844759 19.7295286,27.8942991 L19.7295281,27.8942985 C21.7758496,30.3454905 24.3226085,32.3308209 27.1990371,33.7171557 C28.7266576,34.4409637 29.9847514,34.8542985 31.3542737,34.9419191 L31.6076072,34.9514447 C32.6857009,34.9523952 33.5437991,34.5904906 34.2333214,33.850492 C34.3285594,33.7381112 34.8257009,33.2314429 35.7266563,32.330492 C36.62475,31.4000143 36.6342768,30.2343 35.7304658,29.3343 L32.9895149,26.6114429 C32.5961814,26.2457286 32.1447515,26.0238223 31.6180864,26.0238223 C31.0704658,26.0238223 30.5618944,26.2714415 30.1390373,26.6914429 L28.4857024,28.3362063 L28.3533216,28.2581112 L28.1742739,28.1609683 L27.7780832,27.9609683 L27.7780832,27.9609683 C27.6748881,27.9100768 27.5744328,27.8538089 27.477131,27.7923969 C26.0180819,26.8704906 24.700939,25.6781112 23.4447515,24.135254 L23.2304658,23.8609683 C22.9095136,23.4419205 22.5152292,22.7828746 22.0466578,21.8866826 L23.6904658,20.2819192 C24.1266564,19.8476335 24.3818944,19.3428701 24.3818944,18.7895397 C24.3818944,18.235254 24.1266564,17.731446 23.6866578,17.2933478 L21.0247515,14.6438254 C20.6285609,14.2428732 20.1199881,14 19.5742738,14 L19.5742876,14 Z\",\n    id: \"PhonebookFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PhonebookFill;", "map": {"version": 3, "names": ["React", "PhonebookFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/PhonebookFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PhonebookFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookFill-PhonebookFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PhonebookFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PhonebookFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.8333501,4 L37.8333499,4 C40.9892606,4 43.5476356,6.55837054 43.5476356,9.71428571 L43.5476356,38.2857143 L43.5476356,38.2857143 C43.5476356,41.441625 40.9892606,44 37.8333499,44 L13.0714302,44 L13.07143,44 C9.91551925,44 7.35714425,41.441625 7.35714425,38.2857143 C7.35714425,38.2857143 7.35714425,38.2857143 7.35714425,38.2857143 L7.35714425,36.3809509 L4.88095228,36.3809509 L4.88095227,36.3809509 C4.67055807,36.3809509 4.5,36.2103929 4.5,36 L4.5,33.9047621 C4.5,33.6952384 4.6714286,33.5238098 4.88095227,33.5238098 L7.35714423,33.5238098 L7.35714423,24.9523812 L4.88095227,24.9523812 L4.88095225,24.9523812 C4.67055805,24.9523812 4.5,24.7818232 4.5,24.571429 L4.5,22.4761924 C4.5,22.2666687 4.67142859,22.0952402 4.88095225,22.0952402 L7.35714421,22.0952402 L7.35714421,13.5238116 L4.88095225,13.5238116 L4.88095223,13.5238116 C4.67055804,13.5238116 4.5,13.3532536 4.5,13.1428594 C4.5,13.1428594 4.5,13.1428594 4.5,13.1428594 L4.5,11.0476228 C4.5,10.8380991 4.67142857,10.6666705 4.88095223,10.6666705 L7.3571442,10.6666705 L7.3571442,9.71429107 L7.3571442,9.71429193 C7.35714372,6.55838122 9.91551473,4 13.0714299,4 L37.8333496,4 L37.8333501,4 Z M19.5742876,14 C19.0352385,14 18.5238099,14.2333335 18.102859,14.6380937 L16.4495242,16.283808 L16.4495242,16.2838081 C15.8822161,16.8361027 15.5346626,17.5755402 15.4714304,18.364759 C15.3876211,19.4180938 15.5876211,20.5257099 16.0847652,21.8466652 C16.8561938,23.9171429 18.019051,25.844759 19.7295286,27.8942991 L19.7295281,27.8942985 C21.7758496,30.3454905 24.3226085,32.3308209 27.1990371,33.7171557 C28.7266576,34.4409637 29.9847514,34.8542985 31.3542737,34.9419191 L31.6076072,34.9514447 C32.6857009,34.9523952 33.5437991,34.5904906 34.2333214,33.850492 C34.3285594,33.7381112 34.8257009,33.2314429 35.7266563,32.330492 C36.62475,31.4000143 36.6342768,30.2343 35.7304658,29.3343 L32.9895149,26.6114429 C32.5961814,26.2457286 32.1447515,26.0238223 31.6180864,26.0238223 C31.0704658,26.0238223 30.5618944,26.2714415 30.1390373,26.6914429 L28.4857024,28.3362063 L28.3533216,28.2581112 L28.1742739,28.1609683 L27.7780832,27.9609683 L27.7780832,27.9609683 C27.6748881,27.9100768 27.5744328,27.8538089 27.477131,27.7923969 C26.0180819,26.8704906 24.700939,25.6781112 23.4447515,24.135254 L23.2304658,23.8609683 C22.9095136,23.4419205 22.5152292,22.7828746 22.0466578,21.8866826 L23.6904658,20.2819192 C24.1266564,19.8476335 24.3818944,19.3428701 24.3818944,18.7895397 C24.3818944,18.235254 24.1266564,17.731446 23.6866578,17.2933478 L21.0247515,14.6438254 C20.6285609,14.2428732 20.1199881,14 19.5742738,14 L19.5742876,14 Z\",\n    id: \"PhonebookFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PhonebookFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,uhFAAuhF;IAC1hFR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}