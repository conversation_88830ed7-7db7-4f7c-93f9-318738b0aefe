{"ast": null, "code": "import React from 'react';\nimport { ImageViewer, MultiImageViewer } from './image-viewer';\nimport { renderImperatively } from '../../utils/render-imperatively';\nconst handlerSet = new Set();\nexport function showImageViewer(props) {\n  clearImageViewer();\n  const handler = renderImperatively(React.createElement(ImageViewer, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      handlerSet.delete(handler);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  handlerSet.add(handler);\n  return handler;\n}\nexport function showMultiImageViewer(props) {\n  clearImageViewer();\n  const handler = renderImperatively(React.createElement(MultiImageViewer, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      handlerSet.delete(handler);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  handlerSet.add(handler);\n  return handler;\n}\nexport function clearImageViewer() {\n  handlerSet.forEach(handler => {\n    handler.close();\n  });\n  handlerSet.clear();\n}", "map": {"version": 3, "names": ["React", "ImageViewer", "MultiImageViewer", "renderImperatively", "handlerSet", "Set", "showImageViewer", "props", "clearImageViewer", "handler", "createElement", "Object", "assign", "afterClose", "_a", "delete", "call", "add", "showMultiImageViewer", "for<PERSON>ach", "close", "clear"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/image-viewer/methods.js"], "sourcesContent": ["import React from 'react';\nimport { ImageViewer, MultiImageViewer } from './image-viewer';\nimport { renderImperatively } from '../../utils/render-imperatively';\nconst handlerSet = new Set();\nexport function showImageViewer(props) {\n  clearImageViewer();\n  const handler = renderImperatively(React.createElement(ImageViewer, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      handlerSet.delete(handler);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  handlerSet.add(handler);\n  return handler;\n}\nexport function showMultiImageViewer(props) {\n  clearImageViewer();\n  const handler = renderImperatively(React.createElement(MultiImageViewer, Object.assign({}, props, {\n    afterClose: () => {\n      var _a;\n      handlerSet.delete(handler);\n      (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n    }\n  })));\n  handlerSet.add(handler);\n  return handler;\n}\nexport function clearImageViewer() {\n  handlerSet.forEach(handler => {\n    handler.close();\n  });\n  handlerSet.clear();\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,gBAAgB;AAC9D,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC5B,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrCC,gBAAgB,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAGN,kBAAkB,CAACH,KAAK,CAACU,aAAa,CAACT,WAAW,EAAEU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,KAAK,EAAE;IAC3FM,UAAU,EAAEA,CAAA,KAAM;MAChB,IAAIC,EAAE;MACNV,UAAU,CAACW,MAAM,CAACN,OAAO,CAAC;MAC1B,CAACK,EAAE,GAAGP,KAAK,CAACM,UAAU,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACT,KAAK,CAAC;IAC7E;EACF,CAAC,CAAC,CAAC,CAAC;EACJH,UAAU,CAACa,GAAG,CAACR,OAAO,CAAC;EACvB,OAAOA,OAAO;AAChB;AACA,OAAO,SAASS,oBAAoBA,CAACX,KAAK,EAAE;EAC1CC,gBAAgB,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAGN,kBAAkB,CAACH,KAAK,CAACU,aAAa,CAACR,gBAAgB,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,KAAK,EAAE;IAChGM,UAAU,EAAEA,CAAA,KAAM;MAChB,IAAIC,EAAE;MACNV,UAAU,CAACW,MAAM,CAACN,OAAO,CAAC;MAC1B,CAACK,EAAE,GAAGP,KAAK,CAACM,UAAU,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACT,KAAK,CAAC;IAC7E;EACF,CAAC,CAAC,CAAC,CAAC;EACJH,UAAU,CAACa,GAAG,CAACR,OAAO,CAAC;EACvB,OAAOA,OAAO;AAChB;AACA,OAAO,SAASD,gBAAgBA,CAAA,EAAG;EACjCJ,UAAU,CAACe,OAAO,CAACV,OAAO,IAAI;IAC5BA,OAAO,CAACW,KAAK,CAAC,CAAC;EACjB,CAAC,CAAC;EACFhB,UAAU,CAACiB,KAAK,CAAC,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}