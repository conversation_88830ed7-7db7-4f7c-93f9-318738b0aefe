{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-water-mark`;\nconst defaultProps = {\n  fullPage: true\n};\nexport const WaterMark = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    zIndex,\n    gapX = 24,\n    gapY = 48,\n    width = 120,\n    height = 64,\n    rotate = -22,\n    image,\n    imageWidth = 120,\n    imageHeight = 64,\n    content,\n    fontStyle = 'normal',\n    fontWeight = 'normal',\n    fontColor = 'rgba(0,0,0,.15)',\n    fontSize = 14,\n    fontFamily = 'sans-serif'\n  } = props;\n  const [base64Url, setBase64Url] = useState('');\n  useEffect(() => {\n    const canvas = document.createElement('canvas');\n    const ratio = window.devicePixelRatio;\n    const ctx = canvas.getContext('2d');\n    const canvasWidth = `${(gapX + width) * ratio}px`;\n    const canvasHeight = `${(gapY + height) * ratio}px`;\n    const markWidth = width * ratio;\n    const markHeight = height * ratio;\n    canvas.setAttribute('width', canvasWidth);\n    canvas.setAttribute('height', canvasHeight);\n    if (ctx) {\n      if (image) {\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.onload = () => {\n          ctx.drawImage(img, -imageWidth * ratio / 2, -imageHeight * ratio / 2, imageWidth * ratio, imageHeight * ratio);\n          ctx.restore();\n          setBase64Url(canvas.toDataURL());\n        };\n        img.src = image;\n      } else if (content) {\n        ctx.textBaseline = 'middle';\n        ctx.textAlign = 'center';\n        // 文字绕中间旋转\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const markSize = Number(fontSize) * ratio;\n        ctx.font = `${fontStyle} normal ${fontWeight} ${markSize}px/${markHeight}px ${fontFamily}`;\n        ctx.fillStyle = fontColor;\n        if (Array.isArray(content)) {\n          content.forEach((item, index) => ctx.fillText(item, 0, index * markSize));\n        } else {\n          ctx.fillText(content, 0, 0);\n        }\n        ctx.restore();\n        setBase64Url(canvas.toDataURL());\n      }\n    } else {\n      throw new Error('Canvas is not supported in the current environment');\n    }\n  }, [gapX, gapY, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, image, content, fontSize]);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-full-page`]: props.fullPage\n    }),\n    style: {\n      zIndex,\n      backgroundSize: `${gapX + width}px`,\n      // Not give `url` if its empty. Which will cause 404 error.\n      backgroundImage: base64Url === '' ? undefined : `url('${base64Url}')`\n    }\n  }));\n};", "map": {"version": 3, "names": ["classNames", "React", "useEffect", "useState", "withNativeProps", "mergeProps", "classPrefix", "defaultProps", "fullPage", "WaterMark", "p", "props", "zIndex", "gapX", "gapY", "width", "height", "rotate", "image", "imageWidth", "imageHeight", "content", "fontStyle", "fontWeight", "fontColor", "fontSize", "fontFamily", "base64Url", "setBase64Url", "canvas", "document", "createElement", "ratio", "window", "devicePixelRatio", "ctx", "getContext", "canvasWidth", "canvasHeight", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "translate", "Math", "PI", "Number", "img", "Image", "crossOrigin", "referrerPolicy", "onload", "drawImage", "restore", "toDataURL", "src", "textBaseline", "textAlign", "markSize", "font", "fillStyle", "Array", "isArray", "for<PERSON>ach", "item", "index", "fillText", "Error", "className", "style", "backgroundSize", "backgroundImage", "undefined"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/water-mark/water-mark.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { useEffect, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-water-mark`;\nconst defaultProps = {\n  fullPage: true\n};\nexport const WaterMark = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    zIndex,\n    gapX = 24,\n    gapY = 48,\n    width = 120,\n    height = 64,\n    rotate = -22,\n    image,\n    imageWidth = 120,\n    imageHeight = 64,\n    content,\n    fontStyle = 'normal',\n    fontWeight = 'normal',\n    fontColor = 'rgba(0,0,0,.15)',\n    fontSize = 14,\n    fontFamily = 'sans-serif'\n  } = props;\n  const [base64Url, setBase64Url] = useState('');\n  useEffect(() => {\n    const canvas = document.createElement('canvas');\n    const ratio = window.devicePixelRatio;\n    const ctx = canvas.getContext('2d');\n    const canvasWidth = `${(gapX + width) * ratio}px`;\n    const canvasHeight = `${(gapY + height) * ratio}px`;\n    const markWidth = width * ratio;\n    const markHeight = height * ratio;\n    canvas.setAttribute('width', canvasWidth);\n    canvas.setAttribute('height', canvasHeight);\n    if (ctx) {\n      if (image) {\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.onload = () => {\n          ctx.drawImage(img, -imageWidth * ratio / 2, -imageHeight * ratio / 2, imageWidth * ratio, imageHeight * ratio);\n          ctx.restore();\n          setBase64Url(canvas.toDataURL());\n        };\n        img.src = image;\n      } else if (content) {\n        ctx.textBaseline = 'middle';\n        ctx.textAlign = 'center';\n        // 文字绕中间旋转\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const markSize = Number(fontSize) * ratio;\n        ctx.font = `${fontStyle} normal ${fontWeight} ${markSize}px/${markHeight}px ${fontFamily}`;\n        ctx.fillStyle = fontColor;\n        if (Array.isArray(content)) {\n          content.forEach((item, index) => ctx.fillText(item, 0, index * markSize));\n        } else {\n          ctx.fillText(content, 0, 0);\n        }\n        ctx.restore();\n        setBase64Url(canvas.toDataURL());\n      }\n    } else {\n      throw new Error('Canvas is not supported in the current environment');\n    }\n  }, [gapX, gapY, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, image, content, fontSize]);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-full-page`]: props.fullPage\n    }),\n    style: {\n      zIndex,\n      backgroundSize: `${gapX + width}px`,\n      // Not give `url` if its empty. Which will cause 404 error.\n      backgroundImage: base64Url === '' ? undefined : `url('${base64Url}')`\n    }\n  }));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,gBAAgB;AACpC,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,SAAS,GAAGC,CAAC,IAAI;EAC5B,MAAMC,KAAK,GAAGN,UAAU,CAACE,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAM;IACJE,MAAM;IACNC,IAAI,GAAG,EAAE;IACTC,IAAI,GAAG,EAAE;IACTC,KAAK,GAAG,GAAG;IACXC,MAAM,GAAG,EAAE;IACXC,MAAM,GAAG,CAAC,EAAE;IACZC,KAAK;IACLC,UAAU,GAAG,GAAG;IAChBC,WAAW,GAAG,EAAE;IAChBC,OAAO;IACPC,SAAS,GAAG,QAAQ;IACpBC,UAAU,GAAG,QAAQ;IACrBC,SAAS,GAAG,iBAAiB;IAC7BC,QAAQ,GAAG,EAAE;IACbC,UAAU,GAAG;EACf,CAAC,GAAGf,KAAK;EACT,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9CD,SAAS,CAAC,MAAM;IACd,MAAM2B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,KAAK,GAAGC,MAAM,CAACC,gBAAgB;IACrC,MAAMC,GAAG,GAAGN,MAAM,CAACO,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,WAAW,GAAG,GAAG,CAACxB,IAAI,GAAGE,KAAK,IAAIiB,KAAK,IAAI;IACjD,MAAMM,YAAY,GAAG,GAAG,CAACxB,IAAI,GAAGE,MAAM,IAAIgB,KAAK,IAAI;IACnD,MAAMO,SAAS,GAAGxB,KAAK,GAAGiB,KAAK;IAC/B,MAAMQ,UAAU,GAAGxB,MAAM,GAAGgB,KAAK;IACjCH,MAAM,CAACY,YAAY,CAAC,OAAO,EAAEJ,WAAW,CAAC;IACzCR,MAAM,CAACY,YAAY,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC3C,IAAIH,GAAG,EAAE;MACP,IAAIjB,KAAK,EAAE;QACTiB,GAAG,CAACO,SAAS,CAACH,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAG,CAAC,CAAC;QAC5CL,GAAG,CAAClB,MAAM,CAAC0B,IAAI,CAACC,EAAE,GAAG,GAAG,GAAGC,MAAM,CAAC5B,MAAM,CAAC,CAAC;QAC1C,MAAM6B,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;QAC7BF,GAAG,CAACG,cAAc,GAAG,aAAa;QAClCH,GAAG,CAACI,MAAM,GAAG,MAAM;UACjBf,GAAG,CAACgB,SAAS,CAACL,GAAG,EAAE,CAAC3B,UAAU,GAAGa,KAAK,GAAG,CAAC,EAAE,CAACZ,WAAW,GAAGY,KAAK,GAAG,CAAC,EAAEb,UAAU,GAAGa,KAAK,EAAEZ,WAAW,GAAGY,KAAK,CAAC;UAC9GG,GAAG,CAACiB,OAAO,CAAC,CAAC;UACbxB,YAAY,CAACC,MAAM,CAACwB,SAAS,CAAC,CAAC,CAAC;QAClC,CAAC;QACDP,GAAG,CAACQ,GAAG,GAAGpC,KAAK;MACjB,CAAC,MAAM,IAAIG,OAAO,EAAE;QAClBc,GAAG,CAACoB,YAAY,GAAG,QAAQ;QAC3BpB,GAAG,CAACqB,SAAS,GAAG,QAAQ;QACxB;QACArB,GAAG,CAACO,SAAS,CAACH,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAG,CAAC,CAAC;QAC5CL,GAAG,CAAClB,MAAM,CAAC0B,IAAI,CAACC,EAAE,GAAG,GAAG,GAAGC,MAAM,CAAC5B,MAAM,CAAC,CAAC;QAC1C,MAAMwC,QAAQ,GAAGZ,MAAM,CAACpB,QAAQ,CAAC,GAAGO,KAAK;QACzCG,GAAG,CAACuB,IAAI,GAAG,GAAGpC,SAAS,WAAWC,UAAU,IAAIkC,QAAQ,MAAMjB,UAAU,MAAMd,UAAU,EAAE;QAC1FS,GAAG,CAACwB,SAAS,GAAGnC,SAAS;QACzB,IAAIoC,KAAK,CAACC,OAAO,CAACxC,OAAO,CAAC,EAAE;UAC1BA,OAAO,CAACyC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK7B,GAAG,CAAC8B,QAAQ,CAACF,IAAI,EAAE,CAAC,EAAEC,KAAK,GAAGP,QAAQ,CAAC,CAAC;QAC3E,CAAC,MAAM;UACLtB,GAAG,CAAC8B,QAAQ,CAAC5C,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7B;QACAc,GAAG,CAACiB,OAAO,CAAC,CAAC;QACbxB,YAAY,CAACC,MAAM,CAACwB,SAAS,CAAC,CAAC,CAAC;MAClC;IACF,CAAC,MAAM;MACL,MAAM,IAAIa,KAAK,CAAC,oDAAoD,CAAC;IACvE;EACF,CAAC,EAAE,CAACrD,IAAI,EAAEC,IAAI,EAAEG,MAAM,EAAEK,SAAS,EAAEC,UAAU,EAAER,KAAK,EAAEC,MAAM,EAAEU,UAAU,EAAEF,SAAS,EAAEN,KAAK,EAAEG,OAAO,EAAEI,QAAQ,CAAC,CAAC;EAC/G,OAAOrB,eAAe,CAACO,KAAK,EAAEV,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACvDoC,SAAS,EAAEnE,UAAU,CAACM,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,YAAY,GAAGK,KAAK,CAACH;IACtC,CAAC,CAAC;IACF4D,KAAK,EAAE;MACLxD,MAAM;MACNyD,cAAc,EAAE,GAAGxD,IAAI,GAAGE,KAAK,IAAI;MACnC;MACAuD,eAAe,EAAE3C,SAAS,KAAK,EAAE,GAAG4C,SAAS,GAAG,QAAQ5C,SAAS;IACnE;EACF,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}