{"ast": null, "code": "import { render, unmount as reactUnmount } from './render';\nexport function renderToBody(element) {\n  const container = document.createElement('div');\n  document.body.appendChild(container);\n  function unmount() {\n    const unmountResult = reactUnmount(container);\n    if (unmountResult && container.parentNode) {\n      container.parentNode.removeChild(container);\n    }\n  }\n  render(element, container);\n  return unmount;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}