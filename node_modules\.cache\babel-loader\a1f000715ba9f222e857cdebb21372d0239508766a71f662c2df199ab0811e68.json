{"ast": null, "code": "import { CheckOutline } from 'antd-mobile-icons';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nimport { CheckListContext } from './context';\nconst classPrefix = 'adm-check-list';\nconst defaultProps = {\n  multiple: false,\n  defaultValue: [],\n  activeIcon: React.createElement(CheckOutline, null)\n};\nexport const CheckList = props => {\n  const {\n    checkList: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  function check(val) {\n    if (mergedProps.multiple) {\n      setValue([...value, val]);\n    } else {\n      setValue([val]);\n    }\n  }\n  function uncheck(val) {\n    setValue(value.filter(item => item !== val));\n  }\n  const {\n    activeIcon,\n    extra,\n    disabled,\n    readOnly\n  } = mergedProps;\n  return React.createElement(CheckListContext.Provider, {\n    value: {\n      value,\n      check,\n      uncheck,\n      activeIcon,\n      extra,\n      disabled,\n      readOnly\n    }\n  }, withNativeProps(mergedProps, React.createElement(List, {\n    mode: mergedProps.mode,\n    className: classPrefix\n  }, mergedProps.children)));\n};", "map": {"version": 3, "names": ["CheckOutline", "React", "withNativeProps", "usePropsValue", "mergeProps", "useConfig", "List", "CheckListContext", "classPrefix", "defaultProps", "multiple", "defaultValue", "activeIcon", "createElement", "CheckList", "props", "checkList", "componentConfig", "mergedProps", "value", "setValue", "check", "val", "uncheck", "filter", "item", "extra", "disabled", "readOnly", "Provider", "mode", "className", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/check-list/check-list.js"], "sourcesContent": ["import { CheckOutline } from 'antd-mobile-icons';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nimport { CheckListContext } from './context';\nconst classPrefix = 'adm-check-list';\nconst defaultProps = {\n  multiple: false,\n  defaultValue: [],\n  activeIcon: React.createElement(CheckOutline, null)\n};\nexport const CheckList = props => {\n  const {\n    checkList: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  function check(val) {\n    if (mergedProps.multiple) {\n      setValue([...value, val]);\n    } else {\n      setValue([val]);\n    }\n  }\n  function uncheck(val) {\n    setValue(value.filter(item => item !== val));\n  }\n  const {\n    activeIcon,\n    extra,\n    disabled,\n    readOnly\n  } = mergedProps;\n  return React.createElement(CheckListContext.Provider, {\n    value: {\n      value,\n      check,\n      uncheck,\n      activeIcon,\n      extra,\n      disabled,\n      readOnly\n    }\n  }, withNativeProps(mergedProps, React.createElement(List, {\n    mode: mergedProps.mode,\n    className: classPrefix\n  }, mergedProps.children)));\n};"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,MAAMC,WAAW,GAAG,gBAAgB;AACpC,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE,EAAE;EAChBC,UAAU,EAAEX,KAAK,CAACY,aAAa,CAACb,YAAY,EAAE,IAAI;AACpD,CAAC;AACD,OAAO,MAAMc,SAAS,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,SAAS,EAAEC,eAAe,GAAG,CAAC;EAChC,CAAC,GAAGZ,SAAS,CAAC,CAAC;EACf,MAAMa,WAAW,GAAGd,UAAU,CAACK,YAAY,EAAEQ,eAAe,EAAEF,KAAK,CAAC;EACpE,MAAM,CAACI,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,aAAa,CAACe,WAAW,CAAC;EACpD,SAASG,KAAKA,CAACC,GAAG,EAAE;IAClB,IAAIJ,WAAW,CAACR,QAAQ,EAAE;MACxBU,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAEG,GAAG,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLF,QAAQ,CAAC,CAACE,GAAG,CAAC,CAAC;IACjB;EACF;EACA,SAASC,OAAOA,CAACD,GAAG,EAAE;IACpBF,QAAQ,CAACD,KAAK,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKH,GAAG,CAAC,CAAC;EAC9C;EACA,MAAM;IACJV,UAAU;IACVc,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGV,WAAW;EACf,OAAOjB,KAAK,CAACY,aAAa,CAACN,gBAAgB,CAACsB,QAAQ,EAAE;IACpDV,KAAK,EAAE;MACLA,KAAK;MACLE,KAAK;MACLE,OAAO;MACPX,UAAU;MACVc,KAAK;MACLC,QAAQ;MACRC;IACF;EACF,CAAC,EAAE1B,eAAe,CAACgB,WAAW,EAAEjB,KAAK,CAACY,aAAa,CAACP,IAAI,EAAE;IACxDwB,IAAI,EAAEZ,WAAW,CAACY,IAAI;IACtBC,SAAS,EAAEvB;EACb,CAAC,EAAEU,WAAW,CAACc,QAAQ,CAAC,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}