{"ast": null, "code": "var getScrollTop = function (el) {\n  if (el === document || el === document.documentElement || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n  return el.scrollTop;\n};\nvar getScrollHeight = function (el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\nvar getClientHeight = function (el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\nexport { getScrollTop, getScrollHeight, getClientHeight };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}