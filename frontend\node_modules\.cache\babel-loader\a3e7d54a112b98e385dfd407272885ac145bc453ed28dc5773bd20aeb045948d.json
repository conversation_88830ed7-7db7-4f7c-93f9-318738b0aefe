{"ast": null, "code": "import { __assign } from \"tslib\";\nvar cache = new Map();\nvar setCache = function (key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n  var timer = undefined;\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache.delete(key);\n    }, cacheTime);\n  }\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\nvar getCache = function (key) {\n  return cache.get(key);\n};\nvar clearCache = function (key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache.delete(cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\nexport { getCache, setCache, clearCache };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}