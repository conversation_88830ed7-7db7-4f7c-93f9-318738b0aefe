{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staged = void 0;\nconst react_1 = __importDefault(require(\"react\"));\nfunction processNext(next) {\n  if (typeof next === 'function') {\n    return react_1.default.createElement(Stage, {\n      stage: next\n    });\n  } else {\n    return next;\n  }\n}\nfunction Stage(props) {\n  const next = props.stage();\n  return processNext(next);\n}\nfunction staged(stage) {\n  return function Staged(props, ref) {\n    const next = stage(props, ref);\n    return processNext(next);\n  };\n}\nexports.staged = staged;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "staged", "react_1", "require", "processNext", "next", "default", "createElement", "Stage", "stage", "props", "Staged", "ref"], "sources": ["C:/Users/<USER>/node_modules/staged-components/index.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.staged = void 0;\nconst react_1 = __importDefault(require(\"react\"));\nfunction processNext(next) {\n    if (typeof next === 'function') {\n        return (react_1.default.createElement(Stage, { stage: next }));\n    }\n    else {\n        return next;\n    }\n}\nfunction Stage(props) {\n    const next = props.stage();\n    return processNext(next);\n}\nfunction staged(stage) {\n    return function Staged(props, ref) {\n        const next = stage(props, ref);\n        return processNext(next);\n    };\n}\nexports.staged = staged;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,MAAM,GAAG,KAAK,CAAC;AACvB,MAAMC,OAAO,GAAGR,eAAe,CAACS,OAAO,CAAC,OAAO,CAAC,CAAC;AACjD,SAASC,WAAWA,CAACC,IAAI,EAAE;EACvB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAQH,OAAO,CAACI,OAAO,CAACC,aAAa,CAACC,KAAK,EAAE;MAAEC,KAAK,EAAEJ;IAAK,CAAC,CAAC;EACjE,CAAC,MACI;IACD,OAAOA,IAAI;EACf;AACJ;AACA,SAASG,KAAKA,CAACE,KAAK,EAAE;EAClB,MAAML,IAAI,GAAGK,KAAK,CAACD,KAAK,CAAC,CAAC;EAC1B,OAAOL,WAAW,CAACC,IAAI,CAAC;AAC5B;AACA,SAASJ,MAAMA,CAACQ,KAAK,EAAE;EACnB,OAAO,SAASE,MAAMA,CAACD,KAAK,EAAEE,GAAG,EAAE;IAC/B,MAAMP,IAAI,GAAGI,KAAK,CAACC,KAAK,EAAEE,GAAG,CAAC;IAC9B,OAAOR,WAAW,CAACC,IAAI,CAAC;EAC5B,CAAC;AACL;AACAN,OAAO,CAACE,MAAM,GAAGA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}