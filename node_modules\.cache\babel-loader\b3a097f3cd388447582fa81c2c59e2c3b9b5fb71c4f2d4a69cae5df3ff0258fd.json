{"ast": null, "code": "import \"./capsule-tabs.css\";\nimport { CapsuleTab, CapsuleTabs } from './capsule-tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(CapsuleTabs, {\n  Tab: CapsuleTab\n});", "map": {"version": 3, "names": ["CapsuleTab", "CapsuleTabs", "attachPropertiesToComponent", "Tab"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/capsule-tabs/index.js"], "sourcesContent": ["import \"./capsule-tabs.css\";\nimport { CapsuleTab, CapsuleTabs } from './capsule-tabs';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(CapsuleTabs, {\n  Tab: CapsuleTab\n});"], "mappings": "AAAA,OAAO,oBAAoB;AAC3B,SAASA,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;AACxD,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACD,WAAW,EAAE;EACtDE,GAAG,EAAEH;AACP,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}