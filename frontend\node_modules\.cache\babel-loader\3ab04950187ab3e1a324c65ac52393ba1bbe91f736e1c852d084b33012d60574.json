{"ast": null, "code": "import { useEffect, useRef } from 'react';\nvar useUnmountedRef = function () {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\nexport default useUnmountedRef;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}