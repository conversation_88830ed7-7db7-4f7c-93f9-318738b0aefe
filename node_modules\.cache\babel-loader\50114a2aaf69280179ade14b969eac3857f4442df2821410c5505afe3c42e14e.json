{"ast": null, "code": "import \"./swipe-action.css\";\nimport { SwipeAction } from './swipe-action';\nexport default SwipeAction;", "map": {"version": 3, "names": ["SwipeAction"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/swipe-action/index.js"], "sourcesContent": ["import \"./swipe-action.css\";\nimport { SwipeAction } from './swipe-action';\nexport default SwipeAction;"], "mappings": "AAAA,OAAO,oBAAoB;AAC3B,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}