{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport useMeasure from './useMeasure';\nconst classPrefix = `adm-ellipsis`;\nconst defaultProps = {\n  direction: 'end',\n  rows: 1,\n  expandText: '',\n  content: '',\n  collapseText: '',\n  stopPropagationForActionButtons: [],\n  onContentClick: () => {},\n  defaultExpanded: false\n};\nexport const Ellipsis = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    content,\n    direction,\n    rows,\n    expandText,\n    collapseText,\n    stopPropagationForActionButtons,\n    onContentClick,\n    defaultExpanded\n  } = props;\n  // ============================ Refs ============================\n  const rootRef = React.useRef(null);\n  // ========================== Expanded ==========================\n  const [expanded, setExpanded] = React.useState(defaultExpanded);\n  const expandNode = expandText ? withStopPropagation(stopPropagationForActionButtons, React.createElement(\"a\", {\n    onClick: () => {\n      setExpanded(true);\n    }\n  }, expandText)) : null;\n  const collapseNode = collapseText ? withStopPropagation(stopPropagationForActionButtons, React.createElement(\"a\", {\n    onClick: () => {\n      setExpanded(false);\n    }\n  }, collapseText)) : null;\n  // ========================== Ellipsis ==========================\n  const [measureNodes, forceResize] = useMeasure(rootRef, content, rows, direction, expanded, expandNode, collapseNode);\n  useResizeEffect(forceResize, rootRef);\n  // =========================== Render ===========================\n  return withNativeProps(props, React.createElement(\"div\", {\n    ref: rootRef,\n    className: classPrefix,\n    onClick: e => {\n      if (e.target === e.currentTarget) {\n        onContentClick(e);\n      }\n    }\n  }, measureNodes));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "useResizeEffect", "mergeProps", "withStopPropagation", "useMeasure", "classPrefix", "defaultProps", "direction", "rows", "expandText", "content", "collapseText", "stopPropagationForActionButtons", "onContentClick", "defaultExpanded", "El<PERSON><PERSON>", "p", "props", "rootRef", "useRef", "expanded", "setExpanded", "useState", "expandNode", "createElement", "onClick", "collapseNode", "measureNodes", "forceResize", "ref", "className", "e", "target", "currentTarget"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/ellipsis/ellipsis.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport useMeasure from './useMeasure';\nconst classPrefix = `adm-ellipsis`;\nconst defaultProps = {\n  direction: 'end',\n  rows: 1,\n  expandText: '',\n  content: '',\n  collapseText: '',\n  stopPropagationForActionButtons: [],\n  onContentClick: () => {},\n  defaultExpanded: false\n};\nexport const Ellipsis = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    content,\n    direction,\n    rows,\n    expandText,\n    collapseText,\n    stopPropagationForActionButtons,\n    onContentClick,\n    defaultExpanded\n  } = props;\n  // ============================ Refs ============================\n  const rootRef = React.useRef(null);\n  // ========================== Expanded ==========================\n  const [expanded, setExpanded] = React.useState(defaultExpanded);\n  const expandNode = expandText ? withStopPropagation(stopPropagationForActionButtons, React.createElement(\"a\", {\n    onClick: () => {\n      setExpanded(true);\n    }\n  }, expandText)) : null;\n  const collapseNode = collapseText ? withStopPropagation(stopPropagationForActionButtons, React.createElement(\"a\", {\n    onClick: () => {\n      setExpanded(false);\n    }\n  }, collapseText)) : null;\n  // ========================== Ellipsis ==========================\n  const [measureNodes, forceResize] = useMeasure(rootRef, content, rows, direction, expanded, expandNode, collapseNode);\n  useResizeEffect(forceResize, rootRef);\n  // =========================== Render ===========================\n  return withNativeProps(props, React.createElement(\"div\", {\n    ref: rootRef,\n    className: classPrefix,\n    onClick: e => {\n      if (e.target === e.currentTarget) {\n        onContentClick(e);\n      }\n    }\n  }, measureNodes));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,OAAOC,UAAU,MAAM,cAAc;AACrC,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAE,CAAC;EACPC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,EAAE;EACXC,YAAY,EAAE,EAAE;EAChBC,+BAA+B,EAAE,EAAE;EACnCC,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;EACxBC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGC,CAAC,IAAI;EAC3B,MAAMC,KAAK,GAAGf,UAAU,CAACI,YAAY,EAAEU,CAAC,CAAC;EACzC,MAAM;IACJN,OAAO;IACPH,SAAS;IACTC,IAAI;IACJC,UAAU;IACVE,YAAY;IACZC,+BAA+B;IAC/BC,cAAc;IACdC;EACF,CAAC,GAAGG,KAAK;EACT;EACA,MAAMC,OAAO,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EAClC;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,KAAK,CAACuB,QAAQ,CAACR,eAAe,CAAC;EAC/D,MAAMS,UAAU,GAAGd,UAAU,GAAGN,mBAAmB,CAACS,+BAA+B,EAAEb,KAAK,CAACyB,aAAa,CAAC,GAAG,EAAE;IAC5GC,OAAO,EAAEA,CAAA,KAAM;MACbJ,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC,EAAEZ,UAAU,CAAC,CAAC,GAAG,IAAI;EACtB,MAAMiB,YAAY,GAAGf,YAAY,GAAGR,mBAAmB,CAACS,+BAA+B,EAAEb,KAAK,CAACyB,aAAa,CAAC,GAAG,EAAE;IAChHC,OAAO,EAAEA,CAAA,KAAM;MACbJ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC,EAAEV,YAAY,CAAC,CAAC,GAAG,IAAI;EACxB;EACA,MAAM,CAACgB,YAAY,EAAEC,WAAW,CAAC,GAAGxB,UAAU,CAACc,OAAO,EAAER,OAAO,EAAEF,IAAI,EAAED,SAAS,EAAEa,QAAQ,EAAEG,UAAU,EAAEG,YAAY,CAAC;EACrHzB,eAAe,CAAC2B,WAAW,EAAEV,OAAO,CAAC;EACrC;EACA,OAAOlB,eAAe,CAACiB,KAAK,EAAElB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IACvDK,GAAG,EAAEX,OAAO;IACZY,SAAS,EAAEzB,WAAW;IACtBoB,OAAO,EAAEM,CAAC,IAAI;MACZ,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;QAChCpB,cAAc,CAACkB,CAAC,CAAC;MACnB;IACF;EACF,CAAC,EAAEJ,YAAY,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}