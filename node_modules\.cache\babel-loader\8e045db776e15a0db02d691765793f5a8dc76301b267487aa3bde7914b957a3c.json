{"ast": null, "code": "import * as React from \"react\";\nfunction TextOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TextOutline-TextOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TextOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TextOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M31.3431458,3 C32.4040117,3 33.4214274,3.42142736 34.1715729,4.17157288 L41.8284271,11.8284271 C42.5785726,12.5785726 43,13.5959883 43,14.6568542 L43,39 C43,42.3137085 40.3137085,45 37,45 L11,45 C7.6862915,45 5,42.3137085 5,39 L5,9 C5,5.6862915 7.6862915,3 11,3 L31.3431458,3 Z M30,6 L11,6 C9.40231912,6 8.09633912,7.24891996 8.00509269,8.82372721 L8,9 L8,39 C8,40.5976809 9.24891996,41.9036609 10.8237272,41.9949073 L11,42 L37,42 C38.5976809,42 39.9036609,40.75108 39.9949073,39.1762728 L40,39 L40,17 L33,17 C31.3431458,17 30,15.6568542 30,14 L30,6 Z M37,25.4 L37,27.6 C37,27.8209139 36.8209139,28 36.6,28 L11.4,28 C11.1790861,28 11,27.8209139 11,27.6 L11,25.4 C11,25.1790861 11.1790861,25 11.4,25 L36.6,25 C36.8209139,25 37,25.1790861 37,25.4 Z M24,19.4 L24,21.6 C24,21.8209139 23.8209139,22 23.6,22 L11.4,22 C11.1790861,22 11,21.8209139 11,21.6 L11,19.4 C11,19.1790861 11.1790861,19 11.4,19 L23.6,19 C23.8209139,19 24,19.1790861 24,19.4 Z M33,8.20775065 L33,13.5969918 C33,13.8179057 33.1790861,13.9969918 33.4,13.9969918 C33.400999,13.9969918 33.4019979,13.9969881 33.4029969,13.9969806 L38.7516527,13.9569061 C38.9725604,13.955251 39.1502997,13.7748282 39.1486446,13.5539205 C39.1478576,13.4488816 39.1057843,13.3483675 39.031512,13.2740882 L33.6828562,7.92492145 C33.526654,7.76870427 33.273388,7.76869218 33.1171708,7.92489443 C33.042148,7.99991008 33,8.10165744 33,8.20775065 Z\",\n    id: \"TextOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TextOutline;", "map": {"version": 3, "names": ["React", "TextOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/TextOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TextOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TextOutline-TextOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TextOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TextOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M31.3431458,3 C32.4040117,3 33.4214274,3.42142736 34.1715729,4.17157288 L41.8284271,11.8284271 C42.5785726,12.5785726 43,13.5959883 43,14.6568542 L43,39 C43,42.3137085 40.3137085,45 37,45 L11,45 C7.6862915,45 5,42.3137085 5,39 L5,9 C5,5.6862915 7.6862915,3 11,3 L31.3431458,3 Z M30,6 L11,6 C9.40231912,6 8.09633912,7.24891996 8.00509269,8.82372721 L8,9 L8,39 C8,40.5976809 9.24891996,41.9036609 10.8237272,41.9949073 L11,42 L37,42 C38.5976809,42 39.9036609,40.75108 39.9949073,39.1762728 L40,39 L40,17 L33,17 C31.3431458,17 30,15.6568542 30,14 L30,6 Z M37,25.4 L37,27.6 C37,27.8209139 36.8209139,28 36.6,28 L11.4,28 C11.1790861,28 11,27.8209139 11,27.6 L11,25.4 C11,25.1790861 11.1790861,25 11.4,25 L36.6,25 C36.8209139,25 37,25.1790861 37,25.4 Z M24,19.4 L24,21.6 C24,21.8209139 23.8209139,22 23.6,22 L11.4,22 C11.1790861,22 11,21.8209139 11,21.6 L11,19.4 C11,19.1790861 11.1790861,19 11.4,19 L23.6,19 C23.8209139,19 24,19.1790861 24,19.4 Z M33,8.20775065 L33,13.5969918 C33,13.8179057 33.1790861,13.9969918 33.4,13.9969918 C33.400999,13.9969918 33.4019979,13.9969881 33.4029969,13.9969806 L38.7516527,13.9569061 C38.9725604,13.955251 39.1502997,13.7748282 39.1486446,13.5539205 C39.1478576,13.4488816 39.1057843,13.3483675 39.031512,13.2740882 L33.6828562,7.92492145 C33.526654,7.76870427 33.273388,7.76869218 33.1171708,7.92489443 C33.042148,7.99991008 33,8.10165744 33,8.20775065 Z\",\n    id: \"TextOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TextOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,22CAA22C;IAC92CR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}