{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState, useCallback } from 'react';\nimport useLatest from '../useLatest';\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useLatest(state);\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\nexport default useGetState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}