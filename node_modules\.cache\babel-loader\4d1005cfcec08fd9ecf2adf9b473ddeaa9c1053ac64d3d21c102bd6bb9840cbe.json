{"ast": null, "code": "import * as React from \"react\";\nfunction <PERSON><PERSON>iFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON><PERSON><PERSON><PERSON><PERSON>-KoubeiFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON>ubeiFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KoubeiFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,8 C41.2383969,8 43.8775718,10.5655749 43.9958615,13.7750617 L44,14 L44,20.2955401 C44,34.3726558 30.4644444,43.9454416 17.7133333,42.9256752 C17.2015873,42.881351 16.9830045,42.7191648 16.9543894,42.1924347 L16.9511111,42.0642981 L16.9511111,38.9746229 C9.5008547,36.2294318 4.16619172,28.8860579 4.00381161,20.6828507 L4,20.2977098 L4,14 C4,10.7616031 6.56557489,8.12242824 9.77506174,8.00413847 L10,8 L38,8 Z M14.2173913,18.5 C13.3889642,18.5 12.7173913,19.1715729 12.7173913,20 C12.7173913,24.738695 15.9222641,29.5943092 20.777631,31.0318564 C25.6050195,32.4611199 31.019456,30.4234468 33.4590735,26.8239108 C33.9238549,26.1381487 33.7447147,25.2054488 33.0589525,24.7406674 C32.3731903,24.275886 31.4404905,24.4550263 30.9757091,25.1407885 C29.2583855,27.6746151 25.1843467,29.2078419 21.6293078,28.1552878 C18.1212764,27.1166515 15.7173913,23.4745916 15.7173913,20 C15.7173913,19.1715729 15.0458184,18.5 14.2173913,18.5 Z M34.5,18.4782609 C33.6624489,18.5091223 33,19.1715713 33,19.9782609 C33,20.7849505 33.6624489,21.4473994 34.5,21.4782609 C35.3375511,21.4473994 36,20.7849505 36,19.9782609 C36,19.1715713 35.3375511,18.5091223 34.5,18.4782609 Z\",\n    id: \"KoubeiFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default KoubeiFill;", "map": {"version": 3, "names": ["React", "KoubeiFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/KoubeiFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction <PERSON><PERSON>iFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON><PERSON><PERSON><PERSON><PERSON>-KoubeiFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON>ubeiFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KoubeiFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,8 C41.2383969,8 43.8775718,10.5655749 43.9958615,13.7750617 L44,14 L44,20.2955401 C44,34.3726558 30.4644444,43.9454416 17.7133333,42.9256752 C17.2015873,42.881351 16.9830045,42.7191648 16.9543894,42.1924347 L16.9511111,42.0642981 L16.9511111,38.9746229 C9.5008547,36.2294318 4.16619172,28.8860579 4.00381161,20.6828507 L4,20.2977098 L4,14 C4,10.7616031 6.56557489,8.12242824 9.77506174,8.00413847 L10,8 L38,8 Z M14.2173913,18.5 C13.3889642,18.5 12.7173913,19.1715729 12.7173913,20 C12.7173913,24.738695 15.9222641,29.5943092 20.777631,31.0318564 C25.6050195,32.4611199 31.019456,30.4234468 33.4590735,26.8239108 C33.9238549,26.1381487 33.7447147,25.2054488 33.0589525,24.7406674 C32.3731903,24.275886 31.4404905,24.4550263 30.9757091,25.1407885 C29.2583855,27.6746151 25.1843467,29.2078419 21.6293078,28.1552878 C18.1212764,27.1166515 15.7173913,23.4745916 15.7173913,20 C15.7173913,19.1715729 15.0458184,18.5 14.2173913,18.5 Z M34.5,18.4782609 C33.6624489,18.5091223 33,19.1715713 33,19.9782609 C33,20.7849505 33.6624489,21.4473994 34.5,21.4782609 C35.3375511,21.4473994 36,20.7849505 36,19.9782609 C36,19.1715713 35.3375511,18.5091223 34.5,18.4782609 Z\",\n    id: \"KoubeiFill-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default KoubeiFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uBAAuB;IAC3BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,woCAAwoC;IAC3oCR,EAAE,EAAE,qCAAqC;IACzCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}