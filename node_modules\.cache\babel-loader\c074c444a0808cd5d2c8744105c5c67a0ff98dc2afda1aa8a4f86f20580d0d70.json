{"ast": null, "code": "import * as React from \"react\";\nfunction CompassOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CompassOutline-CompassOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CompassOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CompassOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M33.3801878,15.7161996 C33.5273657,16.1209296 33.5273657,16.5645541 33.3801877,16.9692858 L29.1223491,26.9287962 C28.751718,27.948527 27.9484932,28.7517514 26.928762,29.1223831 L16.9692515,33.3802217 C16.0176687,33.7261863 14.9657967,33.2352351 14.6198335,32.2836493 C14.4727126,31.8789902 14.4727126,31.4354581 14.6198336,31.0307994 L18.8776722,21.071289 C19.2483033,20.0515582 20.0515281,19.2483338 21.0712593,18.8777021 L31.0307697,14.6198636 C31.9822881,14.2738758 33.03413,14.7647079 33.3801878,15.7161996 Z M30.1241839,17.8758673 L22.010828,21.4627051 C21.7937007,21.5414402 21.6142978,21.6992367 21.5084927,21.9045403 L21.4626591,22.0108739 L17.8758214,30.1242299 L25.9891773,26.5373921 C26.2063046,26.4586569 26.3857075,26.3008605 26.4915126,26.0955568 L26.5373462,25.9892232 L30.1241839,17.8758673 Z\",\n    id: \"CompassOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default CompassOutline;", "map": {"version": 3, "names": ["React", "CompassOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/CompassOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CompassOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CompassOutline-CompassOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CompassOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CompassOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,2 C36.1502645,2 46,11.8497355 46,24 C46,36.1502645 36.1502645,46 24,46 C11.8497355,46 2,36.1502645 2,24 C2,11.8497355 11.8497355,2 24,2 Z M24,5 C13.5065898,5 5,13.5065898 5,24 C5,34.4934102 13.5065898,43 24,43 C34.4934102,43 43,34.4934102 43,24 C43,13.5065898 34.4934102,5 24,5 Z M33.3801878,15.7161996 C33.5273657,16.1209296 33.5273657,16.5645541 33.3801877,16.9692858 L29.1223491,26.9287962 C28.751718,27.948527 27.9484932,28.7517514 26.928762,29.1223831 L16.9692515,33.3802217 C16.0176687,33.7261863 14.9657967,33.2352351 14.6198335,32.2836493 C14.4727126,31.8789902 14.4727126,31.4354581 14.6198336,31.0307994 L18.8776722,21.071289 C19.2483033,20.0515582 20.0515281,19.2483338 21.0712593,18.8777021 L31.0307697,14.6198636 C31.9822881,14.2738758 33.03413,14.7647079 33.3801878,15.7161996 Z M30.1241839,17.8758673 L22.010828,21.4627051 C21.7937007,21.5414402 21.6142978,21.6992367 21.5084927,21.9045403 L21.4626591,22.0108739 L17.8758214,30.1242299 L25.9891773,26.5373921 C26.2063046,26.4586569 26.3857075,26.3008605 26.4915126,26.0955568 L26.5373462,25.9892232 L30.1241839,17.8758673 Z\",\n    id: \"CompassOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default CompassOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+BAA+B;IACnCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,skCAAskC;IACzkCR,EAAE,EAAE,yCAAyC;IAC7CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}