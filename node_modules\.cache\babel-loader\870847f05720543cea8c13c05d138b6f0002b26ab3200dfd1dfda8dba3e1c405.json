{"ast": null, "code": "import \"./grid.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Grid, GridItem } from './grid';\nexport default attachPropertiesToComponent(Grid, {\n  Item: GridItem\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Grid", "GridItem", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/grid/index.js"], "sourcesContent": ["import \"./grid.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Grid, GridItem } from './grid';\nexport default attachPropertiesToComponent(Grid, {\n  Item: GridItem\n});"], "mappings": "AAAA,OAAO,YAAY;AACnB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,IAAI,EAAEC,QAAQ,QAAQ,QAAQ;AACvC,eAAeF,2BAA2B,CAACC,IAAI,EAAE;EAC/CE,IAAI,EAAED;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}