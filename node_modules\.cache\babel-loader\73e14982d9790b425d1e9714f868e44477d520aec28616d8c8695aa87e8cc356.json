{"ast": null, "code": "import React, { useRef } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { Star } from './star';\nimport { useDrag } from '@use-gesture/react';\nimport { bound } from '../../utils/bound';\nconst classPrefix = `adm-rate`;\nconst defaultProps = {\n  count: 5,\n  allowHalf: false,\n  character: React.createElement(Star, null),\n  defaultValue: 0,\n  readOnly: false,\n  allowClear: true\n};\nexport const Rate = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue(props);\n  const containerRef = useRef(null);\n  const starList = Array(props.count).fill(null);\n  function renderStar(v, half) {\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-star`, {\n        [`${classPrefix}-star-active`]: value >= v,\n        [`${classPrefix}-star-half`]: half,\n        [`${classPrefix}-star-readonly`]: props.readOnly\n      }),\n      role: 'radio',\n      \"aria-checked\": value >= v,\n      \"aria-label\": '' + v\n    }, props.character);\n  }\n  const bind = useDrag(state => {\n    if (props.readOnly) return;\n    const {\n      xy: [clientX],\n      tap\n    } = state;\n    const container = containerRef.current;\n    if (!container) return;\n    const rect = container.getBoundingClientRect();\n    const rawValue = (clientX - rect.left) / rect.width * props.count;\n    const ceiledValue = props.allowHalf ? Math.ceil(rawValue * 2) / 2 : Math.ceil(rawValue);\n    const boundValue = bound(ceiledValue, 0, props.count);\n    if (tap) {\n      if (props.allowClear && boundValue === value) {\n        setValue(0);\n        return;\n      }\n    }\n    setValue(boundValue);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    },\n    filterTaps: true\n  });\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classNames(classPrefix, {\n      [`${classPrefix}-half`]: props.allowHalf\n    }),\n    role: 'radiogroup',\n    \"aria-readonly\": props.readOnly,\n    ref: containerRef\n  }, bind()), starList.map((_, i) => React.createElement(\"div\", {\n    key: i,\n    className: classNames(`${classPrefix}-box`)\n  }, props.allowHalf && renderStar(i + 0.5, true), renderStar(i + 1, false)))));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}