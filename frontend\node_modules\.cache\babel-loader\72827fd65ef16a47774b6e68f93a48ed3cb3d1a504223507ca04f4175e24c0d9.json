{"ast": null, "code": "import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}