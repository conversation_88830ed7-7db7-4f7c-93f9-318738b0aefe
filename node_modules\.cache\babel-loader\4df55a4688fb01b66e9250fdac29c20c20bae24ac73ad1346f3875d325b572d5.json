{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) return;\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\nexport default useSafeState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}