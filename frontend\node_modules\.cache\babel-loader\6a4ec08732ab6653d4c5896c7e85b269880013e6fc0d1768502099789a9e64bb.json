{"ast": null, "code": "import { useRef } from 'react';\nvar defaultShouldUpdate = function (a, b) {\n  return !Object.is(a, b);\n};\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n  var prevRef = useRef();\n  var curRef = useRef();\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n  return prevRef.current;\n}\nexport default usePrevious;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}