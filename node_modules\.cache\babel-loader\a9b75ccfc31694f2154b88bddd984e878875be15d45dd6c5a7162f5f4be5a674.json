{"ast": null, "code": "const typeTemplate = '${label}不是一个有效的${type}';\nconst zhCN = {\n  locale: 'zh-CH',\n  common: {\n    confirm: '确定',\n    cancel: '取消',\n    loading: '加载中',\n    close: '关闭'\n  },\n  Calendar: {\n    title: '日期选择',\n    confirm: '确认',\n    start: '开始',\n    end: '结束',\n    today: '今日',\n    markItems: ['一', '二', '三', '四', '五', '六', '日'],\n    yearAndMonth: '${year}年${month}月'\n  },\n  Cascader: {\n    placeholder: '请选择'\n  },\n  Dialog: {\n    ok: '我知道了'\n  },\n  DatePicker: {\n    tillNow: '至今'\n  },\n  ErrorBlock: {\n    default: {\n      title: '页面遇到一些小问题',\n      description: '待会来试试'\n    },\n    busy: {\n      title: '前方拥堵',\n      description: '刷新试试'\n    },\n    disconnected: {\n      title: '网络有点忙',\n      description: '动动手指帮忙修复'\n    },\n    empty: {\n      title: '没有找到你需要的东西',\n      description: '找找其他的吧'\n    }\n  },\n  Form: {\n    required: '必填',\n    optional: '选填',\n    defaultValidateMessages: {\n      default: '字段验证错误${label}',\n      required: '请输入${label}',\n      enum: '${label}必须是其中一个[${enum}]',\n      whitespace: '${label}不能为空字符',\n      date: {\n        format: '${label}日期格式无效',\n        parse: '${label}不能转换为日期',\n        invalid: '${label}是一个无效日期'\n      },\n      types: {\n        string: typeTemplate,\n        method: typeTemplate,\n        array: typeTemplate,\n        object: typeTemplate,\n        number: typeTemplate,\n        date: typeTemplate,\n        boolean: typeTemplate,\n        integer: typeTemplate,\n        float: typeTemplate,\n        regexp: typeTemplate,\n        email: typeTemplate,\n        url: typeTemplate,\n        hex: typeTemplate\n      },\n      string: {\n        len: '${label}须为${len}个字符',\n        min: '${label}最少${min}个字符',\n        max: '${label}最多${max}个字符',\n        range: '${label}须在${min}-${max}字符之间'\n      },\n      number: {\n        len: '${label}必须等于${len}',\n        min: '${label}最小值为${min}',\n        max: '${label}最大值为${max}',\n        range: '${label}须在${min}-${max}之间'\n      },\n      array: {\n        len: '须为${len}个${label}',\n        min: '最少${min}个${label}',\n        max: '最多${max}个${label}',\n        range: '${label}数量须在${min}-${max}之间'\n      },\n      pattern: {\n        mismatch: '${label}与模式不匹配${pattern}'\n      }\n    }\n  },\n  ImageUploader: {\n    uploading: '上传中...',\n    upload: '上传'\n  },\n  InfiniteScroll: {\n    noMore: '没有更多了',\n    failedToLoad: '加载失败',\n    retry: '重新加载'\n  },\n  Input: {\n    clear: '清除'\n  },\n  Mask: {\n    name: '背景蒙层'\n  },\n  Modal: {\n    ok: '我知道了'\n  },\n  PasscodeInput: {\n    name: '密码输入框'\n  },\n  PullToRefresh: {\n    pulling: '下拉刷新',\n    canRelease: '释放立即刷新',\n    complete: '刷新成功'\n  },\n  SearchBar: {\n    name: '搜索框'\n  },\n  Slider: {\n    name: '滑动输入条'\n  },\n  Stepper: {\n    decrease: '减少',\n    increase: '增加'\n  },\n  Switch: {\n    name: '开关'\n  },\n  Selector: {\n    name: '选择组'\n  }\n};\nexport default zhCN;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}