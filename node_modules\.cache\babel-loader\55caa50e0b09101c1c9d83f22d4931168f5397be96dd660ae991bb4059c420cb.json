{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const Arrow = memo(props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 30 16'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor'\n  }, React.createElement(\"path\", {\n    d: 'M0,0 L30,0 L18.07289,14.312538 C16.65863,16.009645 14.13637,16.238942 12.43926,14.824685 C12.25341,14.669808 12.08199,14.49839 11.92711,14.312538 L0,0 L0,0 Z'\n  }))));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}