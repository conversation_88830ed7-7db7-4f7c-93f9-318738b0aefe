{"ast": null, "code": "import * as React from \"react\";\nfunction BillOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BillOutline-BillOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BillOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BillOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39,6 C42.3137051,6 45,8.68624337 45,11.9998979 L45,35.9994897 C45,39.3131396 42.3137051,41.9993876 39,41.9993876 L37,41.9990814 C37,40.894533 36.1045648,39.9991139 35,39.9991139 C33.9456327,39.9991139 33.0818253,40.8149771 33.0054757,41.8498213 L32.99999,41.9990814 C32.99999,41.9992855 33,41.9994897 33,42 L26,42 C26,40.8951454 25.1045725,39.9997262 24,39.9997262 C22.9456401,39.9997262 22.081835,40.8155894 22.0054857,41.8507 L22,42 L22,42 L15.0000102,42 L15.0000102,41.9993876 C15.0000102,40.8948392 14.1045801,39.9994201 13,39.9994201 C11.9456473,39.9994201 11.0818357,40.8152833 11.0054858,41.8501275 L11,41.9993876 C11,41.9995918 11,42 11,42 L9,42 C5.76160643,42 3.12242839,39.4344666 3.00413848,36.2250364 L3,36.0001021 L3,36.0001021 L3,11.9999019 C3,8.68625199 5.68629026,6 9,6 L39,6 Z M39,9 L9,9 C7.41144231,9 6.09819222,10.2381891 6.00500002,11.8240074 L6,12 L6,36.0001669 C6,37.5887227 7.23833915,38.901902 8.8241674,38.9950097 L9,39 L9.06300115,38.9180002 C9.96316059,37.7665021 11.3227419,37.0673383 12.7830014,37.0049992 L13,37 C14.5990014,37 16.0219999,37.749999 16.937,38.9179971 L17,39 L20,39 L20.0630002,38.9179971 C20.9631597,37.766499 22.322741,37.0673351 23.7830005,37.0049961 L24,37 C25.5990005,37 27.021999,37.7499959 27.9369991,38.9179939 L28,39 L31,39 L31.0629993,38.917994 C31.9631587,37.7664959 33.3227401,37.067332 34.7829996,37.004993 L35,37 C36.5989996,37 38.0219981,37.7499927 38.9369982,38.9179908 L39,39 C40.5889437,39.0006088 41.9027797,37.7621957 41.996,36.1759931 L42,36 L42,11.9998331 C42,10.4112773 40.7616608,9.098098 39.1758326,9.00499022 L39,9 Z M21.7322027,13.0281599 L21.8059845,13.061299 L23.7015088,14.1527727 C23.7018048,14.1529431 23.7021006,14.1531139 23.7023961,14.1532851 C23.8696621,14.250171 23.9404928,14.4496518 23.8814088,14.6260712 L23.8480355,14.6999012 L20.782026,19.991873 L22.501974,19.992127 C22.7228879,19.992127 22.901974,20.1712131 22.901974,20.392127 L22.901974,22.592127 C22.901974,22.8130409 22.7228879,22.992127 22.501974,22.992127 L18.901026,22.991873 L18.901026,24.991873 L22.501974,24.992127 C22.7228879,24.992127 22.901974,25.1712131 22.901974,25.392127 L22.901974,27.592127 C22.901974,27.8130409 22.7228879,27.992127 22.501974,27.992127 L18.901026,27.991873 L18.901974,31.592127 C18.901974,31.8130409 18.7228879,31.992127 18.501974,31.992127 L16.301974,31.992127 C16.0810601,31.992127 15.901974,31.8130409 15.901974,31.592127 L15.901026,27.991873 L12.301974,27.992127 C12.0810601,27.992127 11.901974,27.8130409 11.901974,27.592127 L11.901974,25.392127 C11.901974,25.1712131 12.0810601,24.992127 12.301974,24.992127 L15.901026,24.991873 L15.901026,22.991873 L12.301974,22.992127 C12.0810601,22.992127 11.901974,22.8130409 11.901974,22.592127 L11.901974,20.392127 C11.901974,20.1712131 12.0810601,19.992127 12.301974,19.992127 L14.217026,19.991873 L11.1519645,14.6999012 C11.0412379,14.5087402 11.1064428,14.2640118 11.2976039,14.1532851 C11.2978994,14.1531139 11.2981952,14.1529431 11.2984912,14.1527727 L13.1940155,13.061299 C13.3851129,12.951262 13.6292181,13.0166359 13.7397442,13.2074508 L17.500026,19.697873 L21.2602558,13.2074508 C21.3569661,13.0404878 21.555948,12.9695655 21.7322027,13.0281599 Z M35.6000001,25 C35.82,25 36,25.1800002 36,25.4000001 L36,27.5999999 C36,27.820914 35.8209141,28 35.6000001,28 L28.3999999,28 C28.2067002,28 28.045425,27.8628872 28.0081266,27.6806139 L28,27.5999999 L28,27.5999999 L28,25.4000001 C28,25.1800001 28.18,25 28.3999999,25 L35.6000001,25 Z M35.6000001,20 C35.82,20 36,20.1800002 36,20.4000001 L36,22.5999999 C36,22.7932997 35.8628873,22.9545749 35.6806141,22.9918734 L35.6000001,23 L35.6000001,23 L28.3999999,23 C28.2067002,23 28.045425,22.8628872 28.0081266,22.6806139 L28,22.5999998 L28,22.5999998 L28,20.4000001 C28,20.1800001 28.18,20 28.3999999,20 L35.6000001,20 Z\",\n    id: \"BillOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default BillOutline;", "map": {"version": 3, "names": ["React", "BillOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/BillOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction BillOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BillOutline-BillOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"BillOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"BillOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39,6 C42.3137051,6 45,8.68624337 45,11.9998979 L45,35.9994897 C45,39.3131396 42.3137051,41.9993876 39,41.9993876 L37,41.9990814 C37,40.894533 36.1045648,39.9991139 35,39.9991139 C33.9456327,39.9991139 33.0818253,40.8149771 33.0054757,41.8498213 L32.99999,41.9990814 C32.99999,41.9992855 33,41.9994897 33,42 L26,42 C26,40.8951454 25.1045725,39.9997262 24,39.9997262 C22.9456401,39.9997262 22.081835,40.8155894 22.0054857,41.8507 L22,42 L22,42 L15.0000102,42 L15.0000102,41.9993876 C15.0000102,40.8948392 14.1045801,39.9994201 13,39.9994201 C11.9456473,39.9994201 11.0818357,40.8152833 11.0054858,41.8501275 L11,41.9993876 C11,41.9995918 11,42 11,42 L9,42 C5.76160643,42 3.12242839,39.4344666 3.00413848,36.2250364 L3,36.0001021 L3,36.0001021 L3,11.9999019 C3,8.68625199 5.68629026,6 9,6 L39,6 Z M39,9 L9,9 C7.41144231,9 6.09819222,10.2381891 6.00500002,11.8240074 L6,12 L6,36.0001669 C6,37.5887227 7.23833915,38.901902 8.8241674,38.9950097 L9,39 L9.06300115,38.9180002 C9.96316059,37.7665021 11.3227419,37.0673383 12.7830014,37.0049992 L13,37 C14.5990014,37 16.0219999,37.749999 16.937,38.9179971 L17,39 L20,39 L20.0630002,38.9179971 C20.9631597,37.766499 22.322741,37.0673351 23.7830005,37.0049961 L24,37 C25.5990005,37 27.021999,37.7499959 27.9369991,38.9179939 L28,39 L31,39 L31.0629993,38.917994 C31.9631587,37.7664959 33.3227401,37.067332 34.7829996,37.004993 L35,37 C36.5989996,37 38.0219981,37.7499927 38.9369982,38.9179908 L39,39 C40.5889437,39.0006088 41.9027797,37.7621957 41.996,36.1759931 L42,36 L42,11.9998331 C42,10.4112773 40.7616608,9.098098 39.1758326,9.00499022 L39,9 Z M21.7322027,13.0281599 L21.8059845,13.061299 L23.7015088,14.1527727 C23.7018048,14.1529431 23.7021006,14.1531139 23.7023961,14.1532851 C23.8696621,14.250171 23.9404928,14.4496518 23.8814088,14.6260712 L23.8480355,14.6999012 L20.782026,19.991873 L22.501974,19.992127 C22.7228879,19.992127 22.901974,20.1712131 22.901974,20.392127 L22.901974,22.592127 C22.901974,22.8130409 22.7228879,22.992127 22.501974,22.992127 L18.901026,22.991873 L18.901026,24.991873 L22.501974,24.992127 C22.7228879,24.992127 22.901974,25.1712131 22.901974,25.392127 L22.901974,27.592127 C22.901974,27.8130409 22.7228879,27.992127 22.501974,27.992127 L18.901026,27.991873 L18.901974,31.592127 C18.901974,31.8130409 18.7228879,31.992127 18.501974,31.992127 L16.301974,31.992127 C16.0810601,31.992127 15.901974,31.8130409 15.901974,31.592127 L15.901026,27.991873 L12.301974,27.992127 C12.0810601,27.992127 11.901974,27.8130409 11.901974,27.592127 L11.901974,25.392127 C11.901974,25.1712131 12.0810601,24.992127 12.301974,24.992127 L15.901026,24.991873 L15.901026,22.991873 L12.301974,22.992127 C12.0810601,22.992127 11.901974,22.8130409 11.901974,22.592127 L11.901974,20.392127 C11.901974,20.1712131 12.0810601,19.992127 12.301974,19.992127 L14.217026,19.991873 L11.1519645,14.6999012 C11.0412379,14.5087402 11.1064428,14.2640118 11.2976039,14.1532851 C11.2978994,14.1531139 11.2981952,14.1529431 11.2984912,14.1527727 L13.1940155,13.061299 C13.3851129,12.951262 13.6292181,13.0166359 13.7397442,13.2074508 L17.500026,19.697873 L21.2602558,13.2074508 C21.3569661,13.0404878 21.555948,12.9695655 21.7322027,13.0281599 Z M35.6000001,25 C35.82,25 36,25.1800002 36,25.4000001 L36,27.5999999 C36,27.820914 35.8209141,28 35.6000001,28 L28.3999999,28 C28.2067002,28 28.045425,27.8628872 28.0081266,27.6806139 L28,27.5999999 L28,27.5999999 L28,25.4000001 C28,25.1800001 28.18,25 28.3999999,25 L35.6000001,25 Z M35.6000001,20 C35.82,20 36,20.1800002 36,20.4000001 L36,22.5999999 C36,22.7932997 35.8628873,22.9545749 35.6806141,22.9918734 L35.6000001,23 L35.6000001,23 L28.3999999,23 C28.2067002,23 28.045425,22.8628872 28.0081266,22.6806139 L28,22.5999998 L28,22.5999998 L28,20.4000001 C28,20.1800001 28.18,20 28.3999999,20 L35.6000001,20 Z\",\n    id: \"BillOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default BillOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+sHAA+sH;IACltHR,EAAE,EAAE,sCAAsC;IAC1CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}