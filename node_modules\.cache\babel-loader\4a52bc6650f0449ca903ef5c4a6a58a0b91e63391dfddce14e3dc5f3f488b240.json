{"ast": null, "code": "import * as React from \"react\";\nfunction UserSetOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserSetOutline-UserSetOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserSetOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserSetOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M40.5753904,32.1103044 L43.8815641,37.7547783 C43.9547078,37.8796529 43.9546967,38.0342971 43.8815353,38.1591613 L40.5751134,43.8022161 C40.5033314,43.9247261 40.3719829,44 40.2299922,44 L33.6080949,44 C33.4661042,44 33.3347557,43.9247261 33.2629737,43.8022161 L29.9565517,38.1591613 C29.8833904,38.0342971 29.8833793,37.8796529 29.956523,37.7547783 L33.2626967,32.1103044 C33.3344715,31.9877666 33.4658357,31.9124713 33.6078467,31.9124713 L40.2302404,31.9124713 C40.3722514,31.9124713 40.5036156,31.9877666 40.5753904,32.1103044 Z M23.7683288,4 C29.35542,4 33.8846523,8.46331622 33.8846523,13.9690958 L33.8846523,17.9567341 C33.8846523,21.6463012 31.850714,24.8677302 28.8274881,26.5915737 L28.8264906,26.9289203 L31.266768,28.2111429 C31.4623292,28.3138988 31.5375627,28.5557326 31.4348068,28.7512938 C31.4332269,28.7543006 31.4316087,28.7572871 31.4299528,28.7602527 L30.3570966,30.6815673 C30.2513328,30.8709733 30.0138688,30.9415517 29.8218235,30.8406597 L26.0009657,28.8333513 C25.8690211,28.7640334 25.7865589,28.6271052 25.7869997,28.4780611 L25.7970023,25.0954539 C25.797426,24.9522021 25.8744232,24.8201136 25.9988669,24.7491557 L27.3075368,24.0029521 L27.3075368,24.0029521 C29.4061201,22.8063399 30.7511566,20.6400279 30.8445539,18.2259481 L30.8497553,17.9567341 L30.8497553,13.9690958 C30.8497553,10.1150501 27.6792926,6.99072874 23.7683288,6.99072874 C19.9388434,6.99072874 16.8193264,9.98622608 16.6910086,13.7291898 L16.6869024,13.9690958 L16.6869024,17.9567341 C16.6869024,20.3884528 17.9572813,22.5990835 19.9997365,23.8664547 L20.2298279,24.0033553 L21.5432287,24.7520568 C21.6680669,24.8232206 21.7451404,24.9558788 21.7451348,25.099576 L21.7450041,28.4817526 C21.7449983,28.6303426 21.6626265,28.7666844 21.5310962,28.835813 L9.00536301,35.4189811 L9.00536301,35.4189811 C7.66114862,36.1254619 6.88959666,37.56793 7.05783279,39.0600194 C7.16916078,40.0473894 7.97928576,40.806392 8.96984625,40.8798239 L9.12767443,40.8856545 L27.6971183,40.8850082 C27.8421299,40.8850031 27.9757875,40.9634812 28.0464416,41.090116 L29.2690883,43.281491 C29.3767243,43.4744092 29.3075894,43.7180566 29.1146712,43.8256926 C29.055093,43.8589333 28.9880029,43.8763832 28.9197789,43.8763832 L9.12767443,43.8763832 L9.12767443,43.8763832 C6.52006839,43.8763832 4.32945989,41.9442297 4.04149798,39.3902901 C3.73607071,36.68145 5.13679836,34.0626958 7.57717616,32.7801028 L18.7101671,26.9289203 L18.7101801,26.5921498 C15.6863969,24.868448 13.6520053,21.6467123 13.6520053,17.9567341 L13.6520053,13.9690958 C13.6520053,8.46331622 18.1812376,4 23.7683288,4 Z M38.4778317,34.9032001 L35.3602554,34.9032001 C35.2182443,34.9032001 35.0868802,34.9784954 35.0151053,35.1010332 L33.4608532,37.7545342 C33.3877096,37.8794088 33.3877206,38.034053 33.460882,38.1589172 L35.0150963,40.8114873 C35.0868783,40.9339974 35.2182268,41.0092713 35.3602175,41.0092713 L38.4778696,41.0092713 C38.6198603,41.0092713 38.7512088,40.9339974 38.8229908,40.8114873 L40.3772051,38.1589172 C40.4503665,38.034053 40.4503775,37.8794088 40.3772339,37.7545342 L38.8229818,35.1010332 C38.7512069,34.9784954 38.6198428,34.9032001 38.4778317,34.9032001 Z\",\n    id: \"UserSetOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UserSetOutline;", "map": {"version": 3, "names": ["React", "UserSetOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/UserSetOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UserSetOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserSetOutline-UserSetOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UserSetOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UserSetOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M40.5753904,32.1103044 L43.8815641,37.7547783 C43.9547078,37.8796529 43.9546967,38.0342971 43.8815353,38.1591613 L40.5751134,43.8022161 C40.5033314,43.9247261 40.3719829,44 40.2299922,44 L33.6080949,44 C33.4661042,44 33.3347557,43.9247261 33.2629737,43.8022161 L29.9565517,38.1591613 C29.8833904,38.0342971 29.8833793,37.8796529 29.956523,37.7547783 L33.2626967,32.1103044 C33.3344715,31.9877666 33.4658357,31.9124713 33.6078467,31.9124713 L40.2302404,31.9124713 C40.3722514,31.9124713 40.5036156,31.9877666 40.5753904,32.1103044 Z M23.7683288,4 C29.35542,4 33.8846523,8.46331622 33.8846523,13.9690958 L33.8846523,17.9567341 C33.8846523,21.6463012 31.850714,24.8677302 28.8274881,26.5915737 L28.8264906,26.9289203 L31.266768,28.2111429 C31.4623292,28.3138988 31.5375627,28.5557326 31.4348068,28.7512938 C31.4332269,28.7543006 31.4316087,28.7572871 31.4299528,28.7602527 L30.3570966,30.6815673 C30.2513328,30.8709733 30.0138688,30.9415517 29.8218235,30.8406597 L26.0009657,28.8333513 C25.8690211,28.7640334 25.7865589,28.6271052 25.7869997,28.4780611 L25.7970023,25.0954539 C25.797426,24.9522021 25.8744232,24.8201136 25.9988669,24.7491557 L27.3075368,24.0029521 L27.3075368,24.0029521 C29.4061201,22.8063399 30.7511566,20.6400279 30.8445539,18.2259481 L30.8497553,17.9567341 L30.8497553,13.9690958 C30.8497553,10.1150501 27.6792926,6.99072874 23.7683288,6.99072874 C19.9388434,6.99072874 16.8193264,9.98622608 16.6910086,13.7291898 L16.6869024,13.9690958 L16.6869024,17.9567341 C16.6869024,20.3884528 17.9572813,22.5990835 19.9997365,23.8664547 L20.2298279,24.0033553 L21.5432287,24.7520568 C21.6680669,24.8232206 21.7451404,24.9558788 21.7451348,25.099576 L21.7450041,28.4817526 C21.7449983,28.6303426 21.6626265,28.7666844 21.5310962,28.835813 L9.00536301,35.4189811 L9.00536301,35.4189811 C7.66114862,36.1254619 6.88959666,37.56793 7.05783279,39.0600194 C7.16916078,40.0473894 7.97928576,40.806392 8.96984625,40.8798239 L9.12767443,40.8856545 L27.6971183,40.8850082 C27.8421299,40.8850031 27.9757875,40.9634812 28.0464416,41.090116 L29.2690883,43.281491 C29.3767243,43.4744092 29.3075894,43.7180566 29.1146712,43.8256926 C29.055093,43.8589333 28.9880029,43.8763832 28.9197789,43.8763832 L9.12767443,43.8763832 L9.12767443,43.8763832 C6.52006839,43.8763832 4.32945989,41.9442297 4.04149798,39.3902901 C3.73607071,36.68145 5.13679836,34.0626958 7.57717616,32.7801028 L18.7101671,26.9289203 L18.7101801,26.5921498 C15.6863969,24.868448 13.6520053,21.6467123 13.6520053,17.9567341 L13.6520053,13.9690958 C13.6520053,8.46331622 18.1812376,4 23.7683288,4 Z M38.4778317,34.9032001 L35.3602554,34.9032001 C35.2182443,34.9032001 35.0868802,34.9784954 35.0151053,35.1010332 L33.4608532,37.7545342 C33.3877096,37.8794088 33.3877206,38.034053 33.460882,38.1589172 L35.0150963,40.8114873 C35.0868783,40.9339974 35.2182268,41.0092713 35.3602175,41.0092713 L38.4778696,41.0092713 C38.6198603,41.0092713 38.7512088,40.9339974 38.8229908,40.8114873 L40.3772051,38.1589172 C40.4503665,38.034053 40.4503775,37.8794088 40.3772339,37.7545342 L38.8229818,35.1010332 C38.7512069,34.9784954 38.6198428,34.9032001 38.4778317,34.9032001 Z\",\n    id: \"UserSetOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UserSetOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+BAA+B;IACnCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6iGAA6iG;IAChjGR,EAAE,EAAE,6BAA6B;IACjCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}