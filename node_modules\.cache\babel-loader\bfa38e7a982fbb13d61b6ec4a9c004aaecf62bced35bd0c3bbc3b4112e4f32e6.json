{"ast": null, "code": "import { defineHidden, is, createInterpolator, eachProp, hasFluidValue, getFluidValue, each, isAnimatedString, useForceUpdate, useIsomorphicLayoutEffect, addFluidObserver, removeFluidObserver, raf, useOnce } from '@react-spring/shared';\nimport * as React from 'react';\nimport { forwardRef, useRef, useCallback, useEffect } from 'react';\nconst $node = Symbol.for('Animated:node');\nconst isAnimated = value => !!value && value[$node] === value;\nconst getAnimated = owner => owner && owner[$node];\nconst setAnimated = (owner, node) => defineHidden(owner, $node, node);\nconst getPayload = owner => owner && owner[$node] && owner[$node].getPayload();\nclass Animated {\n  constructor() {\n    this.payload = void 0;\n    setAnimated(this, this);\n  }\n  getPayload() {\n    return this.payload || [];\n  }\n}\nclass AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this.done = true;\n    this.elapsedTime = void 0;\n    this.lastPosition = void 0;\n    this.lastVelocity = void 0;\n    this.v0 = void 0;\n    this.durationProgress = 0;\n    this._value = _value;\n    if (is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  static create(value) {\n    return new AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const {\n      done\n    } = this;\n    this.done = false;\n    if (is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n}\nclass AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = void 0;\n    this._toString = createInterpolator({\n      output: [value, value]\n    });\n  }\n  static create(value) {\n    return new AnimatedString(value);\n  }\n  getValue() {\n    let value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n}\nconst TreeContext = {\n  dependencies: null\n};\nclass AnimatedObject extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset());\n    }\n  }\n  _makePayload(source) {\n    if (source) {\n      const payload = new Set();\n      eachProp(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  _addToPayload(source) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      each(payload, node => this.add(node));\n    }\n  }\n}\nclass AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  static create(source) {\n    return new AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map(node => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n}\nfunction makeAnimated(value) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : is.arr(value) ? AnimatedArray : isAnimatedString(value) ? AnimatedString : AnimatedValue;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nconst withAnimated = (Component, host) => {\n  const hasInstance = !is.fun(Component) || Component.prototype && Component.prototype.isReactComponent;\n  return forwardRef((givenProps, givenRef) => {\n    const instanceRef = useRef(null);\n    const ref = hasInstance && useCallback(value => {\n      instanceRef.current = updateRef(givenRef, value);\n    }, [givenRef]);\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = useForceUpdate();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = useRef();\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer;\n      each(deps, dep => addFluidObserver(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep => removeFluidObserver(dep, observerRef.current));\n          raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    useEffect(callback, []);\n    useOnce(() => () => {\n      const observer = observerRef.current;\n      each(observer.deps, dep => removeFluidObserver(dep, observer));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return React.createElement(Component, _extends({}, usedProps, {\n      ref: ref\n    }));\n  });\n};\nclass PropsObserver {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      raf.write(this.update);\n    }\n  }\n}\nfunction getAnimatedState(props, host) {\n  const dependencies = new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style) props = _extends({}, props, {\n    style: host.createAnimatedStyle(props.style)\n  });\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (is.fun(ref)) ref(value);else ref.current = value;\n  }\n  return value;\n}\nconst cacheKey = Symbol.for('AnimatedComponent');\nconst createHost = (components, {\n  applyAnimatedValues: _applyAnimatedValues = () => false,\n  createAnimatedStyle: _createAnimatedStyle = style => new AnimatedObject(style),\n  getComponentProps: _getComponentProps = props => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues: _applyAnimatedValues,\n    createAnimatedStyle: _createAnimatedStyle,\n    getComponentProps: _getComponentProps\n  };\n  const animated = Component => {\n    const displayName = getDisplayName(Component) || 'Anonymous';\n    if (is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nconst getDisplayName = arg => is.str(arg) ? arg : arg && is.str(arg.displayName) ? arg.displayName : is.fun(arg) && arg.name || null;\nexport { Animated, AnimatedArray, AnimatedObject, AnimatedString, AnimatedValue, createHost, getAnimated, getAnimatedType, getPayload, isAnimated, setAnimated };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}