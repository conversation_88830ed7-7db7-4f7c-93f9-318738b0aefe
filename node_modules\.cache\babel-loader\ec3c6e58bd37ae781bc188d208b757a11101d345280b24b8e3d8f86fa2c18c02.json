{"ast": null, "code": "import \"./tab-bar.css\";\nimport { TabBar, TabBarItem } from './tab-bar';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(TabBar, {\n  Item: TabBarItem\n});", "map": {"version": 3, "names": ["TabBar", "TabBarItem", "attachPropertiesToComponent", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/tab-bar/index.js"], "sourcesContent": ["import \"./tab-bar.css\";\nimport { TabBar, TabBarItem } from './tab-bar';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(TabBar, {\n  Item: TabBarItem\n});"], "mappings": "AAAA,OAAO,eAAe;AACtB,SAASA,MAAM,EAAEC,UAAU,QAAQ,WAAW;AAC9C,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACF,MAAM,EAAE;EACjDG,IAAI,EAAEF;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}