{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Divider from '../divider';\nconst classPrefix = `adm-footer`;\nconst defaultProps = {\n  label: '',\n  links: [],\n  content: '',\n  chips: []\n};\nexport const Footer = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    label,\n    links,\n    content,\n    chips,\n    onChipClick,\n    onLinkClick\n  } = props;\n  const clickChipItem = (item, index) => {\n    if ((chips === null || chips === void 0 ? void 0 : chips.length) && item.type === 'link') {\n      onChipClick === null || onChipClick === void 0 ? void 0 : onChipClick(item, index);\n    }\n  };\n  const clickLinkItem = (item, index, e) => {\n    if (onLinkClick) {\n      e.preventDefault();\n      onLinkClick(item, index);\n    }\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix)\n  }, label && React.createElement(\"div\", {\n    className: `${classPrefix}-label`\n  }, React.createElement(Divider, null, label)), !!(links === null || links === void 0 ? void 0 : links.length) && React.createElement(\"div\", {\n    className: `${classPrefix}-links`\n  }, links.map((link, index) => React.createElement(React.Fragment, {\n    key: index\n  }, React.createElement(\"a\", {\n    href: link.href,\n    rel: 'noopener noreferrer',\n    onClick: event => clickLinkItem(link, index, event)\n  }, link.text), index !== links.length - 1 && React.createElement(Divider, {\n    direction: 'vertical'\n  })))), content && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, content), chips && chips.length > 0 && React.createElement(\"div\", {\n    className: `${classPrefix}-chips`\n  }, chips.map((chip, index) => React.createElement(\"div\", {\n    key: index,\n    onClick: () => clickChipItem(chip, index),\n    className: classNames(`${classPrefix}-chip`, {\n      [`${classPrefix}-chip-link`]: chip.type === 'link'\n    })\n  }, chip.text)))));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}