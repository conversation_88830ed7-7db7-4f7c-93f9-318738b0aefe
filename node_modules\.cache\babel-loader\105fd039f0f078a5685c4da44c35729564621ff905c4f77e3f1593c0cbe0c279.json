{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\n// support refreshDeps & ready\nvar useAutoRunPlugin = function (fetchInstance, _a) {\n  var manual = _a.manual,\n    _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    _c = _a.defaultParams,\n    defaultParams = _c === void 0 ? [] : _c,\n    _d = _a.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function () {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\nexport default useAutoRunPlugin;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useRef", "useUpdateEffect", "useAutoRunPlugin", "fetchInstance", "_a", "manual", "_b", "ready", "_c", "defaultParams", "_d", "refreshDeps", "refreshDepsAction", "hasAutoRun", "current", "run", "apply", "refresh", "onBefore", "stopNow", "onInit", "loading"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\n// support refreshDeps & ready\nvar useAutoRunPlugin = function (fetchInstance, _a) {\n  var manual = _a.manual,\n    _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    _c = _a.defaultParams,\n    defaultParams = _c === void 0 ? [] : _c,\n    _d = _a.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function () {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\nexport default useAutoRunPlugin;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,eAAe,MAAM,0BAA0B;AACtD;AACA,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EAClD,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;IACpBC,EAAE,GAAGF,EAAE,CAACG,KAAK;IACbA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjCE,EAAE,GAAGJ,EAAE,CAACK,aAAa;IACrBA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACvCE,EAAE,GAAGN,EAAE,CAACO,WAAW;IACnBA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACrCE,iBAAiB,GAAGR,EAAE,CAACQ,iBAAiB;EAC1C,IAAIC,UAAU,GAAGb,MAAM,CAAC,KAAK,CAAC;EAC9Ba,UAAU,CAACC,OAAO,GAAG,KAAK;EAC1Bb,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACI,MAAM,IAAIE,KAAK,EAAE;MACpBM,UAAU,CAACC,OAAO,GAAG,IAAI;MACzBX,aAAa,CAACY,GAAG,CAACC,KAAK,CAACb,aAAa,EAAEJ,aAAa,CAAC,EAAE,EAAED,MAAM,CAACW,aAAa,CAAC,EAAE,KAAK,CAAC,CAAC;IACzF;EACF,CAAC,EAAE,CAACF,KAAK,CAAC,CAAC;EACXN,eAAe,CAAC,YAAY;IAC1B,IAAIY,UAAU,CAACC,OAAO,EAAE;MACtB;IACF;IACA,IAAI,CAACT,MAAM,EAAE;MACXQ,UAAU,CAACC,OAAO,GAAG,IAAI;MACzB,IAAIF,iBAAiB,EAAE;QACrBA,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM;QACLT,aAAa,CAACc,OAAO,CAAC,CAAC;MACzB;IACF;EACF,CAAC,EAAElB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACa,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;EACjD,OAAO;IACLO,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpB,IAAI,CAACX,KAAK,EAAE;QACV,OAAO;UACLY,OAAO,EAAE;QACX,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC;AACDjB,gBAAgB,CAACkB,MAAM,GAAG,UAAUhB,EAAE,EAAE;EACtC,IAAIE,EAAE,GAAGF,EAAE,CAACG,KAAK;IACfA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjCD,MAAM,GAAGD,EAAE,CAACC,MAAM;EACpB,OAAO;IACLgB,OAAO,EAAE,CAAChB,MAAM,IAAIE;EACtB,CAAC;AACH,CAAC;AACD,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}