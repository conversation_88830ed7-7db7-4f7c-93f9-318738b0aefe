{"ast": null, "code": "import React from 'react';\nimport { <PERSON>C<PERSON>cle<PERSON>ill, CloseCircleFill, InformationCircleFill, ClockCircleFill, ExclamationCircleFill } from 'antd-mobile-icons';\nimport { useConfig } from '../config-provider';\nexport const useResultIcon = status => {\n  const {\n    result: componentConfig = {}\n  } = useConfig();\n  const {\n    successIcon = React.createElement(CheckCircleFill, null),\n    errorIcon = React.createElement(CloseCircleFill, null),\n    infoIcon = React.createElement(InformationCircleFill, null),\n    waitingIcon = React.createElement(ClockCircleFill, null),\n    warningIcon = React.createElement(ExclamationCircleFill, null)\n  } = componentConfig || {};\n  switch (status) {\n    case 'success':\n      return successIcon;\n    case 'error':\n      return errorIcon;\n    case 'info':\n      return infoIcon;\n    case 'waiting':\n      return waitingIcon;\n    case 'warning':\n      return warningIcon;\n    default:\n      return null;\n  }\n};", "map": {"version": 3, "names": ["React", "CheckCircleFill", "CloseCircleFill", "InformationCircleFill", "ClockCircleFill", "ExclamationCircleFill", "useConfig", "useResultIcon", "status", "result", "componentConfig", "successIcon", "createElement", "errorIcon", "infoIcon", "waitingIcon", "warningIcon"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/result/use-result-icon.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>C<PERSON>cle<PERSON>ill, CloseCircleFill, InformationCircleFill, ClockCircleFill, ExclamationCircleFill } from 'antd-mobile-icons';\nimport { useConfig } from '../config-provider';\nexport const useResultIcon = status => {\n  const {\n    result: componentConfig = {}\n  } = useConfig();\n  const {\n    successIcon = React.createElement(CheckCircleFill, null),\n    errorIcon = React.createElement(CloseCircleFill, null),\n    infoIcon = React.createElement(InformationCircleFill, null),\n    waitingIcon = React.createElement(ClockCircleFill, null),\n    warningIcon = React.createElement(ExclamationCircleFill, null)\n  } = componentConfig || {};\n  switch (status) {\n    case 'success':\n      return successIcon;\n    case 'error':\n      return errorIcon;\n    case 'info':\n      return infoIcon;\n    case 'waiting':\n      return waitingIcon;\n    case 'warning':\n      return warningIcon;\n    default:\n      return null;\n  }\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,mBAAmB;AACnI,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAO,MAAMC,aAAa,GAAGC,MAAM,IAAI;EACrC,MAAM;IACJC,MAAM,EAAEC,eAAe,GAAG,CAAC;EAC7B,CAAC,GAAGJ,SAAS,CAAC,CAAC;EACf,MAAM;IACJK,WAAW,GAAGX,KAAK,CAACY,aAAa,CAACX,eAAe,EAAE,IAAI,CAAC;IACxDY,SAAS,GAAGb,KAAK,CAACY,aAAa,CAACV,eAAe,EAAE,IAAI,CAAC;IACtDY,QAAQ,GAAGd,KAAK,CAACY,aAAa,CAACT,qBAAqB,EAAE,IAAI,CAAC;IAC3DY,WAAW,GAAGf,KAAK,CAACY,aAAa,CAACR,eAAe,EAAE,IAAI,CAAC;IACxDY,WAAW,GAAGhB,KAAK,CAACY,aAAa,CAACP,qBAAqB,EAAE,IAAI;EAC/D,CAAC,GAAGK,eAAe,IAAI,CAAC,CAAC;EACzB,QAAQF,MAAM;IACZ,KAAK,SAAS;MACZ,OAAOG,WAAW;IACpB,KAAK,OAAO;MACV,OAAOE,SAAS;IAClB,KAAK,MAAM;MACT,OAAOC,QAAQ;IACjB,KAAK,SAAS;MACZ,OAAOC,WAAW;IACpB,KAAK,SAAS;MACZ,OAAOC,WAAW;IACpB;MACE,OAAO,IAAI;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}