{"ast": null, "code": "import { useTouch } from './use-touch';\nimport { useEffect } from 'react';\nimport { getScrollParent } from './get-scroll-parent';\nimport { supportsPassive } from './supports-passive';\nlet totalLockCount = 0;\nconst BODY_LOCK_CLASS = 'adm-overflow-hidden';\nfunction getScrollableElement(el) {\n  let current = el === null || el === void 0 ? void 0 : el.parentElement;\n  while (current) {\n    if (current.clientHeight < current.scrollHeight) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n// 移植自vant：https://github.com/youzan/vant/blob/HEAD/src/composables/use-lock-scroll.ts\nexport function useLockScroll(rootRef, shouldLock) {\n  const touch = useTouch();\n  const onTouchMove = event => {\n    touch.move(event);\n    const direction = touch.deltaY.current > 0 ? '10' : '01';\n    const el = getScrollParent(event.target, rootRef.current);\n    if (!el) return;\n    // This has perf cost but we have to compatible with iOS 12\n    if (shouldLock === 'strict') {\n      const scrollableParent = getScrollableElement(event.target);\n      if (scrollableParent === document.body || scrollableParent === document.documentElement) {\n        event.preventDefault();\n        return;\n      }\n    }\n    const {\n      scrollHeight,\n      offsetHeight,\n      scrollTop\n    } = el;\n    const {\n      height\n    } = el.getBoundingClientRect();\n    let status = '11';\n    if (scrollTop === 0) {\n      status = offsetHeight >= scrollHeight ? '00' : '01';\n    } else if (scrollHeight <= Math.round(height + scrollTop)) {\n      status = '10';\n    }\n    if (status !== '11' && touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {\n      if (event.cancelable && supportsPassive) {\n        // https://github.com/ant-design/ant-design-mobile/issues/6282\n        event.preventDefault();\n      }\n    }\n  };\n  const lock = () => {\n    document.addEventListener('touchstart', touch.start);\n    document.addEventListener('touchmove', onTouchMove, supportsPassive ? {\n      passive: false\n    } : false);\n    if (!totalLockCount) {\n      document.body.classList.add(BODY_LOCK_CLASS);\n    }\n    totalLockCount++;\n  };\n  const unlock = () => {\n    if (totalLockCount) {\n      document.removeEventListener('touchstart', touch.start);\n      document.removeEventListener('touchmove', onTouchMove);\n      totalLockCount--;\n      if (!totalLockCount) {\n        document.body.classList.remove(BODY_LOCK_CLASS);\n      }\n    }\n  };\n  useEffect(() => {\n    if (shouldLock) {\n      lock();\n      return () => {\n        unlock();\n      };\n    }\n  }, [shouldLock]);\n}", "map": {"version": 3, "names": ["useTouch", "useEffect", "getScrollParent", "supportsPassive", "totalLockCount", "BODY_LOCK_CLASS", "getScrollableElement", "el", "current", "parentElement", "clientHeight", "scrollHeight", "useLockScroll", "rootRef", "shouldLock", "touch", "onTouchMove", "event", "move", "direction", "deltaY", "target", "scrollableParent", "document", "body", "documentElement", "preventDefault", "offsetHeight", "scrollTop", "height", "getBoundingClientRect", "status", "Math", "round", "isVertical", "parseInt", "cancelable", "lock", "addEventListener", "start", "passive", "classList", "add", "unlock", "removeEventListener", "remove"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/use-lock-scroll.js"], "sourcesContent": ["import { useTouch } from './use-touch';\nimport { useEffect } from 'react';\nimport { getScrollParent } from './get-scroll-parent';\nimport { supportsPassive } from './supports-passive';\nlet totalLockCount = 0;\nconst BODY_LOCK_CLASS = 'adm-overflow-hidden';\nfunction getScrollableElement(el) {\n  let current = el === null || el === void 0 ? void 0 : el.parentElement;\n  while (current) {\n    if (current.clientHeight < current.scrollHeight) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\n// 移植自vant：https://github.com/youzan/vant/blob/HEAD/src/composables/use-lock-scroll.ts\nexport function useLockScroll(rootRef, shouldLock) {\n  const touch = useTouch();\n  const onTouchMove = event => {\n    touch.move(event);\n    const direction = touch.deltaY.current > 0 ? '10' : '01';\n    const el = getScrollParent(event.target, rootRef.current);\n    if (!el) return;\n    // This has perf cost but we have to compatible with iOS 12\n    if (shouldLock === 'strict') {\n      const scrollableParent = getScrollableElement(event.target);\n      if (scrollableParent === document.body || scrollableParent === document.documentElement) {\n        event.preventDefault();\n        return;\n      }\n    }\n    const {\n      scrollHeight,\n      offsetHeight,\n      scrollTop\n    } = el;\n    const {\n      height\n    } = el.getBoundingClientRect();\n    let status = '11';\n    if (scrollTop === 0) {\n      status = offsetHeight >= scrollHeight ? '00' : '01';\n    } else if (scrollHeight <= Math.round(height + scrollTop)) {\n      status = '10';\n    }\n    if (status !== '11' && touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {\n      if (event.cancelable && supportsPassive) {\n        // https://github.com/ant-design/ant-design-mobile/issues/6282\n        event.preventDefault();\n      }\n    }\n  };\n  const lock = () => {\n    document.addEventListener('touchstart', touch.start);\n    document.addEventListener('touchmove', onTouchMove, supportsPassive ? {\n      passive: false\n    } : false);\n    if (!totalLockCount) {\n      document.body.classList.add(BODY_LOCK_CLASS);\n    }\n    totalLockCount++;\n  };\n  const unlock = () => {\n    if (totalLockCount) {\n      document.removeEventListener('touchstart', touch.start);\n      document.removeEventListener('touchmove', onTouchMove);\n      totalLockCount--;\n      if (!totalLockCount) {\n        document.body.classList.remove(BODY_LOCK_CLASS);\n      }\n    }\n  };\n  useEffect(() => {\n    if (shouldLock) {\n      lock();\n      return () => {\n        unlock();\n      };\n    }\n  }, [shouldLock]);\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,aAAa;AACtC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,IAAIC,cAAc,GAAG,CAAC;AACtB,MAAMC,eAAe,GAAG,qBAAqB;AAC7C,SAASC,oBAAoBA,CAACC,EAAE,EAAE;EAChC,IAAIC,OAAO,GAAGD,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,aAAa;EACtE,OAAOD,OAAO,EAAE;IACd,IAAIA,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY,EAAE;MAC/C,OAAOH,OAAO;IAChB;IACAA,OAAO,GAAGA,OAAO,CAACC,aAAa;EACjC;EACA,OAAO,IAAI;AACb;AACA;AACA,OAAO,SAASG,aAAaA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACjD,MAAMC,KAAK,GAAGf,QAAQ,CAAC,CAAC;EACxB,MAAMgB,WAAW,GAAGC,KAAK,IAAI;IAC3BF,KAAK,CAACG,IAAI,CAACD,KAAK,CAAC;IACjB,MAAME,SAAS,GAAGJ,KAAK,CAACK,MAAM,CAACZ,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IACxD,MAAMD,EAAE,GAAGL,eAAe,CAACe,KAAK,CAACI,MAAM,EAAER,OAAO,CAACL,OAAO,CAAC;IACzD,IAAI,CAACD,EAAE,EAAE;IACT;IACA,IAAIO,UAAU,KAAK,QAAQ,EAAE;MAC3B,MAAMQ,gBAAgB,GAAGhB,oBAAoB,CAACW,KAAK,CAACI,MAAM,CAAC;MAC3D,IAAIC,gBAAgB,KAAKC,QAAQ,CAACC,IAAI,IAAIF,gBAAgB,KAAKC,QAAQ,CAACE,eAAe,EAAE;QACvFR,KAAK,CAACS,cAAc,CAAC,CAAC;QACtB;MACF;IACF;IACA,MAAM;MACJf,YAAY;MACZgB,YAAY;MACZC;IACF,CAAC,GAAGrB,EAAE;IACN,MAAM;MACJsB;IACF,CAAC,GAAGtB,EAAE,CAACuB,qBAAqB,CAAC,CAAC;IAC9B,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIH,SAAS,KAAK,CAAC,EAAE;MACnBG,MAAM,GAAGJ,YAAY,IAAIhB,YAAY,GAAG,IAAI,GAAG,IAAI;IACrD,CAAC,MAAM,IAAIA,YAAY,IAAIqB,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAGD,SAAS,CAAC,EAAE;MACzDG,MAAM,GAAG,IAAI;IACf;IACA,IAAIA,MAAM,KAAK,IAAI,IAAIhB,KAAK,CAACmB,UAAU,CAAC,CAAC,IAAI,EAAEC,QAAQ,CAACJ,MAAM,EAAE,CAAC,CAAC,GAAGI,QAAQ,CAAChB,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;MAC5F,IAAIF,KAAK,CAACmB,UAAU,IAAIjC,eAAe,EAAE;QACvC;QACAc,KAAK,CAACS,cAAc,CAAC,CAAC;MACxB;IACF;EACF,CAAC;EACD,MAAMW,IAAI,GAAGA,CAAA,KAAM;IACjBd,QAAQ,CAACe,gBAAgB,CAAC,YAAY,EAAEvB,KAAK,CAACwB,KAAK,CAAC;IACpDhB,QAAQ,CAACe,gBAAgB,CAAC,WAAW,EAAEtB,WAAW,EAAEb,eAAe,GAAG;MACpEqC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK,CAAC;IACV,IAAI,CAACpC,cAAc,EAAE;MACnBmB,QAAQ,CAACC,IAAI,CAACiB,SAAS,CAACC,GAAG,CAACrC,eAAe,CAAC;IAC9C;IACAD,cAAc,EAAE;EAClB,CAAC;EACD,MAAMuC,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIvC,cAAc,EAAE;MAClBmB,QAAQ,CAACqB,mBAAmB,CAAC,YAAY,EAAE7B,KAAK,CAACwB,KAAK,CAAC;MACvDhB,QAAQ,CAACqB,mBAAmB,CAAC,WAAW,EAAE5B,WAAW,CAAC;MACtDZ,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,EAAE;QACnBmB,QAAQ,CAACC,IAAI,CAACiB,SAAS,CAACI,MAAM,CAACxC,eAAe,CAAC;MACjD;IACF;EACF,CAAC;EACDJ,SAAS,CAAC,MAAM;IACd,IAAIa,UAAU,EAAE;MACduB,IAAI,CAAC,CAAC;MACN,OAAO,MAAM;QACXM,MAAM,CAAC,CAAC;MACV,CAAC;IACH;EACF,CAAC,EAAE,CAAC7B,UAAU,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}