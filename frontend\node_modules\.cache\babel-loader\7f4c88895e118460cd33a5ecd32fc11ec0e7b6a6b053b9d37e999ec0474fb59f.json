{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\nvar useRefreshOnWindowFocusPlugin = function (fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n    _b = _a.focusTimespan,\n    focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef();\n  var stopSubscribe = function () {\n    var _a;\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\nexport default useRefreshOnWindowFocusPlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}