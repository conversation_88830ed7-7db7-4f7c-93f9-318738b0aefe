{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nexport function withNativeProps(props, element) {\n  const p = Object.assign({}, element.props);\n  if (props.className) {\n    p.className = classNames(element.props.className, props.className);\n  }\n  if (props.style) {\n    p.style = Object.assign(Object.assign({}, p.style), props.style);\n  }\n  if (props.tabIndex !== undefined) {\n    p.tabIndex = props.tabIndex;\n  }\n  for (const key in props) {\n    if (!props.hasOwnProperty(key)) continue;\n    if (key.startsWith('data-') || key.startsWith('aria-')) {\n      p[key] = props[key];\n    }\n  }\n  return React.cloneElement(element, p);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}