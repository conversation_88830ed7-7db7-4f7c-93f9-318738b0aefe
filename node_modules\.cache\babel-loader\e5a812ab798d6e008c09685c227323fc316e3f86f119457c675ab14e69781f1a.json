{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-steps`;\nconst stepClassPrefix = `adm-step`;\nconst defaultIcon = React.createElement(\"span\", {\n  className: `${stepClassPrefix}-icon-dot`\n});\nconst defaultProps = {\n  current: 0,\n  direction: 'horizontal'\n};\nexport const Steps = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    direction,\n    current\n  } = props;\n  const classString = classNames(classPrefix, `${classPrefix}-${direction}`);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classString\n  }, React.Children.map(props.children, (child, index) => {\n    var _a;\n    if (!React.isValidElement(child)) {\n      return child;\n    }\n    const childProps = child.props;\n    let status = childProps.status || 'wait';\n    if (index < current) {\n      status = childProps.status || 'finish';\n    } else if (index === current) {\n      status = childProps.status || 'process';\n    }\n    const icon = (_a = childProps.icon) !== null && _a !== void 0 ? _a : defaultIcon;\n    return React.cloneElement(child, {\n      status,\n      icon\n    });\n  })));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}