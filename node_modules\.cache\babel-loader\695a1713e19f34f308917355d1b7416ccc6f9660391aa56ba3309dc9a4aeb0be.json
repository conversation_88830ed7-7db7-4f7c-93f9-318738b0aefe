{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select`;\nconst defaultProps = {\n  options: [],\n  fieldNames: {},\n  defaultValue: []\n};\nexport const TreeSelect = p => {\n  const props = mergeProps(defaultProps, p);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  const onItemSelect = node => {\n    var _a;\n    // 找到父级节点\n    const parentNodes = [];\n    let current = node;\n    while (current) {\n      parentNodes.push(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const values = parentNodes.reverse().map(i => i[valueName]);\n    setValue(values);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, values, {\n      options: parentNodes\n    });\n  };\n  const renderItems = (columnOptions = [], index) => {\n    return columnOptions.map(item => {\n      const isActive = item[valueName] === value[index];\n      return React.createElement(\"div\", {\n        key: item[valueName],\n        className: classNames(`${classPrefix}-item`, {\n          [`${classPrefix}-item-active`]: isActive\n        }),\n        onClick: () => {\n          if (!isActive) {\n            onItemSelect(item);\n          }\n        }\n      }, item[labelName]);\n    });\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(value[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}