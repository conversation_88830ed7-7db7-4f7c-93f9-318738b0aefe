{"ast": null, "code": "export function mergeProps(...items) {\n  const ret = {};\n  items.forEach(item => {\n    if (item) {\n      Object.keys(item).forEach(key => {\n        if (item[key] !== undefined) {\n          ret[key] = item[key];\n        }\n      });\n    }\n  });\n  return ret;\n}\n/**\n * Merge props and return the first non-undefined value.\n * The later has higher priority. e.g. (10, 1, 5) => 5 wins.\n * This is useful with legacy props that have been deprecated.\n */\nexport function mergeProp(defaultProp, ...propList) {\n  for (let i = propList.length - 1; i >= 0; i -= 1) {\n    if (propList[i] !== undefined) {\n      return propList[i];\n    }\n  }\n  return defaultProp;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}