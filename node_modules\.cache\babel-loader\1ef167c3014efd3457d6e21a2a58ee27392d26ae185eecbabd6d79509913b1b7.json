{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { devWarning } from '../../utils/dev-log';\nimport { isDev } from '../../utils/is-dev';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { CheckIcon } from '../checkbox/check-icon';\nimport { NativeInput } from '../checkbox/native-input';\nimport { RadioGroupContext } from './group-context';\nconst classPrefix = `adm-radio`;\nconst defaultProps = {\n  defaultChecked: false\n};\nexport const Radio = p => {\n  const props = mergeProps(defaultProps, p);\n  const groupContext = useContext(RadioGroupContext);\n  let [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  let disabled = props.disabled;\n  const {\n    value\n  } = props;\n  if (groupContext && value !== undefined) {\n    if (isDev) {\n      if (p.checked !== undefined) {\n        devWarning('Radio', 'When used within `Radio.Group`, the `checked` prop of `Radio` will not work.');\n      }\n      if (p.defaultChecked !== undefined) {\n        devWarning('Radio', 'When used within `Radio.Group`, the `defaultChecked` prop of `Radio` will not work.');\n      }\n    }\n    checked = groupContext.value.includes(value);\n    setChecked = innerChecked => {\n      var _a;\n      if (innerChecked) {\n        groupContext.check(value);\n      } else {\n        groupContext.uncheck(value);\n      }\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, innerChecked);\n    };\n    disabled = disabled || groupContext.disabled;\n  }\n  const renderIcon = () => {\n    if (props.icon) {\n      return React.createElement(\"div\", {\n        className: `${classPrefix}-custom-icon`\n      }, props.icon(checked));\n    }\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-icon`\n    }, checked && React.createElement(CheckIcon, null));\n  };\n  return withNativeProps(props, React.createElement(\"label\", {\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-block`]: props.block\n    })\n  }, React.createElement(NativeInput, {\n    type: 'radio',\n    checked: checked,\n    onChange: setChecked,\n    disabled: disabled,\n    id: props.id\n  }), renderIcon(), props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n};", "map": {"version": 3, "names": ["classNames", "React", "useContext", "dev<PERSON><PERSON><PERSON>", "isDev", "withNativeProps", "usePropsValue", "mergeProps", "CheckIcon", "NativeInput", "RadioGroupContext", "classPrefix", "defaultProps", "defaultChecked", "Radio", "p", "props", "groupContext", "checked", "setChecked", "value", "defaultValue", "onChange", "disabled", "undefined", "includes", "innerChecked", "_a", "check", "uncheck", "call", "renderIcon", "icon", "createElement", "className", "onClick", "block", "type", "id", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/radio/radio.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { devWarning } from '../../utils/dev-log';\nimport { isDev } from '../../utils/is-dev';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { CheckIcon } from '../checkbox/check-icon';\nimport { NativeInput } from '../checkbox/native-input';\nimport { RadioGroupContext } from './group-context';\nconst classPrefix = `adm-radio`;\nconst defaultProps = {\n  defaultChecked: false\n};\nexport const Radio = p => {\n  const props = mergeProps(defaultProps, p);\n  const groupContext = useContext(RadioGroupContext);\n  let [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  let disabled = props.disabled;\n  const {\n    value\n  } = props;\n  if (groupContext && value !== undefined) {\n    if (isDev) {\n      if (p.checked !== undefined) {\n        devWarning('Radio', 'When used within `Radio.Group`, the `checked` prop of `Radio` will not work.');\n      }\n      if (p.defaultChecked !== undefined) {\n        devWarning('Radio', 'When used within `Radio.Group`, the `defaultChecked` prop of `Radio` will not work.');\n      }\n    }\n    checked = groupContext.value.includes(value);\n    setChecked = innerChecked => {\n      var _a;\n      if (innerChecked) {\n        groupContext.check(value);\n      } else {\n        groupContext.uncheck(value);\n      }\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, innerChecked);\n    };\n    disabled = disabled || groupContext.disabled;\n  }\n  const renderIcon = () => {\n    if (props.icon) {\n      return React.createElement(\"div\", {\n        className: `${classPrefix}-custom-icon`\n      }, props.icon(checked));\n    }\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-icon`\n    }, checked && React.createElement(CheckIcon, null));\n  };\n  return withNativeProps(props, React.createElement(\"label\", {\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-block`]: props.block\n    })\n  }, React.createElement(NativeInput, {\n    type: 'radio',\n    checked: checked,\n    onChange: setChecked,\n    disabled: disabled,\n    id: props.id\n  }), renderIcon(), props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGT,UAAU,CAACK,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAME,YAAY,GAAGf,UAAU,CAACQ,iBAAiB,CAAC;EAClD,IAAI,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGb,aAAa,CAAC;IACxCc,KAAK,EAAEJ,KAAK,CAACE,OAAO;IACpBG,YAAY,EAAEL,KAAK,CAACH,cAAc;IAClCS,QAAQ,EAAEN,KAAK,CAACM;EAClB,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;EAC7B,MAAM;IACJH;EACF,CAAC,GAAGJ,KAAK;EACT,IAAIC,YAAY,IAAIG,KAAK,KAAKI,SAAS,EAAE;IACvC,IAAIpB,KAAK,EAAE;MACT,IAAIW,CAAC,CAACG,OAAO,KAAKM,SAAS,EAAE;QAC3BrB,UAAU,CAAC,OAAO,EAAE,8EAA8E,CAAC;MACrG;MACA,IAAIY,CAAC,CAACF,cAAc,KAAKW,SAAS,EAAE;QAClCrB,UAAU,CAAC,OAAO,EAAE,qFAAqF,CAAC;MAC5G;IACF;IACAe,OAAO,GAAGD,YAAY,CAACG,KAAK,CAACK,QAAQ,CAACL,KAAK,CAAC;IAC5CD,UAAU,GAAGO,YAAY,IAAI;MAC3B,IAAIC,EAAE;MACN,IAAID,YAAY,EAAE;QAChBT,YAAY,CAACW,KAAK,CAACR,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLH,YAAY,CAACY,OAAO,CAACT,KAAK,CAAC;MAC7B;MACA,CAACO,EAAE,GAAGX,KAAK,CAACM,QAAQ,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACd,KAAK,EAAEU,YAAY,CAAC;IACzF,CAAC;IACDH,QAAQ,GAAGA,QAAQ,IAAIN,YAAY,CAACM,QAAQ;EAC9C;EACA,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIf,KAAK,CAACgB,IAAI,EAAE;MACd,OAAO/B,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;QAChCC,SAAS,EAAE,GAAGvB,WAAW;MAC3B,CAAC,EAAEK,KAAK,CAACgB,IAAI,CAACd,OAAO,CAAC,CAAC;IACzB;IACA,OAAOjB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE,GAAGvB,WAAW;IAC3B,CAAC,EAAEO,OAAO,IAAIjB,KAAK,CAACgC,aAAa,CAACzB,SAAS,EAAE,IAAI,CAAC,CAAC;EACrD,CAAC;EACD,OAAOH,eAAe,CAACW,KAAK,EAAEf,KAAK,CAACgC,aAAa,CAAC,OAAO,EAAE;IACzDE,OAAO,EAAEnB,KAAK,CAACmB,OAAO;IACtBD,SAAS,EAAElC,UAAU,CAACW,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,UAAU,GAAGO,OAAO;MACnC,CAAC,GAAGP,WAAW,WAAW,GAAGY,QAAQ;MACrC,CAAC,GAAGZ,WAAW,QAAQ,GAAGK,KAAK,CAACoB;IAClC,CAAC;EACH,CAAC,EAAEnC,KAAK,CAACgC,aAAa,CAACxB,WAAW,EAAE;IAClC4B,IAAI,EAAE,OAAO;IACbnB,OAAO,EAAEA,OAAO;IAChBI,QAAQ,EAAEH,UAAU;IACpBI,QAAQ,EAAEA,QAAQ;IAClBe,EAAE,EAAEtB,KAAK,CAACsB;EACZ,CAAC,CAAC,EAAEP,UAAU,CAAC,CAAC,EAAEf,KAAK,CAACuB,QAAQ,IAAItC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7DC,SAAS,EAAE,GAAGvB,WAAW;EAC3B,CAAC,EAAEK,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}