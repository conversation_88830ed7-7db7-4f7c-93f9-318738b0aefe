{"ast": null, "code": "import { bound } from './bound';\nexport function rubberband(distance, dimension, constant) {\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nexport function rubberbandIfOutOfBounds(position, min, max, dimension, constant = 0.15) {\n  if (constant === 0) return bound(position, min, max);\n  if (position < min) return -rubberband(min - position, dimension, constant) + min;\n  if (position > max) return +rubberband(position - max, dimension, constant) + max;\n  return position;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}