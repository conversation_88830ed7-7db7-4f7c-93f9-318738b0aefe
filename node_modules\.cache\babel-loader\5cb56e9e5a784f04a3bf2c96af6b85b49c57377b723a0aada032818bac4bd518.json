{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent } from 'rc-util';\nimport useState from \"rc-util/es/hooks/useState\";\nimport useSyncState from \"rc-util/es/hooks/useSyncState\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport { STATUS_APPEAR, STATUS_ENTER, STATUS_LEAVE, STATUS_NONE, STEP_ACTIVE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useDomMotionEvents from \"./useDomMotionEvents\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useStepQueue, { DoStep, isActive, SkipStep } from \"./useStepQueue\";\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = useSyncState(STATUS_NONE),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = useRef(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = useEvent(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case STATUS_APPEAR:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onAppearPrepare), STEP_START, onAppearStart), STEP_ACTIVE, onAppearActive);\n      case STATUS_ENTER:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onEnterPrepare), STEP_START, onEnterStart), STEP_ACTIVE, onEnterActive);\n      case STATUS_LEAVE:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onLeavePrepare), STEP_START, onLeaveStart), STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = React.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = useStepQueue(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE && currentStatus !== STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = useRef(null);\n\n  // Update with new status\n  useIsomorphicLayoutEffect(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  useEffect(function () {\n    if (\n    // Cancel appear\n    currentStatus === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = React.useRef(false);\n  useEffect(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_slicedToArray", "useEvent", "useState", "useSyncState", "React", "useEffect", "useRef", "STATUS_APPEAR", "STATUS_ENTER", "STATUS_LEAVE", "STATUS_NONE", "STEP_ACTIVE", "STEP_PREPARE", "STEP_PREPARED", "STEP_START", "useDomMotionEvents", "useIsomorphicLayoutEffect", "useStepQueue", "DoStep", "isActive", "SkipStep", "useStatus", "supportMotion", "visible", "getElement", "_ref", "_ref$motionEnter", "motionEnter", "_ref$motionAppear", "motionAppear", "_ref$motionLeave", "motionLeave", "motionDeadline", "motionLeaveImmediately", "onAppearPrepare", "onEnterPrepare", "onLeavePrepare", "onAppearStart", "onEnterStart", "onLeaveStart", "onAppearActive", "onEnterActive", "onLeaveActive", "onAppearEnd", "onEnterEnd", "onLeaveEnd", "onVisibleChanged", "_useState", "_useState2", "asyncVisible", "setAsyncVisible", "_useSyncState", "_useSyncState2", "getStatus", "setStatus", "_useState3", "_useState4", "style", "setStyle", "currentStatus", "mountedRef", "deadlineRef", "getDomElement", "activeRef", "updateMotionEndStatus", "onInternalMotionEnd", "event", "status", "element", "deadline", "target", "currentActive", "current", "canEnd", "_useDomMotionEvents", "_useDomMotionEvents2", "patchMotionEvents", "getEventHandlers", "targetStatus", "eventHandlers", "useMemo", "_useStepQueue", "newStep", "onPrepare", "step", "_eventHandlers$step", "call", "clearTimeout", "setTimeout", "_useStepQueue2", "startStep", "active", "visibleRef", "isMounted", "nextStatus", "nextEventHandlers", "firstMountChangeRef", "undefined", "mergedStyle", "transition"], "sources": ["C:/Users/<USER>/node_modules/rc-motion/es/hooks/useStatus.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEvent } from 'rc-util';\nimport useState from \"rc-util/es/hooks/useState\";\nimport useSyncState from \"rc-util/es/hooks/useSyncState\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport { STATUS_APPEAR, STATUS_ENTER, STATUS_LEAVE, STATUS_NONE, STEP_ACTIVE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useDomMotionEvents from \"./useDomMotionEvents\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useStepQueue, { DoStep, isActive, SkipStep } from \"./useStepQueue\";\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = useSyncState(STATUS_NONE),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = useRef(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = useEvent(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case STATUS_APPEAR:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onAppearPrepare), STEP_START, onAppearStart), STEP_ACTIVE, onAppearActive);\n      case STATUS_ENTER:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onEnterPrepare), STEP_START, onEnterStart), STEP_ACTIVE, onEnterActive);\n      case STATUS_LEAVE:\n        return _defineProperty(_defineProperty(_defineProperty({}, STEP_PREPARE, onLeavePrepare), STEP_START, onLeaveStart), STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = React.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = useStepQueue(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE && currentStatus !== STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  var visibleRef = useRef(null);\n\n  // Update with new status\n  useIsomorphicLayoutEffect(function () {\n    // When use Suspense, the `visible` will repeat trigger,\n    // But not real change of the `visible`, we need to skip it.\n    // https://github.com/ant-design/ant-design/issues/44379\n    if (mountedRef.current && visibleRef.current === visible) {\n      return;\n    }\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(STATUS_NONE);\n    }\n    visibleRef.current = visible;\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  useEffect(function () {\n    if (\n    // Cancel appear\n    currentStatus === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = React.useRef(false);\n  useEffect(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAEC,UAAU,QAAQ,cAAc;AAC3I,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,YAAY,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACzE,eAAe,SAASC,SAASA,CAACC,aAAa,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC1E,IAAIC,gBAAgB,GAAGD,IAAI,CAACE,WAAW;IACrCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,iBAAiB,GAAGH,IAAI,CAACI,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACtEE,gBAAgB,GAAGL,IAAI,CAACM,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,cAAc,GAAGP,IAAI,CAACO,cAAc;IACpCC,sBAAsB,GAAGR,IAAI,CAACQ,sBAAsB;IACpDC,eAAe,GAAGT,IAAI,CAACS,eAAe;IACtCC,cAAc,GAAGV,IAAI,CAACU,cAAc;IACpCC,cAAc,GAAGX,IAAI,CAACW,cAAc;IACpCC,aAAa,GAAGZ,IAAI,CAACY,aAAa;IAClCC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,YAAY,GAAGd,IAAI,CAACc,YAAY;IAChCC,cAAc,GAAGf,IAAI,CAACe,cAAc;IACpCC,aAAa,GAAGhB,IAAI,CAACgB,aAAa;IAClCC,aAAa,GAAGjB,IAAI,CAACiB,aAAa;IAClCC,WAAW,GAAGlB,IAAI,CAACkB,WAAW;IAC9BC,UAAU,GAAGnB,IAAI,CAACmB,UAAU;IAC5BC,UAAU,GAAGpB,IAAI,CAACoB,UAAU;IAC5BC,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB;EAC1C;EACA,IAAIC,SAAS,GAAG7C,QAAQ,CAAC,CAAC;IACxB8C,UAAU,GAAGhD,cAAc,CAAC+C,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,aAAa,GAAGhD,YAAY,CAACO,WAAW,CAAC;IAC3C0C,cAAc,GAAGpD,cAAc,CAACmD,aAAa,EAAE,CAAC,CAAC;IACjDE,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BE,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC;EAC/B,IAAIG,UAAU,GAAGrD,QAAQ,CAAC,IAAI,CAAC;IAC7BsD,UAAU,GAAGxD,cAAc,CAACuD,UAAU,EAAE,CAAC,CAAC;IAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIG,aAAa,GAAGN,SAAS,CAAC,CAAC;EAC/B,IAAIO,UAAU,GAAGtD,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAIuD,WAAW,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,SAASwD,aAAaA,CAAA,EAAG;IACvB,OAAOtC,UAAU,CAAC,CAAC;EACrB;;EAEA;EACA,IAAIuC,SAAS,GAAGzD,MAAM,CAAC,KAAK,CAAC;;EAE7B;AACF;AACA;EACE,SAAS0D,qBAAqBA,CAAA,EAAG;IAC/BV,SAAS,CAAC5C,WAAW,CAAC;IACtBgD,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACtB;EACA,IAAIO,mBAAmB,GAAGhE,QAAQ,CAAC,UAAUiE,KAAK,EAAE;IAClD,IAAIC,MAAM,GAAGd,SAAS,CAAC,CAAC;IACxB;IACA;IACA,IAAIc,MAAM,KAAKzD,WAAW,EAAE;MAC1B;IACF;IACA,IAAI0D,OAAO,GAAGN,aAAa,CAAC,CAAC;IAC7B,IAAII,KAAK,IAAI,CAACA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACI,MAAM,KAAKF,OAAO,EAAE;MACxD;MACA;MACA;MACA;IACF;IACA,IAAIG,aAAa,GAAGR,SAAS,CAACS,OAAO;IACrC,IAAIC,MAAM;IACV,IAAIN,MAAM,KAAK5D,aAAa,IAAIgE,aAAa,EAAE;MAC7CE,MAAM,GAAG9B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACyB,OAAO,EAAEF,KAAK,CAAC;IAChG,CAAC,MAAM,IAAIC,MAAM,KAAK3D,YAAY,IAAI+D,aAAa,EAAE;MACnDE,MAAM,GAAG7B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACwB,OAAO,EAAEF,KAAK,CAAC;IAC7F,CAAC,MAAM,IAAIC,MAAM,KAAK1D,YAAY,IAAI8D,aAAa,EAAE;MACnDE,MAAM,GAAG5B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACuB,OAAO,EAAEF,KAAK,CAAC;IAC7F;;IAEA;IACA,IAAIK,aAAa,IAAIE,MAAM,KAAK,KAAK,EAAE;MACrCT,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,CAAC;EACF,IAAIU,mBAAmB,GAAG3D,kBAAkB,CAACkD,mBAAmB,CAAC;IAC/DU,oBAAoB,GAAG3E,cAAc,CAAC0E,mBAAmB,EAAE,CAAC,CAAC;IAC7DE,iBAAiB,GAAGD,oBAAoB,CAAC,CAAC,CAAC;;EAE7C;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,QAAQA,YAAY;MAClB,KAAKvE,aAAa;QAChB,OAAOR,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEa,YAAY,EAAEsB,eAAe,CAAC,EAAEpB,UAAU,EAAEuB,aAAa,CAAC,EAAE1B,WAAW,EAAE6B,cAAc,CAAC;MACrJ,KAAKhC,YAAY;QACf,OAAOT,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEa,YAAY,EAAEuB,cAAc,CAAC,EAAErB,UAAU,EAAEwB,YAAY,CAAC,EAAE3B,WAAW,EAAE8B,aAAa,CAAC;MAClJ,KAAKhC,YAAY;QACf,OAAOV,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEa,YAAY,EAAEwB,cAAc,CAAC,EAAEtB,UAAU,EAAEyB,YAAY,CAAC,EAAE5B,WAAW,EAAE+B,aAAa,CAAC;MAClJ;QACE,OAAO,CAAC,CAAC;IACb;EACF,CAAC;EACD,IAAIqC,aAAa,GAAG3E,KAAK,CAAC4E,OAAO,CAAC,YAAY;IAC5C,OAAOH,gBAAgB,CAAClB,aAAa,CAAC;EACxC,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACnB,IAAIsB,aAAa,GAAGhE,YAAY,CAAC0C,aAAa,EAAE,CAACrC,aAAa,EAAE,UAAU4D,OAAO,EAAE;MAC/E;MACA,IAAIA,OAAO,KAAKtE,YAAY,EAAE;QAC5B,IAAIuE,SAAS,GAAGJ,aAAa,CAACnE,YAAY,CAAC;QAC3C,IAAI,CAACuE,SAAS,EAAE;UACd,OAAO/D,QAAQ;QACjB;QACA,OAAO+D,SAAS,CAACrB,aAAa,CAAC,CAAC,CAAC;MACnC;;MAEA;MACA,IAAIsB,IAAI,IAAIL,aAAa,EAAE;QACzB,IAAIM,mBAAmB;QACvB3B,QAAQ,CAAC,CAAC,CAAC2B,mBAAmB,GAAGN,aAAa,CAACK,IAAI,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,IAAI,CAACP,aAAa,EAAEjB,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;MACtL;MACA,IAAIsB,IAAI,KAAKzE,WAAW,IAAIgD,aAAa,KAAKjD,WAAW,EAAE;QACzD;QACAkE,iBAAiB,CAACd,aAAa,CAAC,CAAC,CAAC;QAClC,IAAI9B,cAAc,GAAG,CAAC,EAAE;UACtBuD,YAAY,CAAC1B,WAAW,CAACW,OAAO,CAAC;UACjCX,WAAW,CAACW,OAAO,GAAGgB,UAAU,CAAC,YAAY;YAC3CvB,mBAAmB,CAAC;cAClBI,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAC,EAAErC,cAAc,CAAC;QACpB;MACF;MACA,IAAIoD,IAAI,KAAKvE,aAAa,EAAE;QAC1BmD,qBAAqB,CAAC,CAAC;MACzB;MACA,OAAO9C,MAAM;IACf,CAAC,CAAC;IACFuE,cAAc,GAAGzF,cAAc,CAACiF,aAAa,EAAE,CAAC,CAAC;IACjDS,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BL,IAAI,GAAGK,cAAc,CAAC,CAAC,CAAC;EAC1B,IAAIE,MAAM,GAAGxE,QAAQ,CAACiE,IAAI,CAAC;EAC3BrB,SAAS,CAACS,OAAO,GAAGmB,MAAM;;EAE1B;EACA,IAAIC,UAAU,GAAGtF,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAU,yBAAyB,CAAC,YAAY;IACpC;IACA;IACA;IACA,IAAI4C,UAAU,CAACY,OAAO,IAAIoB,UAAU,CAACpB,OAAO,KAAKjD,OAAO,EAAE;MACxD;IACF;IACA2B,eAAe,CAAC3B,OAAO,CAAC;IACxB,IAAIsE,SAAS,GAAGjC,UAAU,CAACY,OAAO;IAClCZ,UAAU,CAACY,OAAO,GAAG,IAAI;;IAEzB;IACA;IACA;;IAEA,IAAIsB,UAAU;;IAEd;IACA,IAAI,CAACD,SAAS,IAAItE,OAAO,IAAIM,YAAY,EAAE;MACzCiE,UAAU,GAAGvF,aAAa;IAC5B;;IAEA;IACA,IAAIsF,SAAS,IAAItE,OAAO,IAAII,WAAW,EAAE;MACvCmE,UAAU,GAAGtF,YAAY;IAC3B;;IAEA;IACA,IAAIqF,SAAS,IAAI,CAACtE,OAAO,IAAIQ,WAAW,IAAI,CAAC8D,SAAS,IAAI5D,sBAAsB,IAAI,CAACV,OAAO,IAAIQ,WAAW,EAAE;MAC3G+D,UAAU,GAAGrF,YAAY;IAC3B;IACA,IAAIsF,iBAAiB,GAAGlB,gBAAgB,CAACiB,UAAU,CAAC;;IAEpD;IACA,IAAIA,UAAU,KAAKxE,aAAa,IAAIyE,iBAAiB,CAACnF,YAAY,CAAC,CAAC,EAAE;MACpE0C,SAAS,CAACwC,UAAU,CAAC;MACrBJ,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACL;MACApC,SAAS,CAAC5C,WAAW,CAAC;IACxB;IACAkF,UAAU,CAACpB,OAAO,GAAGjD,OAAO;EAC9B,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;;EAEb;EACA;EACAlB,SAAS,CAAC,YAAY;IACpB;IACA;IACAsD,aAAa,KAAKpD,aAAa,IAAI,CAACsB,YAAY;IAChD;IACA8B,aAAa,KAAKnD,YAAY,IAAI,CAACmB,WAAW;IAC9C;IACAgC,aAAa,KAAKlD,YAAY,IAAI,CAACsB,WAAW,EAAE;MAC9CuB,SAAS,CAAC5C,WAAW,CAAC;IACxB;EACF,CAAC,EAAE,CAACmB,YAAY,EAAEF,WAAW,EAAEI,WAAW,CAAC,CAAC;EAC5C1B,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBuD,UAAU,CAACY,OAAO,GAAG,KAAK;MAC1Be,YAAY,CAAC1B,WAAW,CAACW,OAAO,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIwB,mBAAmB,GAAG5F,KAAK,CAACE,MAAM,CAAC,KAAK,CAAC;EAC7CD,SAAS,CAAC,YAAY;IACpB;IACA,IAAI4C,YAAY,EAAE;MAChB+C,mBAAmB,CAACxB,OAAO,GAAG,IAAI;IACpC;IACA,IAAIvB,YAAY,KAAKgD,SAAS,IAAItC,aAAa,KAAKjD,WAAW,EAAE;MAC/D;MACA,IAAIsF,mBAAmB,CAACxB,OAAO,IAAIvB,YAAY,EAAE;QAC/CH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAACG,YAAY,CAAC;MAC5F;MACA+C,mBAAmB,CAACxB,OAAO,GAAG,IAAI;IACpC;EACF,CAAC,EAAE,CAACvB,YAAY,EAAEU,aAAa,CAAC,CAAC;;EAEjC;EACA,IAAIuC,WAAW,GAAGzC,KAAK;EACvB,IAAIsB,aAAa,CAACnE,YAAY,CAAC,IAAIwE,IAAI,KAAKtE,UAAU,EAAE;IACtDoF,WAAW,GAAGpG,aAAa,CAAC;MAC1BqG,UAAU,EAAE;IACd,CAAC,EAAED,WAAW,CAAC;EACjB;EACA,OAAO,CAACvC,aAAa,EAAEyB,IAAI,EAAEc,WAAW,EAAEjD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG1B,OAAO,CAAC;AACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}