{"ast": null, "code": "import * as React from \"react\";\nfunction DeleteOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DeleteOutline-DeleteOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"currentColor\",\n    fillRule: \"nonzero\",\n    id: \"DeleteOutline-\\u5F62\\u72B6\\u7ED3\\u5408\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M33,3 C34.6568542,3 36,4.34314575 36,6 L36,9 L42.6,9 C42.8209139,9 43,9.1790861 43,9.4 L43,11.6 C43,11.8209139 42.8209139,12 42.6,12 L41,12 L41,39 C41,42.3137085 38.3137085,45 35,45 L13,45 C9.6862915,45 7,42.3137085 7,39 L7,12 L5.4,12 C5.1790861,12 5,11.8209139 5,11.6 L5,9.4 C5,9.1790861 5.1790861,9 5.4,9 L12,9 L12,6 C12,4.34314575 13.3431458,3 15,3 L33,3 Z M38,12 L10,12 L10,39 C10,40.5976809 11.24892,41.9036609 12.8237272,41.9949073 L13,42 L35,42 C36.5976809,42 37.9036609,40.75108 37.9949073,39.1762728 L38,39 L38,12 Z M19.6,18 C19.8209139,18 20,18.1790861 20,18.4 L20,35.6 C20,35.8209139 19.8209139,36 19.6,36 L17.4,36 C17.1790861,36 17,35.8209139 17,35.6 L17,18.4 C17,18.1790861 17.1790861,18 17.4,18 L19.6,18 Z M31.6,18 C31.8209139,18 32,18.1790861 32,18.4 L32,35.6 C32,35.8209139 31.8209139,36 31.6,36 L29.4,36 C29.1790861,36 29,35.8209139 29,35.6 L29,18.4 C29,18.1790861 29.1790861,18 29.4,18 L31.6,18 Z M33,6 L15,6 L15,9 L33,9 L33,6 Z\"\n  }))));\n}\nexport default DeleteOutline;", "map": {"version": 3, "names": ["React", "DeleteOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/DeleteOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction DeleteOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"DeleteOutline-DeleteOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    fill: \"currentColor\",\n    fillRule: \"nonzero\",\n    id: \"DeleteOutline-\\u5F62\\u72B6\\u7ED3\\u5408\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M33,3 C34.6568542,3 36,4.34314575 36,6 L36,9 L42.6,9 C42.8209139,9 43,9.1790861 43,9.4 L43,11.6 C43,11.8209139 42.8209139,12 42.6,12 L41,12 L41,39 C41,42.3137085 38.3137085,45 35,45 L13,45 C9.6862915,45 7,42.3137085 7,39 L7,12 L5.4,12 C5.1790861,12 5,11.8209139 5,11.6 L5,9.4 C5,9.1790861 5.1790861,9 5.4,9 L12,9 L12,6 C12,4.34314575 13.3431458,3 15,3 L33,3 Z M38,12 L10,12 L10,39 C10,40.5976809 11.24892,41.9036609 12.8237272,41.9949073 L13,42 L35,42 C36.5976809,42 37.9036609,40.75108 37.9949073,39.1762728 L38,39 L38,12 Z M19.6,18 C19.8209139,18 20,18.1790861 20,18.4 L20,35.6 C20,35.8209139 19.8209139,36 19.6,36 L17.4,36 C17.1790861,36 17,35.8209139 17,35.6 L17,18.4 C17,18.1790861 17.1790861,18 17.4,18 L19.6,18 Z M31.6,18 C31.8209139,18 32,18.1790861 32,18.4 L32,35.6 C32,35.8209139 31.8209139,36 31.6,36 L29.4,36 C29.1790861,36 29,35.8209139 29,35.6 L29,18.4 C29,18.1790861 29.1790861,18 29.4,18 L31.6,18 Z M33,6 L15,6 L15,9 L33,9 L33,6 Z\"\n  }))));\n}\n\nexport default DeleteOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCiB,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE,SAAS;IACnBJ,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1CmB,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAerB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}