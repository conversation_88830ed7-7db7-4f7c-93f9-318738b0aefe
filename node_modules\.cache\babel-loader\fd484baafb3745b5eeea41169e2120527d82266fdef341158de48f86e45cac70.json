{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nexport let supportsPassive = false;\nif (canUseDom) {\n  try {\n    const opts = {};\n    Object.defineProperty(opts, 'passive', {\n      get() {\n        supportsPassive = true;\n      }\n    });\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}