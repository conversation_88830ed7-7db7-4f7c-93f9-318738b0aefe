{"ast": null, "code": "import { __rest } from \"tslib\";\nimport React, { forwardRef } from 'react';\nimport Picker from '../picker';\nimport { useColumnsFn } from './cascade-picker-utils';\nexport const CascadePicker = forwardRef((props, ref) => {\n  const {\n      options\n    } = props,\n    pickerProps = __rest(props, [\"options\"]);\n  const columnsFn = useColumnsFn(options);\n  return React.createElement(Picker, Object.assign({}, pickerProps, {\n    ref: ref,\n    columns: columnsFn\n  }));\n});", "map": {"version": 3, "names": ["__rest", "React", "forwardRef", "Picker", "useColumnsFn", "CascadePicker", "props", "ref", "options", "pickerProps", "columnsFn", "createElement", "Object", "assign", "columns"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascade-picker/cascade-picker.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport React, { forwardRef } from 'react';\nimport Picker from '../picker';\nimport { useColumnsFn } from './cascade-picker-utils';\nexport const CascadePicker = forwardRef((props, ref) => {\n  const {\n      options\n    } = props,\n    pickerProps = __rest(props, [\"options\"]);\n  const columnsFn = useColumnsFn(options);\n  return React.createElement(Picker, Object.assign({}, pickerProps, {\n    ref: ref,\n    columns: columnsFn\n  }));\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAO,MAAMC,aAAa,GAAGH,UAAU,CAAC,CAACI,KAAK,EAAEC,GAAG,KAAK;EACtD,MAAM;MACFC;IACF,CAAC,GAAGF,KAAK;IACTG,WAAW,GAAGT,MAAM,CAACM,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC;EAC1C,MAAMI,SAAS,GAAGN,YAAY,CAACI,OAAO,CAAC;EACvC,OAAOP,KAAK,CAACU,aAAa,CAACR,MAAM,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,WAAW,EAAE;IAChEF,GAAG,EAAEA,GAAG;IACRO,OAAO,EAAEJ;EACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}