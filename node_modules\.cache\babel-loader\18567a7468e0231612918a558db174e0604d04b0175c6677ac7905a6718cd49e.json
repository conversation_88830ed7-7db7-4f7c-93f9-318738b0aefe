{"ast": null, "code": "import { useRef } from 'react';\nvar useLoadingDelayPlugin = function (fetchInstance, _a) {\n  var loadingDelay = _a.loadingDelay,\n    ready = _a.ready;\n  var timerRef = useRef();\n  if (!loadingDelay) {\n    return {};\n  }\n  var cancelTimeout = function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  };\n  return {\n    onBefore: function () {\n      cancelTimeout();\n      // Two cases:\n      // 1. ready === undefined\n      // 2. ready === true\n      if (ready !== false) {\n        timerRef.current = setTimeout(function () {\n          fetchInstance.setState({\n            loading: true\n          });\n        }, loadingDelay);\n      }\n      return {\n        loading: false\n      };\n    },\n    onFinally: function () {\n      cancelTimeout();\n    },\n    onCancel: function () {\n      cancelTimeout();\n    }\n  };\n};\nexport default useLoadingDelayPlugin;", "map": {"version": 3, "names": ["useRef", "useLoadingDelayPlugin", "fetchInstance", "_a", "loadingDelay", "ready", "timerRef", "cancelTimeout", "current", "clearTimeout", "onBefore", "setTimeout", "setState", "loading", "onFinally", "onCancel"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nvar useLoadingDelayPlugin = function (fetchInstance, _a) {\n  var loadingDelay = _a.loadingDelay,\n    ready = _a.ready;\n  var timerRef = useRef();\n  if (!loadingDelay) {\n    return {};\n  }\n  var cancelTimeout = function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  };\n  return {\n    onBefore: function () {\n      cancelTimeout();\n      // Two cases:\n      // 1. ready === undefined\n      // 2. ready === true\n      if (ready !== false) {\n        timerRef.current = setTimeout(function () {\n          fetchInstance.setState({\n            loading: true\n          });\n        }, loadingDelay);\n      }\n      return {\n        loading: false\n      };\n    },\n    onFinally: function () {\n      cancelTimeout();\n    },\n    onCancel: function () {\n      cancelTimeout();\n    }\n  };\n};\nexport default useLoadingDelayPlugin;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,IAAIC,qBAAqB,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EACvD,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAY;IAChCC,KAAK,GAAGF,EAAE,CAACE,KAAK;EAClB,IAAIC,QAAQ,GAAGN,MAAM,CAAC,CAAC;EACvB,IAAI,CAACI,YAAY,EAAE;IACjB,OAAO,CAAC,CAAC;EACX;EACA,IAAIG,aAAa,GAAG,SAAAA,CAAA,EAAY;IAC9B,IAAID,QAAQ,CAACE,OAAO,EAAE;MACpBC,YAAY,CAACH,QAAQ,CAACE,OAAO,CAAC;IAChC;EACF,CAAC;EACD,OAAO;IACLE,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpBH,aAAa,CAAC,CAAC;MACf;MACA;MACA;MACA,IAAIF,KAAK,KAAK,KAAK,EAAE;QACnBC,QAAQ,CAACE,OAAO,GAAGG,UAAU,CAAC,YAAY;UACxCT,aAAa,CAACU,QAAQ,CAAC;YACrBC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,EAAET,YAAY,CAAC;MAClB;MACA,OAAO;QACLS,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDC,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrBP,aAAa,CAAC,CAAC;IACjB,CAAC;IACDQ,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpBR,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;AACH,CAAC;AACD,eAAeN,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}