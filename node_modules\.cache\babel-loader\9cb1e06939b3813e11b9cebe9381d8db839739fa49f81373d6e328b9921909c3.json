{"ast": null, "code": "import * as React from \"react\";\nfunction LocationFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationFill-LocationFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LocationFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.9999945,4 C33.3887345,4 41.0000089,11.6247063 41.0000089,21.0303329 C41.0000089,25.2341017 39.4794425,29.0826239 36.957775,32.0530873 L36.6649973,32.394989 C34.72133,34.628303 31.1494252,38.2349345 25.9483974,43.2148881 L25.9483974,43.2148881 C24.9089533,44.2116333 23.2940812,44.2656685 22.1913962,43.3406012 L22.0516185,43.2148881 L20.4337846,41.6587107 C15.8485201,37.2301805 12.7176858,34.0292729 11.0431852,32.0550056 L11.0431858,32.0550062 C8.43046647,28.9864902 6.9958785,25.0747588 7.00000889,21.0303323 C7.00000889,11.6247057 14.6112833,4 24.0000233,4 L23.9999945,4 Z M23.9999945,14.5065544 C20.3487696,14.5065544 17.388896,17.5008122 17.388896,21.1922064 C17.388896,24.8855068 20.3487873,27.8787958 23.9999945,27.8787958 C27.6512194,27.8787958 30.611093,24.8854889 30.611093,21.1931439 C30.611093,17.5007988 27.6512017,14.5074919 23.9999945,14.5074919 L23.9999945,14.5065544 Z\",\n    id: \"LocationFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default LocationFill;", "map": {"version": 3, "names": ["React", "LocationFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/LocationFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction LocationFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationFill-LocationFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"LocationFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"LocationFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23.9999945,4 C33.3887345,4 41.0000089,11.6247063 41.0000089,21.0303329 C41.0000089,25.2341017 39.4794425,29.0826239 36.957775,32.0530873 L36.6649973,32.394989 C34.72133,34.628303 31.1494252,38.2349345 25.9483974,43.2148881 L25.9483974,43.2148881 C24.9089533,44.2116333 23.2940812,44.2656685 22.1913962,43.3406012 L22.0516185,43.2148881 L20.4337846,41.6587107 C15.8485201,37.2301805 12.7176858,34.0292729 11.0431852,32.0550056 L11.0431858,32.0550062 C8.43046647,28.9864902 6.9958785,25.0747588 7.00000889,21.0303323 C7.00000889,11.6247057 14.6112833,4 24.0000233,4 L23.9999945,4 Z M23.9999945,14.5065544 C20.3487696,14.5065544 17.388896,17.5008122 17.388896,21.1922064 C17.388896,24.8855068 20.3487873,27.8787958 23.9999945,27.8787958 C27.6512194,27.8787958 30.611093,24.8854889 30.611093,21.1931439 C30.611093,17.5007988 27.6512017,14.5074919 23.9999945,14.5074919 L23.9999945,14.5065544 Z\",\n    id: \"LocationFill-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default LocationFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,43BAA43B;IAC/3BR,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}