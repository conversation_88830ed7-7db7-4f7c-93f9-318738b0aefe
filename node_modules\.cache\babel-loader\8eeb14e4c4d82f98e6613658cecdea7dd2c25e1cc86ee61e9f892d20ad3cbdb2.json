{"ast": null, "code": "import { useIsomorphicLayoutEffect } from 'ahooks';\nexport default function useInputHandleKeyDown({\n  onEnterPress,\n  onKeyDown,\n  nativeInputRef,\n  enterKeyHint\n}) {\n  const handleKeydown = e => {\n    if (onEnterPress && (e.code === 'Enter' || e.keyCode === 13)) {\n      onEnterPress(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  useIsomorphicLayoutEffect(() => {\n    const ele = nativeInputRef.current;\n    if (!enterKeyHint || !ele) return;\n    ele.setAttribute('enterkeyhint', enterKeyHint);\n    return () => {\n      ele.removeAttribute('enterkeyhint');\n    };\n  }, [enterKeyHint]);\n  return handleKeydown;\n}", "map": {"version": 3, "names": ["useIsomorphicLayoutEffect", "useInputHandleKeyDown", "onEnterPress", "onKeyDown", "nativeInputRef", "enterKeyHint", "handleKeydown", "e", "code", "keyCode", "ele", "current", "setAttribute", "removeAttribute"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/input/useInputHandleKeyDown.js"], "sourcesContent": ["import { useIsomorphicLayoutEffect } from 'ahooks';\nexport default function useInputHandleKeyDown({\n  onEnterPress,\n  onKeyDown,\n  nativeInputRef,\n  enterKeyHint\n}) {\n  const handleKeydown = e => {\n    if (onEnterPress && (e.code === 'Enter' || e.keyCode === 13)) {\n      onEnterPress(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  useIsomorphicLayoutEffect(() => {\n    const ele = nativeInputRef.current;\n    if (!enterKeyHint || !ele) return;\n    ele.setAttribute('enterkeyhint', enterKeyHint);\n    return () => {\n      ele.removeAttribute('enterkeyhint');\n    };\n  }, [enterKeyHint]);\n  return handleKeydown;\n}"], "mappings": "AAAA,SAASA,yBAAyB,QAAQ,QAAQ;AAClD,eAAe,SAASC,qBAAqBA,CAAC;EAC5CC,YAAY;EACZC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,EAAE;EACD,MAAMC,aAAa,GAAGC,CAAC,IAAI;IACzB,IAAIL,YAAY,KAAKK,CAAC,CAACC,IAAI,KAAK,OAAO,IAAID,CAAC,CAACE,OAAO,KAAK,EAAE,CAAC,EAAE;MAC5DP,YAAY,CAACK,CAAC,CAAC;IACjB;IACAJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,CAAC,CAAC;EACpE,CAAC;EACDP,yBAAyB,CAAC,MAAM;IAC9B,MAAMU,GAAG,GAAGN,cAAc,CAACO,OAAO;IAClC,IAAI,CAACN,YAAY,IAAI,CAACK,GAAG,EAAE;IAC3BA,GAAG,CAACE,YAAY,CAAC,cAAc,EAAEP,YAAY,CAAC;IAC9C,OAAO,MAAM;MACXK,GAAG,CAACG,eAAe,CAAC,cAAc,CAAC;IACrC,CAAC;EACH,CAAC,EAAE,CAACR,YAAY,CAAC,CAAC;EAClB,OAAOC,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}