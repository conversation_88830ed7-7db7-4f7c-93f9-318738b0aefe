{"ast": null, "code": "import React, { useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { animated, useSpring } from '@react-spring/web';\nimport { useThrottleFn } from 'ahooks';\nconst classPrefix = `adm-scroll-mask`;\nexport const ScrollMask = props => {\n  const maskRef = useRef(null);\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, api] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const mask = maskRef.current;\n    if (!mask) return;\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    const scrollLeft = scrollEl.scrollLeft;\n    const showLeftMask = scrollLeft > 0;\n    const showRightMask = scrollLeft + scrollEl.offsetWidth < scrollEl.scrollWidth;\n    api.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useEffect(() => {\n    updateMask(true);\n  }, []);\n  useEffect(() => {\n    const scrollEl = props.scrollTrackRef.current;\n    if (!scrollEl) return;\n    scrollEl.addEventListener('scroll', updateMask);\n    return () => scrollEl.removeEventListener('scroll', updateMask);\n  }, []);\n  return React.createElement(React.Fragment, null, React.createElement(animated.div, {\n    ref: maskRef,\n    className: classNames(classPrefix, `${classPrefix}-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(classPrefix, `${classPrefix}-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}