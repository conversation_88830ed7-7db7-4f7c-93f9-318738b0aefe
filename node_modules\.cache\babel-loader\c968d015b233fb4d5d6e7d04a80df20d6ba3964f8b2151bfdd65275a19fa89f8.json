{"ast": null, "code": "var getScrollTop = function (el) {\n  if (el === document || el === document.documentElement || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n  return el.scrollTop;\n};\nvar getScrollHeight = function (el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\nvar getClientHeight = function (el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\nexport { getScrollTop, getScrollHeight, getClientHeight };", "map": {"version": 3, "names": ["getScrollTop", "el", "document", "documentElement", "body", "Math", "max", "window", "pageYOffset", "scrollTop", "getScrollHeight", "scrollHeight", "getClientHeight", "clientHeight"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/rect.js"], "sourcesContent": ["var getScrollTop = function (el) {\n  if (el === document || el === document.documentElement || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n  return el.scrollTop;\n};\nvar getScrollHeight = function (el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\nvar getClientHeight = function (el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\nexport { getScrollTop, getScrollHeight, getClientHeight };"], "mappings": "AAAA,IAAIA,YAAY,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC/B,IAAIA,EAAE,KAAKC,QAAQ,IAAID,EAAE,KAAKC,QAAQ,CAACC,eAAe,IAAIF,EAAE,KAAKC,QAAQ,CAACE,IAAI,EAAE;IAC9E,OAAOC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,WAAW,EAAEN,QAAQ,CAACC,eAAe,CAACM,SAAS,EAAEP,QAAQ,CAACE,IAAI,CAACK,SAAS,CAAC;EAClG;EACA,OAAOR,EAAE,CAACQ,SAAS;AACrB,CAAC;AACD,IAAIC,eAAe,GAAG,SAAAA,CAAUT,EAAE,EAAE;EAClC,OAAOA,EAAE,CAACU,YAAY,IAAIN,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACC,eAAe,CAACQ,YAAY,EAAET,QAAQ,CAACE,IAAI,CAACO,YAAY,CAAC;AACvG,CAAC;AACD,IAAIC,eAAe,GAAG,SAAAA,CAAUX,EAAE,EAAE;EAClC,OAAOA,EAAE,CAACY,YAAY,IAAIR,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACC,eAAe,CAACU,YAAY,EAAEX,QAAQ,CAACE,IAAI,CAACS,YAAY,CAAC;AACvG,CAAC;AACD,SAASb,YAAY,EAAEU,eAAe,EAAEE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}