{"ast": null, "code": "import \"./index.css\";\nimport { Form } from './form';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { FormItem } from './form-item';\nimport { Header } from './header';\nimport { useWatch, useForm } from 'rc-field-form';\nimport { FormSubscribe } from './form-subscribe';\nimport { FormArray } from './form-array';\nexport default attachPropertiesToComponent(Form, {\n  Item: FormItem,\n  Subscribe: FormSubscribe,\n  Header,\n  Array: FormArray,\n  useForm,\n  useWatch\n});", "map": {"version": 3, "names": ["Form", "attachPropertiesToComponent", "FormItem", "Header", "useWatch", "useForm", "FormSubscribe", "FormArray", "<PERSON><PERSON>", "Subscribe", "Array"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/form/index.js"], "sourcesContent": ["import \"./index.css\";\nimport { Form } from './form';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { FormItem } from './form-item';\nimport { Header } from './header';\nimport { useWatch, useForm } from 'rc-field-form';\nimport { FormSubscribe } from './form-subscribe';\nimport { FormArray } from './form-array';\nexport default attachPropertiesToComponent(Form, {\n  Item: FormItem,\n  Subscribe: FormSubscribe,\n  Header,\n  Array: FormArray,\n  useForm,\n  useWatch\n});"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,eAAe;AACjD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,eAAeN,2BAA2B,CAACD,IAAI,EAAE;EAC/CQ,IAAI,EAAEN,QAAQ;EACdO,SAAS,EAAEH,aAAa;EACxBH,MAAM;EACNO,KAAK,EAAEH,SAAS;EAChBF,OAAO;EACPD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}