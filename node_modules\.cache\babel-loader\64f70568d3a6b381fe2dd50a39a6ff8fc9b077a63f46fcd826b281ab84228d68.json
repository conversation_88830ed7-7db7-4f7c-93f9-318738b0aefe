{"ast": null, "code": "import \"./input.css\";\nimport { Input } from './input';\nexport default Input;", "map": {"version": 3, "names": ["Input"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/input/index.js"], "sourcesContent": ["import \"./input.css\";\nimport { Input } from './input';\nexport default Input;"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,KAAK,QAAQ,SAAS;AAC/B,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}