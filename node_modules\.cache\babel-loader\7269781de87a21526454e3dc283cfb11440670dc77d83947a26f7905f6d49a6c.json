{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { mergeProps } from '../../utils/with-default-props';\nimport React, { useEffect, useRef, useState } from 'react';\nimport { useLockFn, useThrottleFn } from 'ahooks';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getScrollParent } from '../../utils/get-scroll-parent';\nimport { useConfig } from '../config-provider';\nimport DotLoading from '../dot-loading';\nfunction isWindow(element) {\n  return element === window;\n}\nconst classPrefix = `adm-infinite-scroll`;\nconst defaultProps = {\n  threshold: 250,\n  children: (hasMore, failed, retry) => React.createElement(InfiniteScrollContent, {\n    hasMore: hasMore,\n    failed: failed,\n    retry: retry\n  })\n};\nexport const InfiniteScroll = p => {\n  const props = mergeProps(defaultProps, p);\n  const [failed, setFailed] = useState(false);\n  const doLoadMore = useLockFn(isRetry => __awaiter(void 0, void 0, void 0, function* () {\n    try {\n      yield props.loadMore(isRetry);\n    } catch (e) {\n      setFailed(true);\n      throw e;\n    }\n  }));\n  const elementRef = useRef(null);\n  // Prevent duplicated trigger of `check` function\n  const [flag, setFlag] = useState({});\n  const nextFlagRef = useRef(flag);\n  const [scrollParent, setScrollParent] = useState();\n  const {\n    run: check\n  } = useThrottleFn(() => __awaiter(void 0, void 0, void 0, function* () {\n    if (nextFlagRef.current !== flag) return;\n    if (!props.hasMore) return;\n    const element = elementRef.current;\n    if (!element) return;\n    if (!element.offsetParent) return;\n    const parent = getScrollParent(element);\n    setScrollParent(parent);\n    if (!parent) return;\n    const rect = element.getBoundingClientRect();\n    const elementTop = rect.top;\n    const current = isWindow(parent) ? window.innerHeight : parent.getBoundingClientRect().bottom;\n    if (current >= elementTop - props.threshold) {\n      const nextFlag = {};\n      nextFlagRef.current = nextFlag;\n      try {\n        yield doLoadMore(false);\n        setFlag(nextFlag);\n      } catch (e) {}\n    }\n  }), {\n    wait: 100,\n    leading: true,\n    trailing: true\n  });\n  // Make sure to trigger `loadMore` when content changes\n  useEffect(() => {\n    check();\n  });\n  useEffect(() => {\n    const element = elementRef.current;\n    if (!element) return;\n    if (!scrollParent) return;\n    function onScroll() {\n      check();\n    }\n    scrollParent.addEventListener('scroll', onScroll);\n    return () => {\n      scrollParent.removeEventListener('scroll', onScroll);\n    };\n  }, [scrollParent]);\n  function retry() {\n    return __awaiter(this, void 0, void 0, function* () {\n      setFailed(false);\n      try {\n        yield doLoadMore(true);\n        setFlag(nextFlagRef.current);\n      } catch (e) {}\n    });\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: elementRef\n  }, typeof props.children === 'function' ? props.children(props.hasMore, failed, retry) : props.children));\n};\nconst InfiniteScrollContent = props => {\n  const {\n    locale\n  } = useConfig();\n  if (!props.hasMore) {\n    return React.createElement(\"span\", null, locale.InfiniteScroll.noMore);\n  }\n  if (props.failed) {\n    return React.createElement(\"span\", null, React.createElement(\"span\", {\n      className: `${classPrefix}-failed-text`\n    }, locale.InfiniteScroll.failedToLoad), React.createElement(\"a\", {\n      onClick: () => {\n        props.retry();\n      }\n    }, locale.InfiniteScroll.retry));\n  }\n  return React.createElement(React.Fragment, null, React.createElement(\"span\", null, locale.common.loading), React.createElement(DotLoading, null));\n};", "map": {"version": 3, "names": ["__awaiter", "mergeProps", "React", "useEffect", "useRef", "useState", "useLockFn", "useThrottleFn", "withNativeProps", "getScrollParent", "useConfig", "DotLoading", "isWindow", "element", "window", "classPrefix", "defaultProps", "threshold", "children", "hasMore", "failed", "retry", "createElement", "InfiniteScrollContent", "InfiniteScroll", "p", "props", "setFailed", "doLoadMore", "isRetry", "loadMore", "e", "elementRef", "flag", "setFlag", "nextFlagRef", "scrollParent", "setScrollParent", "run", "check", "current", "offsetParent", "parent", "rect", "getBoundingClientRect", "elementTop", "top", "innerHeight", "bottom", "nextFlag", "wait", "leading", "trailing", "onScroll", "addEventListener", "removeEventListener", "className", "ref", "locale", "noMore", "failedToLoad", "onClick", "Fragment", "common", "loading"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/infinite-scroll/infinite-scroll.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport { mergeProps } from '../../utils/with-default-props';\nimport React, { useEffect, useRef, useState } from 'react';\nimport { useLockFn, useThrottleFn } from 'ahooks';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getScrollParent } from '../../utils/get-scroll-parent';\nimport { useConfig } from '../config-provider';\nimport DotLoading from '../dot-loading';\nfunction isWindow(element) {\n  return element === window;\n}\nconst classPrefix = `adm-infinite-scroll`;\nconst defaultProps = {\n  threshold: 250,\n  children: (hasMore, failed, retry) => React.createElement(InfiniteScrollContent, {\n    hasMore: hasMore,\n    failed: failed,\n    retry: retry\n  })\n};\nexport const InfiniteScroll = p => {\n  const props = mergeProps(defaultProps, p);\n  const [failed, setFailed] = useState(false);\n  const doLoadMore = useLockFn(isRetry => __awaiter(void 0, void 0, void 0, function* () {\n    try {\n      yield props.loadMore(isRetry);\n    } catch (e) {\n      setFailed(true);\n      throw e;\n    }\n  }));\n  const elementRef = useRef(null);\n  // Prevent duplicated trigger of `check` function\n  const [flag, setFlag] = useState({});\n  const nextFlagRef = useRef(flag);\n  const [scrollParent, setScrollParent] = useState();\n  const {\n    run: check\n  } = useThrottleFn(() => __awaiter(void 0, void 0, void 0, function* () {\n    if (nextFlagRef.current !== flag) return;\n    if (!props.hasMore) return;\n    const element = elementRef.current;\n    if (!element) return;\n    if (!element.offsetParent) return;\n    const parent = getScrollParent(element);\n    setScrollParent(parent);\n    if (!parent) return;\n    const rect = element.getBoundingClientRect();\n    const elementTop = rect.top;\n    const current = isWindow(parent) ? window.innerHeight : parent.getBoundingClientRect().bottom;\n    if (current >= elementTop - props.threshold) {\n      const nextFlag = {};\n      nextFlagRef.current = nextFlag;\n      try {\n        yield doLoadMore(false);\n        setFlag(nextFlag);\n      } catch (e) {}\n    }\n  }), {\n    wait: 100,\n    leading: true,\n    trailing: true\n  });\n  // Make sure to trigger `loadMore` when content changes\n  useEffect(() => {\n    check();\n  });\n  useEffect(() => {\n    const element = elementRef.current;\n    if (!element) return;\n    if (!scrollParent) return;\n    function onScroll() {\n      check();\n    }\n    scrollParent.addEventListener('scroll', onScroll);\n    return () => {\n      scrollParent.removeEventListener('scroll', onScroll);\n    };\n  }, [scrollParent]);\n  function retry() {\n    return __awaiter(this, void 0, void 0, function* () {\n      setFailed(false);\n      try {\n        yield doLoadMore(true);\n        setFlag(nextFlagRef.current);\n      } catch (e) {}\n    });\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    ref: elementRef\n  }, typeof props.children === 'function' ? props.children(props.hasMore, failed, retry) : props.children));\n};\nconst InfiniteScrollContent = props => {\n  const {\n    locale\n  } = useConfig();\n  if (!props.hasMore) {\n    return React.createElement(\"span\", null, locale.InfiniteScroll.noMore);\n  }\n  if (props.failed) {\n    return React.createElement(\"span\", null, React.createElement(\"span\", {\n      className: `${classPrefix}-failed-text`\n    }, locale.InfiniteScroll.failedToLoad), React.createElement(\"a\", {\n      onClick: () => {\n        props.retry();\n      }\n    }, locale.InfiniteScroll.retry));\n  }\n  return React.createElement(React.Fragment, null, React.createElement(\"span\", null, locale.common.loading), React.createElement(DotLoading, null));\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,aAAa,QAAQ,QAAQ;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,QAAQA,CAACC,OAAO,EAAE;EACzB,OAAOA,OAAO,KAAKC,MAAM;AAC3B;AACA,MAAMC,WAAW,GAAG,qBAAqB;AACzC,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAEA,CAACC,OAAO,EAAEC,MAAM,EAAEC,KAAK,KAAKnB,KAAK,CAACoB,aAAa,CAACC,qBAAqB,EAAE;IAC/EJ,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA;EACT,CAAC;AACH,CAAC;AACD,OAAO,MAAMG,cAAc,GAAGC,CAAC,IAAI;EACjC,MAAMC,KAAK,GAAGzB,UAAU,CAACe,YAAY,EAAES,CAAC,CAAC;EACzC,MAAM,CAACL,MAAM,EAAEO,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMuB,UAAU,GAAGtB,SAAS,CAACuB,OAAO,IAAI7B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IACrF,IAAI;MACF,MAAM0B,KAAK,CAACI,QAAQ,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,OAAOE,CAAC,EAAE;MACVJ,SAAS,CAAC,IAAI,CAAC;MACf,MAAMI,CAAC;IACT;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,UAAU,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA,MAAM,CAAC6B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM8B,WAAW,GAAG/B,MAAM,CAAC6B,IAAI,CAAC;EAChC,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EAClD,MAAM;IACJiC,GAAG,EAAEC;EACP,CAAC,GAAGhC,aAAa,CAAC,MAAMP,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IACrE,IAAImC,WAAW,CAACK,OAAO,KAAKP,IAAI,EAAE;IAClC,IAAI,CAACP,KAAK,CAACP,OAAO,EAAE;IACpB,MAAMN,OAAO,GAAGmB,UAAU,CAACQ,OAAO;IAClC,IAAI,CAAC3B,OAAO,EAAE;IACd,IAAI,CAACA,OAAO,CAAC4B,YAAY,EAAE;IAC3B,MAAMC,MAAM,GAAGjC,eAAe,CAACI,OAAO,CAAC;IACvCwB,eAAe,CAACK,MAAM,CAAC;IACvB,IAAI,CAACA,MAAM,EAAE;IACb,MAAMC,IAAI,GAAG9B,OAAO,CAAC+B,qBAAqB,CAAC,CAAC;IAC5C,MAAMC,UAAU,GAAGF,IAAI,CAACG,GAAG;IAC3B,MAAMN,OAAO,GAAG5B,QAAQ,CAAC8B,MAAM,CAAC,GAAG5B,MAAM,CAACiC,WAAW,GAAGL,MAAM,CAACE,qBAAqB,CAAC,CAAC,CAACI,MAAM;IAC7F,IAAIR,OAAO,IAAIK,UAAU,GAAGnB,KAAK,CAACT,SAAS,EAAE;MAC3C,MAAMgC,QAAQ,GAAG,CAAC,CAAC;MACnBd,WAAW,CAACK,OAAO,GAAGS,QAAQ;MAC9B,IAAI;QACF,MAAMrB,UAAU,CAAC,KAAK,CAAC;QACvBM,OAAO,CAACe,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOlB,CAAC,EAAE,CAAC;IACf;EACF,CAAC,CAAC,EAAE;IACFmB,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF;EACAjD,SAAS,CAAC,MAAM;IACdoC,KAAK,CAAC,CAAC;EACT,CAAC,CAAC;EACFpC,SAAS,CAAC,MAAM;IACd,MAAMU,OAAO,GAAGmB,UAAU,CAACQ,OAAO;IAClC,IAAI,CAAC3B,OAAO,EAAE;IACd,IAAI,CAACuB,YAAY,EAAE;IACnB,SAASiB,QAAQA,CAAA,EAAG;MAClBd,KAAK,CAAC,CAAC;IACT;IACAH,YAAY,CAACkB,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;IACjD,OAAO,MAAM;MACXjB,YAAY,CAACmB,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;EAClB,SAASf,KAAKA,CAAA,EAAG;IACf,OAAOrB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClD2B,SAAS,CAAC,KAAK,CAAC;MAChB,IAAI;QACF,MAAMC,UAAU,CAAC,IAAI,CAAC;QACtBM,OAAO,CAACC,WAAW,CAACK,OAAO,CAAC;MAC9B,CAAC,CAAC,OAAOT,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;EACJ;EACA,OAAOvB,eAAe,CAACkB,KAAK,EAAExB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACvDkC,SAAS,EAAEzC,WAAW;IACtB0C,GAAG,EAAEzB;EACP,CAAC,EAAE,OAAON,KAAK,CAACR,QAAQ,KAAK,UAAU,GAAGQ,KAAK,CAACR,QAAQ,CAACQ,KAAK,CAACP,OAAO,EAAEC,MAAM,EAAEC,KAAK,CAAC,GAAGK,KAAK,CAACR,QAAQ,CAAC,CAAC;AAC3G,CAAC;AACD,MAAMK,qBAAqB,GAAGG,KAAK,IAAI;EACrC,MAAM;IACJgC;EACF,CAAC,GAAGhD,SAAS,CAAC,CAAC;EACf,IAAI,CAACgB,KAAK,CAACP,OAAO,EAAE;IAClB,OAAOjB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEoC,MAAM,CAAClC,cAAc,CAACmC,MAAM,CAAC;EACxE;EACA,IAAIjC,KAAK,CAACN,MAAM,EAAE;IAChB,OAAOlB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEpB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;MACnEkC,SAAS,EAAE,GAAGzC,WAAW;IAC3B,CAAC,EAAE2C,MAAM,CAAClC,cAAc,CAACoC,YAAY,CAAC,EAAE1D,KAAK,CAACoB,aAAa,CAAC,GAAG,EAAE;MAC/DuC,OAAO,EAAEA,CAAA,KAAM;QACbnC,KAAK,CAACL,KAAK,CAAC,CAAC;MACf;IACF,CAAC,EAAEqC,MAAM,CAAClC,cAAc,CAACH,KAAK,CAAC,CAAC;EAClC;EACA,OAAOnB,KAAK,CAACoB,aAAa,CAACpB,KAAK,CAAC4D,QAAQ,EAAE,IAAI,EAAE5D,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEoC,MAAM,CAACK,MAAM,CAACC,OAAO,CAAC,EAAE9D,KAAK,CAACoB,aAAa,CAACX,UAAU,EAAE,IAAI,CAAC,CAAC;AACnJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}