{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nexport function isDef(val) {\n  return val !== undefined && val !== null;\n}\nexport function isObject(val) {\n  return val !== null && typeof val === 'object';\n}\nexport function isPromise(obj) {\n  return !!obj && typeof obj === 'object' && typeof obj.then === 'function';\n}\nexport function isDate(val) {\n  return Object.prototype.toString.call(val) === '[object Date]' && !Number.isNaN(val.getTime());\n}\nexport function isMobile(value) {\n  value = value.replace(/[^-|\\d]/g, '');\n  return /^((\\+86)|(86))?(1)\\d{10}$/.test(value) || /^0[0-9-]{10,13}$/.test(value);\n}\nexport function isNumeric(val) {\n  return typeof val === 'number' || /^\\d+(\\.\\d+)?$/.test(val);\n}\nexport function isAndroid() {\n  return canUseDom ? /android/.test(navigator.userAgent.toLowerCase()) : false;\n}\nexport function isIOS() {\n  return canUseDom ? /ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()) : false;\n}", "map": {"version": 3, "names": ["canUseDom", "isDef", "val", "undefined", "isObject", "isPromise", "obj", "then", "isDate", "Object", "prototype", "toString", "call", "Number", "isNaN", "getTime", "isMobile", "value", "replace", "test", "isNumeric", "isAndroid", "navigator", "userAgent", "toLowerCase", "isIOS"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/validate.js"], "sourcesContent": ["import { canUseDom } from './can-use-dom';\nexport function isDef(val) {\n  return val !== undefined && val !== null;\n}\nexport function isObject(val) {\n  return val !== null && typeof val === 'object';\n}\nexport function isPromise(obj) {\n  return !!obj && typeof obj === 'object' && typeof obj.then === 'function';\n}\nexport function isDate(val) {\n  return Object.prototype.toString.call(val) === '[object Date]' && !Number.isNaN(val.getTime());\n}\nexport function isMobile(value) {\n  value = value.replace(/[^-|\\d]/g, '');\n  return /^((\\+86)|(86))?(1)\\d{10}$/.test(value) || /^0[0-9-]{10,13}$/.test(value);\n}\nexport function isNumeric(val) {\n  return typeof val === 'number' || /^\\d+(\\.\\d+)?$/.test(val);\n}\nexport function isAndroid() {\n  return canUseDom ? /android/.test(navigator.userAgent.toLowerCase()) : false;\n}\nexport function isIOS() {\n  return canUseDom ? /ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()) : false;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI;AAC1C;AACA,OAAO,SAASE,QAAQA,CAACF,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAChD;AACA,OAAO,SAASG,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAO,CAAC,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU;AAC3E;AACA,OAAO,SAASC,MAAMA,CAACN,GAAG,EAAE;EAC1B,OAAOO,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACV,GAAG,CAAC,KAAK,eAAe,IAAI,CAACW,MAAM,CAACC,KAAK,CAACZ,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC;AAChG;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9BA,KAAK,GAAGA,KAAK,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EACrC,OAAO,2BAA2B,CAACC,IAAI,CAACF,KAAK,CAAC,IAAI,kBAAkB,CAACE,IAAI,CAACF,KAAK,CAAC;AAClF;AACA,OAAO,SAASG,SAASA,CAAClB,GAAG,EAAE;EAC7B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,eAAe,CAACiB,IAAI,CAACjB,GAAG,CAAC;AAC7D;AACA,OAAO,SAASmB,SAASA,CAAA,EAAG;EAC1B,OAAOrB,SAAS,GAAG,SAAS,CAACmB,IAAI,CAACG,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK;AAC9E;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtB,OAAOzB,SAAS,GAAG,sBAAsB,CAACmB,IAAI,CAACG,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}