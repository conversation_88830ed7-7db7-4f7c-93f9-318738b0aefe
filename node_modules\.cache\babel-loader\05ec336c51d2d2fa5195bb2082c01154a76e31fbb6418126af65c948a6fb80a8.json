{"ast": null, "code": "// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible() || !isOnline()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\nexport default subscribe;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "isOnline", "listeners", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice", "revalidate", "i", "length", "window", "addEventListener"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js"], "sourcesContent": ["// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible() || !isOnline()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\nexport default subscribe;"], "mappings": "AAAA;AACA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,QAAQ,MAAM,YAAY;AACjC,IAAIC,SAAS,GAAG,EAAE;AAClB,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC3BF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EACxB,OAAO,SAASE,WAAWA,CAAA,EAAG;IAC5B,IAAIC,KAAK,GAAGL,SAAS,CAACM,OAAO,CAACJ,QAAQ,CAAC;IACvC,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;MACdL,SAAS,CAACO,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC5B;EACF,CAAC;AACH;AACA,IAAIR,SAAS,EAAE;EACb,IAAIW,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,IAAI,CAACV,iBAAiB,CAAC,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;IACzC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIP,QAAQ,GAAGF,SAAS,CAACS,CAAC,CAAC;MAC3BP,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDS,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,UAAU,EAAE,KAAK,CAAC;EAC9DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,UAAU,EAAE,KAAK,CAAC;AACrD;AACA,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}