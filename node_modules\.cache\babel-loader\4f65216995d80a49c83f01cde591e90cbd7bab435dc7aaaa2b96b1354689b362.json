{"ast": null, "code": "export default function get(entity, path) {\n  var current = entity;\n  for (var i = 0; i < path.length; i += 1) {\n    if (current === null || current === undefined) {\n      return undefined;\n    }\n    current = current[path[i]];\n  }\n  return current;\n}", "map": {"version": 3, "names": ["get", "entity", "path", "current", "i", "length", "undefined"], "sources": ["C:/Users/<USER>/node_modules/rc-util/es/utils/get.js"], "sourcesContent": ["export default function get(entity, path) {\n  var current = entity;\n  for (var i = 0; i < path.length; i += 1) {\n    if (current === null || current === undefined) {\n      return undefined;\n    }\n    current = current[path[i]];\n  }\n  return current;\n}"], "mappings": "AAAA,eAAe,SAASA,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACxC,IAAIC,OAAO,GAAGF,MAAM;EACpB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,IAAID,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKG,SAAS,EAAE;MAC7C,OAAOA,SAAS;IAClB;IACAH,OAAO,GAAGA,OAAO,CAACD,IAAI,CAACE,CAAC,CAAC,CAAC;EAC5B;EACA,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}