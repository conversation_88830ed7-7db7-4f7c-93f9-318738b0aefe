{"ast": null, "code": "import React, { useState } from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-index-bar`;\nexport const Sidebar = props => {\n  const [interacting, setInteracting] = useState(false);\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-sidebar`, {\n      [`${classPrefix}-sidebar-interacting`]: interacting\n    }),\n    onMouseDown: () => {\n      setInteracting(true);\n    },\n    onMouseUp: () => {\n      setInteracting(false);\n    },\n    onTouchStart: () => {\n      setInteracting(true);\n    },\n    onTouchEnd: () => {\n      setInteracting(false);\n    },\n    onTouchMove: e => {\n      if (!interacting) return;\n      const {\n        clientX,\n        clientY\n      } = e.touches[0];\n      const target = document.elementFromPoint(clientX, clientY);\n      if (!target) return;\n      const index = target.dataset['index'];\n      if (index) {\n        props.onActive(index);\n      }\n    }\n  }, props.indexItems.map(({\n    index,\n    brief\n  }) => {\n    const active = index === props.activeIndex;\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-row`,\n      onMouseDown: () => {\n        props.onActive(index);\n      },\n      onTouchStart: () => {\n        props.onActive(index);\n      },\n      onMouseEnter: () => {\n        if (interacting) {\n          props.onActive(index);\n        }\n      },\n      \"data-index\": index,\n      key: index\n    }, interacting && active && React.createElement(\"div\", {\n      className: `${classPrefix}-sidebar-bubble`\n    }, brief), React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-sidebar-item`, {\n        [`${classPrefix}-sidebar-item-active`]: active\n      }),\n      \"data-index\": index\n    }, React.createElement(\"div\", null, brief)));\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}