{"ast": null, "code": "import \"./floating-panel.css\";\nimport { FloatingPanel } from './floating-panel';\nexport default FloatingPanel;", "map": {"version": 3, "names": ["FloatingPanel"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/floating-panel/index.js"], "sourcesContent": ["import \"./floating-panel.css\";\nimport { FloatingPanel } from './floating-panel';\nexport default FloatingPanel;"], "mappings": "AAAA,OAAO,sBAAsB;AAC7B,SAASA,aAAa,QAAQ,kBAAkB;AAChD,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}