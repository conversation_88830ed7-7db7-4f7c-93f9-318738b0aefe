{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isLeapYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.isLeapYear = function () {\n      return this.$y % 4 == 0 && this.$y % 100 != 0 || this.$y % 400 == 0;\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}