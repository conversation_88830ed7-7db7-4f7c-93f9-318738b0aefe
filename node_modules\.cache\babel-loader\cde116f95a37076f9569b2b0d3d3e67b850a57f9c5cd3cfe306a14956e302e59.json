{"ast": null, "code": "import { CascadePickerView } from './cascade-picker-view';\nexport default CascadePickerView;", "map": {"version": 3, "names": ["CascadePickerView"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascade-picker-view/index.js"], "sourcesContent": ["import { CascadePickerView } from './cascade-picker-view';\nexport default CascadePickerView;"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}