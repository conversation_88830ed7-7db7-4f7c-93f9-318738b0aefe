{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\nvar useThrottlePlugin = function (fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n    throttleLeading = _a.throttleLeading,\n    throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef();\n  var options = {};\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options);\n      // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n  if (!throttleWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useThrottlePlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}