{"ast": null, "code": "import * as React from \"react\";\nfunction MovieOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MovieOutline-MovieOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MovieOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MovieOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23,3 C34.5979797,3 44,12.4020203 44,24 C44,29.7238929 41.7099789,34.9129227 37.996151,38.700875 L33.1276217,38.7007635 C37.7927889,35.480729 40.85,30.0973894 40.85,24 C40.85,14.1417172 32.8582828,6.15 23,6.15 C13.1417172,6.15 5.15,14.1417172 5.15,24 C5.15,33.8582828 13.1417172,41.85 23,41.85 L23,41.85 L44,41.85 L44,45 L23,45 L23,45 C11.4020203,45 2,35.5979797 2,24 C2,12.4020203 11.4020203,3 23,3 Z M23,29 C25.7614237,29 28,31.2385763 28,34 C28,36.7614237 25.7614237,39 23,39 C20.2385763,39 18,36.7614237 18,34 C18,31.2385763 20.2385763,29 23,29 Z M23,32 C21.8954305,32 21,32.8954305 21,34 C21,35.1045695 21.8954305,36 23,36 C24.1045695,36 25,35.1045695 25,34 C25,32.8954305 24.1045695,32 23,32 Z M13,19 C15.7614237,19 18,21.2385763 18,24 C18,26.7614237 15.7614237,29 13,29 C10.2385763,29 8,26.7614237 8,24 C8,21.2385763 10.2385763,19 13,19 Z M33,19 C35.7614237,19 38,21.2385763 38,24 C38,26.7614237 35.7614237,29 33,29 C30.2385763,29 28,26.7614237 28,24 C28,21.2385763 30.2385763,19 33,19 Z M13,22 C11.8954305,22 11,22.8954305 11,24 C11,25.1045695 11.8954305,26 13,26 C14.1045695,26 15,25.1045695 15,24 C15,22.8954305 14.1045695,22 13,22 Z M33,22 C31.8954305,22 31,22.8954305 31,24 C31,25.1045695 31.8954305,26 33,26 C34.1045695,26 35,25.1045695 35,24 C35,22.8954305 34.1045695,22 33,22 Z M23,9 C25.7614237,9 28,11.2385763 28,14 C28,16.7614237 25.7614237,19 23,19 C20.2385763,19 18,16.7614237 18,14 C18,11.2385763 20.2385763,9 23,9 Z M23,12 C21.8954305,12 21,12.8954305 21,14 C21,15.1045695 21.8954305,16 23,16 C24.1045695,16 25,15.1045695 25,14 C25,12.8954305 24.1045695,12 23,12 Z\",\n    id: \"MovieOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default MovieOutline;", "map": {"version": 3, "names": ["React", "MovieOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/MovieOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction MovieOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MovieOutline-MovieOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MovieOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MovieOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M23,3 C34.5979797,3 44,12.4020203 44,24 C44,29.7238929 41.7099789,34.9129227 37.996151,38.700875 L33.1276217,38.7007635 C37.7927889,35.480729 40.85,30.0973894 40.85,24 C40.85,14.1417172 32.8582828,6.15 23,6.15 C13.1417172,6.15 5.15,14.1417172 5.15,24 C5.15,33.8582828 13.1417172,41.85 23,41.85 L23,41.85 L44,41.85 L44,45 L23,45 L23,45 C11.4020203,45 2,35.5979797 2,24 C2,12.4020203 11.4020203,3 23,3 Z M23,29 C25.7614237,29 28,31.2385763 28,34 C28,36.7614237 25.7614237,39 23,39 C20.2385763,39 18,36.7614237 18,34 C18,31.2385763 20.2385763,29 23,29 Z M23,32 C21.8954305,32 21,32.8954305 21,34 C21,35.1045695 21.8954305,36 23,36 C24.1045695,36 25,35.1045695 25,34 C25,32.8954305 24.1045695,32 23,32 Z M13,19 C15.7614237,19 18,21.2385763 18,24 C18,26.7614237 15.7614237,29 13,29 C10.2385763,29 8,26.7614237 8,24 C8,21.2385763 10.2385763,19 13,19 Z M33,19 C35.7614237,19 38,21.2385763 38,24 C38,26.7614237 35.7614237,29 33,29 C30.2385763,29 28,26.7614237 28,24 C28,21.2385763 30.2385763,19 33,19 Z M13,22 C11.8954305,22 11,22.8954305 11,24 C11,25.1045695 11.8954305,26 13,26 C14.1045695,26 15,25.1045695 15,24 C15,22.8954305 14.1045695,22 13,22 Z M33,22 C31.8954305,22 31,22.8954305 31,24 C31,25.1045695 31.8954305,26 33,26 C34.1045695,26 35,25.1045695 35,24 C35,22.8954305 34.1045695,22 33,22 Z M23,9 C25.7614237,9 28,11.2385763 28,14 C28,16.7614237 25.7614237,19 23,19 C20.2385763,19 18,16.7614237 18,14 C18,11.2385763 20.2385763,9 23,9 Z M23,12 C21.8954305,12 21,12.8954305 21,14 C21,15.1045695 21.8954305,16 23,16 C24.1045695,16 25,15.1045695 25,14 C25,12.8954305 24.1045695,12 23,12 Z\",\n    id: \"MovieOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default MovieOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,ojDAAojD;IACvjDR,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}