{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.staged = void 0;\nconst react_1 = __importDefault(require(\"react\"));\nfunction processNext(next) {\n  if (typeof next === 'function') {\n    return react_1.default.createElement(Stage, {\n      stage: next\n    });\n  } else {\n    return next;\n  }\n}\nfunction Stage(props) {\n  const next = props.stage();\n  return processNext(next);\n}\nfunction staged(stage) {\n  return function Staged(props, ref) {\n    const next = stage(props, ref);\n    return processNext(next);\n  };\n}\nexports.staged = staged;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}