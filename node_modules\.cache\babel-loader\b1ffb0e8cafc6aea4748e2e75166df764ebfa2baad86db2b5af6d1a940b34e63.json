{"ast": null, "code": "import { useRef } from 'react';\nvar useRetryPlugin = function (fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n    retryCount = _a.retryCount;\n  var timerRef = useRef();\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n  if (!retryCount) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n      triggerByRetry.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onError: function () {\n      countRef.current += 1;\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      countRef.current = 0;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\nexport default useRetryPlugin;", "map": {"version": 3, "names": ["useRef", "useRetryPlugin", "fetchInstance", "_a", "retryInterval", "retryCount", "timerRef", "countRef", "triggerByRetry", "onBefore", "current", "clearTimeout", "onSuccess", "onError", "timeout", "Math", "min", "pow", "setTimeout", "refresh", "onCancel"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nvar useRetryPlugin = function (fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n    retryCount = _a.retryCount;\n  var timerRef = useRef();\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n  if (!retryCount) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n      triggerByRetry.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onError: function () {\n      countRef.current += 1;\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      countRef.current = 0;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\nexport default useRetryPlugin;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,IAAIC,cAAc,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EAChD,IAAIC,aAAa,GAAGD,EAAE,CAACC,aAAa;IAClCC,UAAU,GAAGF,EAAE,CAACE,UAAU;EAC5B,IAAIC,QAAQ,GAAGN,MAAM,CAAC,CAAC;EACvB,IAAIO,QAAQ,GAAGP,MAAM,CAAC,CAAC,CAAC;EACxB,IAAIQ,cAAc,GAAGR,MAAM,CAAC,KAAK,CAAC;EAClC,IAAI,CAACK,UAAU,EAAE;IACf,OAAO,CAAC,CAAC;EACX;EACA,OAAO;IACLI,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpB,IAAI,CAACD,cAAc,CAACE,OAAO,EAAE;QAC3BH,QAAQ,CAACG,OAAO,GAAG,CAAC;MACtB;MACAF,cAAc,CAACE,OAAO,GAAG,KAAK;MAC9B,IAAIJ,QAAQ,CAACI,OAAO,EAAE;QACpBC,YAAY,CAACL,QAAQ,CAACI,OAAO,CAAC;MAChC;IACF,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrBL,QAAQ,CAACG,OAAO,GAAG,CAAC;IACtB,CAAC;IACDG,OAAO,EAAE,SAAAA,CAAA,EAAY;MACnBN,QAAQ,CAACG,OAAO,IAAI,CAAC;MACrB,IAAIL,UAAU,KAAK,CAAC,CAAC,IAAIE,QAAQ,CAACG,OAAO,IAAIL,UAAU,EAAE;QACvD;QACA,IAAIS,OAAO,GAAGV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGW,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAACG,OAAO,CAAC,EAAE,KAAK,CAAC;QACxIJ,QAAQ,CAACI,OAAO,GAAGQ,UAAU,CAAC,YAAY;UACxCV,cAAc,CAACE,OAAO,GAAG,IAAI;UAC7BR,aAAa,CAACiB,OAAO,CAAC,CAAC;QACzB,CAAC,EAAEL,OAAO,CAAC;MACb,CAAC,MAAM;QACLP,QAAQ,CAACG,OAAO,GAAG,CAAC;MACtB;IACF,CAAC;IACDU,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpBb,QAAQ,CAACG,OAAO,GAAG,CAAC;MACpB,IAAIJ,QAAQ,CAACI,OAAO,EAAE;QACpBC,YAAY,CAACL,QAAQ,CAACI,OAAO,CAAC;MAChC;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}