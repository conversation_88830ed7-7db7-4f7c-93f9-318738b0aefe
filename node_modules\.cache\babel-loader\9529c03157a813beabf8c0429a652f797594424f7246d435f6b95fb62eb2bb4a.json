{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useMotionReduced } from '../../utils/reduce-and-restore-motion';\nconst classPrefix = 'adm-spin-loading';\nconst colorRecord = {\n  default: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  white: 'var(--adm-color-white)'\n};\nconst defaultProps = {\n  color: 'default'\n};\nconst circumference = 15 * 3.14159265358979 * 2;\nexport const SpinLoading = memo(p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const motionReduced = useMotionReduced();\n  const {\n    percent\n  } = useSpring({\n    cancel: motionReduced,\n    loop: {\n      reverse: true\n    },\n    from: {\n      percent: 80\n    },\n    to: {\n      percent: 30\n    },\n    config: {\n      duration: 1200\n    }\n  });\n  return withNativeProps(props, React.createElement(animated.div, {\n    className: classPrefix,\n    style: {\n      '--color': (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color,\n      '--percent': percent\n    }\n  }, React.createElement(\"svg\", {\n    className: `${classPrefix}-svg`,\n    viewBox: '0 0 32 32'\n  }, React.createElement(animated.circle, {\n    className: `${classPrefix}-fill`,\n    fill: 'transparent',\n    strokeWidth: '2',\n    strokeDasharray: circumference,\n    strokeDashoffset: percent,\n    strokeLinecap: 'square',\n    r: 15,\n    cx: 16,\n    cy: 16\n  }))));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}