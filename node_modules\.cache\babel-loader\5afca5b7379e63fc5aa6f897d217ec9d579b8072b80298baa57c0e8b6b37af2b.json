{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\nvar checkIfAllInShadow = function (targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) return false;\n    if (targetElement.getRootNode() instanceof ShadowRoot) return true;\n    return false;\n  });\n};\nvar getShadow = function (node) {\n  if (!node) {\n    return document;\n  }\n  return node.getRootNode();\n};\nvar getDocumentOrShadow = function (target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n  var targets = Array.isArray(target) ? target : [target];\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n  return document;\n};\nexport default getDocumentOrShadow;", "map": {"version": 3, "names": ["getTargetElement", "checkIfAllInShadow", "targets", "every", "item", "targetElement", "getRootNode", "ShadowRoot", "getShadow", "node", "document", "getDocumentOrShadow", "target", "Array", "isArray"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/getDocumentOrShadow.js"], "sourcesContent": ["import { getTargetElement } from '../utils/domTarget';\nvar checkIfAllInShadow = function (targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) return false;\n    if (targetElement.getRootNode() instanceof ShadowRoot) return true;\n    return false;\n  });\n};\nvar getShadow = function (node) {\n  if (!node) {\n    return document;\n  }\n  return node.getRootNode();\n};\nvar getDocumentOrShadow = function (target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n  var targets = Array.isArray(target) ? target : [target];\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n  return document;\n};\nexport default getDocumentOrShadow;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,IAAIC,kBAAkB,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAC1C,OAAOA,OAAO,CAACC,KAAK,CAAC,UAAUC,IAAI,EAAE;IACnC,IAAIC,aAAa,GAAGL,gBAAgB,CAACI,IAAI,CAAC;IAC1C,IAAI,CAACC,aAAa,EAAE,OAAO,KAAK;IAChC,IAAIA,aAAa,CAACC,WAAW,CAAC,CAAC,YAAYC,UAAU,EAAE,OAAO,IAAI;IAClE,OAAO,KAAK;EACd,CAAC,CAAC;AACJ,CAAC;AACD,IAAIC,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC9B,IAAI,CAACA,IAAI,EAAE;IACT,OAAOC,QAAQ;EACjB;EACA,OAAOD,IAAI,CAACH,WAAW,CAAC,CAAC;AAC3B,CAAC;AACD,IAAIK,mBAAmB,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAC1C,IAAI,CAACA,MAAM,IAAI,CAACF,QAAQ,CAACJ,WAAW,EAAE;IACpC,OAAOI,QAAQ;EACjB;EACA,IAAIR,OAAO,GAAGW,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EACvD,IAAIX,kBAAkB,CAACC,OAAO,CAAC,EAAE;IAC/B,OAAOM,SAAS,CAACR,gBAAgB,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;EACA,OAAOQ,QAAQ;AACjB,CAAC;AACD,eAAeC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}