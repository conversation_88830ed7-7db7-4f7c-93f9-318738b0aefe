{"ast": null, "code": "/*\r\nMIT License\r\n\r\nCopyright (c) 2018-2023 Simon <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\n\tThe above copyright notice and this permission notice shall be included in all\r\ncopies or substantial portions of the Software.\r\n\r\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\nSOFTWARE.\r\n*/function $cf838c15c8b009ba$var$vrgs(f) {\n  var s = f + \"\",\n    i = s.indexOf(\"...\");\n  return i >= 0 && (i < s.indexOf(\")\") || s.indexOf(\"arguments\") >= 0);\n}\nfunction $cf838c15c8b009ba$export$22f15dd4e5be7e52(fn, o) {\n  /*o = {\r\n  serializer, // used to serialize arguments of single argument functions, multis are not serialized\r\n  equals, // equality tester, will force use of slower multiarg approach even for single arg functions\r\n  maxAge, // max cache age is ms, set > 0 && < Infinity if you want automatic clearing\r\n  maxArgs, // max args to use for signature\r\n  vargs = vrgs(fn) // set to true if function may have variable or beyond-signature arguments, default is best attempt at infering\r\n  } = {}\r\n  */\n  o || (o = {});\n  var vargs = o.vargs || $cf838c15c8b009ba$var$vrgs(fn),\n    k = [],\n    cache = new Map(),\n    u,\n    to,\n    d = function (key) {\n      return to = setTimeout(function () {\n        if (u) {\n          cache.delete(key);\n          return;\n        }\n        // dealing with multi-arg function, c and k are Arrays\n        k.splice(key, 1);\n        //v.splice(key,1);\n      }, o.maxAge);\n    },\n    c = o.maxAge > 0 && o.maxAge < Infinity ? d : 0,\n    eq = o.equals ? o.equals : 0,\n    maxargs = o.maxArgs,\n    srlz = o.serializer,\n    f; // memoized function to return\n  if (fn.length === 1 && !o.equals && !vargs) {\n    // for single argument functions, just use a Map lookup\n    f = function (a) {\n      if (srlz) a = srlz(a);\n      var r;\n      return cache.get(a) || (!c || c(a), cache.set(a, r = fn.call(this, a)), r);\n    };\n    u = 1;\n  } else if (eq)\n    // for multiple arg functions, loop through a cache of all the args\n    // looking at each arg separately so a test can abort as soon as possible\n    f = function () {\n      var l = maxargs || arguments.length,\n        kl = k.length,\n        i = -1;\n      while (++i < kl) if (k[i].length === l) {\n        var j = -1;\n        while (++j < l && eq(arguments[j], k[i][j])); // compare each arg\n        if (j === l) return k[i].val //the args matched;\n        ;\n      }\n      // set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\n      k[i] = arguments;\n      return !c || c(i), arguments.val = fn.apply(this, k[i]);\n    };else f = function () {\n    var l = maxargs || arguments.length,\n      kl = k.length,\n      i = -1;\n    while (++i < kl) if (k[i].length === l) {\n      var j = -1;\n      while (++j < l && arguments[j] === k[i][j]); // compare each arg\n      if (j === l) return k[i].val //the args matched;\n      ;\n    }\n    // set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\n    k[i] = arguments;\n    return !c || c(i), arguments.val = fn.apply(this, k[i]);\n  };\n  // reset all the caches\n  f.clear = function () {\n    if (to) clearTimeout(to);\n    cache.clear();\n    k = [];\n  };\n  f.keys = function () {\n    return u ? [...cache.keys()] : k.slice();\n  };\n  f.values = function () {\n    return u ? [...cache.values()] : k.map(args => args.val);\n  };\n  return f;\n}\nexport { $cf838c15c8b009ba$export$22f15dd4e5be7e52 as nanomemoize, $cf838c15c8b009ba$export$22f15dd4e5be7e52 as default };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}