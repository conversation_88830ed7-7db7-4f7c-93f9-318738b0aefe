{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\nvar useDebouncePlugin = function (fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n    debounceLeading = _a.debounceLeading,\n    debounceTrailing = _a.debounceTrailing,\n    debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef();\n  var options = useMemo(function () {\n    var ret = {};\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options);\n      // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n  if (!debounceWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useDebouncePlugin;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "debounce", "useEffect", "useMemo", "useRef", "useDebouncePlugin", "fetchInstance", "_a", "debounce<PERSON>ait", "debounceLeading", "debounceTrailing", "debounceMaxWait", "debouncedRef", "options", "ret", "undefined", "leading", "trailing", "max<PERSON><PERSON>", "_originRunAsync_1", "runAsync", "bind", "current", "callback", "args", "_i", "arguments", "length", "Promise", "resolve", "reject", "call", "apply", "then", "catch", "cancel", "onCancel"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\nvar useDebouncePlugin = function (fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n    debounceLeading = _a.debounceLeading,\n    debounceTrailing = _a.debounceTrailing,\n    debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef();\n  var options = useMemo(function () {\n    var ret = {};\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options);\n      // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n  if (!debounceWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useDebouncePlugin;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,SAAS,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAClD,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EACnD,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAY;IAChCC,eAAe,GAAGF,EAAE,CAACE,eAAe;IACpCC,gBAAgB,GAAGH,EAAE,CAACG,gBAAgB;IACtCC,eAAe,GAAGJ,EAAE,CAACI,eAAe;EACtC,IAAIC,YAAY,GAAGR,MAAM,CAAC,CAAC;EAC3B,IAAIS,OAAO,GAAGV,OAAO,CAAC,YAAY;IAChC,IAAIW,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIL,eAAe,KAAKM,SAAS,EAAE;MACjCD,GAAG,CAACE,OAAO,GAAGP,eAAe;IAC/B;IACA,IAAIC,gBAAgB,KAAKK,SAAS,EAAE;MAClCD,GAAG,CAACG,QAAQ,GAAGP,gBAAgB;IACjC;IACA,IAAIC,eAAe,KAAKI,SAAS,EAAE;MACjCD,GAAG,CAACI,OAAO,GAAGP,eAAe;IAC/B;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,CAACL,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,CAAC,CAAC;EACxDT,SAAS,CAAC,YAAY;IACpB,IAAIM,YAAY,EAAE;MAChB,IAAIW,iBAAiB,GAAGb,aAAa,CAACc,QAAQ,CAACC,IAAI,CAACf,aAAa,CAAC;MAClEM,YAAY,CAACU,OAAO,GAAGrB,QAAQ,CAAC,UAAUsB,QAAQ,EAAE;QAClDA,QAAQ,CAAC,CAAC;MACZ,CAAC,EAAEf,YAAY,EAAEK,OAAO,CAAC;MACzB;MACA;MACAP,aAAa,CAACc,QAAQ,GAAG,YAAY;QACnC,IAAII,IAAI,GAAG,EAAE;QACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;UAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;QAC1B;QACA,OAAO,IAAIG,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;UAC5C,IAAIvB,EAAE;UACN,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,IAAI,CAACnB,YAAY,EAAE,YAAY;YACjGO,iBAAiB,CAACa,KAAK,CAAC,KAAK,CAAC,EAAEhC,aAAa,CAAC,EAAE,EAAED,MAAM,CAACyB,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,KAAK,CAACJ,MAAM,CAAC;UACrG,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACD,OAAO,YAAY;QACjB,IAAIvB,EAAE;QACN,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,MAAM,CAAC,CAAC;QAC5E7B,aAAa,CAACc,QAAQ,GAAGD,iBAAiB;MAC5C,CAAC;IACH;EACF,CAAC,EAAE,CAACX,YAAY,EAAEK,OAAO,CAAC,CAAC;EAC3B,IAAI,CAACL,YAAY,EAAE;IACjB,OAAO,CAAC,CAAC;EACX;EACA,OAAO;IACL4B,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpB,IAAI7B,EAAE;MACN,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAO,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,MAAM,CAAC,CAAC;IAC9E;EACF,CAAC;AACH,CAAC;AACD,eAAe9B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}