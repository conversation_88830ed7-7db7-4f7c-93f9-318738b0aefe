{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"prefixCls\", \"direction\", \"options\", \"disabled\", \"defaultValue\", \"value\", \"onChange\", \"className\", \"motionName\"];\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport MotionThumb from \"./MotionThumb\";\nfunction getValidTitle(option) {\n  if (typeof option.title !== 'undefined') {\n    return option.title;\n  }\n\n  // read `label` when title is `undefined`\n  if (_typeof(option.label) !== 'object') {\n    var _option$label;\n    return (_option$label = option.label) === null || _option$label === void 0 ? void 0 : _option$label.toString();\n  }\n}\nfunction normalizeOptions(options) {\n  return options.map(function (option) {\n    if (_typeof(option) === 'object' && option !== null) {\n      var validTitle = getValidTitle(option);\n      return _objectSpread(_objectSpread({}, option), {}, {\n        title: validTitle\n      });\n    }\n    return {\n      label: option === null || option === void 0 ? void 0 : option.toString(),\n      title: option === null || option === void 0 ? void 0 : option.toString(),\n      value: option\n    };\n  });\n}\nvar InternalSegmentedOption = function InternalSegmentedOption(_ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    disabled = _ref.disabled,\n    checked = _ref.checked,\n    label = _ref.label,\n    title = _ref.title,\n    value = _ref.value,\n    onChange = _ref.onChange;\n  var handleChange = function handleChange(event) {\n    if (disabled) {\n      return;\n    }\n    onChange(event, value);\n  };\n  return /*#__PURE__*/React.createElement(\"label\", {\n    className: classNames(className, _defineProperty({}, \"\".concat(prefixCls, \"-item-disabled\"), disabled))\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    className: \"\".concat(prefixCls, \"-item-input\"),\n    type: \"radio\",\n    disabled: disabled,\n    checked: checked,\n    onChange: handleChange\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-label\"),\n    title: title,\n    role: \"option\",\n    \"aria-selected\": checked\n  }, label));\n};\nvar Segmented = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _segmentedOptions$, _classNames2;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-segmented' : _props$prefixCls,\n    direction = props.direction,\n    _props$options = props.options,\n    options = _props$options === void 0 ? [] : _props$options,\n    disabled = props.disabled,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    onChange = props.onChange,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$motionName = props.motionName,\n    motionName = _props$motionName === void 0 ? 'thumb-motion' : _props$motionName,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var containerRef = React.useRef(null);\n  var mergedRef = React.useMemo(function () {\n    return composeRef(containerRef, ref);\n  }, [containerRef, ref]);\n  var segmentedOptions = React.useMemo(function () {\n    return normalizeOptions(options);\n  }, [options]);\n\n  // Note: We should not auto switch value when value not exist in options\n  // which may break single source of truth.\n  var _useMergedState = useMergedState((_segmentedOptions$ = segmentedOptions[0]) === null || _segmentedOptions$ === void 0 ? void 0 : _segmentedOptions$.value, {\n      value: value,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    rawValue = _useMergedState2[0],\n    setRawValue = _useMergedState2[1];\n\n  // ======================= Change ========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    thumbShow = _React$useState2[0],\n    setThumbShow = _React$useState2[1];\n  var handleChange = function handleChange(event, val) {\n    if (disabled) {\n      return;\n    }\n    setRawValue(val);\n    onChange === null || onChange === void 0 ? void 0 : onChange(val);\n  };\n  var divProps = omit(restProps, ['children']);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"listbox\",\n    \"aria-label\": \"segmented control\"\n  }, divProps, {\n    className: classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _classNames2), className),\n    ref: mergedRef\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-group\")\n  }, /*#__PURE__*/React.createElement(MotionThumb, {\n    prefixCls: prefixCls,\n    value: rawValue,\n    containerRef: containerRef,\n    motionName: \"\".concat(prefixCls, \"-\").concat(motionName),\n    direction: direction,\n    getValueIndex: function getValueIndex(val) {\n      return segmentedOptions.findIndex(function (n) {\n        return n.value === val;\n      });\n    },\n    onMotionStart: function onMotionStart() {\n      setThumbShow(true);\n    },\n    onMotionEnd: function onMotionEnd() {\n      setThumbShow(false);\n    }\n  }), segmentedOptions.map(function (segmentedOption) {\n    return /*#__PURE__*/React.createElement(InternalSegmentedOption, _extends({}, segmentedOption, {\n      key: segmentedOption.value,\n      prefixCls: prefixCls,\n      className: classNames(segmentedOption.className, \"\".concat(prefixCls, \"-item\"), _defineProperty({}, \"\".concat(prefixCls, \"-item-selected\"), segmentedOption.value === rawValue && !thumbShow)),\n      checked: segmentedOption.value === rawValue,\n      onChange: handleChange,\n      disabled: !!disabled || !!segmentedOption.disabled\n    }));\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nvar TypedSegmented = Segmented;\nexport default TypedSegmented;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}