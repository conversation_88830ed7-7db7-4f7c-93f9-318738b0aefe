{"ast": null, "code": "import * as React from \"react\";\nfunction TruckOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TruckOutline-TruckOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TruckOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TruckOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.4818755,12.1666759 L28.4818755,14.082427 L37.9255007,14.0833438 C38.8227032,14.0832951 39.688534,14.4148172 40.3578492,15.0146797 L40.5085014,15.1576798 L43.8465906,18.5090185 C44.4812431,19.1457904 44.8602104,19.9948324 44.911201,20.8941866 L44.9166793,21.1022703 L44.9166793,35.166689 C44.9166793,37.1204789 43.3907393,38.731087 41.4471118,38.8287766 L41.2645059,38.8333598 L40.1624636,38.8333598 C39.4050384,41.5130011 36.6273369,43.0688326 33.958289,42.3084008 C32.2824202,41.8309358 30.9725634,40.51588 30.496986,38.8333593 L28.4818972,38.8333588 L20.0755038,38.8342754 C19.3180786,41.5139167 16.5403771,43.0697482 13.8713293,42.3093164 C12.1954604,41.8318514 10.8856036,40.5167956 10.4100263,38.8342749 L6.56885286,38.8333577 C4.55181216,38.8333576 2.9166793,37.1917347 2.9166793,35.1666882 L2.9166793,12.1666625 C2.9166793,10.1416165 4.55181216,8.49999354 6.56885266,8.49999354 L24.8297409,8.49999354 C26.8467426,8.49999354 28.4818755,10.1416294 28.4818755,12.1666759 Z M12.9601355,37.4583565 C12.9601355,38.7240111 13.9820925,39.7500244 15.2427433,39.7500244 C16.5033941,39.7500244 17.525351,38.7240111 17.525351,37.4583565 C17.525351,36.1927019 16.5033941,35.1666886 15.2427432,35.1666886 C13.9820925,35.1666886 12.9601355,36.1927019 12.9601355,37.4583565 Z M33.0470826,37.4583565 C33.0470826,38.7240111 34.0690396,39.7500244 35.3296904,39.7500244 C36.5903412,39.7500244 37.6122981,38.7240111 37.6122981,37.4583565 C37.6122981,36.1927019 36.5903412,35.1666886 35.3296903,35.1666886 C34.0690396,35.1666886 33.0470826,36.1927019 33.0470826,37.4583565 Z M6.56881404,11.2500096 C6.10592229,11.2500096 5.71631941,11.5978915 5.66216165,12.0594258 L5.65577031,12.1666759 L5.65577031,35.1667015 C5.65577031,35.6313098 6.00216468,36.0225063 6.46186663,36.0769392 L6.56881216,36.0833703 L10.4099856,36.0833703 C11.1674109,33.4037285 13.9451124,31.847897 16.6141602,32.6083287 C18.2900291,33.0857938 19.5998859,34.4008496 20.0754632,36.0833703 L25.7427274,36.0833703 L25.7427274,12.1666913 C25.7430526,11.7413369 25.4519338,11.371657 25.0396505,11.2738471 L24.9364764,11.2555138 L24.8296832,11.2500096 L6.56881404,11.2500096 Z M28.4818628,16.8333626 L28.4818628,36.0833852 L30.4969516,36.0833852 C31.254377,33.4037434 34.0320784,31.8479119 36.7011263,32.6083437 C38.3769952,33.0858087 39.686852,34.4008645 40.1624293,36.0833852 L41.2644716,36.0833852 L41.3712977,36.0769685 C41.790833,36.0272966 42.1216485,35.695168 42.1711241,35.2739676 L42.1775154,35.1667179 L42.1775154,21.1022992 L42.169298,20.981299 C42.1483261,20.8225799 42.0863118,20.6721436 41.9894288,20.5449641 L41.909994,20.4532975 L38.5709932,17.1019588 L38.4806018,17.0222087 C38.3539269,16.9249411 38.2040853,16.8626806 38.0459916,16.8416251 L37.9254694,16.8333626 L28.4818628,16.8333626 Z\",\n    id: \"TruckOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TruckOutline;", "map": {"version": 3, "names": ["React", "TruckOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/TruckOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TruckOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TruckOutline-TruckOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TruckOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TruckOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.4818755,12.1666759 L28.4818755,14.082427 L37.9255007,14.0833438 C38.8227032,14.0832951 39.688534,14.4148172 40.3578492,15.0146797 L40.5085014,15.1576798 L43.8465906,18.5090185 C44.4812431,19.1457904 44.8602104,19.9948324 44.911201,20.8941866 L44.9166793,21.1022703 L44.9166793,35.166689 C44.9166793,37.1204789 43.3907393,38.731087 41.4471118,38.8287766 L41.2645059,38.8333598 L40.1624636,38.8333598 C39.4050384,41.5130011 36.6273369,43.0688326 33.958289,42.3084008 C32.2824202,41.8309358 30.9725634,40.51588 30.496986,38.8333593 L28.4818972,38.8333588 L20.0755038,38.8342754 C19.3180786,41.5139167 16.5403771,43.0697482 13.8713293,42.3093164 C12.1954604,41.8318514 10.8856036,40.5167956 10.4100263,38.8342749 L6.56885286,38.8333577 C4.55181216,38.8333576 2.9166793,37.1917347 2.9166793,35.1666882 L2.9166793,12.1666625 C2.9166793,10.1416165 4.55181216,8.49999354 6.56885266,8.49999354 L24.8297409,8.49999354 C26.8467426,8.49999354 28.4818755,10.1416294 28.4818755,12.1666759 Z M12.9601355,37.4583565 C12.9601355,38.7240111 13.9820925,39.7500244 15.2427433,39.7500244 C16.5033941,39.7500244 17.525351,38.7240111 17.525351,37.4583565 C17.525351,36.1927019 16.5033941,35.1666886 15.2427432,35.1666886 C13.9820925,35.1666886 12.9601355,36.1927019 12.9601355,37.4583565 Z M33.0470826,37.4583565 C33.0470826,38.7240111 34.0690396,39.7500244 35.3296904,39.7500244 C36.5903412,39.7500244 37.6122981,38.7240111 37.6122981,37.4583565 C37.6122981,36.1927019 36.5903412,35.1666886 35.3296903,35.1666886 C34.0690396,35.1666886 33.0470826,36.1927019 33.0470826,37.4583565 Z M6.56881404,11.2500096 C6.10592229,11.2500096 5.71631941,11.5978915 5.66216165,12.0594258 L5.65577031,12.1666759 L5.65577031,35.1667015 C5.65577031,35.6313098 6.00216468,36.0225063 6.46186663,36.0769392 L6.56881216,36.0833703 L10.4099856,36.0833703 C11.1674109,33.4037285 13.9451124,31.847897 16.6141602,32.6083287 C18.2900291,33.0857938 19.5998859,34.4008496 20.0754632,36.0833703 L25.7427274,36.0833703 L25.7427274,12.1666913 C25.7430526,11.7413369 25.4519338,11.371657 25.0396505,11.2738471 L24.9364764,11.2555138 L24.8296832,11.2500096 L6.56881404,11.2500096 Z M28.4818628,16.8333626 L28.4818628,36.0833852 L30.4969516,36.0833852 C31.254377,33.4037434 34.0320784,31.8479119 36.7011263,32.6083437 C38.3769952,33.0858087 39.686852,34.4008645 40.1624293,36.0833852 L41.2644716,36.0833852 L41.3712977,36.0769685 C41.790833,36.0272966 42.1216485,35.695168 42.1711241,35.2739676 L42.1775154,35.1667179 L42.1775154,21.1022992 L42.169298,20.981299 C42.1483261,20.8225799 42.0863118,20.6721436 41.9894288,20.5449641 L41.909994,20.4532975 L38.5709932,17.1019588 L38.4806018,17.0222087 C38.3539269,16.9249411 38.2040853,16.8626806 38.0459916,16.8416251 L37.9254694,16.8333626 L28.4818628,16.8333626 Z\",\n    id: \"TruckOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TruckOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,usFAAusF;IAC1sFR,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}