{"ast": null, "code": "import dayjs from 'dayjs';\nimport isLeapYear from 'dayjs/plugin/isLeapYear';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';\ndayjs.extend(isoWeek);\ndayjs.extend(isoWeeksInYear);\ndayjs.extend(isLeapYear);\nconst precisionRankRecord = {\n  year: 0,\n  week: 1,\n  'week-day': 2\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minWeek = minDay.isoWeek();\n  const maxWeek = maxDay.isoWeek();\n  const minWeekday = minDay.isoWeekday();\n  const maxWeekday = maxDay.isoWeekday();\n  const selectedWeek = parseInt(selected[1]);\n  const isInMinWeek = isInMinYear && selectedWeek === minWeek;\n  const isInMaxWeek = isInMaxYear && selectedWeek === maxWeek;\n  const selectedYearWeeks = dayjs(`${selectedYear}-01-01`).isoWeeksInYear();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.week) {\n    const lower = isInMinYear ? minWeek : 1;\n    const upper = isInMaxYear ? maxWeek : selectedYearWeeks;\n    const weeks = generateColumn(lower, upper, 'week');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord['week-day']) {\n    const lower = isInMinWeek ? minWeekday : 1;\n    const upper = isInMaxWeek ? maxWeekday : 7;\n    const weeks = generateColumn(lower, upper, 'week-day');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week-day', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.isoWeekYear().toString(), day.isoWeek().toString(), day.isoWeekday().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b, _c;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const weekString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const weekdayString = (_c = value[2]) !== null && _c !== void 0 ? _c : '1';\n  const day = dayjs(`${parseInt(yearString)}-01-01`).isoWeek(parseInt(weekString)).isoWeekday(parseInt(weekdayString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}