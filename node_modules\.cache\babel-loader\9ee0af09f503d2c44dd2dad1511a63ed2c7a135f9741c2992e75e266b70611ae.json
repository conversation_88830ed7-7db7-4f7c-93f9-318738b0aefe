{"ast": null, "code": "import debounce from 'lodash/debounce';\nfunction isNodeOrWeb() {\n  var freeGlobal = (typeof global === 'undefined' ? 'undefined' : typeof global) == 'object' && global && global.Object === Object && global;\n  var freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n  return freeGlobal || freeSelf;\n}\nif (!isNodeOrWeb()) {\n  global.Date = Date;\n}\nexport { debounce };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}