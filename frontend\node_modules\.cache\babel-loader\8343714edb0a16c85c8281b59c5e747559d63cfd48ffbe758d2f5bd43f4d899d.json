{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport FieldContext from \"./FieldContext\";\nimport Field from \"./Field\";\nimport { move as _move, getNamePath } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nfunction List(_ref) {\n  var name = _ref.name,\n    initialValue = _ref.initialValue,\n    children = _ref.children,\n    rules = _ref.rules,\n    validateTrigger = _ref.validateTrigger,\n    isListField = _ref.isListField;\n  var context = React.useContext(FieldContext);\n  var wrapperListContext = React.useContext(ListContext);\n  var keyRef = React.useRef({\n    keys: [],\n    id: 0\n  });\n  var keyManager = keyRef.current;\n  var prefixName = React.useMemo(function () {\n    var parentPrefixName = getNamePath(context.prefixName) || [];\n    return [].concat(_toConsumableArray(parentPrefixName), _toConsumableArray(getNamePath(name)));\n  }, [context.prefixName, name]);\n  var fieldContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, context), {}, {\n      prefixName: prefixName\n    });\n  }, [context, prefixName]);\n\n  // List context\n  var listContext = React.useMemo(function () {\n    return {\n      getKey: function getKey(namePath) {\n        var len = prefixName.length;\n        var pathName = namePath[len];\n        return [keyManager.keys[pathName], namePath.slice(len + 1)];\n      }\n    };\n  }, [prefixName]);\n\n  // User should not pass `children` as other type.\n  if (typeof children !== 'function') {\n    warning(false, 'Form.List only accepts function as children.');\n    return null;\n  }\n  var shouldUpdate = function shouldUpdate(prevValue, nextValue, _ref2) {\n    var source = _ref2.source;\n    if (source === 'internal') {\n      return false;\n    }\n    return prevValue !== nextValue;\n  };\n  return /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: listContext\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: fieldContext\n  }, /*#__PURE__*/React.createElement(Field, {\n    name: [],\n    shouldUpdate: shouldUpdate,\n    rules: rules,\n    validateTrigger: validateTrigger,\n    initialValue: initialValue,\n    isList: true,\n    isListField: isListField !== null && isListField !== void 0 ? isListField : !!wrapperListContext\n  }, function (_ref3, meta) {\n    var _ref3$value = _ref3.value,\n      value = _ref3$value === void 0 ? [] : _ref3$value,\n      onChange = _ref3.onChange;\n    var getFieldValue = context.getFieldValue;\n    var getNewValue = function getNewValue() {\n      var values = getFieldValue(prefixName || []);\n      return values || [];\n    };\n    /**\n     * Always get latest value in case user update fields by `form` api.\n     */\n    var operations = {\n      add: function add(defaultValue, index) {\n        // Mapping keys\n        var newValue = getNewValue();\n        if (index >= 0 && index <= newValue.length) {\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys.slice(0, index)), [keyManager.id], _toConsumableArray(keyManager.keys.slice(index)));\n          onChange([].concat(_toConsumableArray(newValue.slice(0, index)), [defaultValue], _toConsumableArray(newValue.slice(index))));\n        } else {\n          if (process.env.NODE_ENV !== 'production' && (index < 0 || index > newValue.length)) {\n            warning(false, 'The second parameter of the add function should be a valid positive number.');\n          }\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys), [keyManager.id]);\n          onChange([].concat(_toConsumableArray(newValue), [defaultValue]));\n        }\n        keyManager.id += 1;\n      },\n      remove: function remove(index) {\n        var newValue = getNewValue();\n        var indexSet = new Set(Array.isArray(index) ? index : [index]);\n        if (indexSet.size <= 0) {\n          return;\n        }\n        keyManager.keys = keyManager.keys.filter(function (_, keysIndex) {\n          return !indexSet.has(keysIndex);\n        });\n\n        // Trigger store change\n        onChange(newValue.filter(function (_, valueIndex) {\n          return !indexSet.has(valueIndex);\n        }));\n      },\n      move: function move(from, to) {\n        if (from === to) {\n          return;\n        }\n        var newValue = getNewValue();\n\n        // Do not handle out of range\n        if (from < 0 || from >= newValue.length || to < 0 || to >= newValue.length) {\n          return;\n        }\n        keyManager.keys = _move(keyManager.keys, from, to);\n\n        // Trigger store change\n        onChange(_move(newValue, from, to));\n      }\n    };\n    var listValue = value || [];\n    if (!Array.isArray(listValue)) {\n      listValue = [];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"Current value of '\".concat(prefixName.join(' > '), \"' is not an array type.\"));\n      }\n    }\n    return children(listValue.map(function (__, index) {\n      var key = keyManager.keys[index];\n      if (key === undefined) {\n        keyManager.keys[index] = keyManager.id;\n        key = keyManager.keys[index];\n        keyManager.id += 1;\n      }\n      return {\n        name: index,\n        key: key,\n        isListField: true\n      };\n    }), operations, meta);\n  })));\n}\nexport default List;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}