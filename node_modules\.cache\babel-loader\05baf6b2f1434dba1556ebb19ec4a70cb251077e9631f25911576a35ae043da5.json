{"ast": null, "code": "import React from 'react';\nimport { <PERSON>C<PERSON>cle<PERSON>ill, CloseCircleFill, InformationCircleFill, ClockCircleFill, ExclamationCircleFill } from 'antd-mobile-icons';\nimport { useConfig } from '../config-provider';\nexport const useResultIcon = status => {\n  const {\n    result: componentConfig = {}\n  } = useConfig();\n  const {\n    successIcon = React.createElement(CheckCircleFill, null),\n    errorIcon = React.createElement(CloseCircleFill, null),\n    infoIcon = React.createElement(InformationCircleFill, null),\n    waitingIcon = React.createElement(ClockCircleFill, null),\n    warningIcon = React.createElement(ExclamationCircleFill, null)\n  } = componentConfig || {};\n  switch (status) {\n    case 'success':\n      return successIcon;\n    case 'error':\n      return errorIcon;\n    case 'info':\n      return infoIcon;\n    case 'waiting':\n      return waitingIcon;\n    case 'warning':\n      return warningIcon;\n    default:\n      return null;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}