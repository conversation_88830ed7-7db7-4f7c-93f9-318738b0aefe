{"ast": null, "code": "import React, { useMemo, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport Ticks from './ticks';\nimport Marks from './marks';\nimport getMiniDecimal, { toFixed } from '@rc-component/mini-decimal';\nimport Thumb from './thumb';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { nearest } from '../../utils/nearest';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { devWarning } from '../../utils/dev-log';\nconst classPrefix = `adm-slider`;\nconst defaultProps = {\n  min: 0,\n  max: 100,\n  step: 1,\n  ticks: false,\n  range: false,\n  disabled: false,\n  popover: false,\n  residentPopover: false\n};\nexport const Slider = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const {\n    min,\n    max,\n    disabled,\n    marks,\n    ticks,\n    step,\n    icon\n  } = props;\n  function sortValue(val) {\n    return val.sort((a, b) => a - b);\n  }\n  function convertValue(value) {\n    return props.range ? value : [props.min, value];\n  }\n  function alignValue(value, decimalLen) {\n    const decimal = getMiniDecimal(value);\n    const fixedStr = toFixed(decimal.toString(), '.', decimalLen);\n    return getMiniDecimal(fixedStr).toNumber();\n  }\n  function reverseValue(value) {\n    const mergedDecimalLen = Math.max(getDecimalLen(step), getDecimalLen(value[0]), getDecimalLen(value[1]));\n    return props.range ? value.map(v => alignValue(v, mergedDecimalLen)) : alignValue(value[1], mergedDecimalLen);\n  }\n  function getDecimalLen(n) {\n    return (`${n}`.split('.')[1] || '').length;\n  }\n  function onAfterChange(value) {\n    var _a;\n    (_a = props.onAfterChange) === null || _a === void 0 ? void 0 : _a.call(props, reverseValue(value));\n  }\n  let propsValue = props.value;\n  if (props.range && typeof props.value === 'number') {\n    devWarning('Slider', 'When `range` prop is enabled, the `value` prop should be an array, like: [0, 0]');\n    propsValue = [0, props.value];\n  }\n  const [rawValue, setRawValue] = usePropsValue({\n    value: propsValue,\n    defaultValue: (_a = props.defaultValue) !== null && _a !== void 0 ? _a : props.range ? [min, min] : min,\n    onChange: props.onChange\n  });\n  const sliderValue = sortValue(convertValue(rawValue));\n  function setSliderValue(value) {\n    const next = sortValue(value);\n    const current = sliderValue;\n    if (next[0] === current[0] && next[1] === current[1]) return;\n    setRawValue(reverseValue(next));\n  }\n  const trackRef = useRef(null);\n  const fillSize = `${100 * (sliderValue[1] - sliderValue[0]) / (max - min)}%`;\n  const fillStart = `${100 * (sliderValue[0] - min) / (max - min)}%`;\n  // 计算要显示的点\n  const pointList = useMemo(() => {\n    if (marks) {\n      return Object.keys(marks).map(parseFloat).sort((a, b) => a - b);\n    } else if (ticks) {\n      const points = [];\n      for (let i = getMiniDecimal(min); i.lessEquals(getMiniDecimal(max)); i = i.add(step)) {\n        points.push(i.toNumber());\n      }\n      return points;\n    }\n    return [];\n  }, [marks, ticks, step, min, max]);\n  function getValueByPosition(position) {\n    const newPosition = position < min ? min : position > max ? max : position;\n    let value = min;\n    // 显示了刻度点，就只能移动到点上\n    if (pointList.length) {\n      value = nearest(pointList, newPosition);\n    } else {\n      // 使用 MiniDecimal 避免精度问题\n      const cell = Math.round((newPosition - min) / step);\n      const nextVal = getMiniDecimal(cell).multi(step);\n      value = getMiniDecimal(min).add(nextVal.toString()).toNumber();\n    }\n    return value;\n  }\n  const dragLockRef = useRef(0);\n  const onTrackClick = event => {\n    if (dragLockRef.current > 0) return;\n    event.stopPropagation();\n    if (disabled) return;\n    const track = trackRef.current;\n    if (!track) return;\n    const sliderOffsetLeft = track.getBoundingClientRect().left;\n    const position = (event.clientX - sliderOffsetLeft) / Math.ceil(track.offsetWidth) * (max - min) + min;\n    const targetValue = getValueByPosition(position);\n    let nextSliderValue;\n    if (props.range) {\n      // 移动的滑块采用就近原则\n      if (Math.abs(targetValue - sliderValue[0]) > Math.abs(targetValue - sliderValue[1])) {\n        nextSliderValue = [sliderValue[0], targetValue];\n      } else {\n        nextSliderValue = [targetValue, sliderValue[1]];\n      }\n    } else {\n      nextSliderValue = [props.min, targetValue];\n    }\n    setSliderValue(nextSliderValue);\n    onAfterChange(nextSliderValue);\n  };\n  const valueBeforeDragRef = useRef();\n  const renderThumb = index => {\n    return React.createElement(Thumb, {\n      key: index,\n      value: sliderValue[index],\n      min: min,\n      max: max,\n      disabled: disabled,\n      trackRef: trackRef,\n      icon: icon,\n      popover: props.popover,\n      residentPopover: props.residentPopover,\n      onDrag: (position, first, last) => {\n        if (first) {\n          dragLockRef.current += 1;\n          valueBeforeDragRef.current = sliderValue;\n        }\n        const val = getValueByPosition(position);\n        const valueBeforeDrag = valueBeforeDragRef.current;\n        if (!valueBeforeDrag) return;\n        const next = [...valueBeforeDrag];\n        next[index] = val;\n        setSliderValue(next);\n        if (last) {\n          onAfterChange(next);\n          window.setTimeout(() => {\n            dragLockRef.current -= 1;\n          }, 100);\n        }\n      },\n      \"aria-label\": props['aria-label']\n    });\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-disabled`]: disabled\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-track-container`,\n    onClick: onTrackClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-track`,\n    onClick: onTrackClick,\n    ref: trackRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-fill`,\n    style: {\n      width: fillSize,\n      left: fillStart\n    }\n  }), props.ticks && React.createElement(Ticks, {\n    points: pointList,\n    min: min,\n    max: max,\n    lowerBound: sliderValue[0],\n    upperBound: sliderValue[1]\n  }), props.range && renderThumb(0), renderThumb(1))), marks && React.createElement(Marks, {\n    min: min,\n    max: max,\n    marks: marks,\n    lowerBound: sliderValue[0],\n    upperBound: sliderValue[1]\n  })));\n};", "map": {"version": 3, "names": ["React", "useMemo", "useRef", "withNativeProps", "classNames", "Ticks", "Marks", "getMiniDecimal", "toFixed", "Thumb", "mergeProps", "nearest", "usePropsValue", "dev<PERSON><PERSON><PERSON>", "classPrefix", "defaultProps", "min", "max", "step", "ticks", "range", "disabled", "popover", "residentPop<PERSON>", "Slide<PERSON>", "p", "_a", "props", "marks", "icon", "sortValue", "val", "sort", "a", "b", "convertValue", "value", "alignValue", "decimalLen", "decimal", "fixedStr", "toString", "toNumber", "reverseValue", "mergedDecimalLen", "Math", "getDecimalLen", "map", "v", "n", "split", "length", "onAfterChange", "call", "props<PERSON><PERSON><PERSON>", "rawValue", "setRawValue", "defaultValue", "onChange", "slider<PERSON><PERSON><PERSON>", "setSliderV<PERSON>ue", "next", "current", "trackRef", "fillSize", "fillStart", "pointList", "Object", "keys", "parseFloat", "points", "i", "lessEquals", "add", "push", "getValueByPosition", "position", "newPosition", "cell", "round", "nextVal", "multi", "dragLockRef", "onTrackClick", "event", "stopPropagation", "track", "sliderOffsetLeft", "getBoundingClientRect", "left", "clientX", "ceil", "offsetWidth", "targetValue", "nextSliderValue", "abs", "valueBeforeDragRef", "renderThumb", "index", "createElement", "key", "onDrag", "first", "last", "valueBeforeDrag", "window", "setTimeout", "className", "onClick", "ref", "style", "width", "lowerBound", "upperBound"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/slider/slider.js"], "sourcesContent": ["import React, { useMemo, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport Ticks from './ticks';\nimport Marks from './marks';\nimport getMiniDecimal, { toFixed } from '@rc-component/mini-decimal';\nimport Thumb from './thumb';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { nearest } from '../../utils/nearest';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { devWarning } from '../../utils/dev-log';\nconst classPrefix = `adm-slider`;\nconst defaultProps = {\n  min: 0,\n  max: 100,\n  step: 1,\n  ticks: false,\n  range: false,\n  disabled: false,\n  popover: false,\n  residentPopover: false\n};\nexport const Slider = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const {\n    min,\n    max,\n    disabled,\n    marks,\n    ticks,\n    step,\n    icon\n  } = props;\n  function sortValue(val) {\n    return val.sort((a, b) => a - b);\n  }\n  function convertValue(value) {\n    return props.range ? value : [props.min, value];\n  }\n  function alignValue(value, decimalLen) {\n    const decimal = getMiniDecimal(value);\n    const fixedStr = toFixed(decimal.toString(), '.', decimalLen);\n    return getMiniDecimal(fixedStr).toNumber();\n  }\n  function reverseValue(value) {\n    const mergedDecimalLen = Math.max(getDecimalLen(step), getDecimalLen(value[0]), getDecimalLen(value[1]));\n    return props.range ? value.map(v => alignValue(v, mergedDecimalLen)) : alignValue(value[1], mergedDecimalLen);\n  }\n  function getDecimalLen(n) {\n    return (`${n}`.split('.')[1] || '').length;\n  }\n  function onAfterChange(value) {\n    var _a;\n    (_a = props.onAfterChange) === null || _a === void 0 ? void 0 : _a.call(props, reverseValue(value));\n  }\n  let propsValue = props.value;\n  if (props.range && typeof props.value === 'number') {\n    devWarning('Slider', 'When `range` prop is enabled, the `value` prop should be an array, like: [0, 0]');\n    propsValue = [0, props.value];\n  }\n  const [rawValue, setRawValue] = usePropsValue({\n    value: propsValue,\n    defaultValue: (_a = props.defaultValue) !== null && _a !== void 0 ? _a : props.range ? [min, min] : min,\n    onChange: props.onChange\n  });\n  const sliderValue = sortValue(convertValue(rawValue));\n  function setSliderValue(value) {\n    const next = sortValue(value);\n    const current = sliderValue;\n    if (next[0] === current[0] && next[1] === current[1]) return;\n    setRawValue(reverseValue(next));\n  }\n  const trackRef = useRef(null);\n  const fillSize = `${100 * (sliderValue[1] - sliderValue[0]) / (max - min)}%`;\n  const fillStart = `${100 * (sliderValue[0] - min) / (max - min)}%`;\n  // 计算要显示的点\n  const pointList = useMemo(() => {\n    if (marks) {\n      return Object.keys(marks).map(parseFloat).sort((a, b) => a - b);\n    } else if (ticks) {\n      const points = [];\n      for (let i = getMiniDecimal(min); i.lessEquals(getMiniDecimal(max)); i = i.add(step)) {\n        points.push(i.toNumber());\n      }\n      return points;\n    }\n    return [];\n  }, [marks, ticks, step, min, max]);\n  function getValueByPosition(position) {\n    const newPosition = position < min ? min : position > max ? max : position;\n    let value = min;\n    // 显示了刻度点，就只能移动到点上\n    if (pointList.length) {\n      value = nearest(pointList, newPosition);\n    } else {\n      // 使用 MiniDecimal 避免精度问题\n      const cell = Math.round((newPosition - min) / step);\n      const nextVal = getMiniDecimal(cell).multi(step);\n      value = getMiniDecimal(min).add(nextVal.toString()).toNumber();\n    }\n    return value;\n  }\n  const dragLockRef = useRef(0);\n  const onTrackClick = event => {\n    if (dragLockRef.current > 0) return;\n    event.stopPropagation();\n    if (disabled) return;\n    const track = trackRef.current;\n    if (!track) return;\n    const sliderOffsetLeft = track.getBoundingClientRect().left;\n    const position = (event.clientX - sliderOffsetLeft) / Math.ceil(track.offsetWidth) * (max - min) + min;\n    const targetValue = getValueByPosition(position);\n    let nextSliderValue;\n    if (props.range) {\n      // 移动的滑块采用就近原则\n      if (Math.abs(targetValue - sliderValue[0]) > Math.abs(targetValue - sliderValue[1])) {\n        nextSliderValue = [sliderValue[0], targetValue];\n      } else {\n        nextSliderValue = [targetValue, sliderValue[1]];\n      }\n    } else {\n      nextSliderValue = [props.min, targetValue];\n    }\n    setSliderValue(nextSliderValue);\n    onAfterChange(nextSliderValue);\n  };\n  const valueBeforeDragRef = useRef();\n  const renderThumb = index => {\n    return React.createElement(Thumb, {\n      key: index,\n      value: sliderValue[index],\n      min: min,\n      max: max,\n      disabled: disabled,\n      trackRef: trackRef,\n      icon: icon,\n      popover: props.popover,\n      residentPopover: props.residentPopover,\n      onDrag: (position, first, last) => {\n        if (first) {\n          dragLockRef.current += 1;\n          valueBeforeDragRef.current = sliderValue;\n        }\n        const val = getValueByPosition(position);\n        const valueBeforeDrag = valueBeforeDragRef.current;\n        if (!valueBeforeDrag) return;\n        const next = [...valueBeforeDrag];\n        next[index] = val;\n        setSliderValue(next);\n        if (last) {\n          onAfterChange(next);\n          window.setTimeout(() => {\n            dragLockRef.current -= 1;\n          }, 100);\n        }\n      },\n      \"aria-label\": props['aria-label']\n    });\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-disabled`]: disabled\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-track-container`,\n    onClick: onTrackClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-track`,\n    onClick: onTrackClick,\n    ref: trackRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-fill`,\n    style: {\n      width: fillSize,\n      left: fillStart\n    }\n  }), props.ticks && React.createElement(Ticks, {\n    points: pointList,\n    min: min,\n    max: max,\n    lowerBound: sliderValue[0],\n    upperBound: sliderValue[1]\n  }), props.range && renderThumb(0), renderThumb(1))), marks && React.createElement(Marks, {\n    min: min,\n    max: max,\n    marks: marks,\n    lowerBound: sliderValue[0],\n    upperBound: sliderValue[1]\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC9C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,IAAIC,OAAO,QAAQ,4BAA4B;AACpE,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,GAAG;EACRC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,KAAK;EACdC,eAAe,EAAE;AACnB,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGjB,UAAU,CAACK,YAAY,EAAEU,CAAC,CAAC;EACzC,MAAM;IACJT,GAAG;IACHC,GAAG;IACHI,QAAQ;IACRO,KAAK;IACLT,KAAK;IACLD,IAAI;IACJW;EACF,CAAC,GAAGF,KAAK;EACT,SAASG,SAASA,CAACC,GAAG,EAAE;IACtB,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAClC;EACA,SAASC,YAAYA,CAACC,KAAK,EAAE;IAC3B,OAAOT,KAAK,CAACP,KAAK,GAAGgB,KAAK,GAAG,CAACT,KAAK,CAACX,GAAG,EAAEoB,KAAK,CAAC;EACjD;EACA,SAASC,UAAUA,CAACD,KAAK,EAAEE,UAAU,EAAE;IACrC,MAAMC,OAAO,GAAGhC,cAAc,CAAC6B,KAAK,CAAC;IACrC,MAAMI,QAAQ,GAAGhC,OAAO,CAAC+B,OAAO,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAEH,UAAU,CAAC;IAC7D,OAAO/B,cAAc,CAACiC,QAAQ,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC5C;EACA,SAASC,YAAYA,CAACP,KAAK,EAAE;IAC3B,MAAMQ,gBAAgB,GAAGC,IAAI,CAAC5B,GAAG,CAAC6B,aAAa,CAAC5B,IAAI,CAAC,EAAE4B,aAAa,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEU,aAAa,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxG,OAAOT,KAAK,CAACP,KAAK,GAAGgB,KAAK,CAACW,GAAG,CAACC,CAAC,IAAIX,UAAU,CAACW,CAAC,EAAEJ,gBAAgB,CAAC,CAAC,GAAGP,UAAU,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEQ,gBAAgB,CAAC;EAC/G;EACA,SAASE,aAAaA,CAACG,CAAC,EAAE;IACxB,OAAO,CAAC,GAAGA,CAAC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;EAC5C;EACA,SAASC,aAAaA,CAAChB,KAAK,EAAE;IAC5B,IAAIV,EAAE;IACN,CAACA,EAAE,GAAGC,KAAK,CAACyB,aAAa,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,IAAI,CAAC1B,KAAK,EAAEgB,YAAY,CAACP,KAAK,CAAC,CAAC;EACrG;EACA,IAAIkB,UAAU,GAAG3B,KAAK,CAACS,KAAK;EAC5B,IAAIT,KAAK,CAACP,KAAK,IAAI,OAAOO,KAAK,CAACS,KAAK,KAAK,QAAQ,EAAE;IAClDvB,UAAU,CAAC,QAAQ,EAAE,iFAAiF,CAAC;IACvGyC,UAAU,GAAG,CAAC,CAAC,EAAE3B,KAAK,CAACS,KAAK,CAAC;EAC/B;EACA,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,aAAa,CAAC;IAC5CwB,KAAK,EAAEkB,UAAU;IACjBG,YAAY,EAAE,CAAC/B,EAAE,GAAGC,KAAK,CAAC8B,YAAY,MAAM,IAAI,IAAI/B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,KAAK,CAACP,KAAK,GAAG,CAACJ,GAAG,EAAEA,GAAG,CAAC,GAAGA,GAAG;IACvG0C,QAAQ,EAAE/B,KAAK,CAAC+B;EAClB,CAAC,CAAC;EACF,MAAMC,WAAW,GAAG7B,SAAS,CAACK,YAAY,CAACoB,QAAQ,CAAC,CAAC;EACrD,SAASK,cAAcA,CAACxB,KAAK,EAAE;IAC7B,MAAMyB,IAAI,GAAG/B,SAAS,CAACM,KAAK,CAAC;IAC7B,MAAM0B,OAAO,GAAGH,WAAW;IAC3B,IAAIE,IAAI,CAAC,CAAC,CAAC,KAAKC,OAAO,CAAC,CAAC,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,OAAO,CAAC,CAAC,CAAC,EAAE;IACtDN,WAAW,CAACb,YAAY,CAACkB,IAAI,CAAC,CAAC;EACjC;EACA,MAAME,QAAQ,GAAG7D,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM8D,QAAQ,GAAG,GAAG,GAAG,IAAIL,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI1C,GAAG,GAAGD,GAAG,CAAC,GAAG;EAC5E,MAAMiD,SAAS,GAAG,GAAG,GAAG,IAAIN,WAAW,CAAC,CAAC,CAAC,GAAG3C,GAAG,CAAC,IAAIC,GAAG,GAAGD,GAAG,CAAC,GAAG;EAClE;EACA,MAAMkD,SAAS,GAAGjE,OAAO,CAAC,MAAM;IAC9B,IAAI2B,KAAK,EAAE;MACT,OAAOuC,MAAM,CAACC,IAAI,CAACxC,KAAK,CAAC,CAACmB,GAAG,CAACsB,UAAU,CAAC,CAACrC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACjE,CAAC,MAAM,IAAIf,KAAK,EAAE;MAChB,MAAMmD,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIC,CAAC,GAAGhE,cAAc,CAACS,GAAG,CAAC,EAAEuD,CAAC,CAACC,UAAU,CAACjE,cAAc,CAACU,GAAG,CAAC,CAAC,EAAEsD,CAAC,GAAGA,CAAC,CAACE,GAAG,CAACvD,IAAI,CAAC,EAAE;QACpFoD,MAAM,CAACI,IAAI,CAACH,CAAC,CAAC7B,QAAQ,CAAC,CAAC,CAAC;MAC3B;MACA,OAAO4B,MAAM;IACf;IACA,OAAO,EAAE;EACX,CAAC,EAAE,CAAC1C,KAAK,EAAET,KAAK,EAAED,IAAI,EAAEF,GAAG,EAAEC,GAAG,CAAC,CAAC;EAClC,SAAS0D,kBAAkBA,CAACC,QAAQ,EAAE;IACpC,MAAMC,WAAW,GAAGD,QAAQ,GAAG5D,GAAG,GAAGA,GAAG,GAAG4D,QAAQ,GAAG3D,GAAG,GAAGA,GAAG,GAAG2D,QAAQ;IAC1E,IAAIxC,KAAK,GAAGpB,GAAG;IACf;IACA,IAAIkD,SAAS,CAACf,MAAM,EAAE;MACpBf,KAAK,GAAGzB,OAAO,CAACuD,SAAS,EAAEW,WAAW,CAAC;IACzC,CAAC,MAAM;MACL;MACA,MAAMC,IAAI,GAAGjC,IAAI,CAACkC,KAAK,CAAC,CAACF,WAAW,GAAG7D,GAAG,IAAIE,IAAI,CAAC;MACnD,MAAM8D,OAAO,GAAGzE,cAAc,CAACuE,IAAI,CAAC,CAACG,KAAK,CAAC/D,IAAI,CAAC;MAChDkB,KAAK,GAAG7B,cAAc,CAACS,GAAG,CAAC,CAACyD,GAAG,CAACO,OAAO,CAACvC,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAChE;IACA,OAAON,KAAK;EACd;EACA,MAAM8C,WAAW,GAAGhF,MAAM,CAAC,CAAC,CAAC;EAC7B,MAAMiF,YAAY,GAAGC,KAAK,IAAI;IAC5B,IAAIF,WAAW,CAACpB,OAAO,GAAG,CAAC,EAAE;IAC7BsB,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAIhE,QAAQ,EAAE;IACd,MAAMiE,KAAK,GAAGvB,QAAQ,CAACD,OAAO;IAC9B,IAAI,CAACwB,KAAK,EAAE;IACZ,MAAMC,gBAAgB,GAAGD,KAAK,CAACE,qBAAqB,CAAC,CAAC,CAACC,IAAI;IAC3D,MAAMb,QAAQ,GAAG,CAACQ,KAAK,CAACM,OAAO,GAAGH,gBAAgB,IAAI1C,IAAI,CAAC8C,IAAI,CAACL,KAAK,CAACM,WAAW,CAAC,IAAI3E,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG;IACtG,MAAM6E,WAAW,GAAGlB,kBAAkB,CAACC,QAAQ,CAAC;IAChD,IAAIkB,eAAe;IACnB,IAAInE,KAAK,CAACP,KAAK,EAAE;MACf;MACA,IAAIyB,IAAI,CAACkD,GAAG,CAACF,WAAW,GAAGlC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAGd,IAAI,CAACkD,GAAG,CAACF,WAAW,GAAGlC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACnFmC,eAAe,GAAG,CAACnC,WAAW,CAAC,CAAC,CAAC,EAAEkC,WAAW,CAAC;MACjD,CAAC,MAAM;QACLC,eAAe,GAAG,CAACD,WAAW,EAAElC,WAAW,CAAC,CAAC,CAAC,CAAC;MACjD;IACF,CAAC,MAAM;MACLmC,eAAe,GAAG,CAACnE,KAAK,CAACX,GAAG,EAAE6E,WAAW,CAAC;IAC5C;IACAjC,cAAc,CAACkC,eAAe,CAAC;IAC/B1C,aAAa,CAAC0C,eAAe,CAAC;EAChC,CAAC;EACD,MAAME,kBAAkB,GAAG9F,MAAM,CAAC,CAAC;EACnC,MAAM+F,WAAW,GAAGC,KAAK,IAAI;IAC3B,OAAOlG,KAAK,CAACmG,aAAa,CAAC1F,KAAK,EAAE;MAChC2F,GAAG,EAAEF,KAAK;MACV9D,KAAK,EAAEuB,WAAW,CAACuC,KAAK,CAAC;MACzBlF,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA,GAAG;MACRI,QAAQ,EAAEA,QAAQ;MAClB0C,QAAQ,EAAEA,QAAQ;MAClBlC,IAAI,EAAEA,IAAI;MACVP,OAAO,EAAEK,KAAK,CAACL,OAAO;MACtBC,eAAe,EAAEI,KAAK,CAACJ,eAAe;MACtC8E,MAAM,EAAEA,CAACzB,QAAQ,EAAE0B,KAAK,EAAEC,IAAI,KAAK;QACjC,IAAID,KAAK,EAAE;UACTpB,WAAW,CAACpB,OAAO,IAAI,CAAC;UACxBkC,kBAAkB,CAAClC,OAAO,GAAGH,WAAW;QAC1C;QACA,MAAM5B,GAAG,GAAG4C,kBAAkB,CAACC,QAAQ,CAAC;QACxC,MAAM4B,eAAe,GAAGR,kBAAkB,CAAClC,OAAO;QAClD,IAAI,CAAC0C,eAAe,EAAE;QACtB,MAAM3C,IAAI,GAAG,CAAC,GAAG2C,eAAe,CAAC;QACjC3C,IAAI,CAACqC,KAAK,CAAC,GAAGnE,GAAG;QACjB6B,cAAc,CAACC,IAAI,CAAC;QACpB,IAAI0C,IAAI,EAAE;UACRnD,aAAa,CAACS,IAAI,CAAC;UACnB4C,MAAM,CAACC,UAAU,CAAC,MAAM;YACtBxB,WAAW,CAACpB,OAAO,IAAI,CAAC;UAC1B,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MACD,YAAY,EAAEnC,KAAK,CAAC,YAAY;IAClC,CAAC,CAAC;EACJ,CAAC;EACD,OAAOxB,eAAe,CAACwB,KAAK,EAAE3B,KAAK,CAACmG,aAAa,CAAC,KAAK,EAAE;IACvDQ,SAAS,EAAEvG,UAAU,CAACU,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,WAAW,GAAGO;IAC/B,CAAC;EACH,CAAC,EAAErB,KAAK,CAACmG,aAAa,CAAC,KAAK,EAAE;IAC5BQ,SAAS,EAAE,GAAG7F,WAAW,kBAAkB;IAC3C8F,OAAO,EAAEzB;EACX,CAAC,EAAEnF,KAAK,CAACmG,aAAa,CAAC,KAAK,EAAE;IAC5BQ,SAAS,EAAE,GAAG7F,WAAW,QAAQ;IACjC8F,OAAO,EAAEzB,YAAY;IACrB0B,GAAG,EAAE9C;EACP,CAAC,EAAE/D,KAAK,CAACmG,aAAa,CAAC,KAAK,EAAE;IAC5BQ,SAAS,EAAE,GAAG7F,WAAW,OAAO;IAChCgG,KAAK,EAAE;MACLC,KAAK,EAAE/C,QAAQ;MACfyB,IAAI,EAAExB;IACR;EACF,CAAC,CAAC,EAAEtC,KAAK,CAACR,KAAK,IAAInB,KAAK,CAACmG,aAAa,CAAC9F,KAAK,EAAE;IAC5CiE,MAAM,EAAEJ,SAAS;IACjBlD,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACR+F,UAAU,EAAErD,WAAW,CAAC,CAAC,CAAC;IAC1BsD,UAAU,EAAEtD,WAAW,CAAC,CAAC;EAC3B,CAAC,CAAC,EAAEhC,KAAK,CAACP,KAAK,IAAI6E,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErE,KAAK,IAAI5B,KAAK,CAACmG,aAAa,CAAC7F,KAAK,EAAE;IACvFU,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRW,KAAK,EAAEA,KAAK;IACZoF,UAAU,EAAErD,WAAW,CAAC,CAAC,CAAC;IAC1BsD,UAAU,EAAEtD,WAAW,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}