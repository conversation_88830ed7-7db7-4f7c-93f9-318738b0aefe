{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\n// {[path]: count}\n// remove external when no used\nvar EXTERNAL_USED_COUNT = {};\nvar loadScript = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\nvar loadCss = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    });\n    // IE9+\n    var isLegacyIECss = 'hideFocus' in newCss_1;\n    // use preload in IE Edge (to detect load errors)\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\nvar useExternal = function (path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n    status = _a[0],\n    setStatus = _a[1];\n  var ref = useRef();\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n    var pathname = path.replace(/[|#].*$/, '');\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n    if (!ref.current) {\n      return;\n    }\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n    var handler = function (event) {\n      var _a;\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n      if (EXTERNAL_USED_COUNT[path] === 0 && !(options === null || options === void 0 ? void 0 : options.keepWhenUnused)) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\nexport default useExternal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}