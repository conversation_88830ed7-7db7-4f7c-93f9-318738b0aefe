{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"name\"];\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { HOOK_MARK } from \"./FieldContext\";\nimport { allPromiseFinish } from \"./utils/asyncUtil\";\nimport { defaultValidateMessages } from \"./utils/messages\";\nimport NameMap from \"./utils/NameMap\";\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue } from \"./utils/valueUtil\";\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n  _classCallCheck(this, FormStore);\n  _defineProperty(this, \"formHooked\", false);\n  _defineProperty(this, \"forceRootUpdate\", void 0);\n  _defineProperty(this, \"subscribable\", true);\n  _defineProperty(this, \"store\", {});\n  _defineProperty(this, \"fieldEntities\", []);\n  _defineProperty(this, \"initialValues\", {});\n  _defineProperty(this, \"callbacks\", {});\n  _defineProperty(this, \"validateMessages\", null);\n  _defineProperty(this, \"preserve\", null);\n  _defineProperty(this, \"lastValidatePromise\", null);\n  _defineProperty(this, \"getForm\", function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldValue: _this.setFieldValue,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  });\n  // ======================== Internal Hooks ========================\n  _defineProperty(this, \"getInternalHooks\", function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  });\n  _defineProperty(this, \"useSubscribe\", function (subscribable) {\n    _this.subscribable = subscribable;\n  });\n  /**\n   * Record prev Form unmount fieldEntities which config preserve false.\n   * This need to be refill with initialValues instead of store value.\n   */\n  _defineProperty(this, \"prevWithoutPreserves\", null);\n  /**\n   * First time `setInitialValues` should update store with initial value\n   */\n  _defineProperty(this, \"setInitialValues\", function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n    if (init) {\n      var _this$prevWithoutPres;\n      var nextStore = merge(initialValues, _this.store);\n\n      // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 || _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n      _this.updateStore(nextStore);\n    }\n  });\n  _defineProperty(this, \"destroyForm\", function () {\n    var prevWithoutPreserves = new NameMap();\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!_this.isMergedPreserve(entity.isPreserve())) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  });\n  _defineProperty(this, \"getInitialValue\", function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath);\n\n    // Not cloneDeep when without `namePath`\n    return namePath.length ? merge(initValue) : initValue;\n  });\n  _defineProperty(this, \"setCallbacks\", function (callbacks) {\n    _this.callbacks = callbacks;\n  });\n  _defineProperty(this, \"setValidateMessages\", function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  });\n  _defineProperty(this, \"setPreserve\", function (preserve) {\n    _this.preserve = preserve;\n  });\n  // ============================= Watch ============================\n  _defineProperty(this, \"watchList\", []);\n  _defineProperty(this, \"registerWatch\", function (callback) {\n    _this.watchList.push(callback);\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  });\n  _defineProperty(this, \"notifyWatch\", function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n      var allValues = _this.getFieldsValue(true);\n      _this.watchList.forEach(function (callback) {\n        callback(values, allValues, namePath);\n      });\n    }\n  });\n  // ========================== Dev Warning =========================\n  _defineProperty(this, \"timeoutId\", null);\n  _defineProperty(this, \"warningUnhooked\", function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  });\n  // ============================ Store =============================\n  _defineProperty(this, \"updateStore\", function (nextStore) {\n    _this.store = nextStore;\n  });\n  // ============================ Fields ============================\n  /**\n   * Get registered field entities.\n   * @param pure Only return field which has a `name`. Default: false\n   */\n  _defineProperty(this, \"getFieldEntities\", function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  });\n  _defineProperty(this, \"getFieldsMap\", function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n    return cache;\n  });\n  _defineProperty(this, \"getFieldEntitiesForNamePathList\", function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n    var cache = _this.getFieldsMap(true);\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  });\n  _defineProperty(this, \"getFieldsValue\", function (nameList, filterFunc) {\n    _this.warningUnhooked();\n\n    // Fill args\n    var mergedNameList;\n    var mergedFilterFunc;\n    var mergedStrict;\n    if (nameList === true || Array.isArray(nameList)) {\n      mergedNameList = nameList;\n      mergedFilterFunc = filterFunc;\n    } else if (nameList && _typeof(nameList) === 'object') {\n      mergedStrict = nameList.strict;\n      mergedFilterFunc = nameList.filter;\n    }\n    if (mergedNameList === true && !mergedFilterFunc) {\n      return _this.store;\n    }\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(mergedNameList) ? mergedNameList : null);\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _isListField, _ref3;\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath();\n\n      // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n      if (mergedStrict) {\n        var _isList, _ref2;\n        if ((_isList = (_ref2 = entity).isList) !== null && _isList !== void 0 && _isList.call(_ref2)) {\n          return;\n        }\n      } else if (!mergedNameList && (_isListField = (_ref3 = entity).isListField) !== null && _isListField !== void 0 && _isListField.call(_ref3)) {\n        return;\n      }\n      if (!mergedFilterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n        if (mergedFilterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  });\n  _defineProperty(this, \"getFieldValue\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  });\n  _defineProperty(this, \"getFieldsError\", function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  });\n  _defineProperty(this, \"getFieldError\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.errors;\n  });\n  _defineProperty(this, \"getFieldWarning\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.warnings;\n  });\n  _defineProperty(this, \"isFieldsTouched\", function () {\n    _this.warningUnhooked();\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var arg0 = args[0],\n      arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n    var fieldEntities = _this.getFieldEntities(true);\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    };\n\n    // ===== Will get fully compare when not config namePathList =====\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(function (entity) {\n        return isFieldTouched(entity) || entity.isList();\n      }) : fieldEntities.some(isFieldTouched);\n    }\n\n    // Generate a nest tree for validate\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath();\n\n      // Find matched entity and put into list\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    });\n\n    // Check if NameMap value is touched\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n    var namePathListEntities = map.map(function (_ref4) {\n      var value = _ref4.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  });\n  _defineProperty(this, \"isFieldTouched\", function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsTouched([name]);\n  });\n  _defineProperty(this, \"isFieldsValidating\", function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntities();\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  });\n  _defineProperty(this, \"isFieldValidating\", function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsValidating([name]);\n  });\n  /**\n   * Reset Field with field `initialValue` prop.\n   * Can pass `entities` or `namePathList` or just nothing.\n   */\n  _defineProperty(this, \"resetWithFieldInitialValue\", function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n    var fieldEntities = _this.getFieldEntities(true);\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath();\n\n      // Record only if has `initialValue`\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    });\n\n    // Reset\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n          var formInitialValue = _this.getInitialValue(namePath);\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath);\n              var isListField = field.isListField();\n\n              // Set `initialValue`\n              if (!isListField && (!info.skipExist || originValue === undefined)) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n    var requiredFieldEntities;\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n        if (records) {\n          var _requiredFieldEntitie;\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n    resetWithFields(requiredFieldEntities);\n  });\n  _defineProperty(this, \"resetFields\", function (nameList) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (!nameList) {\n      _this.updateStore(merge(_this.initialValues));\n      _this.resetWithFieldInitialValue();\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n      _this.notifyWatch();\n      return;\n    }\n\n    // Reset by `nameList`\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n    _this.notifyWatch(namePathList);\n  });\n  _defineProperty(this, \"setFields\", function (fields) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n        data = _objectWithoutProperties(fieldData, _excluded);\n      var namePath = getNamePath(name);\n      namePathList.push(namePath);\n\n      // Value\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n    _this.notifyWatch(namePathList);\n  });\n  _defineProperty(this, \"getFields\", function () {\n    var entities = _this.getFieldEntities(true);\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  });\n  // =========================== Observer ===========================\n  /**\n   * This only trigger when a field is on constructor to avoid we get initialValue too late\n   */\n  _defineProperty(this, \"initEntityValue\", function (entity) {\n    var initialValue = entity.props.initialValue;\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  });\n  _defineProperty(this, \"isMergedPreserve\", function (fieldPreserve) {\n    var mergedPreserve = fieldPreserve !== undefined ? fieldPreserve : _this.preserve;\n    return mergedPreserve !== null && mergedPreserve !== void 0 ? mergedPreserve : true;\n  });\n  _defineProperty(this, \"registerField\", function (entity) {\n    _this.fieldEntities.push(entity);\n    var namePath = entity.getNamePath();\n    _this.notifyWatch([namePath]);\n\n    // Set initial values\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    }\n\n    // un-register field callback\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      });\n\n      // Clean up store value if not preserve\n      if (!_this.isMergedPreserve(preserve) && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (\n            // Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true));\n\n          // Notify that field is unmount\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          });\n\n          // Dependencies update\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n      _this.notifyWatch([namePath]);\n    };\n  });\n  _defineProperty(this, \"dispatch\", function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n            value = action.value;\n          _this.updateValue(namePath, value);\n          break;\n        }\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n            triggerName = action.triggerName;\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n          break;\n        }\n      default:\n      // Currently we don't have other action. Do nothing.\n    }\n  });\n  _defineProperty(this, \"notifyObservers\", function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n      _this.getFieldEntities().forEach(function (_ref5) {\n        var onStoreChange = _ref5.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  });\n  /**\n   * Notify dependencies children with parent update\n   * We need delay to trigger validate in case Field is under render props\n   */\n  _defineProperty(this, \"triggerDependenciesUpdate\", function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n    return childrenFields;\n  });\n  _defineProperty(this, \"updateValue\", function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n    _this.updateStore(setValue(_this.store, namePath, value));\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n    _this.notifyWatch([namePath]);\n\n    // Dependencies update\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath);\n\n    // trigger callback function\n    var onValuesChange = _this.callbacks.onValuesChange;\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  });\n  // Let all child Field get update.\n  _defineProperty(this, \"setFieldsValue\", function (store) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (store) {\n      var nextStore = merge(_this.store, store);\n      _this.updateStore(nextStore);\n    }\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n    _this.notifyWatch();\n  });\n  _defineProperty(this, \"setFieldValue\", function (name, value) {\n    _this.setFields([{\n      name: name,\n      value: value\n    }]);\n  });\n  _defineProperty(this, \"getDependencyChildrenFields\", function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n    fillChildren(rootNamePath);\n    return childrenFields;\n  });\n  _defineProperty(this, \"triggerOnFieldsChange\", function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref6) {\n          var name = _ref6.name,\n            errors = _ref6.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n      var changedFields = fields.filter(function (_ref7) {\n        var fieldName = _ref7.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      if (changedFields.length) {\n        onFieldsChange(changedFields, fields);\n      }\n    }\n  });\n  // =========================== Validate ===========================\n  _defineProperty(this, \"validateFields\", function (arg1, arg2) {\n    _this.warningUnhooked();\n    var nameList;\n    var options;\n    if (Array.isArray(arg1) || typeof arg1 === 'string' || typeof arg2 === 'string') {\n      nameList = arg1;\n      options = arg2;\n    } else {\n      options = arg1;\n    }\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : [];\n\n    // Collect result in promise list\n    var promiseList = [];\n\n    // We temp save the path which need trigger for `onFieldsChange`\n    var TMP_SPLIT = String(Date.now());\n    var validateNamePathList = new Set();\n    var _ref8 = options || {},\n      recursive = _ref8.recursive,\n      dirty = _ref8.dirty;\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n\n      // Skip if without rule\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n\n      // Skip if only validate dirty field\n      if (dirty && !field.isFieldDirty()) {\n        return;\n      }\n      var fieldNamePath = field.getNamePath();\n      validateNamePathList.add(fieldNamePath.join(TMP_SPLIT));\n\n      // Add field validate rule in to promise list\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath, recursive)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options));\n\n        // Wrap promise with field\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var _ruleErrors$forEach;\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 || _ruleErrors$forEach.call(ruleErrors, function (_ref9) {\n            var warningOnly = _ref9.rule.warningOnly,\n              errors = _ref9.errors;\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise;\n\n    // Notify fields with rule that validate has finished and need update\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref10) {\n        var name = _ref10.name;\n        return name;\n      });\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    });\n\n    // Do not throw in console\n    returnPromise.catch(function (e) {\n      return e;\n    });\n\n    // `validating` changed. Trigger `onFieldsChange`\n    var triggerNamePathList = namePathList.filter(function (namePath) {\n      return validateNamePathList.has(namePath.join(TMP_SPLIT));\n    });\n    _this.triggerOnFieldsChange(triggerNamePathList);\n    return returnPromise;\n  });\n  // ============================ Submit ============================\n  _defineProperty(this, \"submit\", function () {\n    _this.warningUnhooked();\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  });\n  this.forceRootUpdate = forceRootUpdate;\n});\nfunction useForm(form) {\n  var formRef = React.useRef();\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n  return [formRef.current];\n}\nexport default useForm;", "map": {"version": 3, "names": ["_slicedToArray", "_objectSpread", "_objectWithoutProperties", "_toConsumableArray", "_typeof", "_createClass", "_classCallCheck", "_defineProperty", "_excluded", "merge", "warning", "React", "HOOK_MARK", "allPromiseFinish", "defaultValidateMessages", "NameMap", "cloneByNamePathList", "containsNamePath", "getNamePath", "getValue", "matchNamePath", "setValue", "FormStore", "forceRootUpdate", "_this", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "setFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "_init", "getInternalHooks", "key", "formHooked", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "setValidateMessages", "getFields", "setPreserve", "getInitialValue", "registerWatch", "subscribable", "initialValues", "init", "_this$prevWithoutPres", "nextStore", "store", "prevWithoutPreserves", "map", "_ref", "namePath", "updateStore", "getFieldEntities", "for<PERSON>ach", "entity", "isMergedPreserve", "isPreserve", "set", "initValue", "length", "callbacks", "validateMessages", "preserve", "callback", "watchList", "push", "filter", "fn", "arguments", "undefined", "values", "allValues", "process", "env", "NODE_ENV", "timeoutId", "window", "setTimeout", "pure", "fieldEntities", "field", "cache", "nameList", "getFieldsMap", "name", "get", "INVALIDATE_NAME_PATH", "filterFunc", "warningUnhooked", "mergedNameList", "mergedFilterFunc", "mergedStrict", "Array", "isArray", "strict", "getFieldEntitiesForNamePathList", "filteredNameList", "_isListField", "_ref3", "_isList", "_ref2", "isList", "call", "isListField", "meta", "getMeta", "index", "errors", "getErrors", "warnings", "getWarnings", "fieldError", "_len", "args", "_key", "arg0", "arg1", "namePathList", "isAllFieldsTouched", "every", "some", "shortNamePath", "fieldNamePath", "nameUnit", "i", "update", "list", "concat", "isNamePathListTouched", "entities", "namePathListEntities", "_ref4", "value", "testField", "info", "initialValue", "props", "records", "Set", "add", "reset<PERSON><PERSON><PERSON><PERSON>s", "formInitialValue", "join", "size", "originValue", "skipExist", "requiredFieldEntities", "_requiredF<PERSON><PERSON><PERSON><PERSON>", "apply", "r", "prevStore", "resetWithFieldInitialValue", "notifyObservers", "type", "notifyWatch", "fields", "fieldData", "data", "Object", "defineProperty", "prevValue", "fieldPreserve", "mergedPreserve", "source", "subNamePath", "item", "defaultValue", "_prevStore", "triggerDependenciesUpdate", "action", "updateValue", "_namePath", "triggerName", "mergedInfo", "_ref5", "onStoreChange", "childrenFields", "getDep<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "relatedFields", "onValuesChange", "changedValues", "trigger<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNamePath", "children", "dependencies2fields", "dependencies", "dependency", "dependencyNamePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filedErrors", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref6", "changed<PERSON>ields", "_ref7", "fieldName", "arg2", "options", "provideNameList", "promiseList", "TMP_SPLIT", "String", "Date", "now", "validateNamePathList", "_ref8", "recursive", "dirty", "rules", "promise", "validateRules", "then", "catch", "ruleErrors", "_ruleErrors$forEach", "mergedErrors", "mergedWarnings", "_ref9", "warningOnly", "rule", "Promise", "reject", "summaryPromise", "lastValidatePromise", "results", "resultNamePathList", "_ref10", "returnPromise", "resolve", "errorList", "result", "errorFields", "outOfDate", "e", "triggerNamePathList", "onFinish", "err", "console", "error", "onFinishFailed", "useForm", "form", "formRef", "useRef", "_React$useState", "useState", "_React$useState2", "forceUpdate", "current", "forceReRender", "formStore", "getForm"], "sources": ["C:/Users/<USER>/node_modules/rc-field-form/es/useForm.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"name\"];\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { HOOK_MARK } from \"./FieldContext\";\nimport { allPromiseFinish } from \"./utils/asyncUtil\";\nimport { defaultValidateMessages } from \"./utils/messages\";\nimport NameMap from \"./utils/NameMap\";\nimport { cloneByNamePathList, containsNamePath, getNamePath, getValue, matchNamePath, setValue } from \"./utils/valueUtil\";\nexport var FormStore = /*#__PURE__*/_createClass(function FormStore(forceRootUpdate) {\n  var _this = this;\n  _classCallCheck(this, FormStore);\n  _defineProperty(this, \"formHooked\", false);\n  _defineProperty(this, \"forceRootUpdate\", void 0);\n  _defineProperty(this, \"subscribable\", true);\n  _defineProperty(this, \"store\", {});\n  _defineProperty(this, \"fieldEntities\", []);\n  _defineProperty(this, \"initialValues\", {});\n  _defineProperty(this, \"callbacks\", {});\n  _defineProperty(this, \"validateMessages\", null);\n  _defineProperty(this, \"preserve\", null);\n  _defineProperty(this, \"lastValidatePromise\", null);\n  _defineProperty(this, \"getForm\", function () {\n    return {\n      getFieldValue: _this.getFieldValue,\n      getFieldsValue: _this.getFieldsValue,\n      getFieldError: _this.getFieldError,\n      getFieldWarning: _this.getFieldWarning,\n      getFieldsError: _this.getFieldsError,\n      isFieldsTouched: _this.isFieldsTouched,\n      isFieldTouched: _this.isFieldTouched,\n      isFieldValidating: _this.isFieldValidating,\n      isFieldsValidating: _this.isFieldsValidating,\n      resetFields: _this.resetFields,\n      setFields: _this.setFields,\n      setFieldValue: _this.setFieldValue,\n      setFieldsValue: _this.setFieldsValue,\n      validateFields: _this.validateFields,\n      submit: _this.submit,\n      _init: true,\n      getInternalHooks: _this.getInternalHooks\n    };\n  });\n  // ======================== Internal Hooks ========================\n  _defineProperty(this, \"getInternalHooks\", function (key) {\n    if (key === HOOK_MARK) {\n      _this.formHooked = true;\n      return {\n        dispatch: _this.dispatch,\n        initEntityValue: _this.initEntityValue,\n        registerField: _this.registerField,\n        useSubscribe: _this.useSubscribe,\n        setInitialValues: _this.setInitialValues,\n        destroyForm: _this.destroyForm,\n        setCallbacks: _this.setCallbacks,\n        setValidateMessages: _this.setValidateMessages,\n        getFields: _this.getFields,\n        setPreserve: _this.setPreserve,\n        getInitialValue: _this.getInitialValue,\n        registerWatch: _this.registerWatch\n      };\n    }\n    warning(false, '`getInternalHooks` is internal usage. Should not call directly.');\n    return null;\n  });\n  _defineProperty(this, \"useSubscribe\", function (subscribable) {\n    _this.subscribable = subscribable;\n  });\n  /**\n   * Record prev Form unmount fieldEntities which config preserve false.\n   * This need to be refill with initialValues instead of store value.\n   */\n  _defineProperty(this, \"prevWithoutPreserves\", null);\n  /**\n   * First time `setInitialValues` should update store with initial value\n   */\n  _defineProperty(this, \"setInitialValues\", function (initialValues, init) {\n    _this.initialValues = initialValues || {};\n    if (init) {\n      var _this$prevWithoutPres;\n      var nextStore = merge(initialValues, _this.store);\n\n      // We will take consider prev form unmount fields.\n      // When the field is not `preserve`, we need fill this with initialValues instead of store.\n      // eslint-disable-next-line array-callback-return\n      (_this$prevWithoutPres = _this.prevWithoutPreserves) === null || _this$prevWithoutPres === void 0 || _this$prevWithoutPres.map(function (_ref) {\n        var namePath = _ref.key;\n        nextStore = setValue(nextStore, namePath, getValue(initialValues, namePath));\n      });\n      _this.prevWithoutPreserves = null;\n      _this.updateStore(nextStore);\n    }\n  });\n  _defineProperty(this, \"destroyForm\", function () {\n    var prevWithoutPreserves = new NameMap();\n    _this.getFieldEntities(true).forEach(function (entity) {\n      if (!_this.isMergedPreserve(entity.isPreserve())) {\n        prevWithoutPreserves.set(entity.getNamePath(), true);\n      }\n    });\n    _this.prevWithoutPreserves = prevWithoutPreserves;\n  });\n  _defineProperty(this, \"getInitialValue\", function (namePath) {\n    var initValue = getValue(_this.initialValues, namePath);\n\n    // Not cloneDeep when without `namePath`\n    return namePath.length ? merge(initValue) : initValue;\n  });\n  _defineProperty(this, \"setCallbacks\", function (callbacks) {\n    _this.callbacks = callbacks;\n  });\n  _defineProperty(this, \"setValidateMessages\", function (validateMessages) {\n    _this.validateMessages = validateMessages;\n  });\n  _defineProperty(this, \"setPreserve\", function (preserve) {\n    _this.preserve = preserve;\n  });\n  // ============================= Watch ============================\n  _defineProperty(this, \"watchList\", []);\n  _defineProperty(this, \"registerWatch\", function (callback) {\n    _this.watchList.push(callback);\n    return function () {\n      _this.watchList = _this.watchList.filter(function (fn) {\n        return fn !== callback;\n      });\n    };\n  });\n  _defineProperty(this, \"notifyWatch\", function () {\n    var namePath = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    // No need to cost perf when nothing need to watch\n    if (_this.watchList.length) {\n      var values = _this.getFieldsValue();\n      var allValues = _this.getFieldsValue(true);\n      _this.watchList.forEach(function (callback) {\n        callback(values, allValues, namePath);\n      });\n    }\n  });\n  // ========================== Dev Warning =========================\n  _defineProperty(this, \"timeoutId\", null);\n  _defineProperty(this, \"warningUnhooked\", function () {\n    if (process.env.NODE_ENV !== 'production' && !_this.timeoutId && typeof window !== 'undefined') {\n      _this.timeoutId = setTimeout(function () {\n        _this.timeoutId = null;\n        if (!_this.formHooked) {\n          warning(false, 'Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?');\n        }\n      });\n    }\n  });\n  // ============================ Store =============================\n  _defineProperty(this, \"updateStore\", function (nextStore) {\n    _this.store = nextStore;\n  });\n  // ============================ Fields ============================\n  /**\n   * Get registered field entities.\n   * @param pure Only return field which has a `name`. Default: false\n   */\n  _defineProperty(this, \"getFieldEntities\", function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!pure) {\n      return _this.fieldEntities;\n    }\n    return _this.fieldEntities.filter(function (field) {\n      return field.getNamePath().length;\n    });\n  });\n  _defineProperty(this, \"getFieldsMap\", function () {\n    var pure = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var cache = new NameMap();\n    _this.getFieldEntities(pure).forEach(function (field) {\n      var namePath = field.getNamePath();\n      cache.set(namePath, field);\n    });\n    return cache;\n  });\n  _defineProperty(this, \"getFieldEntitiesForNamePathList\", function (nameList) {\n    if (!nameList) {\n      return _this.getFieldEntities(true);\n    }\n    var cache = _this.getFieldsMap(true);\n    return nameList.map(function (name) {\n      var namePath = getNamePath(name);\n      return cache.get(namePath) || {\n        INVALIDATE_NAME_PATH: getNamePath(name)\n      };\n    });\n  });\n  _defineProperty(this, \"getFieldsValue\", function (nameList, filterFunc) {\n    _this.warningUnhooked();\n\n    // Fill args\n    var mergedNameList;\n    var mergedFilterFunc;\n    var mergedStrict;\n    if (nameList === true || Array.isArray(nameList)) {\n      mergedNameList = nameList;\n      mergedFilterFunc = filterFunc;\n    } else if (nameList && _typeof(nameList) === 'object') {\n      mergedStrict = nameList.strict;\n      mergedFilterFunc = nameList.filter;\n    }\n    if (mergedNameList === true && !mergedFilterFunc) {\n      return _this.store;\n    }\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(Array.isArray(mergedNameList) ? mergedNameList : null);\n    var filteredNameList = [];\n    fieldEntities.forEach(function (entity) {\n      var _isListField, _ref3;\n      var namePath = 'INVALIDATE_NAME_PATH' in entity ? entity.INVALIDATE_NAME_PATH : entity.getNamePath();\n\n      // Ignore when it's a list item and not specific the namePath,\n      // since parent field is already take in count\n      if (mergedStrict) {\n        var _isList, _ref2;\n        if ((_isList = (_ref2 = entity).isList) !== null && _isList !== void 0 && _isList.call(_ref2)) {\n          return;\n        }\n      } else if (!mergedNameList && (_isListField = (_ref3 = entity).isListField) !== null && _isListField !== void 0 && _isListField.call(_ref3)) {\n        return;\n      }\n      if (!mergedFilterFunc) {\n        filteredNameList.push(namePath);\n      } else {\n        var meta = 'getMeta' in entity ? entity.getMeta() : null;\n        if (mergedFilterFunc(meta)) {\n          filteredNameList.push(namePath);\n        }\n      }\n    });\n    return cloneByNamePathList(_this.store, filteredNameList.map(getNamePath));\n  });\n  _defineProperty(this, \"getFieldValue\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    return getValue(_this.store, namePath);\n  });\n  _defineProperty(this, \"getFieldsError\", function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntitiesForNamePathList(nameList);\n    return fieldEntities.map(function (entity, index) {\n      if (entity && !('INVALIDATE_NAME_PATH' in entity)) {\n        return {\n          name: entity.getNamePath(),\n          errors: entity.getErrors(),\n          warnings: entity.getWarnings()\n        };\n      }\n      return {\n        name: getNamePath(nameList[index]),\n        errors: [],\n        warnings: []\n      };\n    });\n  });\n  _defineProperty(this, \"getFieldError\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.errors;\n  });\n  _defineProperty(this, \"getFieldWarning\", function (name) {\n    _this.warningUnhooked();\n    var namePath = getNamePath(name);\n    var fieldError = _this.getFieldsError([namePath])[0];\n    return fieldError.warnings;\n  });\n  _defineProperty(this, \"isFieldsTouched\", function () {\n    _this.warningUnhooked();\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var arg0 = args[0],\n      arg1 = args[1];\n    var namePathList;\n    var isAllFieldsTouched = false;\n    if (args.length === 0) {\n      namePathList = null;\n    } else if (args.length === 1) {\n      if (Array.isArray(arg0)) {\n        namePathList = arg0.map(getNamePath);\n        isAllFieldsTouched = false;\n      } else {\n        namePathList = null;\n        isAllFieldsTouched = arg0;\n      }\n    } else {\n      namePathList = arg0.map(getNamePath);\n      isAllFieldsTouched = arg1;\n    }\n    var fieldEntities = _this.getFieldEntities(true);\n    var isFieldTouched = function isFieldTouched(field) {\n      return field.isFieldTouched();\n    };\n\n    // ===== Will get fully compare when not config namePathList =====\n    if (!namePathList) {\n      return isAllFieldsTouched ? fieldEntities.every(function (entity) {\n        return isFieldTouched(entity) || entity.isList();\n      }) : fieldEntities.some(isFieldTouched);\n    }\n\n    // Generate a nest tree for validate\n    var map = new NameMap();\n    namePathList.forEach(function (shortNamePath) {\n      map.set(shortNamePath, []);\n    });\n    fieldEntities.forEach(function (field) {\n      var fieldNamePath = field.getNamePath();\n\n      // Find matched entity and put into list\n      namePathList.forEach(function (shortNamePath) {\n        if (shortNamePath.every(function (nameUnit, i) {\n          return fieldNamePath[i] === nameUnit;\n        })) {\n          map.update(shortNamePath, function (list) {\n            return [].concat(_toConsumableArray(list), [field]);\n          });\n        }\n      });\n    });\n\n    // Check if NameMap value is touched\n    var isNamePathListTouched = function isNamePathListTouched(entities) {\n      return entities.some(isFieldTouched);\n    };\n    var namePathListEntities = map.map(function (_ref4) {\n      var value = _ref4.value;\n      return value;\n    });\n    return isAllFieldsTouched ? namePathListEntities.every(isNamePathListTouched) : namePathListEntities.some(isNamePathListTouched);\n  });\n  _defineProperty(this, \"isFieldTouched\", function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsTouched([name]);\n  });\n  _defineProperty(this, \"isFieldsValidating\", function (nameList) {\n    _this.warningUnhooked();\n    var fieldEntities = _this.getFieldEntities();\n    if (!nameList) {\n      return fieldEntities.some(function (testField) {\n        return testField.isFieldValidating();\n      });\n    }\n    var namePathList = nameList.map(getNamePath);\n    return fieldEntities.some(function (testField) {\n      var fieldNamePath = testField.getNamePath();\n      return containsNamePath(namePathList, fieldNamePath) && testField.isFieldValidating();\n    });\n  });\n  _defineProperty(this, \"isFieldValidating\", function (name) {\n    _this.warningUnhooked();\n    return _this.isFieldsValidating([name]);\n  });\n  /**\n   * Reset Field with field `initialValue` prop.\n   * Can pass `entities` or `namePathList` or just nothing.\n   */\n  _defineProperty(this, \"resetWithFieldInitialValue\", function () {\n    var info = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    // Create cache\n    var cache = new NameMap();\n    var fieldEntities = _this.getFieldEntities(true);\n    fieldEntities.forEach(function (field) {\n      var initialValue = field.props.initialValue;\n      var namePath = field.getNamePath();\n\n      // Record only if has `initialValue`\n      if (initialValue !== undefined) {\n        var records = cache.get(namePath) || new Set();\n        records.add({\n          entity: field,\n          value: initialValue\n        });\n        cache.set(namePath, records);\n      }\n    });\n\n    // Reset\n    var resetWithFields = function resetWithFields(entities) {\n      entities.forEach(function (field) {\n        var initialValue = field.props.initialValue;\n        if (initialValue !== undefined) {\n          var namePath = field.getNamePath();\n          var formInitialValue = _this.getInitialValue(namePath);\n          if (formInitialValue !== undefined) {\n            // Warning if conflict with form initialValues and do not modify value\n            warning(false, \"Form already set 'initialValues' with path '\".concat(namePath.join('.'), \"'. Field can not overwrite it.\"));\n          } else {\n            var records = cache.get(namePath);\n            if (records && records.size > 1) {\n              // Warning if multiple field set `initialValue`and do not modify value\n              warning(false, \"Multiple Field with path '\".concat(namePath.join('.'), \"' set 'initialValue'. Can not decide which one to pick.\"));\n            } else if (records) {\n              var originValue = _this.getFieldValue(namePath);\n              var isListField = field.isListField();\n\n              // Set `initialValue`\n              if (!isListField && (!info.skipExist || originValue === undefined)) {\n                _this.updateStore(setValue(_this.store, namePath, _toConsumableArray(records)[0].value));\n              }\n            }\n          }\n        }\n      });\n    };\n    var requiredFieldEntities;\n    if (info.entities) {\n      requiredFieldEntities = info.entities;\n    } else if (info.namePathList) {\n      requiredFieldEntities = [];\n      info.namePathList.forEach(function (namePath) {\n        var records = cache.get(namePath);\n        if (records) {\n          var _requiredFieldEntitie;\n          (_requiredFieldEntitie = requiredFieldEntities).push.apply(_requiredFieldEntitie, _toConsumableArray(_toConsumableArray(records).map(function (r) {\n            return r.entity;\n          })));\n        }\n      });\n    } else {\n      requiredFieldEntities = fieldEntities;\n    }\n    resetWithFields(requiredFieldEntities);\n  });\n  _defineProperty(this, \"resetFields\", function (nameList) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (!nameList) {\n      _this.updateStore(merge(_this.initialValues));\n      _this.resetWithFieldInitialValue();\n      _this.notifyObservers(prevStore, null, {\n        type: 'reset'\n      });\n      _this.notifyWatch();\n      return;\n    }\n\n    // Reset by `nameList`\n    var namePathList = nameList.map(getNamePath);\n    namePathList.forEach(function (namePath) {\n      var initialValue = _this.getInitialValue(namePath);\n      _this.updateStore(setValue(_this.store, namePath, initialValue));\n    });\n    _this.resetWithFieldInitialValue({\n      namePathList: namePathList\n    });\n    _this.notifyObservers(prevStore, namePathList, {\n      type: 'reset'\n    });\n    _this.notifyWatch(namePathList);\n  });\n  _defineProperty(this, \"setFields\", function (fields) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    var namePathList = [];\n    fields.forEach(function (fieldData) {\n      var name = fieldData.name,\n        data = _objectWithoutProperties(fieldData, _excluded);\n      var namePath = getNamePath(name);\n      namePathList.push(namePath);\n\n      // Value\n      if ('value' in data) {\n        _this.updateStore(setValue(_this.store, namePath, data.value));\n      }\n      _this.notifyObservers(prevStore, [namePath], {\n        type: 'setField',\n        data: fieldData\n      });\n    });\n    _this.notifyWatch(namePathList);\n  });\n  _defineProperty(this, \"getFields\", function () {\n    var entities = _this.getFieldEntities(true);\n    var fields = entities.map(function (field) {\n      var namePath = field.getNamePath();\n      var meta = field.getMeta();\n      var fieldData = _objectSpread(_objectSpread({}, meta), {}, {\n        name: namePath,\n        value: _this.getFieldValue(namePath)\n      });\n      Object.defineProperty(fieldData, 'originRCField', {\n        value: true\n      });\n      return fieldData;\n    });\n    return fields;\n  });\n  // =========================== Observer ===========================\n  /**\n   * This only trigger when a field is on constructor to avoid we get initialValue too late\n   */\n  _defineProperty(this, \"initEntityValue\", function (entity) {\n    var initialValue = entity.props.initialValue;\n    if (initialValue !== undefined) {\n      var namePath = entity.getNamePath();\n      var prevValue = getValue(_this.store, namePath);\n      if (prevValue === undefined) {\n        _this.updateStore(setValue(_this.store, namePath, initialValue));\n      }\n    }\n  });\n  _defineProperty(this, \"isMergedPreserve\", function (fieldPreserve) {\n    var mergedPreserve = fieldPreserve !== undefined ? fieldPreserve : _this.preserve;\n    return mergedPreserve !== null && mergedPreserve !== void 0 ? mergedPreserve : true;\n  });\n  _defineProperty(this, \"registerField\", function (entity) {\n    _this.fieldEntities.push(entity);\n    var namePath = entity.getNamePath();\n    _this.notifyWatch([namePath]);\n\n    // Set initial values\n    if (entity.props.initialValue !== undefined) {\n      var prevStore = _this.store;\n      _this.resetWithFieldInitialValue({\n        entities: [entity],\n        skipExist: true\n      });\n      _this.notifyObservers(prevStore, [entity.getNamePath()], {\n        type: 'valueUpdate',\n        source: 'internal'\n      });\n    }\n\n    // un-register field callback\n    return function (isListField, preserve) {\n      var subNamePath = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      _this.fieldEntities = _this.fieldEntities.filter(function (item) {\n        return item !== entity;\n      });\n\n      // Clean up store value if not preserve\n      if (!_this.isMergedPreserve(preserve) && (!isListField || subNamePath.length > 1)) {\n        var defaultValue = isListField ? undefined : _this.getInitialValue(namePath);\n        if (namePath.length && _this.getFieldValue(namePath) !== defaultValue && _this.fieldEntities.every(function (field) {\n          return (\n            // Only reset when no namePath exist\n            !matchNamePath(field.getNamePath(), namePath)\n          );\n        })) {\n          var _prevStore = _this.store;\n          _this.updateStore(setValue(_prevStore, namePath, defaultValue, true));\n\n          // Notify that field is unmount\n          _this.notifyObservers(_prevStore, [namePath], {\n            type: 'remove'\n          });\n\n          // Dependencies update\n          _this.triggerDependenciesUpdate(_prevStore, namePath);\n        }\n      }\n      _this.notifyWatch([namePath]);\n    };\n  });\n  _defineProperty(this, \"dispatch\", function (action) {\n    switch (action.type) {\n      case 'updateValue':\n        {\n          var namePath = action.namePath,\n            value = action.value;\n          _this.updateValue(namePath, value);\n          break;\n        }\n      case 'validateField':\n        {\n          var _namePath = action.namePath,\n            triggerName = action.triggerName;\n          _this.validateFields([_namePath], {\n            triggerName: triggerName\n          });\n          break;\n        }\n      default:\n      // Currently we don't have other action. Do nothing.\n    }\n  });\n  _defineProperty(this, \"notifyObservers\", function (prevStore, namePathList, info) {\n    if (_this.subscribable) {\n      var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n        store: _this.getFieldsValue(true)\n      });\n      _this.getFieldEntities().forEach(function (_ref5) {\n        var onStoreChange = _ref5.onStoreChange;\n        onStoreChange(prevStore, namePathList, mergedInfo);\n      });\n    } else {\n      _this.forceRootUpdate();\n    }\n  });\n  /**\n   * Notify dependencies children with parent update\n   * We need delay to trigger validate in case Field is under render props\n   */\n  _defineProperty(this, \"triggerDependenciesUpdate\", function (prevStore, namePath) {\n    var childrenFields = _this.getDependencyChildrenFields(namePath);\n    if (childrenFields.length) {\n      _this.validateFields(childrenFields);\n    }\n    _this.notifyObservers(prevStore, childrenFields, {\n      type: 'dependenciesUpdate',\n      relatedFields: [namePath].concat(_toConsumableArray(childrenFields))\n    });\n    return childrenFields;\n  });\n  _defineProperty(this, \"updateValue\", function (name, value) {\n    var namePath = getNamePath(name);\n    var prevStore = _this.store;\n    _this.updateStore(setValue(_this.store, namePath, value));\n    _this.notifyObservers(prevStore, [namePath], {\n      type: 'valueUpdate',\n      source: 'internal'\n    });\n    _this.notifyWatch([namePath]);\n\n    // Dependencies update\n    var childrenFields = _this.triggerDependenciesUpdate(prevStore, namePath);\n\n    // trigger callback function\n    var onValuesChange = _this.callbacks.onValuesChange;\n    if (onValuesChange) {\n      var changedValues = cloneByNamePathList(_this.store, [namePath]);\n      onValuesChange(changedValues, _this.getFieldsValue());\n    }\n    _this.triggerOnFieldsChange([namePath].concat(_toConsumableArray(childrenFields)));\n  });\n  // Let all child Field get update.\n  _defineProperty(this, \"setFieldsValue\", function (store) {\n    _this.warningUnhooked();\n    var prevStore = _this.store;\n    if (store) {\n      var nextStore = merge(_this.store, store);\n      _this.updateStore(nextStore);\n    }\n    _this.notifyObservers(prevStore, null, {\n      type: 'valueUpdate',\n      source: 'external'\n    });\n    _this.notifyWatch();\n  });\n  _defineProperty(this, \"setFieldValue\", function (name, value) {\n    _this.setFields([{\n      name: name,\n      value: value\n    }]);\n  });\n  _defineProperty(this, \"getDependencyChildrenFields\", function (rootNamePath) {\n    var children = new Set();\n    var childrenFields = [];\n    var dependencies2fields = new NameMap();\n\n    /**\n     * Generate maps\n     * Can use cache to save perf if user report performance issue with this\n     */\n    _this.getFieldEntities().forEach(function (field) {\n      var dependencies = field.props.dependencies;\n      (dependencies || []).forEach(function (dependency) {\n        var dependencyNamePath = getNamePath(dependency);\n        dependencies2fields.update(dependencyNamePath, function () {\n          var fields = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : new Set();\n          fields.add(field);\n          return fields;\n        });\n      });\n    });\n    var fillChildren = function fillChildren(namePath) {\n      var fields = dependencies2fields.get(namePath) || new Set();\n      fields.forEach(function (field) {\n        if (!children.has(field)) {\n          children.add(field);\n          var fieldNamePath = field.getNamePath();\n          if (field.isFieldDirty() && fieldNamePath.length) {\n            childrenFields.push(fieldNamePath);\n            fillChildren(fieldNamePath);\n          }\n        }\n      });\n    };\n    fillChildren(rootNamePath);\n    return childrenFields;\n  });\n  _defineProperty(this, \"triggerOnFieldsChange\", function (namePathList, filedErrors) {\n    var onFieldsChange = _this.callbacks.onFieldsChange;\n    if (onFieldsChange) {\n      var fields = _this.getFields();\n\n      /**\n       * Fill errors since `fields` may be replaced by controlled fields\n       */\n      if (filedErrors) {\n        var cache = new NameMap();\n        filedErrors.forEach(function (_ref6) {\n          var name = _ref6.name,\n            errors = _ref6.errors;\n          cache.set(name, errors);\n        });\n        fields.forEach(function (field) {\n          // eslint-disable-next-line no-param-reassign\n          field.errors = cache.get(field.name) || field.errors;\n        });\n      }\n      var changedFields = fields.filter(function (_ref7) {\n        var fieldName = _ref7.name;\n        return containsNamePath(namePathList, fieldName);\n      });\n      if (changedFields.length) {\n        onFieldsChange(changedFields, fields);\n      }\n    }\n  });\n  // =========================== Validate ===========================\n  _defineProperty(this, \"validateFields\", function (arg1, arg2) {\n    _this.warningUnhooked();\n    var nameList;\n    var options;\n    if (Array.isArray(arg1) || typeof arg1 === 'string' || typeof arg2 === 'string') {\n      nameList = arg1;\n      options = arg2;\n    } else {\n      options = arg1;\n    }\n    var provideNameList = !!nameList;\n    var namePathList = provideNameList ? nameList.map(getNamePath) : [];\n\n    // Collect result in promise list\n    var promiseList = [];\n\n    // We temp save the path which need trigger for `onFieldsChange`\n    var TMP_SPLIT = String(Date.now());\n    var validateNamePathList = new Set();\n    var _ref8 = options || {},\n      recursive = _ref8.recursive,\n      dirty = _ref8.dirty;\n    _this.getFieldEntities(true).forEach(function (field) {\n      // Add field if not provide `nameList`\n      if (!provideNameList) {\n        namePathList.push(field.getNamePath());\n      }\n\n      // Skip if without rule\n      if (!field.props.rules || !field.props.rules.length) {\n        return;\n      }\n\n      // Skip if only validate dirty field\n      if (dirty && !field.isFieldDirty()) {\n        return;\n      }\n      var fieldNamePath = field.getNamePath();\n      validateNamePathList.add(fieldNamePath.join(TMP_SPLIT));\n\n      // Add field validate rule in to promise list\n      if (!provideNameList || containsNamePath(namePathList, fieldNamePath, recursive)) {\n        var promise = field.validateRules(_objectSpread({\n          validateMessages: _objectSpread(_objectSpread({}, defaultValidateMessages), _this.validateMessages)\n        }, options));\n\n        // Wrap promise with field\n        promiseList.push(promise.then(function () {\n          return {\n            name: fieldNamePath,\n            errors: [],\n            warnings: []\n          };\n        }).catch(function (ruleErrors) {\n          var _ruleErrors$forEach;\n          var mergedErrors = [];\n          var mergedWarnings = [];\n          (_ruleErrors$forEach = ruleErrors.forEach) === null || _ruleErrors$forEach === void 0 || _ruleErrors$forEach.call(ruleErrors, function (_ref9) {\n            var warningOnly = _ref9.rule.warningOnly,\n              errors = _ref9.errors;\n            if (warningOnly) {\n              mergedWarnings.push.apply(mergedWarnings, _toConsumableArray(errors));\n            } else {\n              mergedErrors.push.apply(mergedErrors, _toConsumableArray(errors));\n            }\n          });\n          if (mergedErrors.length) {\n            return Promise.reject({\n              name: fieldNamePath,\n              errors: mergedErrors,\n              warnings: mergedWarnings\n            });\n          }\n          return {\n            name: fieldNamePath,\n            errors: mergedErrors,\n            warnings: mergedWarnings\n          };\n        }));\n      }\n    });\n    var summaryPromise = allPromiseFinish(promiseList);\n    _this.lastValidatePromise = summaryPromise;\n\n    // Notify fields with rule that validate has finished and need update\n    summaryPromise.catch(function (results) {\n      return results;\n    }).then(function (results) {\n      var resultNamePathList = results.map(function (_ref10) {\n        var name = _ref10.name;\n        return name;\n      });\n      _this.notifyObservers(_this.store, resultNamePathList, {\n        type: 'validateFinish'\n      });\n      _this.triggerOnFieldsChange(resultNamePathList, results);\n    });\n    var returnPromise = summaryPromise.then(function () {\n      if (_this.lastValidatePromise === summaryPromise) {\n        return Promise.resolve(_this.getFieldsValue(namePathList));\n      }\n      return Promise.reject([]);\n    }).catch(function (results) {\n      var errorList = results.filter(function (result) {\n        return result && result.errors.length;\n      });\n      return Promise.reject({\n        values: _this.getFieldsValue(namePathList),\n        errorFields: errorList,\n        outOfDate: _this.lastValidatePromise !== summaryPromise\n      });\n    });\n\n    // Do not throw in console\n    returnPromise.catch(function (e) {\n      return e;\n    });\n\n    // `validating` changed. Trigger `onFieldsChange`\n    var triggerNamePathList = namePathList.filter(function (namePath) {\n      return validateNamePathList.has(namePath.join(TMP_SPLIT));\n    });\n    _this.triggerOnFieldsChange(triggerNamePathList);\n    return returnPromise;\n  });\n  // ============================ Submit ============================\n  _defineProperty(this, \"submit\", function () {\n    _this.warningUnhooked();\n    _this.validateFields().then(function (values) {\n      var onFinish = _this.callbacks.onFinish;\n      if (onFinish) {\n        try {\n          onFinish(values);\n        } catch (err) {\n          // Should print error if user `onFinish` callback failed\n          console.error(err);\n        }\n      }\n    }).catch(function (e) {\n      var onFinishFailed = _this.callbacks.onFinishFailed;\n      if (onFinishFailed) {\n        onFinishFailed(e);\n      }\n    });\n  });\n  this.forceRootUpdate = forceRootUpdate;\n});\nfunction useForm(form) {\n  var formRef = React.useRef();\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  if (!formRef.current) {\n    if (form) {\n      formRef.current = form;\n    } else {\n      // Create a new FormStore if not provided\n      var forceReRender = function forceReRender() {\n        forceUpdate({});\n      };\n      var formStore = new FormStore(forceReRender);\n      formRef.current = formStore.getForm();\n    }\n  }\n  return [formRef.current];\n}\nexport default useForm;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,MAAM,CAAC;AACxB,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,mBAAmB;AACzH,OAAO,IAAIC,SAAS,GAAG,aAAajB,YAAY,CAAC,SAASiB,SAASA,CAACC,eAAe,EAAE;EACnF,IAAIC,KAAK,GAAG,IAAI;EAChBlB,eAAe,CAAC,IAAI,EAAEgB,SAAS,CAAC;EAChCf,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC;EAC1CA,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;EAChDA,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC;EAC3CA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;EAClCA,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,EAAE,CAAC;EAC1CA,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;EAC1CA,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;EACtCA,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,CAAC;EAC/CA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;EACvCA,eAAe,CAAC,IAAI,EAAE,qBAAqB,EAAE,IAAI,CAAC;EAClDA,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,YAAY;IAC3C,OAAO;MACLkB,aAAa,EAAED,KAAK,CAACC,aAAa;MAClCC,cAAc,EAAEF,KAAK,CAACE,cAAc;MACpCC,aAAa,EAAEH,KAAK,CAACG,aAAa;MAClCC,eAAe,EAAEJ,KAAK,CAACI,eAAe;MACtCC,cAAc,EAAEL,KAAK,CAACK,cAAc;MACpCC,eAAe,EAAEN,KAAK,CAACM,eAAe;MACtCC,cAAc,EAAEP,KAAK,CAACO,cAAc;MACpCC,iBAAiB,EAAER,KAAK,CAACQ,iBAAiB;MAC1CC,kBAAkB,EAAET,KAAK,CAACS,kBAAkB;MAC5CC,WAAW,EAAEV,KAAK,CAACU,WAAW;MAC9BC,SAAS,EAAEX,KAAK,CAACW,SAAS;MAC1BC,aAAa,EAAEZ,KAAK,CAACY,aAAa;MAClCC,cAAc,EAAEb,KAAK,CAACa,cAAc;MACpCC,cAAc,EAAEd,KAAK,CAACc,cAAc;MACpCC,MAAM,EAAEf,KAAK,CAACe,MAAM;MACpBC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAEjB,KAAK,CAACiB;IAC1B,CAAC;EACH,CAAC,CAAC;EACF;EACAlC,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAUmC,GAAG,EAAE;IACvD,IAAIA,GAAG,KAAK9B,SAAS,EAAE;MACrBY,KAAK,CAACmB,UAAU,GAAG,IAAI;MACvB,OAAO;QACLC,QAAQ,EAAEpB,KAAK,CAACoB,QAAQ;QACxBC,eAAe,EAAErB,KAAK,CAACqB,eAAe;QACtCC,aAAa,EAAEtB,KAAK,CAACsB,aAAa;QAClCC,YAAY,EAAEvB,KAAK,CAACuB,YAAY;QAChCC,gBAAgB,EAAExB,KAAK,CAACwB,gBAAgB;QACxCC,WAAW,EAAEzB,KAAK,CAACyB,WAAW;QAC9BC,YAAY,EAAE1B,KAAK,CAAC0B,YAAY;QAChCC,mBAAmB,EAAE3B,KAAK,CAAC2B,mBAAmB;QAC9CC,SAAS,EAAE5B,KAAK,CAAC4B,SAAS;QAC1BC,WAAW,EAAE7B,KAAK,CAAC6B,WAAW;QAC9BC,eAAe,EAAE9B,KAAK,CAAC8B,eAAe;QACtCC,aAAa,EAAE/B,KAAK,CAAC+B;MACvB,CAAC;IACH;IACA7C,OAAO,CAAC,KAAK,EAAE,iEAAiE,CAAC;IACjF,OAAO,IAAI;EACb,CAAC,CAAC;EACFH,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,UAAUiD,YAAY,EAAE;IAC5DhC,KAAK,CAACgC,YAAY,GAAGA,YAAY;EACnC,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEjD,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,IAAI,CAAC;EACnD;AACF;AACA;EACEA,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAUkD,aAAa,EAAEC,IAAI,EAAE;IACvElC,KAAK,CAACiC,aAAa,GAAGA,aAAa,IAAI,CAAC,CAAC;IACzC,IAAIC,IAAI,EAAE;MACR,IAAIC,qBAAqB;MACzB,IAAIC,SAAS,GAAGnD,KAAK,CAACgD,aAAa,EAAEjC,KAAK,CAACqC,KAAK,CAAC;;MAEjD;MACA;MACA;MACA,CAACF,qBAAqB,GAAGnC,KAAK,CAACsC,oBAAoB,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC7I,IAAIC,QAAQ,GAAGD,IAAI,CAACtB,GAAG;QACvBkB,SAAS,GAAGvC,QAAQ,CAACuC,SAAS,EAAEK,QAAQ,EAAE9C,QAAQ,CAACsC,aAAa,EAAEQ,QAAQ,CAAC,CAAC;MAC9E,CAAC,CAAC;MACFzC,KAAK,CAACsC,oBAAoB,GAAG,IAAI;MACjCtC,KAAK,CAAC0C,WAAW,CAACN,SAAS,CAAC;IAC9B;EACF,CAAC,CAAC;EACFrD,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,YAAY;IAC/C,IAAIuD,oBAAoB,GAAG,IAAI/C,OAAO,CAAC,CAAC;IACxCS,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MACrD,IAAI,CAAC7C,KAAK,CAAC8C,gBAAgB,CAACD,MAAM,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;QAChDT,oBAAoB,CAACU,GAAG,CAACH,MAAM,CAACnD,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;MACtD;IACF,CAAC,CAAC;IACFM,KAAK,CAACsC,oBAAoB,GAAGA,oBAAoB;EACnD,CAAC,CAAC;EACFvD,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,UAAU0D,QAAQ,EAAE;IAC3D,IAAIQ,SAAS,GAAGtD,QAAQ,CAACK,KAAK,CAACiC,aAAa,EAAEQ,QAAQ,CAAC;;IAEvD;IACA,OAAOA,QAAQ,CAACS,MAAM,GAAGjE,KAAK,CAACgE,SAAS,CAAC,GAAGA,SAAS;EACvD,CAAC,CAAC;EACFlE,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,UAAUoE,SAAS,EAAE;IACzDnD,KAAK,CAACmD,SAAS,GAAGA,SAAS;EAC7B,CAAC,CAAC;EACFpE,eAAe,CAAC,IAAI,EAAE,qBAAqB,EAAE,UAAUqE,gBAAgB,EAAE;IACvEpD,KAAK,CAACoD,gBAAgB,GAAGA,gBAAgB;EAC3C,CAAC,CAAC;EACFrE,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAUsE,QAAQ,EAAE;IACvDrD,KAAK,CAACqD,QAAQ,GAAGA,QAAQ;EAC3B,CAAC,CAAC;EACF;EACAtE,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC;EACtCA,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAUuE,QAAQ,EAAE;IACzDtD,KAAK,CAACuD,SAAS,CAACC,IAAI,CAACF,QAAQ,CAAC;IAC9B,OAAO,YAAY;MACjBtD,KAAK,CAACuD,SAAS,GAAGvD,KAAK,CAACuD,SAAS,CAACE,MAAM,CAAC,UAAUC,EAAE,EAAE;QACrD,OAAOA,EAAE,KAAKJ,QAAQ;MACxB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;EACFvE,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,YAAY;IAC/C,IAAI0D,QAAQ,GAAGkB,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACrF;IACA,IAAI3D,KAAK,CAACuD,SAAS,CAACL,MAAM,EAAE;MAC1B,IAAIW,MAAM,GAAG7D,KAAK,CAACE,cAAc,CAAC,CAAC;MACnC,IAAI4D,SAAS,GAAG9D,KAAK,CAACE,cAAc,CAAC,IAAI,CAAC;MAC1CF,KAAK,CAACuD,SAAS,CAACX,OAAO,CAAC,UAAUU,QAAQ,EAAE;QAC1CA,QAAQ,CAACO,MAAM,EAAEC,SAAS,EAAErB,QAAQ,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF;EACA1D,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC;EACxCA,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,YAAY;IACnD,IAAIgF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACjE,KAAK,CAACkE,SAAS,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAC9FnE,KAAK,CAACkE,SAAS,GAAGE,UAAU,CAAC,YAAY;QACvCpE,KAAK,CAACkE,SAAS,GAAG,IAAI;QACtB,IAAI,CAAClE,KAAK,CAACmB,UAAU,EAAE;UACrBjC,OAAO,CAAC,KAAK,EAAE,iGAAiG,CAAC;QACnH;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF;EACAH,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAUqD,SAAS,EAAE;IACxDpC,KAAK,CAACqC,KAAK,GAAGD,SAAS;EACzB,CAAC,CAAC;EACF;EACA;AACF;AACA;AACA;EACErD,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,YAAY;IACpD,IAAIsF,IAAI,GAAGV,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpF,IAAI,CAACU,IAAI,EAAE;MACT,OAAOrE,KAAK,CAACsE,aAAa;IAC5B;IACA,OAAOtE,KAAK,CAACsE,aAAa,CAACb,MAAM,CAAC,UAAUc,KAAK,EAAE;MACjD,OAAOA,KAAK,CAAC7E,WAAW,CAAC,CAAC,CAACwD,MAAM;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EACFnE,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,YAAY;IAChD,IAAIsF,IAAI,GAAGV,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACpF,IAAIa,KAAK,GAAG,IAAIjF,OAAO,CAAC,CAAC;IACzBS,KAAK,CAAC2C,gBAAgB,CAAC0B,IAAI,CAAC,CAACzB,OAAO,CAAC,UAAU2B,KAAK,EAAE;MACpD,IAAI9B,QAAQ,GAAG8B,KAAK,CAAC7E,WAAW,CAAC,CAAC;MAClC8E,KAAK,CAACxB,GAAG,CAACP,QAAQ,EAAE8B,KAAK,CAAC;IAC5B,CAAC,CAAC;IACF,OAAOC,KAAK;EACd,CAAC,CAAC;EACFzF,eAAe,CAAC,IAAI,EAAE,iCAAiC,EAAE,UAAU0F,QAAQ,EAAE;IAC3E,IAAI,CAACA,QAAQ,EAAE;MACb,OAAOzE,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC;IACrC;IACA,IAAI6B,KAAK,GAAGxE,KAAK,CAAC0E,YAAY,CAAC,IAAI,CAAC;IACpC,OAAOD,QAAQ,CAAClC,GAAG,CAAC,UAAUoC,IAAI,EAAE;MAClC,IAAIlC,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;MAChC,OAAOH,KAAK,CAACI,GAAG,CAACnC,QAAQ,CAAC,IAAI;QAC5BoC,oBAAoB,EAAEnF,WAAW,CAACiF,IAAI;MACxC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EACF5F,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU0F,QAAQ,EAAEK,UAAU,EAAE;IACtE9E,KAAK,CAAC+E,eAAe,CAAC,CAAC;;IAEvB;IACA,IAAIC,cAAc;IAClB,IAAIC,gBAAgB;IACpB,IAAIC,YAAY;IAChB,IAAIT,QAAQ,KAAK,IAAI,IAAIU,KAAK,CAACC,OAAO,CAACX,QAAQ,CAAC,EAAE;MAChDO,cAAc,GAAGP,QAAQ;MACzBQ,gBAAgB,GAAGH,UAAU;IAC/B,CAAC,MAAM,IAAIL,QAAQ,IAAI7F,OAAO,CAAC6F,QAAQ,CAAC,KAAK,QAAQ,EAAE;MACrDS,YAAY,GAAGT,QAAQ,CAACY,MAAM;MAC9BJ,gBAAgB,GAAGR,QAAQ,CAAChB,MAAM;IACpC;IACA,IAAIuB,cAAc,KAAK,IAAI,IAAI,CAACC,gBAAgB,EAAE;MAChD,OAAOjF,KAAK,CAACqC,KAAK;IACpB;IACA,IAAIiC,aAAa,GAAGtE,KAAK,CAACsF,+BAA+B,CAACH,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,GAAGA,cAAc,GAAG,IAAI,CAAC;IAChH,IAAIO,gBAAgB,GAAG,EAAE;IACzBjB,aAAa,CAAC1B,OAAO,CAAC,UAAUC,MAAM,EAAE;MACtC,IAAI2C,YAAY,EAAEC,KAAK;MACvB,IAAIhD,QAAQ,GAAG,sBAAsB,IAAII,MAAM,GAAGA,MAAM,CAACgC,oBAAoB,GAAGhC,MAAM,CAACnD,WAAW,CAAC,CAAC;;MAEpG;MACA;MACA,IAAIwF,YAAY,EAAE;QAChB,IAAIQ,OAAO,EAAEC,KAAK;QAClB,IAAI,CAACD,OAAO,GAAG,CAACC,KAAK,GAAG9C,MAAM,EAAE+C,MAAM,MAAM,IAAI,IAAIF,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,IAAI,CAACF,KAAK,CAAC,EAAE;UAC7F;QACF;MACF,CAAC,MAAM,IAAI,CAACX,cAAc,IAAI,CAACQ,YAAY,GAAG,CAACC,KAAK,GAAG5C,MAAM,EAAEiD,WAAW,MAAM,IAAI,IAAIN,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACK,IAAI,CAACJ,KAAK,CAAC,EAAE;QAC3I;MACF;MACA,IAAI,CAACR,gBAAgB,EAAE;QACrBM,gBAAgB,CAAC/B,IAAI,CAACf,QAAQ,CAAC;MACjC,CAAC,MAAM;QACL,IAAIsD,IAAI,GAAG,SAAS,IAAIlD,MAAM,GAAGA,MAAM,CAACmD,OAAO,CAAC,CAAC,GAAG,IAAI;QACxD,IAAIf,gBAAgB,CAACc,IAAI,CAAC,EAAE;UAC1BR,gBAAgB,CAAC/B,IAAI,CAACf,QAAQ,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACF,OAAOjD,mBAAmB,CAACQ,KAAK,CAACqC,KAAK,EAAEkD,gBAAgB,CAAChD,GAAG,CAAC7C,WAAW,CAAC,CAAC;EAC5E,CAAC,CAAC;EACFX,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU4F,IAAI,EAAE;IACrD3E,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAItC,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;IAChC,OAAOhF,QAAQ,CAACK,KAAK,CAACqC,KAAK,EAAEI,QAAQ,CAAC;EACxC,CAAC,CAAC;EACF1D,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU0F,QAAQ,EAAE;IAC1DzE,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAIT,aAAa,GAAGtE,KAAK,CAACsF,+BAA+B,CAACb,QAAQ,CAAC;IACnE,OAAOH,aAAa,CAAC/B,GAAG,CAAC,UAAUM,MAAM,EAAEoD,KAAK,EAAE;MAChD,IAAIpD,MAAM,IAAI,EAAE,sBAAsB,IAAIA,MAAM,CAAC,EAAE;QACjD,OAAO;UACL8B,IAAI,EAAE9B,MAAM,CAACnD,WAAW,CAAC,CAAC;UAC1BwG,MAAM,EAAErD,MAAM,CAACsD,SAAS,CAAC,CAAC;UAC1BC,QAAQ,EAAEvD,MAAM,CAACwD,WAAW,CAAC;QAC/B,CAAC;MACH;MACA,OAAO;QACL1B,IAAI,EAAEjF,WAAW,CAAC+E,QAAQ,CAACwB,KAAK,CAAC,CAAC;QAClCC,MAAM,EAAE,EAAE;QACVE,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;EACFrH,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU4F,IAAI,EAAE;IACrD3E,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAItC,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;IAChC,IAAI2B,UAAU,GAAGtG,KAAK,CAACK,cAAc,CAAC,CAACoC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,OAAO6D,UAAU,CAACJ,MAAM;EAC1B,CAAC,CAAC;EACFnH,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,UAAU4F,IAAI,EAAE;IACvD3E,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAItC,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;IAChC,IAAI2B,UAAU,GAAGtG,KAAK,CAACK,cAAc,CAAC,CAACoC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,OAAO6D,UAAU,CAACF,QAAQ;EAC5B,CAAC,CAAC;EACFrH,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,YAAY;IACnDiB,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,KAAK,IAAIwB,IAAI,GAAG5C,SAAS,CAACT,MAAM,EAAEsD,IAAI,GAAG,IAAIrB,KAAK,CAACoB,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAG9C,SAAS,CAAC8C,IAAI,CAAC;IAC9B;IACA,IAAIC,IAAI,GAAGF,IAAI,CAAC,CAAC,CAAC;MAChBG,IAAI,GAAGH,IAAI,CAAC,CAAC,CAAC;IAChB,IAAII,YAAY;IAChB,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIL,IAAI,CAACtD,MAAM,KAAK,CAAC,EAAE;MACrB0D,YAAY,GAAG,IAAI;IACrB,CAAC,MAAM,IAAIJ,IAAI,CAACtD,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAIiC,KAAK,CAACC,OAAO,CAACsB,IAAI,CAAC,EAAE;QACvBE,YAAY,GAAGF,IAAI,CAACnE,GAAG,CAAC7C,WAAW,CAAC;QACpCmH,kBAAkB,GAAG,KAAK;MAC5B,CAAC,MAAM;QACLD,YAAY,GAAG,IAAI;QACnBC,kBAAkB,GAAGH,IAAI;MAC3B;IACF,CAAC,MAAM;MACLE,YAAY,GAAGF,IAAI,CAACnE,GAAG,CAAC7C,WAAW,CAAC;MACpCmH,kBAAkB,GAAGF,IAAI;IAC3B;IACA,IAAIrC,aAAa,GAAGtE,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC;IAChD,IAAIpC,cAAc,GAAG,SAASA,cAAcA,CAACgE,KAAK,EAAE;MAClD,OAAOA,KAAK,CAAChE,cAAc,CAAC,CAAC;IAC/B,CAAC;;IAED;IACA,IAAI,CAACqG,YAAY,EAAE;MACjB,OAAOC,kBAAkB,GAAGvC,aAAa,CAACwC,KAAK,CAAC,UAAUjE,MAAM,EAAE;QAChE,OAAOtC,cAAc,CAACsC,MAAM,CAAC,IAAIA,MAAM,CAAC+C,MAAM,CAAC,CAAC;MAClD,CAAC,CAAC,GAAGtB,aAAa,CAACyC,IAAI,CAACxG,cAAc,CAAC;IACzC;;IAEA;IACA,IAAIgC,GAAG,GAAG,IAAIhD,OAAO,CAAC,CAAC;IACvBqH,YAAY,CAAChE,OAAO,CAAC,UAAUoE,aAAa,EAAE;MAC5CzE,GAAG,CAACS,GAAG,CAACgE,aAAa,EAAE,EAAE,CAAC;IAC5B,CAAC,CAAC;IACF1C,aAAa,CAAC1B,OAAO,CAAC,UAAU2B,KAAK,EAAE;MACrC,IAAI0C,aAAa,GAAG1C,KAAK,CAAC7E,WAAW,CAAC,CAAC;;MAEvC;MACAkH,YAAY,CAAChE,OAAO,CAAC,UAAUoE,aAAa,EAAE;QAC5C,IAAIA,aAAa,CAACF,KAAK,CAAC,UAAUI,QAAQ,EAAEC,CAAC,EAAE;UAC7C,OAAOF,aAAa,CAACE,CAAC,CAAC,KAAKD,QAAQ;QACtC,CAAC,CAAC,EAAE;UACF3E,GAAG,CAAC6E,MAAM,CAACJ,aAAa,EAAE,UAAUK,IAAI,EAAE;YACxC,OAAO,EAAE,CAACC,MAAM,CAAC3I,kBAAkB,CAAC0I,IAAI,CAAC,EAAE,CAAC9C,KAAK,CAAC,CAAC;UACrD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAIgD,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,QAAQ,EAAE;MACnE,OAAOA,QAAQ,CAACT,IAAI,CAACxG,cAAc,CAAC;IACtC,CAAC;IACD,IAAIkH,oBAAoB,GAAGlF,GAAG,CAACA,GAAG,CAAC,UAAUmF,KAAK,EAAE;MAClD,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACF,OAAOd,kBAAkB,GAAGY,oBAAoB,CAACX,KAAK,CAACS,qBAAqB,CAAC,GAAGE,oBAAoB,CAACV,IAAI,CAACQ,qBAAqB,CAAC;EAClI,CAAC,CAAC;EACFxI,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU4F,IAAI,EAAE;IACtD3E,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,OAAO/E,KAAK,CAACM,eAAe,CAAC,CAACqE,IAAI,CAAC,CAAC;EACtC,CAAC,CAAC;EACF5F,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,UAAU0F,QAAQ,EAAE;IAC9DzE,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAIT,aAAa,GAAGtE,KAAK,CAAC2C,gBAAgB,CAAC,CAAC;IAC5C,IAAI,CAAC8B,QAAQ,EAAE;MACb,OAAOH,aAAa,CAACyC,IAAI,CAAC,UAAUa,SAAS,EAAE;QAC7C,OAAOA,SAAS,CAACpH,iBAAiB,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ;IACA,IAAIoG,YAAY,GAAGnC,QAAQ,CAAClC,GAAG,CAAC7C,WAAW,CAAC;IAC5C,OAAO4E,aAAa,CAACyC,IAAI,CAAC,UAAUa,SAAS,EAAE;MAC7C,IAAIX,aAAa,GAAGW,SAAS,CAAClI,WAAW,CAAC,CAAC;MAC3C,OAAOD,gBAAgB,CAACmH,YAAY,EAAEK,aAAa,CAAC,IAAIW,SAAS,CAACpH,iBAAiB,CAAC,CAAC;IACvF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFzB,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,UAAU4F,IAAI,EAAE;IACzD3E,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,OAAO/E,KAAK,CAACS,kBAAkB,CAAC,CAACkE,IAAI,CAAC,CAAC;EACzC,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5F,eAAe,CAAC,IAAI,EAAE,4BAA4B,EAAE,YAAY;IAC9D,IAAI8I,IAAI,GAAGlE,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjF;IACA,IAAIa,KAAK,GAAG,IAAIjF,OAAO,CAAC,CAAC;IACzB,IAAI+E,aAAa,GAAGtE,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC;IAChD2B,aAAa,CAAC1B,OAAO,CAAC,UAAU2B,KAAK,EAAE;MACrC,IAAIuD,YAAY,GAAGvD,KAAK,CAACwD,KAAK,CAACD,YAAY;MAC3C,IAAIrF,QAAQ,GAAG8B,KAAK,CAAC7E,WAAW,CAAC,CAAC;;MAElC;MACA,IAAIoI,YAAY,KAAKlE,SAAS,EAAE;QAC9B,IAAIoE,OAAO,GAAGxD,KAAK,CAACI,GAAG,CAACnC,QAAQ,CAAC,IAAI,IAAIwF,GAAG,CAAC,CAAC;QAC9CD,OAAO,CAACE,GAAG,CAAC;UACVrF,MAAM,EAAE0B,KAAK;UACboD,KAAK,EAAEG;QACT,CAAC,CAAC;QACFtD,KAAK,CAACxB,GAAG,CAACP,QAAQ,EAAEuF,OAAO,CAAC;MAC9B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACX,QAAQ,EAAE;MACvDA,QAAQ,CAAC5E,OAAO,CAAC,UAAU2B,KAAK,EAAE;QAChC,IAAIuD,YAAY,GAAGvD,KAAK,CAACwD,KAAK,CAACD,YAAY;QAC3C,IAAIA,YAAY,KAAKlE,SAAS,EAAE;UAC9B,IAAInB,QAAQ,GAAG8B,KAAK,CAAC7E,WAAW,CAAC,CAAC;UAClC,IAAI0I,gBAAgB,GAAGpI,KAAK,CAAC8B,eAAe,CAACW,QAAQ,CAAC;UACtD,IAAI2F,gBAAgB,KAAKxE,SAAS,EAAE;YAClC;YACA1E,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAACoI,MAAM,CAAC7E,QAAQ,CAAC4F,IAAI,CAAC,GAAG,CAAC,EAAE,gCAAgC,CAAC,CAAC;UAC7H,CAAC,MAAM;YACL,IAAIL,OAAO,GAAGxD,KAAK,CAACI,GAAG,CAACnC,QAAQ,CAAC;YACjC,IAAIuF,OAAO,IAAIA,OAAO,CAACM,IAAI,GAAG,CAAC,EAAE;cAC/B;cACApJ,OAAO,CAAC,KAAK,EAAE,4BAA4B,CAACoI,MAAM,CAAC7E,QAAQ,CAAC4F,IAAI,CAAC,GAAG,CAAC,EAAE,yDAAyD,CAAC,CAAC;YACpI,CAAC,MAAM,IAAIL,OAAO,EAAE;cAClB,IAAIO,WAAW,GAAGvI,KAAK,CAACC,aAAa,CAACwC,QAAQ,CAAC;cAC/C,IAAIqD,WAAW,GAAGvB,KAAK,CAACuB,WAAW,CAAC,CAAC;;cAErC;cACA,IAAI,CAACA,WAAW,KAAK,CAAC+B,IAAI,CAACW,SAAS,IAAID,WAAW,KAAK3E,SAAS,CAAC,EAAE;gBAClE5D,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACG,KAAK,CAACqC,KAAK,EAAEI,QAAQ,EAAE9D,kBAAkB,CAACqJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAACL,KAAK,CAAC,CAAC;cAC1F;YACF;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIc,qBAAqB;IACzB,IAAIZ,IAAI,CAACL,QAAQ,EAAE;MACjBiB,qBAAqB,GAAGZ,IAAI,CAACL,QAAQ;IACvC,CAAC,MAAM,IAAIK,IAAI,CAACjB,YAAY,EAAE;MAC5B6B,qBAAqB,GAAG,EAAE;MAC1BZ,IAAI,CAACjB,YAAY,CAAChE,OAAO,CAAC,UAAUH,QAAQ,EAAE;QAC5C,IAAIuF,OAAO,GAAGxD,KAAK,CAACI,GAAG,CAACnC,QAAQ,CAAC;QACjC,IAAIuF,OAAO,EAAE;UACX,IAAIU,qBAAqB;UACzB,CAACA,qBAAqB,GAAGD,qBAAqB,EAAEjF,IAAI,CAACmF,KAAK,CAACD,qBAAqB,EAAE/J,kBAAkB,CAACA,kBAAkB,CAACqJ,OAAO,CAAC,CAACzF,GAAG,CAAC,UAAUqG,CAAC,EAAE;YAChJ,OAAOA,CAAC,CAAC/F,MAAM;UACjB,CAAC,CAAC,CAAC,CAAC;QACN;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL4F,qBAAqB,GAAGnE,aAAa;IACvC;IACA6D,eAAe,CAACM,qBAAqB,CAAC;EACxC,CAAC,CAAC;EACF1J,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU0F,QAAQ,EAAE;IACvDzE,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAI8D,SAAS,GAAG7I,KAAK,CAACqC,KAAK;IAC3B,IAAI,CAACoC,QAAQ,EAAE;MACbzE,KAAK,CAAC0C,WAAW,CAACzD,KAAK,CAACe,KAAK,CAACiC,aAAa,CAAC,CAAC;MAC7CjC,KAAK,CAAC8I,0BAA0B,CAAC,CAAC;MAClC9I,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE,IAAI,EAAE;QACrCG,IAAI,EAAE;MACR,CAAC,CAAC;MACFhJ,KAAK,CAACiJ,WAAW,CAAC,CAAC;MACnB;IACF;;IAEA;IACA,IAAIrC,YAAY,GAAGnC,QAAQ,CAAClC,GAAG,CAAC7C,WAAW,CAAC;IAC5CkH,YAAY,CAAChE,OAAO,CAAC,UAAUH,QAAQ,EAAE;MACvC,IAAIqF,YAAY,GAAG9H,KAAK,CAAC8B,eAAe,CAACW,QAAQ,CAAC;MAClDzC,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACG,KAAK,CAACqC,KAAK,EAAEI,QAAQ,EAAEqF,YAAY,CAAC,CAAC;IAClE,CAAC,CAAC;IACF9H,KAAK,CAAC8I,0BAA0B,CAAC;MAC/BlC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF5G,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAEjC,YAAY,EAAE;MAC7CoC,IAAI,EAAE;IACR,CAAC,CAAC;IACFhJ,KAAK,CAACiJ,WAAW,CAACrC,YAAY,CAAC;EACjC,CAAC,CAAC;EACF7H,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,UAAUmK,MAAM,EAAE;IACnDlJ,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAI8D,SAAS,GAAG7I,KAAK,CAACqC,KAAK;IAC3B,IAAIuE,YAAY,GAAG,EAAE;IACrBsC,MAAM,CAACtG,OAAO,CAAC,UAAUuG,SAAS,EAAE;MAClC,IAAIxE,IAAI,GAAGwE,SAAS,CAACxE,IAAI;QACvByE,IAAI,GAAG1K,wBAAwB,CAACyK,SAAS,EAAEnK,SAAS,CAAC;MACvD,IAAIyD,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;MAChCiC,YAAY,CAACpD,IAAI,CAACf,QAAQ,CAAC;;MAE3B;MACA,IAAI,OAAO,IAAI2G,IAAI,EAAE;QACnBpJ,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACG,KAAK,CAACqC,KAAK,EAAEI,QAAQ,EAAE2G,IAAI,CAACzB,KAAK,CAAC,CAAC;MAChE;MACA3H,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE,CAACpG,QAAQ,CAAC,EAAE;QAC3CuG,IAAI,EAAE,UAAU;QAChBI,IAAI,EAAED;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACFnJ,KAAK,CAACiJ,WAAW,CAACrC,YAAY,CAAC;EACjC,CAAC,CAAC;EACF7H,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,YAAY;IAC7C,IAAIyI,QAAQ,GAAGxH,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC;IAC3C,IAAIuG,MAAM,GAAG1B,QAAQ,CAACjF,GAAG,CAAC,UAAUgC,KAAK,EAAE;MACzC,IAAI9B,QAAQ,GAAG8B,KAAK,CAAC7E,WAAW,CAAC,CAAC;MAClC,IAAIqG,IAAI,GAAGxB,KAAK,CAACyB,OAAO,CAAC,CAAC;MAC1B,IAAImD,SAAS,GAAG1K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsH,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDpB,IAAI,EAAElC,QAAQ;QACdkF,KAAK,EAAE3H,KAAK,CAACC,aAAa,CAACwC,QAAQ;MACrC,CAAC,CAAC;MACF4G,MAAM,CAACC,cAAc,CAACH,SAAS,EAAE,eAAe,EAAE;QAChDxB,KAAK,EAAE;MACT,CAAC,CAAC;MACF,OAAOwB,SAAS;IAClB,CAAC,CAAC;IACF,OAAOD,MAAM;EACf,CAAC,CAAC;EACF;EACA;AACF;AACA;EACEnK,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,UAAU8D,MAAM,EAAE;IACzD,IAAIiF,YAAY,GAAGjF,MAAM,CAACkF,KAAK,CAACD,YAAY;IAC5C,IAAIA,YAAY,KAAKlE,SAAS,EAAE;MAC9B,IAAInB,QAAQ,GAAGI,MAAM,CAACnD,WAAW,CAAC,CAAC;MACnC,IAAI6J,SAAS,GAAG5J,QAAQ,CAACK,KAAK,CAACqC,KAAK,EAAEI,QAAQ,CAAC;MAC/C,IAAI8G,SAAS,KAAK3F,SAAS,EAAE;QAC3B5D,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACG,KAAK,CAACqC,KAAK,EAAEI,QAAQ,EAAEqF,YAAY,CAAC,CAAC;MAClE;IACF;EACF,CAAC,CAAC;EACF/I,eAAe,CAAC,IAAI,EAAE,kBAAkB,EAAE,UAAUyK,aAAa,EAAE;IACjE,IAAIC,cAAc,GAAGD,aAAa,KAAK5F,SAAS,GAAG4F,aAAa,GAAGxJ,KAAK,CAACqD,QAAQ;IACjF,OAAOoG,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,IAAI;EACrF,CAAC,CAAC;EACF1K,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU8D,MAAM,EAAE;IACvD7C,KAAK,CAACsE,aAAa,CAACd,IAAI,CAACX,MAAM,CAAC;IAChC,IAAIJ,QAAQ,GAAGI,MAAM,CAACnD,WAAW,CAAC,CAAC;IACnCM,KAAK,CAACiJ,WAAW,CAAC,CAACxG,QAAQ,CAAC,CAAC;;IAE7B;IACA,IAAII,MAAM,CAACkF,KAAK,CAACD,YAAY,KAAKlE,SAAS,EAAE;MAC3C,IAAIiF,SAAS,GAAG7I,KAAK,CAACqC,KAAK;MAC3BrC,KAAK,CAAC8I,0BAA0B,CAAC;QAC/BtB,QAAQ,EAAE,CAAC3E,MAAM,CAAC;QAClB2F,SAAS,EAAE;MACb,CAAC,CAAC;MACFxI,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE,CAAChG,MAAM,CAACnD,WAAW,CAAC,CAAC,CAAC,EAAE;QACvDsJ,IAAI,EAAE,aAAa;QACnBU,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO,UAAU5D,WAAW,EAAEzC,QAAQ,EAAE;MACtC,IAAIsG,WAAW,GAAGhG,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;MACxF3D,KAAK,CAACsE,aAAa,GAAGtE,KAAK,CAACsE,aAAa,CAACb,MAAM,CAAC,UAAUmG,IAAI,EAAE;QAC/D,OAAOA,IAAI,KAAK/G,MAAM;MACxB,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC7C,KAAK,CAAC8C,gBAAgB,CAACO,QAAQ,CAAC,KAAK,CAACyC,WAAW,IAAI6D,WAAW,CAACzG,MAAM,GAAG,CAAC,CAAC,EAAE;QACjF,IAAI2G,YAAY,GAAG/D,WAAW,GAAGlC,SAAS,GAAG5D,KAAK,CAAC8B,eAAe,CAACW,QAAQ,CAAC;QAC5E,IAAIA,QAAQ,CAACS,MAAM,IAAIlD,KAAK,CAACC,aAAa,CAACwC,QAAQ,CAAC,KAAKoH,YAAY,IAAI7J,KAAK,CAACsE,aAAa,CAACwC,KAAK,CAAC,UAAUvC,KAAK,EAAE;UAClH;YACE;YACA,CAAC3E,aAAa,CAAC2E,KAAK,CAAC7E,WAAW,CAAC,CAAC,EAAE+C,QAAQ;UAAC;QAEjD,CAAC,CAAC,EAAE;UACF,IAAIqH,UAAU,GAAG9J,KAAK,CAACqC,KAAK;UAC5BrC,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACiK,UAAU,EAAErH,QAAQ,EAAEoH,YAAY,EAAE,IAAI,CAAC,CAAC;;UAErE;UACA7J,KAAK,CAAC+I,eAAe,CAACe,UAAU,EAAE,CAACrH,QAAQ,CAAC,EAAE;YAC5CuG,IAAI,EAAE;UACR,CAAC,CAAC;;UAEF;UACAhJ,KAAK,CAAC+J,yBAAyB,CAACD,UAAU,EAAErH,QAAQ,CAAC;QACvD;MACF;MACAzC,KAAK,CAACiJ,WAAW,CAAC,CAACxG,QAAQ,CAAC,CAAC;IAC/B,CAAC;EACH,CAAC,CAAC;EACF1D,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,UAAUiL,MAAM,EAAE;IAClD,QAAQA,MAAM,CAAChB,IAAI;MACjB,KAAK,aAAa;QAChB;UACE,IAAIvG,QAAQ,GAAGuH,MAAM,CAACvH,QAAQ;YAC5BkF,KAAK,GAAGqC,MAAM,CAACrC,KAAK;UACtB3H,KAAK,CAACiK,WAAW,CAACxH,QAAQ,EAAEkF,KAAK,CAAC;UAClC;QACF;MACF,KAAK,eAAe;QAClB;UACE,IAAIuC,SAAS,GAAGF,MAAM,CAACvH,QAAQ;YAC7B0H,WAAW,GAAGH,MAAM,CAACG,WAAW;UAClCnK,KAAK,CAACc,cAAc,CAAC,CAACoJ,SAAS,CAAC,EAAE;YAChCC,WAAW,EAAEA;UACf,CAAC,CAAC;UACF;QACF;MACF;MACA;IACF;EACF,CAAC,CAAC;EACFpL,eAAe,CAAC,IAAI,EAAE,iBAAiB,EAAE,UAAU8J,SAAS,EAAEjC,YAAY,EAAEiB,IAAI,EAAE;IAChF,IAAI7H,KAAK,CAACgC,YAAY,EAAE;MACtB,IAAIoI,UAAU,GAAG3L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoJ,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1DxF,KAAK,EAAErC,KAAK,CAACE,cAAc,CAAC,IAAI;MAClC,CAAC,CAAC;MACFF,KAAK,CAAC2C,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUyH,KAAK,EAAE;QAChD,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;QACvCA,aAAa,CAACzB,SAAS,EAAEjC,YAAY,EAAEwD,UAAU,CAAC;MACpD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLpK,KAAK,CAACD,eAAe,CAAC,CAAC;IACzB;EACF,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEhB,eAAe,CAAC,IAAI,EAAE,2BAA2B,EAAE,UAAU8J,SAAS,EAAEpG,QAAQ,EAAE;IAChF,IAAI8H,cAAc,GAAGvK,KAAK,CAACwK,2BAA2B,CAAC/H,QAAQ,CAAC;IAChE,IAAI8H,cAAc,CAACrH,MAAM,EAAE;MACzBlD,KAAK,CAACc,cAAc,CAACyJ,cAAc,CAAC;IACtC;IACAvK,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE0B,cAAc,EAAE;MAC/CvB,IAAI,EAAE,oBAAoB;MAC1ByB,aAAa,EAAE,CAAChI,QAAQ,CAAC,CAAC6E,MAAM,CAAC3I,kBAAkB,CAAC4L,cAAc,CAAC;IACrE,CAAC,CAAC;IACF,OAAOA,cAAc;EACvB,CAAC,CAAC;EACFxL,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU4F,IAAI,EAAEgD,KAAK,EAAE;IAC1D,IAAIlF,QAAQ,GAAG/C,WAAW,CAACiF,IAAI,CAAC;IAChC,IAAIkE,SAAS,GAAG7I,KAAK,CAACqC,KAAK;IAC3BrC,KAAK,CAAC0C,WAAW,CAAC7C,QAAQ,CAACG,KAAK,CAACqC,KAAK,EAAEI,QAAQ,EAAEkF,KAAK,CAAC,CAAC;IACzD3H,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE,CAACpG,QAAQ,CAAC,EAAE;MAC3CuG,IAAI,EAAE,aAAa;MACnBU,MAAM,EAAE;IACV,CAAC,CAAC;IACF1J,KAAK,CAACiJ,WAAW,CAAC,CAACxG,QAAQ,CAAC,CAAC;;IAE7B;IACA,IAAI8H,cAAc,GAAGvK,KAAK,CAAC+J,yBAAyB,CAAClB,SAAS,EAAEpG,QAAQ,CAAC;;IAEzE;IACA,IAAIiI,cAAc,GAAG1K,KAAK,CAACmD,SAAS,CAACuH,cAAc;IACnD,IAAIA,cAAc,EAAE;MAClB,IAAIC,aAAa,GAAGnL,mBAAmB,CAACQ,KAAK,CAACqC,KAAK,EAAE,CAACI,QAAQ,CAAC,CAAC;MAChEiI,cAAc,CAACC,aAAa,EAAE3K,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC;IACvD;IACAF,KAAK,CAAC4K,qBAAqB,CAAC,CAACnI,QAAQ,CAAC,CAAC6E,MAAM,CAAC3I,kBAAkB,CAAC4L,cAAc,CAAC,CAAC,CAAC;EACpF,CAAC,CAAC;EACF;EACAxL,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAUsD,KAAK,EAAE;IACvDrC,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAI8D,SAAS,GAAG7I,KAAK,CAACqC,KAAK;IAC3B,IAAIA,KAAK,EAAE;MACT,IAAID,SAAS,GAAGnD,KAAK,CAACe,KAAK,CAACqC,KAAK,EAAEA,KAAK,CAAC;MACzCrC,KAAK,CAAC0C,WAAW,CAACN,SAAS,CAAC;IAC9B;IACApC,KAAK,CAAC+I,eAAe,CAACF,SAAS,EAAE,IAAI,EAAE;MACrCG,IAAI,EAAE,aAAa;MACnBU,MAAM,EAAE;IACV,CAAC,CAAC;IACF1J,KAAK,CAACiJ,WAAW,CAAC,CAAC;EACrB,CAAC,CAAC;EACFlK,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,UAAU4F,IAAI,EAAEgD,KAAK,EAAE;IAC5D3H,KAAK,CAACW,SAAS,CAAC,CAAC;MACfgE,IAAI,EAAEA,IAAI;MACVgD,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF5I,eAAe,CAAC,IAAI,EAAE,6BAA6B,EAAE,UAAU8L,YAAY,EAAE;IAC3E,IAAIC,QAAQ,GAAG,IAAI7C,GAAG,CAAC,CAAC;IACxB,IAAIsC,cAAc,GAAG,EAAE;IACvB,IAAIQ,mBAAmB,GAAG,IAAIxL,OAAO,CAAC,CAAC;;IAEvC;AACJ;AACA;AACA;IACIS,KAAK,CAAC2C,gBAAgB,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU2B,KAAK,EAAE;MAChD,IAAIyG,YAAY,GAAGzG,KAAK,CAACwD,KAAK,CAACiD,YAAY;MAC3C,CAACA,YAAY,IAAI,EAAE,EAAEpI,OAAO,CAAC,UAAUqI,UAAU,EAAE;QACjD,IAAIC,kBAAkB,GAAGxL,WAAW,CAACuL,UAAU,CAAC;QAChDF,mBAAmB,CAAC3D,MAAM,CAAC8D,kBAAkB,EAAE,YAAY;UACzD,IAAIhC,MAAM,GAAGvF,SAAS,CAACT,MAAM,GAAG,CAAC,IAAIS,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAIsE,GAAG,CAAC,CAAC;UAC1FiB,MAAM,CAAChB,GAAG,CAAC3D,KAAK,CAAC;UACjB,OAAO2E,MAAM;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIiC,YAAY,GAAG,SAASA,YAAYA,CAAC1I,QAAQ,EAAE;MACjD,IAAIyG,MAAM,GAAG6B,mBAAmB,CAACnG,GAAG,CAACnC,QAAQ,CAAC,IAAI,IAAIwF,GAAG,CAAC,CAAC;MAC3DiB,MAAM,CAACtG,OAAO,CAAC,UAAU2B,KAAK,EAAE;QAC9B,IAAI,CAACuG,QAAQ,CAACM,GAAG,CAAC7G,KAAK,CAAC,EAAE;UACxBuG,QAAQ,CAAC5C,GAAG,CAAC3D,KAAK,CAAC;UACnB,IAAI0C,aAAa,GAAG1C,KAAK,CAAC7E,WAAW,CAAC,CAAC;UACvC,IAAI6E,KAAK,CAAC8G,YAAY,CAAC,CAAC,IAAIpE,aAAa,CAAC/D,MAAM,EAAE;YAChDqH,cAAc,CAAC/G,IAAI,CAACyD,aAAa,CAAC;YAClCkE,YAAY,CAAClE,aAAa,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACDkE,YAAY,CAACN,YAAY,CAAC;IAC1B,OAAON,cAAc;EACvB,CAAC,CAAC;EACFxL,eAAe,CAAC,IAAI,EAAE,uBAAuB,EAAE,UAAU6H,YAAY,EAAE0E,WAAW,EAAE;IAClF,IAAIC,cAAc,GAAGvL,KAAK,CAACmD,SAAS,CAACoI,cAAc;IACnD,IAAIA,cAAc,EAAE;MAClB,IAAIrC,MAAM,GAAGlJ,KAAK,CAAC4B,SAAS,CAAC,CAAC;;MAE9B;AACN;AACA;MACM,IAAI0J,WAAW,EAAE;QACf,IAAI9G,KAAK,GAAG,IAAIjF,OAAO,CAAC,CAAC;QACzB+L,WAAW,CAAC1I,OAAO,CAAC,UAAU4I,KAAK,EAAE;UACnC,IAAI7G,IAAI,GAAG6G,KAAK,CAAC7G,IAAI;YACnBuB,MAAM,GAAGsF,KAAK,CAACtF,MAAM;UACvB1B,KAAK,CAACxB,GAAG,CAAC2B,IAAI,EAAEuB,MAAM,CAAC;QACzB,CAAC,CAAC;QACFgD,MAAM,CAACtG,OAAO,CAAC,UAAU2B,KAAK,EAAE;UAC9B;UACAA,KAAK,CAAC2B,MAAM,GAAG1B,KAAK,CAACI,GAAG,CAACL,KAAK,CAACI,IAAI,CAAC,IAAIJ,KAAK,CAAC2B,MAAM;QACtD,CAAC,CAAC;MACJ;MACA,IAAIuF,aAAa,GAAGvC,MAAM,CAACzF,MAAM,CAAC,UAAUiI,KAAK,EAAE;QACjD,IAAIC,SAAS,GAAGD,KAAK,CAAC/G,IAAI;QAC1B,OAAOlF,gBAAgB,CAACmH,YAAY,EAAE+E,SAAS,CAAC;MAClD,CAAC,CAAC;MACF,IAAIF,aAAa,CAACvI,MAAM,EAAE;QACxBqI,cAAc,CAACE,aAAa,EAAEvC,MAAM,CAAC;MACvC;IACF;EACF,CAAC,CAAC;EACF;EACAnK,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,UAAU4H,IAAI,EAAEiF,IAAI,EAAE;IAC5D5L,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB,IAAIN,QAAQ;IACZ,IAAIoH,OAAO;IACX,IAAI1G,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOiF,IAAI,KAAK,QAAQ,EAAE;MAC/EnH,QAAQ,GAAGkC,IAAI;MACfkF,OAAO,GAAGD,IAAI;IAChB,CAAC,MAAM;MACLC,OAAO,GAAGlF,IAAI;IAChB;IACA,IAAImF,eAAe,GAAG,CAAC,CAACrH,QAAQ;IAChC,IAAImC,YAAY,GAAGkF,eAAe,GAAGrH,QAAQ,CAAClC,GAAG,CAAC7C,WAAW,CAAC,GAAG,EAAE;;IAEnE;IACA,IAAIqM,WAAW,GAAG,EAAE;;IAEpB;IACA,IAAIC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAClC,IAAIC,oBAAoB,GAAG,IAAInE,GAAG,CAAC,CAAC;IACpC,IAAIoE,KAAK,GAAGR,OAAO,IAAI,CAAC,CAAC;MACvBS,SAAS,GAAGD,KAAK,CAACC,SAAS;MAC3BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBvM,KAAK,CAAC2C,gBAAgB,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAU2B,KAAK,EAAE;MACpD;MACA,IAAI,CAACuH,eAAe,EAAE;QACpBlF,YAAY,CAACpD,IAAI,CAACe,KAAK,CAAC7E,WAAW,CAAC,CAAC,CAAC;MACxC;;MAEA;MACA,IAAI,CAAC6E,KAAK,CAACwD,KAAK,CAACyE,KAAK,IAAI,CAACjI,KAAK,CAACwD,KAAK,CAACyE,KAAK,CAACtJ,MAAM,EAAE;QACnD;MACF;;MAEA;MACA,IAAIqJ,KAAK,IAAI,CAAChI,KAAK,CAAC8G,YAAY,CAAC,CAAC,EAAE;QAClC;MACF;MACA,IAAIpE,aAAa,GAAG1C,KAAK,CAAC7E,WAAW,CAAC,CAAC;MACvC0M,oBAAoB,CAAClE,GAAG,CAACjB,aAAa,CAACoB,IAAI,CAAC2D,SAAS,CAAC,CAAC;;MAEvD;MACA,IAAI,CAACF,eAAe,IAAIrM,gBAAgB,CAACmH,YAAY,EAAEK,aAAa,EAAEqF,SAAS,CAAC,EAAE;QAChF,IAAIG,OAAO,GAAGlI,KAAK,CAACmI,aAAa,CAACjO,aAAa,CAAC;UAC9C2E,gBAAgB,EAAE3E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEa,uBAAuB,CAAC,EAAEU,KAAK,CAACoD,gBAAgB;QACpG,CAAC,EAAEyI,OAAO,CAAC,CAAC;;QAEZ;QACAE,WAAW,CAACvI,IAAI,CAACiJ,OAAO,CAACE,IAAI,CAAC,YAAY;UACxC,OAAO;YACLhI,IAAI,EAAEsC,aAAa;YACnBf,MAAM,EAAE,EAAE;YACVE,QAAQ,EAAE;UACZ,CAAC;QACH,CAAC,CAAC,CAACwG,KAAK,CAAC,UAAUC,UAAU,EAAE;UAC7B,IAAIC,mBAAmB;UACvB,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAIC,cAAc,GAAG,EAAE;UACvB,CAACF,mBAAmB,GAAGD,UAAU,CAACjK,OAAO,MAAM,IAAI,IAAIkK,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACjH,IAAI,CAACgH,UAAU,EAAE,UAAUI,KAAK,EAAE;YAC7I,IAAIC,WAAW,GAAGD,KAAK,CAACE,IAAI,CAACD,WAAW;cACtChH,MAAM,GAAG+G,KAAK,CAAC/G,MAAM;YACvB,IAAIgH,WAAW,EAAE;cACfF,cAAc,CAACxJ,IAAI,CAACmF,KAAK,CAACqE,cAAc,EAAErO,kBAAkB,CAACuH,MAAM,CAAC,CAAC;YACvE,CAAC,MAAM;cACL6G,YAAY,CAACvJ,IAAI,CAACmF,KAAK,CAACoE,YAAY,EAAEpO,kBAAkB,CAACuH,MAAM,CAAC,CAAC;YACnE;UACF,CAAC,CAAC;UACF,IAAI6G,YAAY,CAAC7J,MAAM,EAAE;YACvB,OAAOkK,OAAO,CAACC,MAAM,CAAC;cACpB1I,IAAI,EAAEsC,aAAa;cACnBf,MAAM,EAAE6G,YAAY;cACpB3G,QAAQ,EAAE4G;YACZ,CAAC,CAAC;UACJ;UACA,OAAO;YACLrI,IAAI,EAAEsC,aAAa;YACnBf,MAAM,EAAE6G,YAAY;YACpB3G,QAAQ,EAAE4G;UACZ,CAAC;QACH,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;IACF,IAAIM,cAAc,GAAGjO,gBAAgB,CAAC0M,WAAW,CAAC;IAClD/L,KAAK,CAACuN,mBAAmB,GAAGD,cAAc;;IAE1C;IACAA,cAAc,CAACV,KAAK,CAAC,UAAUY,OAAO,EAAE;MACtC,OAAOA,OAAO;IAChB,CAAC,CAAC,CAACb,IAAI,CAAC,UAAUa,OAAO,EAAE;MACzB,IAAIC,kBAAkB,GAAGD,OAAO,CAACjL,GAAG,CAAC,UAAUmL,MAAM,EAAE;QACrD,IAAI/I,IAAI,GAAG+I,MAAM,CAAC/I,IAAI;QACtB,OAAOA,IAAI;MACb,CAAC,CAAC;MACF3E,KAAK,CAAC+I,eAAe,CAAC/I,KAAK,CAACqC,KAAK,EAAEoL,kBAAkB,EAAE;QACrDzE,IAAI,EAAE;MACR,CAAC,CAAC;MACFhJ,KAAK,CAAC4K,qBAAqB,CAAC6C,kBAAkB,EAAED,OAAO,CAAC;IAC1D,CAAC,CAAC;IACF,IAAIG,aAAa,GAAGL,cAAc,CAACX,IAAI,CAAC,YAAY;MAClD,IAAI3M,KAAK,CAACuN,mBAAmB,KAAKD,cAAc,EAAE;QAChD,OAAOF,OAAO,CAACQ,OAAO,CAAC5N,KAAK,CAACE,cAAc,CAAC0G,YAAY,CAAC,CAAC;MAC5D;MACA,OAAOwG,OAAO,CAACC,MAAM,CAAC,EAAE,CAAC;IAC3B,CAAC,CAAC,CAACT,KAAK,CAAC,UAAUY,OAAO,EAAE;MAC1B,IAAIK,SAAS,GAAGL,OAAO,CAAC/J,MAAM,CAAC,UAAUqK,MAAM,EAAE;QAC/C,OAAOA,MAAM,IAAIA,MAAM,CAAC5H,MAAM,CAAChD,MAAM;MACvC,CAAC,CAAC;MACF,OAAOkK,OAAO,CAACC,MAAM,CAAC;QACpBxJ,MAAM,EAAE7D,KAAK,CAACE,cAAc,CAAC0G,YAAY,CAAC;QAC1CmH,WAAW,EAAEF,SAAS;QACtBG,SAAS,EAAEhO,KAAK,CAACuN,mBAAmB,KAAKD;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAK,aAAa,CAACf,KAAK,CAAC,UAAUqB,CAAC,EAAE;MAC/B,OAAOA,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIC,mBAAmB,GAAGtH,YAAY,CAACnD,MAAM,CAAC,UAAUhB,QAAQ,EAAE;MAChE,OAAO2J,oBAAoB,CAAChB,GAAG,CAAC3I,QAAQ,CAAC4F,IAAI,CAAC2D,SAAS,CAAC,CAAC;IAC3D,CAAC,CAAC;IACFhM,KAAK,CAAC4K,qBAAqB,CAACsD,mBAAmB,CAAC;IAChD,OAAOP,aAAa;EACtB,CAAC,CAAC;EACF;EACA5O,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY;IAC1CiB,KAAK,CAAC+E,eAAe,CAAC,CAAC;IACvB/E,KAAK,CAACc,cAAc,CAAC,CAAC,CAAC6L,IAAI,CAAC,UAAU9I,MAAM,EAAE;MAC5C,IAAIsK,QAAQ,GAAGnO,KAAK,CAACmD,SAAS,CAACgL,QAAQ;MACvC,IAAIA,QAAQ,EAAE;QACZ,IAAI;UACFA,QAAQ,CAACtK,MAAM,CAAC;QAClB,CAAC,CAAC,OAAOuK,GAAG,EAAE;UACZ;UACAC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;QACpB;MACF;IACF,CAAC,CAAC,CAACxB,KAAK,CAAC,UAAUqB,CAAC,EAAE;MACpB,IAAIM,cAAc,GAAGvO,KAAK,CAACmD,SAAS,CAACoL,cAAc;MACnD,IAAIA,cAAc,EAAE;QAClBA,cAAc,CAACN,CAAC,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAAClO,eAAe,GAAGA,eAAe;AACxC,CAAC,CAAC;AACF,SAASyO,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIC,OAAO,GAAGvP,KAAK,CAACwP,MAAM,CAAC,CAAC;EAC5B,IAAIC,eAAe,GAAGzP,KAAK,CAAC0P,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGtQ,cAAc,CAACoQ,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAI,CAACJ,OAAO,CAACM,OAAO,EAAE;IACpB,IAAIP,IAAI,EAAE;MACRC,OAAO,CAACM,OAAO,GAAGP,IAAI;IACxB,CAAC,MAAM;MACL;MACA,IAAIQ,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;QAC3CF,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;MACD,IAAIG,SAAS,GAAG,IAAIpP,SAAS,CAACmP,aAAa,CAAC;MAC5CP,OAAO,CAACM,OAAO,GAAGE,SAAS,CAACC,OAAO,CAAC,CAAC;IACvC;EACF;EACA,OAAO,CAACT,OAAO,CAACM,OAAO,CAAC;AAC1B;AACA,eAAeR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}