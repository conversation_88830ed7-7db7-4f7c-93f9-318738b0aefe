{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nvar usePagination = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var _b = options.defaultPageSize,\n    defaultPageSize = _b === void 0 ? 10 : _b,\n    _c = options.defaultCurrent,\n    defaultCurrent = _c === void 0 ? 1 : _c,\n    rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function () {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n  var _d = result.params[0] || {},\n    _e = _d.current,\n    current = _e === void 0 ? 1 : _e,\n    _f = _d.pageSize,\n    pageSize = _f === void 0 ? defaultPageSize : _f;\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n  var onChange = function (c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n    var _a = __read(result.params || []),\n      _b = _a[0],\n      oldPaginationParams = _b === void 0 ? {} : _b,\n      restParams = _a.slice(1);\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n  var changeCurrent = function (c) {\n    onChange(c, pageSize);\n  };\n  var changePageSize = function (p) {\n    onChange(current, p);\n  };\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\nexport default usePagination;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}