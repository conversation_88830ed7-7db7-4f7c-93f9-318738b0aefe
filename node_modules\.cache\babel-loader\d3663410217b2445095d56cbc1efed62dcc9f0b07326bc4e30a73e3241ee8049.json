{"ast": null, "code": "import * as React from \"react\";\nfunction TransportQRcodeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TransportQRcodeOutline-TransportQRcodeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TransportQRcodeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TransportQRcodeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39.4210563,25 C41.3976532,25 43,26.6023468 43,28.5789436 L43,36.4210564 C43,38.3976532 41.3976532,40 39.4210564,40 L36,40 L36,42.6 C36,42.8209139 35.8209139,43 35.6,43 L33.4,43 C33.1790861,43 33,42.8209139 33,42.6 L33,40 L15,40 L15,42.6 C15,42.8209139 14.8209139,43 14.6,43 L12.4,43 C12.1790861,43 12,42.8209139 12,42.6 L12,40 L8.57894374,40 C6.60234679,40 5,38.3976532 5,36.4210564 L5,28.5789436 C5,26.6023473 6.60234679,25 8.57894358,25 L39.4210563,25 Z M39.1538488,28 L8.84615295,28 C8.41717324,28 8.05611316,28.321121 8.00592306,28.7471517 L8,28.8461516 L8,36.1538464 C8,36.5827146 8.32101708,36.9438183 8.74704068,36.9940639 L8.84615124,37 L39.153847,37 C39.5828268,37 39.9438868,36.678879 39.9940769,36.2528483 L40,36.1538484 L40,28.8461536 C40,28.4172855 39.6789833,28.0561818 39.2529593,28.0059362 L39.1538488,28 Z M14,30.5 C15.1045695,30.5 16,31.3954305 16,32.5 C16,33.6045695 15.1045695,34.5 14,34.5 C12.8954305,34.5 12,33.6045695 12,32.5 C12,31.3954305 12.8954305,30.5 14,30.5 Z M35,30.5 C36.1045695,30.5 37,31.3954305 37,32.5 C37,33.6045695 36.1045695,34.5 35,34.5 C33.8954305,34.5 33,33.6045695 33,32.5 C33,31.3954305 33.8954305,30.5 35,30.5 Z M28.6,5 C28.8209139,5 29,5.1790861 29,5.4 L29,21.6 C29,21.8209139 28.8209139,22 28.6,22 L26.4,22 C26.1790861,22 26,21.8209139 26,21.6 L26,5.4 C26,5.1790861 26.1790861,5 26.4,5 L28.6,5 Z M35.1,12 C35.3209139,12 35.5,12.1790861 35.5,12.4 L35.5,21.6 C35.5,21.8209139 35.3209139,22 35.1,22 L32.9,22 C32.6790861,22 32.5,21.8209139 32.5,21.6 L32.5,12.4 C32.5,12.1790861 32.6790861,12 32.9,12 L35.1,12 Z M41.6,19 C41.8209139,19 42,19.1790861 42,19.4 L42,21.6 C42,21.8209139 41.8209139,22 41.6,22 L39.4,22 C39.1790861,22 39,21.8209139 39,21.6 L39,19.4 C39,19.1790861 39.1790861,19 39.4,19 L41.6,19 Z M18.4210563,5 C20.3976532,5 22,6.60234681 22,8.57894362 L22,18.4210564 C22,20.3976532 20.3976532,22 18.4210564,22 L8.57894374,22 C6.60234679,22 5,20.3976532 5,18.4210564 L5,8.57894362 C5,6.60234735 6.60234679,5 8.57894358,5 L18.4210563,5 Z M18.1538488,8 L8.84615295,8 C8.41717324,8 8.05611316,8.32112097 8.00592306,8.74715167 L8,8.84615155 L8,18.1538464 C8,18.5827146 8.32101708,18.9438183 8.74704068,18.9940639 L8.84615124,19 L18.153847,19 C18.5828268,19 18.9438868,18.678879 18.9940769,18.2528483 L19,18.1538484 L19,8.84615359 C19,8.41728552 18.6789833,8.05618185 18.2529593,8.00593623 L18.1538488,8 Z M14.95239,11.3333277 C15.4520763,11.3333277 15.8571487,11.7384032 15.8571487,12.2380876 L15.8571487,14.9523715 C15.8571487,15.4520584 15.4520737,15.8571314 14.9523899,15.8571314 L12.2381091,15.8571314 C11.7384228,15.8571314 11.3333503,15.4520559 11.3333503,14.9523715 L11.3333503,12.2380876 C11.3333503,11.7384007 11.7384254,11.3333277 12.2381092,11.3333277 L14.95239,11.3333277 Z M41.6,5 C41.8209139,5 42,5.1790861 42,5.4 L42,14.6 C42,14.8209139 41.8209139,15 41.6,15 L39.4,15 C39.1790861,15 39,14.8209139 39,14.6 L39,5.4 C39,5.1790861 39.1790861,5 39.4,5 L41.6,5 Z M35.1,5 C35.3209139,5 35.5,5.1790861 35.5,5.4 L35.5,7.6 C35.5,7.8209139 35.3209139,8 35.1,8 L32.9,8 C32.6790861,8 32.5,7.8209139 32.5,7.6 L32.5,5.4 C32.5,5.1790861 32.6790861,5 32.9,5 L35.1,5 Z\",\n    id: \"TransportQRcodeOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default TransportQRcodeOutline;", "map": {"version": 3, "names": ["React", "TransportQRcodeOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/TransportQRcodeOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction TransportQRcodeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TransportQRcodeOutline-TransportQRcodeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"TransportQRcodeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"TransportQRcodeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M39.4210563,25 C41.3976532,25 43,26.6023468 43,28.5789436 L43,36.4210564 C43,38.3976532 41.3976532,40 39.4210564,40 L36,40 L36,42.6 C36,42.8209139 35.8209139,43 35.6,43 L33.4,43 C33.1790861,43 33,42.8209139 33,42.6 L33,40 L15,40 L15,42.6 C15,42.8209139 14.8209139,43 14.6,43 L12.4,43 C12.1790861,43 12,42.8209139 12,42.6 L12,40 L8.57894374,40 C6.60234679,40 5,38.3976532 5,36.4210564 L5,28.5789436 C5,26.6023473 6.60234679,25 8.57894358,25 L39.4210563,25 Z M39.1538488,28 L8.84615295,28 C8.41717324,28 8.05611316,28.321121 8.00592306,28.7471517 L8,28.8461516 L8,36.1538464 C8,36.5827146 8.32101708,36.9438183 8.74704068,36.9940639 L8.84615124,37 L39.153847,37 C39.5828268,37 39.9438868,36.678879 39.9940769,36.2528483 L40,36.1538484 L40,28.8461536 C40,28.4172855 39.6789833,28.0561818 39.2529593,28.0059362 L39.1538488,28 Z M14,30.5 C15.1045695,30.5 16,31.3954305 16,32.5 C16,33.6045695 15.1045695,34.5 14,34.5 C12.8954305,34.5 12,33.6045695 12,32.5 C12,31.3954305 12.8954305,30.5 14,30.5 Z M35,30.5 C36.1045695,30.5 37,31.3954305 37,32.5 C37,33.6045695 36.1045695,34.5 35,34.5 C33.8954305,34.5 33,33.6045695 33,32.5 C33,31.3954305 33.8954305,30.5 35,30.5 Z M28.6,5 C28.8209139,5 29,5.1790861 29,5.4 L29,21.6 C29,21.8209139 28.8209139,22 28.6,22 L26.4,22 C26.1790861,22 26,21.8209139 26,21.6 L26,5.4 C26,5.1790861 26.1790861,5 26.4,5 L28.6,5 Z M35.1,12 C35.3209139,12 35.5,12.1790861 35.5,12.4 L35.5,21.6 C35.5,21.8209139 35.3209139,22 35.1,22 L32.9,22 C32.6790861,22 32.5,21.8209139 32.5,21.6 L32.5,12.4 C32.5,12.1790861 32.6790861,12 32.9,12 L35.1,12 Z M41.6,19 C41.8209139,19 42,19.1790861 42,19.4 L42,21.6 C42,21.8209139 41.8209139,22 41.6,22 L39.4,22 C39.1790861,22 39,21.8209139 39,21.6 L39,19.4 C39,19.1790861 39.1790861,19 39.4,19 L41.6,19 Z M18.4210563,5 C20.3976532,5 22,6.60234681 22,8.57894362 L22,18.4210564 C22,20.3976532 20.3976532,22 18.4210564,22 L8.57894374,22 C6.60234679,22 5,20.3976532 5,18.4210564 L5,8.57894362 C5,6.60234735 6.60234679,5 8.57894358,5 L18.4210563,5 Z M18.1538488,8 L8.84615295,8 C8.41717324,8 8.05611316,8.32112097 8.00592306,8.74715167 L8,8.84615155 L8,18.1538464 C8,18.5827146 8.32101708,18.9438183 8.74704068,18.9940639 L8.84615124,19 L18.153847,19 C18.5828268,19 18.9438868,18.678879 18.9940769,18.2528483 L19,18.1538484 L19,8.84615359 C19,8.41728552 18.6789833,8.05618185 18.2529593,8.00593623 L18.1538488,8 Z M14.95239,11.3333277 C15.4520763,11.3333277 15.8571487,11.7384032 15.8571487,12.2380876 L15.8571487,14.9523715 C15.8571487,15.4520584 15.4520737,15.8571314 14.9523899,15.8571314 L12.2381091,15.8571314 C11.7384228,15.8571314 11.3333503,15.4520559 11.3333503,14.9523715 L11.3333503,12.2380876 C11.3333503,11.7384007 11.7384254,11.3333277 12.2381092,11.3333277 L14.95239,11.3333277 Z M41.6,5 C41.8209139,5 42,5.1790861 42,5.4 L42,14.6 C42,14.8209139 41.8209139,15 41.6,15 L39.4,15 C39.1790861,15 39,14.8209139 39,14.6 L39,5.4 C39,5.1790861 39.1790861,5 39.4,5 L41.6,5 Z M35.1,5 C35.3209139,5 35.5,5.1790861 35.5,5.4 L35.5,7.6 C35.5,7.8209139 35.3209139,8 35.1,8 L32.9,8 C32.6790861,8 32.5,7.8209139 32.5,7.6 L32.5,5.4 C32.5,5.1790861 32.6790861,5 32.9,5 L35.1,5 Z\",\n    id: \"TransportQRcodeOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default TransportQRcodeOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACrC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,+CAA+C;IACnDC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,qCAAqC;IACzCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,4iGAA4iG;IAC/iGR,EAAE,EAAE,iDAAiD;IACrDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}