{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"./warning\";\n\n/**\n * Deeply compares two object literals.\n * @param obj1 object 1\n * @param obj2 object 2\n * @param shallow shallow compare\n * @returns\n */\nfunction isEqual(obj1, obj2) {\n  var shallow = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  // https://github.com/mapbox/mapbox-gl-js/pull/5979/files#diff-fde7145050c47cc3a306856efd5f9c3016e86e859de9afbd02c879be5067e58f\n  var refSet = new Set();\n  function deepEqual(a, b) {\n    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n    var circular = refSet.has(a);\n    warning(!circular, 'Warning: There may be circular references');\n    if (circular) {\n      return false;\n    }\n    if (a === b) {\n      return true;\n    }\n    if (shallow && level > 1) {\n      return false;\n    }\n    refSet.add(a);\n    var newLevel = level + 1;\n    if (Array.isArray(a)) {\n      if (!Array.isArray(b) || a.length !== b.length) {\n        return false;\n      }\n      for (var i = 0; i < a.length; i++) {\n        if (!deepEqual(a[i], b[i], newLevel)) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a && b && _typeof(a) === 'object' && _typeof(b) === 'object') {\n      var keys = Object.keys(a);\n      if (keys.length !== Object.keys(b).length) {\n        return false;\n      }\n      return keys.every(function (key) {\n        return deepEqual(a[key], b[key], newLevel);\n      });\n    }\n    // other\n    return false;\n  }\n  return deepEqual(obj1, obj2);\n}\nexport default isEqual;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}