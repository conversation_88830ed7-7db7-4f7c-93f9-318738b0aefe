{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState, useRef } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isBoolean } from '../utils';\nvar useFullscreen = function (target, options) {\n  var _a = options || {},\n    onExit = _a.onExit,\n    onEnter = _a.onEnter,\n    _b = _a.pageFullscreen,\n    pageFullscreen = _b === void 0 ? false : _b;\n  var _c = isBoolean(pageFullscreen) || !pageFullscreen ? {} : pageFullscreen,\n    _d = _c.className,\n    className = _d === void 0 ? 'ahooks-page-fullscreen' : _d,\n    _e = _c.zIndex,\n    zIndex = _e === void 0 ? 999999 : _e;\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n  // The state of full screen may be changed by other scripts/components,\n  // so the initial value needs to be computed dynamically.\n  var _f = __read(useState(getIsFullscreen), 2),\n    state = _f[0],\n    setState = _f[1];\n  var stateRef = useRef(getIsFullscreen());\n  function getIsFullscreen() {\n    return screenfull.isEnabled && !!screenfull.element && screenfull.element === getTargetElement(target);\n  }\n  var invokeCallback = function (fullscreen) {\n    var _a, _b;\n    if (fullscreen) {\n      (_a = onEnterRef.current) === null || _a === void 0 ? void 0 : _a.call(onEnterRef);\n    } else {\n      (_b = onExitRef.current) === null || _b === void 0 ? void 0 : _b.call(onExitRef);\n    }\n  };\n  var updateFullscreenState = function (fullscreen) {\n    // Prevent repeated calls when the state is not changed.\n    if (stateRef.current !== fullscreen) {\n      invokeCallback(fullscreen);\n      setState(fullscreen);\n      stateRef.current = fullscreen;\n    }\n  };\n  var onScreenfullChange = function () {\n    var fullscreen = getIsFullscreen();\n    updateFullscreenState(fullscreen);\n  };\n  var togglePageFullscreen = function (fullscreen) {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var styleElem = document.getElementById(className);\n    if (fullscreen) {\n      el.classList.add(className);\n      if (!styleElem) {\n        styleElem = document.createElement('style');\n        styleElem.setAttribute('id', className);\n        styleElem.textContent = \"\\n          .\".concat(className, \" {\\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\\n            width: 100% !important; height: 100% !important;\\n            z-index: \").concat(zIndex, \";\\n          }\");\n        el.appendChild(styleElem);\n      }\n    } else {\n      el.classList.remove(className);\n      if (styleElem) {\n        styleElem.remove();\n      }\n    }\n    updateFullscreenState(fullscreen);\n  };\n  var enterFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(true);\n      return;\n    }\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  var exitFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(false);\n      return;\n    }\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n  var toggleFullscreen = function () {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n  useEffect(function () {\n    if (!screenfull.isEnabled || pageFullscreen) {\n      return;\n    }\n    screenfull.on('change', onScreenfullChange);\n    return function () {\n      screenfull.off('change', onScreenfullChange);\n    };\n  }, []);\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\nexport default useFullscreen;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}