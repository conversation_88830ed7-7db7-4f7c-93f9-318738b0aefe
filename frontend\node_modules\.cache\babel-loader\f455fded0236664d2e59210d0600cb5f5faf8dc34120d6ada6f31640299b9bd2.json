{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\nvar initState = __assign({\n  text: ''\n}, initRect);\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n  var range = selection.getRangeAt(0);\n  var _a = range.getBoundingClientRect(),\n    height = _a.height,\n    width = _a.width,\n    top = _a.top,\n    left = _a.left,\n    right = _a.right,\n    bottom = _a.bottom;\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  var isInRangeRef = useRef(false);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var mouseupHandler = function () {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) return;\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n      if (text && isInRangeRef.current) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    };\n    // 任意点击都需要清空之前的 range\n    var mousedownHandler = function (e) {\n      // 如果是鼠标右键需要跳过 这样选中的数据就不会被清空\n      if (e.button === 2) return;\n      if (!window.getSelection) return;\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n      isInRangeRef.current = false;\n      var selObj = window.getSelection();\n      if (!selObj) return;\n      selObj.removeAllRanges();\n      isInRangeRef.current = el.contains(e.target);\n    };\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\nexport default useTextSelection;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}