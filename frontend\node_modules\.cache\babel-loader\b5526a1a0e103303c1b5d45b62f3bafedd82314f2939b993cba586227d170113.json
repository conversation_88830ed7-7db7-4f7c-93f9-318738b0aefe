{"ast": null, "code": "import React, { memo } from 'react';\nexport const CheckMark = memo(() => {\n  return React.createElement(\"svg\", {\n    width: '17px',\n    height: '13px',\n    viewBox: '0 0 17 13',\n    version: '1.1',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    stroke: 'none',\n    strokeWidth: '1',\n    fill: 'none',\n    fillRule: 'evenodd',\n    strokeLinecap: 'round',\n    strokeLinejoin: 'round'\n  }, React.createElement(\"g\", {\n    transform: 'translate(-2832.000000, -1103.000000)',\n    stroke: '#FFFFFF',\n    strokeWidth: '3'\n  }, React.createElement(\"g\", {\n    transform: 'translate(2610.000000, 955.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(24.000000, 91.000000)'\n  }, React.createElement(\"g\", {\n    transform: 'translate(179.177408, 36.687816)'\n  }, React.createElement(\"polyline\", {\n    points: '34.2767388 22 24.797043 31.4796958 21 27.6826527'\n  })))))));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}