{"ast": null, "code": "import { __assign, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\nvar useInfiniteScroll = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var target = options.target,\n    isNoMore = options.isNoMore,\n    _a = options.threshold,\n    threshold = _a === void 0 ? 100 : _a,\n    _b = options.direction,\n    direction = _b === void 0 ? 'bottom' : _b,\n    _c = options.reloadDeps,\n    reloadDeps = _c === void 0 ? [] : _c,\n    manual = options.manual,\n    onBefore = options.onBefore,\n    onSuccess = options.onSuccess,\n    onError = options.onError,\n    onFinally = options.onFinally;\n  var _d = __read(useState(), 2),\n    finalData = _d[0],\n    setFinalData = _d[1];\n  var _e = __read(useState(false), 2),\n    loadingMore = _e[0],\n    setLoadingMore = _e[1];\n  var isScrollToTop = direction === 'top';\n  // lastScrollTop is used to determine whether the scroll direction is up or down\n  var lastScrollTop = useRef();\n  // scrollBottom is used to record the distance from the bottom of the scroll bar\n  var scrollBottom = useRef(0);\n  var noMore = useMemo(function () {\n    if (!isNoMore) return false;\n    return isNoMore(finalData);\n  }, [finalData]);\n  var _f = useRequest(function (lastData) {\n      return __awaiter(void 0, void 0, void 0, function () {\n        var currentData;\n        var _a, _b, _c;\n        return __generator(this, function (_d) {\n          switch (_d.label) {\n            case 0:\n              return [4 /*yield*/, service(lastData)];\n            case 1:\n              currentData = _d.sent();\n              if (!lastData) {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: __spreadArray([], __read((_a = currentData.list) !== null && _a !== void 0 ? _a : []), false)\n                }));\n              } else {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: isScrollToTop ? __spreadArray(__spreadArray([], __read(currentData.list), false), __read((_b = lastData.list) !== null && _b !== void 0 ? _b : []), false) : __spreadArray(__spreadArray([], __read((_c = lastData.list) !== null && _c !== void 0 ? _c : []), false), __read(currentData.list), false)\n                }));\n              }\n              return [2 /*return*/, currentData];\n          }\n        });\n      });\n    }, {\n      manual: manual,\n      onFinally: function (_, d, e) {\n        setLoadingMore(false);\n        onFinally === null || onFinally === void 0 ? void 0 : onFinally(d, e);\n      },\n      onBefore: function () {\n        return onBefore === null || onBefore === void 0 ? void 0 : onBefore();\n      },\n      onSuccess: function (d) {\n        setTimeout(function () {\n          if (isScrollToTop) {\n            var el = getTargetElement(target);\n            el = el === document ? document.documentElement : el;\n            if (el) {\n              var scrollHeight = getScrollHeight(el);\n              el.scrollTo(0, scrollHeight - scrollBottom.current);\n            }\n          } else {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            scrollMethod();\n          }\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(d);\n      },\n      onError: function (e) {\n        return onError === null || onError === void 0 ? void 0 : onError(e);\n      }\n    }),\n    loading = _f.loading,\n    error = _f.error,\n    run = _f.run,\n    runAsync = _f.runAsync,\n    cancel = _f.cancel;\n  var loadMore = useMemoizedFn(function () {\n    if (noMore) return;\n    setLoadingMore(true);\n    run(finalData);\n  });\n  var loadMoreAsync = useMemoizedFn(function () {\n    if (noMore) return Promise.reject();\n    setLoadingMore(true);\n    return runAsync(finalData);\n  });\n  var reload = function () {\n    setLoadingMore(false);\n    return run();\n  };\n  var reloadAsync = function () {\n    setLoadingMore(false);\n    return runAsync();\n  };\n  var scrollMethod = function () {\n    var el = getTargetElement(target);\n    if (!el) return;\n    var targetEl = el === document ? document.documentElement : el;\n    var scrollTop = getScrollTop(targetEl);\n    var scrollHeight = getScrollHeight(targetEl);\n    var clientHeight = getClientHeight(targetEl);\n    if (isScrollToTop) {\n      if (lastScrollTop.current !== undefined && lastScrollTop.current > scrollTop && scrollTop <= threshold) {\n        loadMore();\n      }\n      lastScrollTop.current = scrollTop;\n      scrollBottom.current = scrollHeight - scrollTop;\n    } else if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    error: error,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: loadMore,\n    loadMoreAsync: loadMoreAsync,\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\nexport default useInfiniteScroll;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}