{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isLeapYear = t();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, t) {\n    t.prototype.isLeapYear = function () {\n      return this.$y % 4 == 0 && this.$y % 100 != 0 || this.$y % 400 == 0;\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isLeapYear", "prototype", "isLeapYear", "$y"], "sources": ["C:/Users/<USER>/node_modules/dayjs/plugin/isLeapYear.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isLeapYear=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.isLeapYear=function(){return this.$y%4==0&&this.$y%100!=0||this.$y%400==0}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,uBAAuB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAACA,CAAC,CAACQ,SAAS,CAACC,UAAU,GAAC,YAAU;MAAC,OAAO,IAAI,CAACC,EAAE,GAAC,CAAC,IAAE,CAAC,IAAE,IAAI,CAACA,EAAE,GAAC,GAAG,IAAE,CAAC,IAAE,IAAI,CAACA,EAAE,GAAC,GAAG,IAAE,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}