{"ast": null, "code": "import { __assign, __awaiter, __generator, __read, __rest, __spreadArray } from \"tslib\";\n/* eslint-disable @typescript-eslint/no-parameter-properties */\nimport { isFunction } from '../../utils';\nvar Fetch = /** @class */function () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    // @ts-ignore\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n  Fetch.prototype.runAsync = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _a, _b, stopNow, _c, returnNow, state, servicePromise, res, error_1;\n      var _d;\n      var _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _a = this.runPluginHandler('onBefore', params), _b = _a.stopNow, stopNow = _b === void 0 ? false : _b, _c = _a.returnNow, returnNow = _c === void 0 ? false : _c, state = __rest(_a, [\"stopNow\", \"returnNow\"]);\n            // stop request\n            if (stopNow) {\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state));\n            // return now\n            if (returnNow) {\n              return [2 /*return*/, Promise.resolve(state.data)];\n            }\n            (_f = (_e = this.options).onBefore) === null || _f === void 0 ? void 0 : _f.call(_e, params);\n            _q.label = 1;\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n            if (!servicePromise) {\n              servicePromise = (_d = this.serviceRef).current.apply(_d, __spreadArray([], __read(params), false));\n            }\n            return [4 /*yield*/, servicePromise];\n          case 2:\n            res = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_h = (_g = this.options).onSuccess) === null || _h === void 0 ? void 0 : _h.call(_g, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, res, undefined);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n            return [2 /*return*/, res];\n          case 3:\n            error_1 = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_m = (_l = this.options).onError) === null || _m === void 0 ? void 0 : _m.call(_l, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_p = (_o = this.options).onFinally) === null || _p === void 0 ? void 0 : _p.call(_o, params, undefined, error_1);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n            throw error_1;\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  Fetch.prototype.run = function () {\n    var _this = this;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    this.runAsync.apply(this, __spreadArray([], __read(params), false)).catch(function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n  return Fetch;\n}();\nexport default Fetch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}