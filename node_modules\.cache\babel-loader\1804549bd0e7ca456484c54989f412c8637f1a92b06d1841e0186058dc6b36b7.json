{"ast": null, "code": "import * as React from \"react\";\nfunction SystemQRcodeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SystemQRcodeOutline-SystemQRcodeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SystemQRcodeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SystemQRcodeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.4999772,37.3333332 L28.4999772,40.5000014 L35.6666664,40.5000014 C35.8499998,40.5000014 36,40.6500014 36,40.8333346 L36,42.6666668 C36,42.8507617 35.8507615,43 35.6666664,43 L26.3333337,43 C26.1492385,43 26,42.8507617 26,42.6666668 L26,37.3333461 C26,37.1500129 26.1500002,37.0000129 26.3333336,37.0000129 L28.1666681,37.0000129 C28.349977,37.0000129 28.4999772,37.15 28.4999772,37.3333332 Z M42.638096,37.5714068 C42.8371432,37.5714068 43,37.7342638 43,37.9333111 L43,42.6380558 L43,42.6380558 C43,42.8379301 42.8379702,42.9999601 42.638096,42.9999601 L40.6476249,42.9999601 L40.6476249,42.9999601 C40.4477508,42.9999601 40.2857209,42.8379301 40.2857209,42.6380558 L40.2857209,37.9333111 C40.2857209,37.7342638 40.4485778,37.5714068 40.6476249,37.5714068 L42.638096,37.5714068 L42.638096,37.5714068 Z M36.5999999,35 C36.8199999,35 37,35.1800001 37,35.4000001 L37,37.5999999 C37,37.7932997 36.8628872,37.9545749 36.6806139,37.9918734 L36.5999999,38 L36.5999999,38 L34.4000001,38 C34.179086,38 34,37.820914 34,37.5999999 L34,35.4000001 C34,35.1800001 34.1800001,35 34.4000001,35 L36.5999999,35 Z M36.6666662,26 C36.8499998,26 37,26.1600001 37,26.3555556 L37,28.3111105 C37,28.5074786 36.8507615,28.6666661 36.6666664,28.6666661 L28.4999772,28.6666661 L28.4999772,31.3333339 L26.3333337,34 C26.1492385,34 26,33.8408124 26,33.6444444 L26,26.3555664 C26,26.1600109 26.1500002,26 26.3333336,26 L36.6666662,26 Z M42.6571437,31 C42.8457146,31 43,31.1800001 43,31.4000001 L43,33.5999999 C43,33.7932997 42.8824751,33.9545749 42.7262413,33.9918734 L42.6571437,34 L42.6571437,34 L37.3428563,34 C37.1771712,34 37.0389357,33.8628872 37.0069656,33.6806139 L37,33.5999999 L37,33.5999999 L37,31.4000001 C37,31.1800001 37.1542854,31 37.3428563,31 L42.6571437,31 Z M33.600001,31 C33.8200004,31 34,31.1800001 34,31.4000001 L34,33.5999999 C34,33.820914 33.8209145,34 33.600001,34 L26.399999,34 C26.1790855,34 26,33.820914 26,33.5999999 L26,31.4000001 C26,31.1800001 26.1799996,31 26.399999,31 L33.600001,31 Z M42.5999999,26 C42.8199999,26 43,26.1800001 43,26.4000001 L43,28.5999999 C43,28.7932997 42.8628872,28.9545749 42.6806139,28.9918734 L42.5999999,29 L42.5999999,29 L40.4000001,29 C40.2067003,29 40.0454251,28.8628872 40.0081266,28.6806139 L40,28.5999999 L40,28.5999999 L40,26.4000001 C40,26.1800001 40.1800001,26 40.4000001,26 L42.5999999,26 Z M36.6363635,26 C36.8363635,26 37,26.1800001 37,26.4000001 L37,28.5999999 C37,28.820914 36.8371945,29 36.6363636,29 L27.3636365,29 C27.1628055,29 27,28.820914 27,28.5999999 L27,26.4000001 C27,26.1800001 27.1636365,26 27.3636364,26 L36.6363635,26 Z M22,8.57894362 L22,18.4210564 C22,20.3976532 20.3976532,22 18.4210564,22 L8.57894374,22 C6.60234679,22 5,20.3976532 5,18.4210564 L5,8.57894362 C5,6.60234735 6.60234679,5 8.57894358,5 L18.4210563,5 C20.3976532,5 22,6.60234681 22,8.57894362 Z M43,8.57894362 L43,18.4210564 C43,20.3976532 41.3976532,22 39.4210564,22 L29.5789437,22 C27.6023468,22 26,20.3976532 26,18.4210564 L26,8.57894362 C26,6.60234735 27.6023468,5 29.5789436,5 L39.4210563,5 C41.3976532,5 43,6.60234681 43,8.57894362 Z M18.1538487,8 L8.84615295,8 L8.84615295,8 C8.41717324,8 8.05611316,8.32112097 8.00592306,8.74715167 L8,8.84615155 L8,18.1538464 L8,18.1537349 C8,18.5827146 8.32101708,18.9438183 8.74704068,18.9940639 L8.84615124,19 L18.153847,19 L18.153847,19 C18.5828268,19 18.9438868,18.678879 18.9940769,18.2528483 L19,18.1538484 L19,8.84615359 L19,8.8462652 C19,8.41728552 18.6789833,8.05618185 18.2529593,8.00593623 L18.1538488,8 L18.1538487,8 Z M39.1538487,8 L29.8461529,8 L29.846153,8 C29.4171732,8 29.0561132,8.32112097 29.0059231,8.74715167 L29,8.84615155 L29,18.1538464 L29,18.1537349 C29,18.5827146 29.3210171,18.9438183 29.7470407,18.9940639 L29.8461512,19 L39.153847,19 L39.153847,19 C39.5828268,19 39.9438868,18.678879 39.9940769,18.2528483 L40,18.1538484 L40,8.84615359 L40,8.8462652 C40,8.41728552 39.6789833,8.05618185 39.2529593,8.00593623 L39.1538488,8 L39.1538487,8 Z M14.9523899,11.3333277 L14.9523899,11.3333277 C15.4520763,11.3333277 15.8571487,11.7384032 15.8571487,12.2380876 C15.8571487,12.2380876 15.8571487,12.2380876 15.8571487,12.2380876 L15.8571487,14.9523715 L15.8571487,14.9523715 C15.8571487,15.4520584 15.4520737,15.8571314 14.9523899,15.8571314 L12.2381091,15.8571314 L12.2381092,15.8571314 C11.7384228,15.8571314 11.3333503,15.4520559 11.3333503,14.9523715 C11.3333503,14.9523715 11.3333503,14.9523715 11.3333503,14.9523715 L11.3333503,12.2380876 L11.3333503,12.2380877 C11.3333503,11.7384007 11.7384254,11.3333277 12.2381092,11.3333277 C12.2381092,11.3333277 12.2381092,11.3333277 12.2381093,11.3333277 L14.95239,11.3333277 L14.9523899,11.3333277 Z M22,29.5789436 L22,39.4210564 C22,41.3976532 20.3976532,43 18.4210564,43 L8.57894374,43 C6.60234679,43 5,41.3976532 5,39.4210564 L5,29.5789436 C5,27.6023473 6.60234679,26 8.57894358,26 L18.4210563,26 C20.3976532,26 22,27.6023468 22,29.5789436 Z M18.1538487,29 L8.84615295,29 L8.84615295,29 C8.41717324,29 8.05611316,29.321121 8.00592306,29.7471517 L8,29.8461516 L8,39.1538464 L8,39.1537349 C8,39.5827146 8.32101708,39.9438183 8.74704068,39.9940639 L8.84615124,40 L18.153847,40 L18.153847,40 C18.5828268,40 18.9438868,39.678879 18.9940769,39.2528483 L19,39.1538484 L19,29.8461536 L19,29.8462652 C19,29.4172855 18.6789833,29.0561818 18.2529593,29.0059362 L18.1538488,29 L18.1538487,29 Z M14.9523899,32.3333277 L14.9523899,32.3333277 C15.4520763,32.3333277 15.8571487,32.7384032 15.8571487,33.2380876 C15.8571487,33.2380876 15.8571487,33.2380876 15.8571487,33.2380876 L15.8571487,35.9523715 L15.8571487,35.9523715 C15.8571487,36.4520584 15.4520737,36.8571314 14.9523899,36.8571314 L12.2381091,36.8571314 L12.2381092,36.8571314 C11.7384228,36.8571314 11.3333503,36.4520559 11.3333503,35.9523715 C11.3333503,35.9523715 11.3333503,35.9523715 11.3333503,35.9523715 L11.3333503,33.2380876 L11.3333503,33.2380877 C11.3333503,32.7384007 11.7384254,32.3333277 12.2381092,32.3333277 C12.2381092,32.3333277 12.2381092,32.3333277 12.2381093,32.3333277 L14.95239,32.3333277 L14.9523899,32.3333277 Z M35.9523899,11.3333277 L35.9523899,11.3333277 C36.4520763,11.3333277 36.8571487,11.7384032 36.8571487,12.2380876 C36.8571487,12.2380876 36.8571487,12.2380876 36.8571487,12.2380876 L36.8571487,14.9523715 L36.8571487,14.9523715 C36.8571487,15.4520584 36.4520737,15.8571314 35.9523899,15.8571314 L33.2381091,15.8571314 L33.2381092,15.8571314 C32.7384228,15.8571314 32.3333503,15.4520559 32.3333503,14.9523715 C32.3333503,14.9523715 32.3333503,14.9523715 32.3333503,14.9523715 L32.3333503,12.2380876 L32.3333503,12.2380877 C32.3333503,11.7384007 32.7384254,11.3333277 33.2381092,11.3333277 C33.2381092,11.3333277 33.2381092,11.3333277 33.2381093,11.3333277 L35.95239,11.3333277 L35.9523899,11.3333277 Z\",\n    id: \"SystemQRcodeOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default SystemQRcodeOutline;", "map": {"version": 3, "names": ["React", "SystemQRcodeOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/SystemQRcodeOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction SystemQRcodeOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SystemQRcodeOutline-SystemQRcodeOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SystemQRcodeOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SystemQRcodeOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M28.4999772,37.3333332 L28.4999772,40.5000014 L35.6666664,40.5000014 C35.8499998,40.5000014 36,40.6500014 36,40.8333346 L36,42.6666668 C36,42.8507617 35.8507615,43 35.6666664,43 L26.3333337,43 C26.1492385,43 26,42.8507617 26,42.6666668 L26,37.3333461 C26,37.1500129 26.1500002,37.0000129 26.3333336,37.0000129 L28.1666681,37.0000129 C28.349977,37.0000129 28.4999772,37.15 28.4999772,37.3333332 Z M42.638096,37.5714068 C42.8371432,37.5714068 43,37.7342638 43,37.9333111 L43,42.6380558 L43,42.6380558 C43,42.8379301 42.8379702,42.9999601 42.638096,42.9999601 L40.6476249,42.9999601 L40.6476249,42.9999601 C40.4477508,42.9999601 40.2857209,42.8379301 40.2857209,42.6380558 L40.2857209,37.9333111 C40.2857209,37.7342638 40.4485778,37.5714068 40.6476249,37.5714068 L42.638096,37.5714068 L42.638096,37.5714068 Z M36.5999999,35 C36.8199999,35 37,35.1800001 37,35.4000001 L37,37.5999999 C37,37.7932997 36.8628872,37.9545749 36.6806139,37.9918734 L36.5999999,38 L36.5999999,38 L34.4000001,38 C34.179086,38 34,37.820914 34,37.5999999 L34,35.4000001 C34,35.1800001 34.1800001,35 34.4000001,35 L36.5999999,35 Z M36.6666662,26 C36.8499998,26 37,26.1600001 37,26.3555556 L37,28.3111105 C37,28.5074786 36.8507615,28.6666661 36.6666664,28.6666661 L28.4999772,28.6666661 L28.4999772,31.3333339 L26.3333337,34 C26.1492385,34 26,33.8408124 26,33.6444444 L26,26.3555664 C26,26.1600109 26.1500002,26 26.3333336,26 L36.6666662,26 Z M42.6571437,31 C42.8457146,31 43,31.1800001 43,31.4000001 L43,33.5999999 C43,33.7932997 42.8824751,33.9545749 42.7262413,33.9918734 L42.6571437,34 L42.6571437,34 L37.3428563,34 C37.1771712,34 37.0389357,33.8628872 37.0069656,33.6806139 L37,33.5999999 L37,33.5999999 L37,31.4000001 C37,31.1800001 37.1542854,31 37.3428563,31 L42.6571437,31 Z M33.600001,31 C33.8200004,31 34,31.1800001 34,31.4000001 L34,33.5999999 C34,33.820914 33.8209145,34 33.600001,34 L26.399999,34 C26.1790855,34 26,33.820914 26,33.5999999 L26,31.4000001 C26,31.1800001 26.1799996,31 26.399999,31 L33.600001,31 Z M42.5999999,26 C42.8199999,26 43,26.1800001 43,26.4000001 L43,28.5999999 C43,28.7932997 42.8628872,28.9545749 42.6806139,28.9918734 L42.5999999,29 L42.5999999,29 L40.4000001,29 C40.2067003,29 40.0454251,28.8628872 40.0081266,28.6806139 L40,28.5999999 L40,28.5999999 L40,26.4000001 C40,26.1800001 40.1800001,26 40.4000001,26 L42.5999999,26 Z M36.6363635,26 C36.8363635,26 37,26.1800001 37,26.4000001 L37,28.5999999 C37,28.820914 36.8371945,29 36.6363636,29 L27.3636365,29 C27.1628055,29 27,28.820914 27,28.5999999 L27,26.4000001 C27,26.1800001 27.1636365,26 27.3636364,26 L36.6363635,26 Z M22,8.57894362 L22,18.4210564 C22,20.3976532 20.3976532,22 18.4210564,22 L8.57894374,22 C6.60234679,22 5,20.3976532 5,18.4210564 L5,8.57894362 C5,6.60234735 6.60234679,5 8.57894358,5 L18.4210563,5 C20.3976532,5 22,6.60234681 22,8.57894362 Z M43,8.57894362 L43,18.4210564 C43,20.3976532 41.3976532,22 39.4210564,22 L29.5789437,22 C27.6023468,22 26,20.3976532 26,18.4210564 L26,8.57894362 C26,6.60234735 27.6023468,5 29.5789436,5 L39.4210563,5 C41.3976532,5 43,6.60234681 43,8.57894362 Z M18.1538487,8 L8.84615295,8 L8.84615295,8 C8.41717324,8 8.05611316,8.32112097 8.00592306,8.74715167 L8,8.84615155 L8,18.1538464 L8,18.1537349 C8,18.5827146 8.32101708,18.9438183 8.74704068,18.9940639 L8.84615124,19 L18.153847,19 L18.153847,19 C18.5828268,19 18.9438868,18.678879 18.9940769,18.2528483 L19,18.1538484 L19,8.84615359 L19,8.8462652 C19,8.41728552 18.6789833,8.05618185 18.2529593,8.00593623 L18.1538488,8 L18.1538487,8 Z M39.1538487,8 L29.8461529,8 L29.846153,8 C29.4171732,8 29.0561132,8.32112097 29.0059231,8.74715167 L29,8.84615155 L29,18.1538464 L29,18.1537349 C29,18.5827146 29.3210171,18.9438183 29.7470407,18.9940639 L29.8461512,19 L39.153847,19 L39.153847,19 C39.5828268,19 39.9438868,18.678879 39.9940769,18.2528483 L40,18.1538484 L40,8.84615359 L40,8.8462652 C40,8.41728552 39.6789833,8.05618185 39.2529593,8.00593623 L39.1538488,8 L39.1538487,8 Z M14.9523899,11.3333277 L14.9523899,11.3333277 C15.4520763,11.3333277 15.8571487,11.7384032 15.8571487,12.2380876 C15.8571487,12.2380876 15.8571487,12.2380876 15.8571487,12.2380876 L15.8571487,14.9523715 L15.8571487,14.9523715 C15.8571487,15.4520584 15.4520737,15.8571314 14.9523899,15.8571314 L12.2381091,15.8571314 L12.2381092,15.8571314 C11.7384228,15.8571314 11.3333503,15.4520559 11.3333503,14.9523715 C11.3333503,14.9523715 11.3333503,14.9523715 11.3333503,14.9523715 L11.3333503,12.2380876 L11.3333503,12.2380877 C11.3333503,11.7384007 11.7384254,11.3333277 12.2381092,11.3333277 C12.2381092,11.3333277 12.2381092,11.3333277 12.2381093,11.3333277 L14.95239,11.3333277 L14.9523899,11.3333277 Z M22,29.5789436 L22,39.4210564 C22,41.3976532 20.3976532,43 18.4210564,43 L8.57894374,43 C6.60234679,43 5,41.3976532 5,39.4210564 L5,29.5789436 C5,27.6023473 6.60234679,26 8.57894358,26 L18.4210563,26 C20.3976532,26 22,27.6023468 22,29.5789436 Z M18.1538487,29 L8.84615295,29 L8.84615295,29 C8.41717324,29 8.05611316,29.321121 8.00592306,29.7471517 L8,29.8461516 L8,39.1538464 L8,39.1537349 C8,39.5827146 8.32101708,39.9438183 8.74704068,39.9940639 L8.84615124,40 L18.153847,40 L18.153847,40 C18.5828268,40 18.9438868,39.678879 18.9940769,39.2528483 L19,39.1538484 L19,29.8461536 L19,29.8462652 C19,29.4172855 18.6789833,29.0561818 18.2529593,29.0059362 L18.1538488,29 L18.1538487,29 Z M14.9523899,32.3333277 L14.9523899,32.3333277 C15.4520763,32.3333277 15.8571487,32.7384032 15.8571487,33.2380876 C15.8571487,33.2380876 15.8571487,33.2380876 15.8571487,33.2380876 L15.8571487,35.9523715 L15.8571487,35.9523715 C15.8571487,36.4520584 15.4520737,36.8571314 14.9523899,36.8571314 L12.2381091,36.8571314 L12.2381092,36.8571314 C11.7384228,36.8571314 11.3333503,36.4520559 11.3333503,35.9523715 C11.3333503,35.9523715 11.3333503,35.9523715 11.3333503,35.9523715 L11.3333503,33.2380876 L11.3333503,33.2380877 C11.3333503,32.7384007 11.7384254,32.3333277 12.2381092,32.3333277 C12.2381092,32.3333277 12.2381092,32.3333277 12.2381093,32.3333277 L14.95239,32.3333277 L14.9523899,32.3333277 Z M35.9523899,11.3333277 L35.9523899,11.3333277 C36.4520763,11.3333277 36.8571487,11.7384032 36.8571487,12.2380876 C36.8571487,12.2380876 36.8571487,12.2380876 36.8571487,12.2380876 L36.8571487,14.9523715 L36.8571487,14.9523715 C36.8571487,15.4520584 36.4520737,15.8571314 35.9523899,15.8571314 L33.2381091,15.8571314 L33.2381092,15.8571314 C32.7384228,15.8571314 32.3333503,15.4520559 32.3333503,14.9523715 C32.3333503,14.9523715 32.3333503,14.9523715 32.3333503,14.9523715 L32.3333503,12.2380876 L32.3333503,12.2380877 C32.3333503,11.7384007 32.7384254,11.3333277 33.2381092,11.3333277 C33.2381092,11.3333277 33.2381092,11.3333277 33.2381093,11.3333277 L35.95239,11.3333277 L35.9523899,11.3333277 Z\",\n    id: \"SystemQRcodeOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default SystemQRcodeOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yCAAyC;IAC7CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,ikNAAikN;IACpkNR,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}