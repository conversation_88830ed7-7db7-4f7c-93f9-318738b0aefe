{"ast": null, "code": "import React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport runes from 'runes2';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { devError } from '../../utils/dev-log';\nimport useInputHandleKeyDown from '../../components/input/useInputHandleKeyDown';\nconst classPrefix = 'adm-text-area';\nconst defaultProps = {\n  rows: 2,\n  showCount: false,\n  autoSize: false,\n  defaultValue: ''\n};\nexport const TextArea = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    autoSize,\n    showCount,\n    maxLength\n  } = props;\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    value: props.value === null ? '' : props.value\n  }));\n  if (props.value === null) {\n    devError('TextArea', '`value` prop on `TextArea` should not be `null`. Consider using an empty string to clear the component.');\n  }\n  const nativeTextAreaRef = useRef(null);\n  // https://github.com/ant-design/ant-design-mobile/issues/5961\n  const heightRef = useRef('auto');\n  // https://github.com/ant-design/ant-design-mobile/issues/6051\n  const hiddenTextAreaRef = useRef(null);\n  const handleKeydown = useInputHandleKeyDown({\n    onEnterPress: props.onEnterPress,\n    onKeyDown: props.onKeyDown,\n    nativeInputRef: nativeTextAreaRef,\n    enterKeyHint: props.enterKeyHint\n  });\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      setValue('');\n    },\n    focus: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      return nativeTextAreaRef.current;\n    }\n  }));\n  useIsomorphicLayoutEffect(() => {\n    if (!autoSize) return;\n    const textArea = nativeTextAreaRef.current;\n    const hiddenTextArea = hiddenTextAreaRef.current;\n    if (!textArea) return;\n    textArea.style.height = heightRef.current;\n    if (!hiddenTextArea) return;\n    let height = hiddenTextArea.scrollHeight;\n    if (typeof autoSize === 'object') {\n      const computedStyle = window.getComputedStyle(textArea);\n      const lineHeight = parseFloat(computedStyle.lineHeight);\n      if (autoSize.minRows) {\n        height = Math.max(height, autoSize.minRows * lineHeight);\n      }\n      if (autoSize.maxRows) {\n        height = Math.min(height, autoSize.maxRows * lineHeight);\n      }\n    }\n    heightRef.current = `${height}px`;\n    textArea.style.height = `${height}px`;\n  }, [value, autoSize]);\n  const compositingRef = useRef(false);\n  let count;\n  const valueLength = runes(value).length;\n  if (typeof showCount === 'function') {\n    count = showCount(valueLength, maxLength);\n  } else if (showCount) {\n    count = React.createElement(\"div\", {\n      className: `${classPrefix}-count`\n    }, maxLength === undefined ? valueLength : valueLength + '/' + maxLength);\n  }\n  let rows = props.rows;\n  if (typeof autoSize === 'object') {\n    if (autoSize.maxRows && rows > autoSize.maxRows) {\n      rows = autoSize.maxRows;\n    }\n    if (autoSize.minRows && rows < autoSize.minRows) {\n      rows = autoSize.minRows;\n    }\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"textarea\", {\n    ref: nativeTextAreaRef,\n    className: `${classPrefix}-element`,\n    rows: rows,\n    value: value,\n    placeholder: props.placeholder,\n    onChange: e => {\n      let v = e.target.value;\n      if (maxLength && !compositingRef.current) {\n        v = runes(v).slice(0, maxLength).join('');\n      }\n      setValue(v);\n    },\n    id: props.id,\n    onCompositionStart: e => {\n      var _a;\n      compositingRef.current = true;\n      (_a = props.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      compositingRef.current = false;\n      if (maxLength) {\n        const v = e.target.value;\n        setValue(runes(v).slice(0, maxLength).join(''));\n      }\n      (_a = props.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    autoComplete: props.autoComplete,\n    autoFocus: props.autoFocus,\n    disabled: props.disabled,\n    readOnly: props.readOnly,\n    name: props.name,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur,\n    onClick: props.onClick,\n    onKeyDown: handleKeydown\n  }), count, autoSize && React.createElement(\"textarea\", {\n    ref: hiddenTextAreaRef,\n    className: `${classPrefix}-element ${classPrefix}-element-hidden`,\n    value: value,\n    rows: rows,\n    \"aria-hidden\": true,\n    readOnly: true\n  })));\n});\nTextArea.defaultProps = defaultProps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}