{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useResultIcon } from './use-result-icon';\nconst classPrefix = `adm-result`;\nconst defaultProps = {\n  status: 'info'\n};\nexport const Result = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    status,\n    title,\n    description,\n    icon\n  } = props;\n  const fallbackIcon = useResultIcon(status);\n  if (!status) return null;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${status}`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon`\n  }, icon || fallbackIcon), React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), !!description && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description)));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}