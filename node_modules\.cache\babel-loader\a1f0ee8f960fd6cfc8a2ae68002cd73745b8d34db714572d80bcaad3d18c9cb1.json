{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { ModalActionButton } from './modal-action-button';\nimport Image from '../image';\nimport Space from '../space';\nimport AutoCenter from '../auto-center';\nimport CenterPopup from '../center-popup';\nconst defaultProps = {\n  actions: [],\n  closeOnAction: false,\n  closeOnMaskClick: false,\n  getContainer: null\n};\nexport const Modal = p => {\n  const props = mergeProps(defaultProps, p);\n  const element = React.createElement(React.Fragment, null, !!props.image && React.createElement(\"div\", {\n    className: cls('image-container')\n  }, React.createElement(Image, {\n    src: props.image,\n    alt: 'modal header image',\n    width: '100%'\n  })), !!props.header && React.createElement(\"div\", {\n    className: cls('header')\n  }, React.createElement(AutoCenter, null, props.header)), !!props.title && React.createElement(\"div\", {\n    className: cls('title')\n  }, props.title), React.createElement(\"div\", {\n    className: cls('content')\n  }, typeof props.content === 'string' ? React.createElement(AutoCenter, null, props.content) : props.content), React.createElement(Space, {\n    direction: 'vertical',\n    block: true,\n    className: classNames(cls('footer'), props.actions.length === 0 && cls('footer-empty'))\n  }, props.actions.map((action, index) => React.createElement(ModalActionButton, {\n    key: action.key,\n    action: action,\n    onAction: () => __awaiter(void 0, void 0, void 0, function* () {\n      var _a, _b, _c;\n      yield Promise.all([(_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action), (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index)]);\n      if (props.closeOnAction) {\n        (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n      }\n    })\n  }))));\n  return React.createElement(CenterPopup, {\n    className: classNames(cls(), props.className),\n    style: props.style,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    showCloseButton: props.showCloseButton,\n    closeOnMaskClick: props.closeOnMaskClick,\n    onClose: props.onClose,\n    visible: props.visible,\n    getContainer: props.getContainer,\n    bodyStyle: props.bodyStyle,\n    bodyClassName: classNames(cls('body'), props.image && cls('with-image'), props.bodyClassName),\n    maskStyle: props.maskStyle,\n    maskClassName: props.maskClassName,\n    stopPropagation: props.stopPropagation,\n    disableBodyScroll: props.disableBodyScroll,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, element);\n};\nfunction cls(name = '') {\n  return 'adm-modal' + (name && '-') + name;\n}", "map": {"version": 3, "names": ["__awaiter", "React", "mergeProps", "classNames", "ModalActionButton", "Image", "Space", "AutoCenter", "CenterPopup", "defaultProps", "actions", "closeOnAction", "closeOnMaskClick", "getContainer", "Modal", "p", "props", "element", "createElement", "Fragment", "image", "className", "cls", "src", "alt", "width", "header", "title", "content", "direction", "block", "length", "map", "action", "index", "key", "onAction", "_a", "_b", "_c", "Promise", "all", "onClick", "call", "onClose", "style", "afterClose", "afterShow", "showCloseButton", "visible", "bodyStyle", "bodyClassName", "maskStyle", "maskClassName", "stopPropagation", "disableBodyScroll", "destroyOnClose", "forceRender", "name"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/modal/modal.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport classNames from 'classnames';\nimport { ModalActionButton } from './modal-action-button';\nimport Image from '../image';\nimport Space from '../space';\nimport AutoCenter from '../auto-center';\nimport CenterPopup from '../center-popup';\nconst defaultProps = {\n  actions: [],\n  closeOnAction: false,\n  closeOnMaskClick: false,\n  getContainer: null\n};\nexport const Modal = p => {\n  const props = mergeProps(defaultProps, p);\n  const element = React.createElement(React.Fragment, null, !!props.image && React.createElement(\"div\", {\n    className: cls('image-container')\n  }, React.createElement(Image, {\n    src: props.image,\n    alt: 'modal header image',\n    width: '100%'\n  })), !!props.header && React.createElement(\"div\", {\n    className: cls('header')\n  }, React.createElement(AutoCenter, null, props.header)), !!props.title && React.createElement(\"div\", {\n    className: cls('title')\n  }, props.title), React.createElement(\"div\", {\n    className: cls('content')\n  }, typeof props.content === 'string' ? React.createElement(AutoCenter, null, props.content) : props.content), React.createElement(Space, {\n    direction: 'vertical',\n    block: true,\n    className: classNames(cls('footer'), props.actions.length === 0 && cls('footer-empty'))\n  }, props.actions.map((action, index) => React.createElement(ModalActionButton, {\n    key: action.key,\n    action: action,\n    onAction: () => __awaiter(void 0, void 0, void 0, function* () {\n      var _a, _b, _c;\n      yield Promise.all([(_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action), (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, index)]);\n      if (props.closeOnAction) {\n        (_c = props.onClose) === null || _c === void 0 ? void 0 : _c.call(props);\n      }\n    })\n  }))));\n  return React.createElement(CenterPopup, {\n    className: classNames(cls(), props.className),\n    style: props.style,\n    afterClose: props.afterClose,\n    afterShow: props.afterShow,\n    showCloseButton: props.showCloseButton,\n    closeOnMaskClick: props.closeOnMaskClick,\n    onClose: props.onClose,\n    visible: props.visible,\n    getContainer: props.getContainer,\n    bodyStyle: props.bodyStyle,\n    bodyClassName: classNames(cls('body'), props.image && cls('with-image'), props.bodyClassName),\n    maskStyle: props.maskStyle,\n    maskClassName: props.maskClassName,\n    stopPropagation: props.stopPropagation,\n    disableBodyScroll: props.disableBodyScroll,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, element);\n};\nfunction cls(name = '') {\n  return 'adm-modal' + (name && '-') + name;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,WAAW,MAAM,iBAAiB;AACzC,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,KAAK;EACpBC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGd,UAAU,CAACO,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAME,OAAO,GAAGhB,KAAK,CAACiB,aAAa,CAACjB,KAAK,CAACkB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAACH,KAAK,CAACI,KAAK,IAAInB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IACpGG,SAAS,EAAEC,GAAG,CAAC,iBAAiB;EAClC,CAAC,EAAErB,KAAK,CAACiB,aAAa,CAACb,KAAK,EAAE;IAC5BkB,GAAG,EAAEP,KAAK,CAACI,KAAK;IAChBI,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAACT,KAAK,CAACU,MAAM,IAAIzB,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAChDG,SAAS,EAAEC,GAAG,CAAC,QAAQ;EACzB,CAAC,EAAErB,KAAK,CAACiB,aAAa,CAACX,UAAU,EAAE,IAAI,EAAES,KAAK,CAACU,MAAM,CAAC,CAAC,EAAE,CAAC,CAACV,KAAK,CAACW,KAAK,IAAI1B,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IACnGG,SAAS,EAAEC,GAAG,CAAC,OAAO;EACxB,CAAC,EAAEN,KAAK,CAACW,KAAK,CAAC,EAAE1B,KAAK,CAACiB,aAAa,CAAC,KAAK,EAAE;IAC1CG,SAAS,EAAEC,GAAG,CAAC,SAAS;EAC1B,CAAC,EAAE,OAAON,KAAK,CAACY,OAAO,KAAK,QAAQ,GAAG3B,KAAK,CAACiB,aAAa,CAACX,UAAU,EAAE,IAAI,EAAES,KAAK,CAACY,OAAO,CAAC,GAAGZ,KAAK,CAACY,OAAO,CAAC,EAAE3B,KAAK,CAACiB,aAAa,CAACZ,KAAK,EAAE;IACvIuB,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,IAAI;IACXT,SAAS,EAAElB,UAAU,CAACmB,GAAG,CAAC,QAAQ,CAAC,EAAEN,KAAK,CAACN,OAAO,CAACqB,MAAM,KAAK,CAAC,IAAIT,GAAG,CAAC,cAAc,CAAC;EACxF,CAAC,EAAEN,KAAK,CAACN,OAAO,CAACsB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAKjC,KAAK,CAACiB,aAAa,CAACd,iBAAiB,EAAE;IAC7E+B,GAAG,EAAEF,MAAM,CAACE,GAAG;IACfF,MAAM,EAAEA,MAAM;IACdG,QAAQ,EAAEA,CAAA,KAAMpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAC7D,IAAIqC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,MAAMC,OAAO,CAACC,GAAG,CAAC,CAAC,CAACJ,EAAE,GAAGJ,MAAM,CAACS,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAACV,MAAM,CAAC,EAAE,CAACK,EAAE,GAAGtB,KAAK,CAACoB,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC3B,KAAK,EAAEiB,MAAM,EAAEC,KAAK,CAAC,CAAC,CAAC;MACzL,IAAIlB,KAAK,CAACL,aAAa,EAAE;QACvB,CAAC4B,EAAE,GAAGvB,KAAK,CAAC4B,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAC3B,KAAK,CAAC;MAC1E;IACF,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,OAAOf,KAAK,CAACiB,aAAa,CAACV,WAAW,EAAE;IACtCa,SAAS,EAAElB,UAAU,CAACmB,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACK,SAAS,CAAC;IAC7CwB,KAAK,EAAE7B,KAAK,CAAC6B,KAAK;IAClBC,UAAU,EAAE9B,KAAK,CAAC8B,UAAU;IAC5BC,SAAS,EAAE/B,KAAK,CAAC+B,SAAS;IAC1BC,eAAe,EAAEhC,KAAK,CAACgC,eAAe;IACtCpC,gBAAgB,EAAEI,KAAK,CAACJ,gBAAgB;IACxCgC,OAAO,EAAE5B,KAAK,CAAC4B,OAAO;IACtBK,OAAO,EAAEjC,KAAK,CAACiC,OAAO;IACtBpC,YAAY,EAAEG,KAAK,CAACH,YAAY;IAChCqC,SAAS,EAAElC,KAAK,CAACkC,SAAS;IAC1BC,aAAa,EAAEhD,UAAU,CAACmB,GAAG,CAAC,MAAM,CAAC,EAAEN,KAAK,CAACI,KAAK,IAAIE,GAAG,CAAC,YAAY,CAAC,EAAEN,KAAK,CAACmC,aAAa,CAAC;IAC7FC,SAAS,EAAEpC,KAAK,CAACoC,SAAS;IAC1BC,aAAa,EAAErC,KAAK,CAACqC,aAAa;IAClCC,eAAe,EAAEtC,KAAK,CAACsC,eAAe;IACtCC,iBAAiB,EAAEvC,KAAK,CAACuC,iBAAiB;IAC1CC,cAAc,EAAExC,KAAK,CAACwC,cAAc;IACpCC,WAAW,EAAEzC,KAAK,CAACyC;EACrB,CAAC,EAAExC,OAAO,CAAC;AACb,CAAC;AACD,SAASK,GAAGA,CAACoC,IAAI,GAAG,EAAE,EAAE;EACtB,OAAO,WAAW,IAAIA,IAAI,IAAI,GAAG,CAAC,GAAGA,IAAI;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}