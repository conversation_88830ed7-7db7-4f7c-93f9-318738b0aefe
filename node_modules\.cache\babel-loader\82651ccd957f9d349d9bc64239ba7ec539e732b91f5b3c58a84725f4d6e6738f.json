{"ast": null, "code": "import React, { forwardRef, useContext, useImperative<PERSON><PERSON>le } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { CheckboxGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { devWarning } from '../../utils/dev-log';\nimport { CheckIcon } from './check-icon';\nimport { IndeterminateIcon } from './indeterminate-icon';\nimport { isDev } from '../../utils/is-dev';\nimport { NativeInput } from './native-input';\nconst classPrefix = `adm-checkbox`;\nconst defaultProps = {\n  defaultChecked: false,\n  indeterminate: false\n};\nexport const Checkbox = forwardRef((p, ref) => {\n  const groupContext = useContext(CheckboxGroupContext);\n  const props = mergeProps(defaultProps, p);\n  let [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  let disabled = props.disabled;\n  const {\n    value\n  } = props;\n  if (groupContext && value !== undefined) {\n    if (isDev) {\n      if (p.checked !== undefined) {\n        devWarning('Checkbox', 'When used within `Checkbox.Group`, the `checked` prop of `Checkbox` will not work.');\n      }\n      if (p.defaultChecked !== undefined) {\n        devWarning('Checkbox', 'When used within `Checkbox.Group`, the `defaultChecked` prop of `Checkbox` will not work.');\n      }\n    }\n    checked = groupContext.value.includes(value);\n    setChecked = checked => {\n      var _a;\n      if (checked) {\n        groupContext.check(value);\n      } else {\n        groupContext.uncheck(value);\n      }\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, checked);\n    };\n    disabled = disabled || groupContext.disabled;\n  }\n  useImperativeHandle(ref, () => ({\n    check: () => {\n      setChecked(true);\n    },\n    uncheck: () => {\n      setChecked(false);\n    },\n    toggle: () => {\n      setChecked(!checked);\n    }\n  }));\n  const renderIcon = () => {\n    if (props.icon) {\n      return React.createElement(\"div\", {\n        className: `${classPrefix}-custom-icon`\n      }, props.icon(checked, props.indeterminate));\n    }\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-icon`\n    }, props.indeterminate ? React.createElement(IndeterminateIcon, null) : checked && React.createElement(CheckIcon, null));\n  };\n  return withNativeProps(props, React.createElement(\"label\", {\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked && !props.indeterminate,\n      [`${classPrefix}-indeterminate`]: props.indeterminate,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-block`]: props.block\n    })\n  }, React.createElement(NativeInput, {\n    type: 'checkbox',\n    checked: checked,\n    onChange: setChecked,\n    disabled: disabled,\n    id: props.id\n  }), renderIcon(), props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n});", "map": {"version": 3, "names": ["React", "forwardRef", "useContext", "useImperativeHandle", "withNativeProps", "classNames", "CheckboxGroupContext", "usePropsValue", "mergeProps", "dev<PERSON><PERSON><PERSON>", "CheckIcon", "IndeterminateIcon", "isDev", "NativeInput", "classPrefix", "defaultProps", "defaultChecked", "indeterminate", "Checkbox", "p", "ref", "groupContext", "props", "checked", "setChecked", "value", "defaultValue", "onChange", "disabled", "undefined", "includes", "_a", "check", "uncheck", "call", "toggle", "renderIcon", "icon", "createElement", "className", "onClick", "block", "type", "id", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/checkbox/checkbox.js"], "sourcesContent": ["import React, { forwardRef, useContext, useImperative<PERSON><PERSON>le } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { CheckboxGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { devWarning } from '../../utils/dev-log';\nimport { CheckIcon } from './check-icon';\nimport { IndeterminateIcon } from './indeterminate-icon';\nimport { isDev } from '../../utils/is-dev';\nimport { NativeInput } from './native-input';\nconst classPrefix = `adm-checkbox`;\nconst defaultProps = {\n  defaultChecked: false,\n  indeterminate: false\n};\nexport const Checkbox = forwardRef((p, ref) => {\n  const groupContext = useContext(CheckboxGroupContext);\n  const props = mergeProps(defaultProps, p);\n  let [checked, setChecked] = usePropsValue({\n    value: props.checked,\n    defaultValue: props.defaultChecked,\n    onChange: props.onChange\n  });\n  let disabled = props.disabled;\n  const {\n    value\n  } = props;\n  if (groupContext && value !== undefined) {\n    if (isDev) {\n      if (p.checked !== undefined) {\n        devWarning('Checkbox', 'When used within `Checkbox.Group`, the `checked` prop of `Checkbox` will not work.');\n      }\n      if (p.defaultChecked !== undefined) {\n        devWarning('Checkbox', 'When used within `Checkbox.Group`, the `defaultChecked` prop of `Checkbox` will not work.');\n      }\n    }\n    checked = groupContext.value.includes(value);\n    setChecked = checked => {\n      var _a;\n      if (checked) {\n        groupContext.check(value);\n      } else {\n        groupContext.uncheck(value);\n      }\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, checked);\n    };\n    disabled = disabled || groupContext.disabled;\n  }\n  useImperativeHandle(ref, () => ({\n    check: () => {\n      setChecked(true);\n    },\n    uncheck: () => {\n      setChecked(false);\n    },\n    toggle: () => {\n      setChecked(!checked);\n    }\n  }));\n  const renderIcon = () => {\n    if (props.icon) {\n      return React.createElement(\"div\", {\n        className: `${classPrefix}-custom-icon`\n      }, props.icon(checked, props.indeterminate));\n    }\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-icon`\n    }, props.indeterminate ? React.createElement(IndeterminateIcon, null) : checked && React.createElement(CheckIcon, null));\n  };\n  return withNativeProps(props, React.createElement(\"label\", {\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-checked`]: checked && !props.indeterminate,\n      [`${classPrefix}-indeterminate`]: props.indeterminate,\n      [`${classPrefix}-disabled`]: disabled,\n      [`${classPrefix}-block`]: props.block\n    })\n  }, React.createElement(NativeInput, {\n    type: 'checkbox',\n    checked: checked,\n    onChange: setChecked,\n    disabled: disabled,\n    id: props.id\n  }), renderIcon(), props.children && React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, props.children)));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAC1E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,YAAY,GAAG;EACnBC,cAAc,EAAE,KAAK;EACrBC,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGjB,UAAU,CAAC,CAACkB,CAAC,EAAEC,GAAG,KAAK;EAC7C,MAAMC,YAAY,GAAGnB,UAAU,CAACI,oBAAoB,CAAC;EACrD,MAAMgB,KAAK,GAAGd,UAAU,CAACO,YAAY,EAAEI,CAAC,CAAC;EACzC,IAAI,CAACI,OAAO,EAAEC,UAAU,CAAC,GAAGjB,aAAa,CAAC;IACxCkB,KAAK,EAAEH,KAAK,CAACC,OAAO;IACpBG,YAAY,EAAEJ,KAAK,CAACN,cAAc;IAClCW,QAAQ,EAAEL,KAAK,CAACK;EAClB,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;EAC7B,MAAM;IACJH;EACF,CAAC,GAAGH,KAAK;EACT,IAAID,YAAY,IAAII,KAAK,KAAKI,SAAS,EAAE;IACvC,IAAIjB,KAAK,EAAE;MACT,IAAIO,CAAC,CAACI,OAAO,KAAKM,SAAS,EAAE;QAC3BpB,UAAU,CAAC,UAAU,EAAE,oFAAoF,CAAC;MAC9G;MACA,IAAIU,CAAC,CAACH,cAAc,KAAKa,SAAS,EAAE;QAClCpB,UAAU,CAAC,UAAU,EAAE,2FAA2F,CAAC;MACrH;IACF;IACAc,OAAO,GAAGF,YAAY,CAACI,KAAK,CAACK,QAAQ,CAACL,KAAK,CAAC;IAC5CD,UAAU,GAAGD,OAAO,IAAI;MACtB,IAAIQ,EAAE;MACN,IAAIR,OAAO,EAAE;QACXF,YAAY,CAACW,KAAK,CAACP,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLJ,YAAY,CAACY,OAAO,CAACR,KAAK,CAAC;MAC7B;MACA,CAACM,EAAE,GAAGT,KAAK,CAACK,QAAQ,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACZ,KAAK,EAAEC,OAAO,CAAC;IACpF,CAAC;IACDK,QAAQ,GAAGA,QAAQ,IAAIP,YAAY,CAACO,QAAQ;EAC9C;EACAzB,mBAAmB,CAACiB,GAAG,EAAE,OAAO;IAC9BY,KAAK,EAAEA,CAAA,KAAM;MACXR,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC;IACDS,OAAO,EAAEA,CAAA,KAAM;MACbT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACDW,MAAM,EAAEA,CAAA,KAAM;MACZX,UAAU,CAAC,CAACD,OAAO,CAAC;IACtB;EACF,CAAC,CAAC,CAAC;EACH,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAId,KAAK,CAACe,IAAI,EAAE;MACd,OAAOrC,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;QAChCC,SAAS,EAAE,GAAGzB,WAAW;MAC3B,CAAC,EAAEQ,KAAK,CAACe,IAAI,CAACd,OAAO,EAAED,KAAK,CAACL,aAAa,CAAC,CAAC;IAC9C;IACA,OAAOjB,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;MAChCC,SAAS,EAAE,GAAGzB,WAAW;IAC3B,CAAC,EAAEQ,KAAK,CAACL,aAAa,GAAGjB,KAAK,CAACsC,aAAa,CAAC3B,iBAAiB,EAAE,IAAI,CAAC,GAAGY,OAAO,IAAIvB,KAAK,CAACsC,aAAa,CAAC5B,SAAS,EAAE,IAAI,CAAC,CAAC;EAC1H,CAAC;EACD,OAAON,eAAe,CAACkB,KAAK,EAAEtB,KAAK,CAACsC,aAAa,CAAC,OAAO,EAAE;IACzDE,OAAO,EAAElB,KAAK,CAACkB,OAAO;IACtBD,SAAS,EAAElC,UAAU,CAACS,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,UAAU,GAAGS,OAAO,IAAI,CAACD,KAAK,CAACL,aAAa;MAC3D,CAAC,GAAGH,WAAW,gBAAgB,GAAGQ,KAAK,CAACL,aAAa;MACrD,CAAC,GAAGH,WAAW,WAAW,GAAGc,QAAQ;MACrC,CAAC,GAAGd,WAAW,QAAQ,GAAGQ,KAAK,CAACmB;IAClC,CAAC;EACH,CAAC,EAAEzC,KAAK,CAACsC,aAAa,CAACzB,WAAW,EAAE;IAClC6B,IAAI,EAAE,UAAU;IAChBnB,OAAO,EAAEA,OAAO;IAChBI,QAAQ,EAAEH,UAAU;IACpBI,QAAQ,EAAEA,QAAQ;IAClBe,EAAE,EAAErB,KAAK,CAACqB;EACZ,CAAC,CAAC,EAAEP,UAAU,CAAC,CAAC,EAAEd,KAAK,CAACsB,QAAQ,IAAI5C,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IAC7DC,SAAS,EAAE,GAAGzB,WAAW;EAC3B,CAAC,EAAEQ,KAAK,CAACsB,QAAQ,CAAC,CAAC,CAAC;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}