{"ast": null, "code": "import { __rest } from \"tslib\";\nimport classNames from 'classnames';\nimport React, { forwardRef, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Button from '../button';\nimport CalendarPickerView from '../calendar-picker-view';\nimport { Context } from '../calendar-picker-view/calendar-picker-view';\nimport { useConfig } from '../config-provider';\nimport Divider from '../divider';\nimport Popup from '../popup';\nconst classPrefix = 'adm-calendar-picker';\nconst defaultProps = {\n  weekStartsOn: 'Sunday',\n  defaultValue: null,\n  allowClear: true,\n  usePopup: true,\n  selectionMode: 'single'\n};\nexport const CalendarPicker = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const calendarRef = ref !== null && ref !== void 0 ? ref : useRef(null);\n  const {\n      visible,\n      confirmText,\n      popupClassName,\n      popupStyle,\n      popupBodyStyle,\n      forceRender,\n      closeOnMaskClick,\n      onClose,\n      onConfirm,\n      onMaskClick,\n      getContainer\n    } = props,\n    calendarViewProps = __rest(props, [\"visible\", \"confirmText\", \"popupClassName\", \"popupStyle\", \"popupBodyStyle\", \"forceRender\", \"closeOnMaskClick\", \"onClose\", \"onConfirm\", \"onMaskClick\", \"getContainer\"]);\n  const viewContext = React.useMemo(() => ({\n    visible: !!visible\n  }), [visible]);\n  const footer = React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, React.createElement(Divider, null), React.createElement(\"div\", {\n    className: `${classPrefix}-footer-bottom`\n  }, React.createElement(Button, {\n    color: 'primary',\n    onClick: () => {\n      var _a, _b, _c, _d;\n      const dateRange = (_b = (_a = calendarRef.current) === null || _a === void 0 ? void 0 : _a.getDateRange()) !== null && _b !== void 0 ? _b : null;\n      if (props.selectionMode === 'single') {\n        (_c = props.onConfirm) === null || _c === void 0 ? void 0 : _c.call(props, dateRange ? dateRange[0] : null);\n      } else if (props.selectionMode === 'range') {\n        (_d = props.onConfirm) === null || _d === void 0 ? void 0 : _d.call(props, dateRange);\n      }\n      onClose === null || onClose === void 0 ? void 0 : onClose();\n    }\n  }, confirmText !== null && confirmText !== void 0 ? confirmText : locale.Calendar.confirm)));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(Popup, {\n    visible: visible,\n    className: classNames(`${classPrefix}-popup`, popupClassName),\n    showCloseButton: true,\n    forceRender: ref ? true : forceRender,\n    style: popupStyle,\n    bodyStyle: Object.assign({\n      borderTopLeftRadius: '8px',\n      borderTopRightRadius: '8px',\n      minHeight: '80vh',\n      overflow: 'auto'\n    }, popupBodyStyle),\n    onClose: onClose,\n    onMaskClick: () => {\n      onMaskClick === null || onMaskClick === void 0 ? void 0 : onMaskClick();\n      if (closeOnMaskClick) {\n        onClose === null || onClose === void 0 ? void 0 : onClose();\n      }\n    },\n    getContainer: getContainer\n  }, React.createElement(Context.Provider, {\n    value: viewContext\n  }, React.createElement(CalendarPickerView, Object.assign({\n    ref: calendarRef\n  }, calendarViewProps))), footer)));\n});", "map": {"version": 3, "names": ["__rest", "classNames", "React", "forwardRef", "useRef", "withNativeProps", "mergeProps", "<PERSON><PERSON>", "CalendarPickerView", "Context", "useConfig", "Divider", "Popup", "classPrefix", "defaultProps", "weekStartsOn", "defaultValue", "allowClear", "usePopup", "selectionMode", "CalendarPicker", "p", "ref", "props", "locale", "calendarRef", "visible", "confirmText", "popupClassName", "popupStyle", "popupBodyStyle", "forceRender", "closeOnMaskClick", "onClose", "onConfirm", "onMaskClick", "getContainer", "calendarViewProps", "viewContext", "useMemo", "footer", "createElement", "className", "color", "onClick", "_a", "_b", "_c", "_d", "date<PERSON><PERSON><PERSON>", "current", "getDateRange", "call", "Calendar", "confirm", "showCloseButton", "style", "bodyStyle", "Object", "assign", "borderTopLeftRadius", "borderTopRightRadius", "minHeight", "overflow", "Provider", "value"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/calendar-picker/calendar-picker.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport classNames from 'classnames';\nimport React, { forwardRef, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Button from '../button';\nimport CalendarPickerView from '../calendar-picker-view';\nimport { Context } from '../calendar-picker-view/calendar-picker-view';\nimport { useConfig } from '../config-provider';\nimport Divider from '../divider';\nimport Popup from '../popup';\nconst classPrefix = 'adm-calendar-picker';\nconst defaultProps = {\n  weekStartsOn: 'Sunday',\n  defaultValue: null,\n  allowClear: true,\n  usePopup: true,\n  selectionMode: 'single'\n};\nexport const CalendarPicker = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const calendarRef = ref !== null && ref !== void 0 ? ref : useRef(null);\n  const {\n      visible,\n      confirmText,\n      popupClassName,\n      popupStyle,\n      popupBodyStyle,\n      forceRender,\n      closeOnMaskClick,\n      onClose,\n      onConfirm,\n      onMaskClick,\n      getContainer\n    } = props,\n    calendarViewProps = __rest(props, [\"visible\", \"confirmText\", \"popupClassName\", \"popupStyle\", \"popupBodyStyle\", \"forceRender\", \"closeOnMaskClick\", \"onClose\", \"onConfirm\", \"onMaskClick\", \"getContainer\"]);\n  const viewContext = React.useMemo(() => ({\n    visible: !!visible\n  }), [visible]);\n  const footer = React.createElement(\"div\", {\n    className: `${classPrefix}-footer`\n  }, React.createElement(Divider, null), React.createElement(\"div\", {\n    className: `${classPrefix}-footer-bottom`\n  }, React.createElement(Button, {\n    color: 'primary',\n    onClick: () => {\n      var _a, _b, _c, _d;\n      const dateRange = (_b = (_a = calendarRef.current) === null || _a === void 0 ? void 0 : _a.getDateRange()) !== null && _b !== void 0 ? _b : null;\n      if (props.selectionMode === 'single') {\n        (_c = props.onConfirm) === null || _c === void 0 ? void 0 : _c.call(props, dateRange ? dateRange[0] : null);\n      } else if (props.selectionMode === 'range') {\n        (_d = props.onConfirm) === null || _d === void 0 ? void 0 : _d.call(props, dateRange);\n      }\n      onClose === null || onClose === void 0 ? void 0 : onClose();\n    }\n  }, confirmText !== null && confirmText !== void 0 ? confirmText : locale.Calendar.confirm)));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(Popup, {\n    visible: visible,\n    className: classNames(`${classPrefix}-popup`, popupClassName),\n    showCloseButton: true,\n    forceRender: ref ? true : forceRender,\n    style: popupStyle,\n    bodyStyle: Object.assign({\n      borderTopLeftRadius: '8px',\n      borderTopRightRadius: '8px',\n      minHeight: '80vh',\n      overflow: 'auto'\n    }, popupBodyStyle),\n    onClose: onClose,\n    onMaskClick: () => {\n      onMaskClick === null || onMaskClick === void 0 ? void 0 : onMaskClick();\n      if (closeOnMaskClick) {\n        onClose === null || onClose === void 0 ? void 0 : onClose();\n      }\n    },\n    getContainer: getContainer\n  }, React.createElement(Context.Provider, {\n    value: viewContext\n  }, React.createElement(CalendarPickerView, Object.assign({\n    ref: calendarRef\n  }, calendarViewProps))), footer)));\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,OAAO,QAAQ,8CAA8C;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,KAAK,MAAM,UAAU;AAC5B,MAAMC,WAAW,GAAG,qBAAqB;AACzC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,QAAQ;EACtBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGjB,UAAU,CAAC,CAACkB,CAAC,EAAEC,GAAG,KAAK;EACnD,MAAMC,KAAK,GAAGjB,UAAU,CAACQ,YAAY,EAAEO,CAAC,CAAC;EACzC,MAAM;IACJG;EACF,CAAC,GAAGd,SAAS,CAAC,CAAC;EACf,MAAMe,WAAW,GAAGH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACvE,MAAM;MACFsB,OAAO;MACPC,WAAW;MACXC,cAAc;MACdC,UAAU;MACVC,cAAc;MACdC,WAAW;MACXC,gBAAgB;MAChBC,OAAO;MACPC,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,GAAGb,KAAK;IACTc,iBAAiB,GAAGrC,MAAM,CAACuB,KAAK,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;EAC3M,MAAMe,WAAW,GAAGpC,KAAK,CAACqC,OAAO,CAAC,OAAO;IACvCb,OAAO,EAAE,CAAC,CAACA;EACb,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACd,MAAMc,MAAM,GAAGtC,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;IACxCC,SAAS,EAAE,GAAG7B,WAAW;EAC3B,CAAC,EAAEX,KAAK,CAACuC,aAAa,CAAC9B,OAAO,EAAE,IAAI,CAAC,EAAET,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;IAChEC,SAAS,EAAE,GAAG7B,WAAW;EAC3B,CAAC,EAAEX,KAAK,CAACuC,aAAa,CAAClC,MAAM,EAAE;IAC7BoC,KAAK,EAAE,SAAS;IAChBC,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAClB,MAAMC,SAAS,GAAG,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGpB,WAAW,CAACyB,OAAO,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,YAAY,CAAC,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;MAChJ,IAAIvB,KAAK,CAACJ,aAAa,KAAK,QAAQ,EAAE;QACpC,CAAC4B,EAAE,GAAGxB,KAAK,CAACW,SAAS,MAAM,IAAI,IAAIa,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,CAAC7B,KAAK,EAAE0B,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAC7G,CAAC,MAAM,IAAI1B,KAAK,CAACJ,aAAa,KAAK,OAAO,EAAE;QAC1C,CAAC6B,EAAE,GAAGzB,KAAK,CAACW,SAAS,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,IAAI,CAAC7B,KAAK,EAAE0B,SAAS,CAAC;MACvF;MACAhB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC7D;EACF,CAAC,EAAEN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGH,MAAM,CAAC6B,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC;EAC5F,OAAOjD,eAAe,CAACkB,KAAK,EAAErB,KAAK,CAACuC,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE7B;EACb,CAAC,EAAEX,KAAK,CAACuC,aAAa,CAAC7B,KAAK,EAAE;IAC5Bc,OAAO,EAAEA,OAAO;IAChBgB,SAAS,EAAEzC,UAAU,CAAC,GAAGY,WAAW,QAAQ,EAAEe,cAAc,CAAC;IAC7D2B,eAAe,EAAE,IAAI;IACrBxB,WAAW,EAAET,GAAG,GAAG,IAAI,GAAGS,WAAW;IACrCyB,KAAK,EAAE3B,UAAU;IACjB4B,SAAS,EAAEC,MAAM,CAACC,MAAM,CAAC;MACvBC,mBAAmB,EAAE,KAAK;MAC1BC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE;IACZ,CAAC,EAAEjC,cAAc,CAAC;IAClBG,OAAO,EAAEA,OAAO;IAChBE,WAAW,EAAEA,CAAA,KAAM;MACjBA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;MACvE,IAAIH,gBAAgB,EAAE;QACpBC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;MAC7D;IACF,CAAC;IACDG,YAAY,EAAEA;EAChB,CAAC,EAAElC,KAAK,CAACuC,aAAa,CAAChC,OAAO,CAACuD,QAAQ,EAAE;IACvCC,KAAK,EAAE3B;EACT,CAAC,EAAEpC,KAAK,CAACuC,aAAa,CAACjC,kBAAkB,EAAEkD,MAAM,CAACC,MAAM,CAAC;IACvDrC,GAAG,EAAEG;EACP,CAAC,EAAEY,iBAAiB,CAAC,CAAC,CAAC,EAAEG,MAAM,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}