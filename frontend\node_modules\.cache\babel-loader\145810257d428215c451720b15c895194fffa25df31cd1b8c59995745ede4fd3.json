{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { useEffect, useRef } from 'react';\nexport default function useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n    prevProps.current = props;\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}