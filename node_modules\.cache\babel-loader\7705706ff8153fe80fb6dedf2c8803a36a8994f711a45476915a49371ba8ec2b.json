{"ast": null, "code": "import * as React from \"react\";\nfunction PlayOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PlayOutline-PlayOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PlayOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PlayOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.7542635,5 L13.7542635,5 C14.4346997,5 15.1023464,5.17879782 15.6858218,5.51730753 L42.1766882,20.8867866 L42.1766884,20.8867867 C43.954638,21.9182878 44.5311886,24.1481853 43.4644541,25.8674033 C43.1473985,26.3783891 42.7051305,26.8060484 42.1766884,27.1126297 L15.6858221,42.4821088 L15.6858224,42.4821086 C13.9078904,43.5136394 11.6018067,42.9561767 10.5350149,41.2369758 C10.1849222,40.6727702 10,40.0271653 10,39.3691893 L10,8.63023111 L10,8.63023166 C10,6.62531187 11.6808306,5 13.7542421,5 L13.7542635,5 Z M13.7542635,7.72267123 L13.7542635,7.72267123 C13.2784351,7.72267123 12.8779439,8.06709552 12.8222725,8.52404271 L12.8157026,8.63022688 L12.8157026,39.369185 L12.8157026,39.3692026 C12.8157026,39.8704336 13.2355864,40.2770731 13.7539409,40.2770731 C13.8870633,40.2770731 14.0186722,40.249778 14.1400304,40.1968699 L14.236702,40.1478619 L40.7284922,24.7774894 L40.7284923,24.7774894 C41.1728598,24.5194214 41.3167368,23.9618834 41.0498545,23.5321949 C40.9896328,23.4352363 40.9111988,23.3500171 40.8185937,23.2809268 L40.7284918,23.2219354 L14.2366576,7.85156296 L14.2366576,7.85156295 C14.0908783,7.76717376 13.9241432,7.72267123 13.7542388,7.72267123 L13.7542635,7.72267123 Z\",\n    id: \"PlayOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default PlayOutline;", "map": {"version": 3, "names": ["React", "PlayOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/PlayOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction PlayOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PlayOutline-PlayOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"PlayOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"PlayOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.7542635,5 L13.7542635,5 C14.4346997,5 15.1023464,5.17879782 15.6858218,5.51730753 L42.1766882,20.8867866 L42.1766884,20.8867867 C43.954638,21.9182878 44.5311886,24.1481853 43.4644541,25.8674033 C43.1473985,26.3783891 42.7051305,26.8060484 42.1766884,27.1126297 L15.6858221,42.4821088 L15.6858224,42.4821086 C13.9078904,43.5136394 11.6018067,42.9561767 10.5350149,41.2369758 C10.1849222,40.6727702 10,40.0271653 10,39.3691893 L10,8.63023111 L10,8.63023166 C10,6.62531187 11.6808306,5 13.7542421,5 L13.7542635,5 Z M13.7542635,7.72267123 L13.7542635,7.72267123 C13.2784351,7.72267123 12.8779439,8.06709552 12.8222725,8.52404271 L12.8157026,8.63022688 L12.8157026,39.369185 L12.8157026,39.3692026 C12.8157026,39.8704336 13.2355864,40.2770731 13.7539409,40.2770731 C13.8870633,40.2770731 14.0186722,40.249778 14.1400304,40.1968699 L14.236702,40.1478619 L40.7284922,24.7774894 L40.7284923,24.7774894 C41.1728598,24.5194214 41.3167368,23.9618834 41.0498545,23.5321949 C40.9896328,23.4352363 40.9111988,23.3500171 40.8185937,23.2809268 L40.7284918,23.2219354 L14.2366576,7.85156296 L14.2366576,7.85156295 C14.0908783,7.76717376 13.9241432,7.72267123 13.7542388,7.72267123 L13.7542635,7.72267123 Z\",\n    id: \"PlayOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default PlayOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,0qCAA0qC;IAC7qCR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}