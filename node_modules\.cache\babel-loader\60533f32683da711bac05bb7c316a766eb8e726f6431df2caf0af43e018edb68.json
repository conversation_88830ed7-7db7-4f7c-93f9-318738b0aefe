{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { generateIntArray } from '../../utils/generate-int-array';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = 'adm-skeleton';\nexport const Skeleton = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-animated`]: props.animated\n    })\n  }));\n};\nexport const SkeletonTitle = props => {\n  return withNativeProps(props, React.createElement(Skeleton, {\n    animated: props.animated,\n    className: `${classPrefix}-title`\n  }));\n};\nconst defaultSkeletonParagraphProps = {\n  lineCount: 3\n};\nexport const SkeletonParagraph = p => {\n  const props = mergeProps(defaultSkeletonParagraphProps, p);\n  const keys = generateIntArray(1, props.lineCount);\n  const node = React.createElement(\"div\", {\n    className: `${classPrefix}-paragraph`\n  }, keys.map(key => React.createElement(Skeleton, {\n    key: key,\n    animated: props.animated,\n    className: `${classPrefix}-paragraph-line`\n  })));\n  return withNativeProps(props, node);\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "classNames", "generateIntArray", "mergeProps", "classPrefix", "Skeleton", "props", "createElement", "className", "animated", "SkeletonTitle", "defaultSkeletonParagraphProps", "lineCount", "SkeletonParagraph", "p", "keys", "node", "map", "key"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/skeleton/skeleton.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { generateIntArray } from '../../utils/generate-int-array';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = 'adm-skeleton';\nexport const Skeleton = props => {\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-animated`]: props.animated\n    })\n  }));\n};\nexport const SkeletonTitle = props => {\n  return withNativeProps(props, React.createElement(Skeleton, {\n    animated: props.animated,\n    className: `${classPrefix}-title`\n  }));\n};\nconst defaultSkeletonParagraphProps = {\n  lineCount: 3\n};\nexport const SkeletonParagraph = p => {\n  const props = mergeProps(defaultSkeletonParagraphProps, p);\n  const keys = generateIntArray(1, props.lineCount);\n  const node = React.createElement(\"div\", {\n    className: `${classPrefix}-paragraph`\n  }, keys.map(key => React.createElement(Skeleton, {\n    key: key,\n    animated: props.animated,\n    className: `${classPrefix}-paragraph-line`\n  })));\n  return withNativeProps(props, node);\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,cAAc;AAClC,OAAO,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EAC/B,OAAON,eAAe,CAACM,KAAK,EAAEP,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEP,UAAU,CAACG,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,WAAW,GAAGE,KAAK,CAACG;IACrC,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGJ,KAAK,IAAI;EACpC,OAAON,eAAe,CAACM,KAAK,EAAEP,KAAK,CAACQ,aAAa,CAACF,QAAQ,EAAE;IAC1DI,QAAQ,EAAEH,KAAK,CAACG,QAAQ;IACxBD,SAAS,EAAE,GAAGJ,WAAW;EAC3B,CAAC,CAAC,CAAC;AACL,CAAC;AACD,MAAMO,6BAA6B,GAAG;EACpCC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,iBAAiB,GAAGC,CAAC,IAAI;EACpC,MAAMR,KAAK,GAAGH,UAAU,CAACQ,6BAA6B,EAAEG,CAAC,CAAC;EAC1D,MAAMC,IAAI,GAAGb,gBAAgB,CAAC,CAAC,EAAEI,KAAK,CAACM,SAAS,CAAC;EACjD,MAAMI,IAAI,GAAGjB,KAAK,CAACQ,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAE,GAAGJ,WAAW;EAC3B,CAAC,EAAEW,IAAI,CAACE,GAAG,CAACC,GAAG,IAAInB,KAAK,CAACQ,aAAa,CAACF,QAAQ,EAAE;IAC/Ca,GAAG,EAAEA,GAAG;IACRT,QAAQ,EAAEH,KAAK,CAACG,QAAQ;IACxBD,SAAS,EAAE,GAAGJ,WAAW;EAC3B,CAAC,CAAC,CAAC,CAAC;EACJ,OAAOJ,eAAe,CAACM,KAAK,EAAEU,IAAI,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}