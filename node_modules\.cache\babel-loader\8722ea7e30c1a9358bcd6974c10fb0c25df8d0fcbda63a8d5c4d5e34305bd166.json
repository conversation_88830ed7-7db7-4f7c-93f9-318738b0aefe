{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\nvar useRefreshOnWindowFocusPlugin = function (fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n    _b = _a.focusTimespan,\n    focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef();\n  var stopSubscribe = function () {\n    var _a;\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\nexport default useRefreshOnWindowFocusPlugin;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmount", "limit", "subscribeFocus", "useRefreshOnWindowFocusPlugin", "fetchInstance", "_a", "refreshOnWindowFocus", "_b", "focusTimespan", "unsubscribeRef", "stopSubscribe", "current", "call", "limitRefresh_1", "refresh", "bind"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\nvar useRefreshOnWindowFocusPlugin = function (fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n    _b = _a.focusTimespan,\n    focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef();\n  var stopSubscribe = function () {\n    var _a;\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\nexport default useRefreshOnWindowFocusPlugin;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,cAAc,MAAM,yBAAyB;AACpD,IAAIC,6BAA6B,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EAC/D,IAAIC,oBAAoB,GAAGD,EAAE,CAACC,oBAAoB;IAChDC,EAAE,GAAGF,EAAE,CAACG,aAAa;IACrBA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;EAC3C,IAAIE,cAAc,GAAGV,MAAM,CAAC,CAAC;EAC7B,IAAIW,aAAa,GAAG,SAAAA,CAAA,EAAY;IAC9B,IAAIL,EAAE;IACN,CAACA,EAAE,GAAGI,cAAc,CAACE,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAACH,cAAc,CAAC;EAC5F,CAAC;EACDX,SAAS,CAAC,YAAY;IACpB,IAAIQ,oBAAoB,EAAE;MACxB,IAAIO,cAAc,GAAGZ,KAAK,CAACG,aAAa,CAACU,OAAO,CAACC,IAAI,CAACX,aAAa,CAAC,EAAEI,aAAa,CAAC;MACpFC,cAAc,CAACE,OAAO,GAAGT,cAAc,CAAC,YAAY;QAClDW,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;IACA,OAAO,YAAY;MACjBH,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACJ,oBAAoB,EAAEE,aAAa,CAAC,CAAC;EACzCR,UAAU,CAAC,YAAY;IACrBU,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,OAAO,CAAC,CAAC;AACX,CAAC;AACD,eAAeP,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}