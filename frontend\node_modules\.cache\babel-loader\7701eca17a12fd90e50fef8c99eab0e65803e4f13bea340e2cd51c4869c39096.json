{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useDebounceEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useDebounceFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useDebounceEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}