{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { arrow, autoUpdate, computePosition, flip, hide, limitShift, offset, shift } from '@floating-ui/dom';\nimport { useClickAway, useIsomorphicLayoutEffect } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { convertPx } from '../../utils/convert-px';\nimport { withNativeProps } from '../../utils/native-props';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { useShouldRender } from '../../utils/should-render';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { Arrow } from './arrow';\nimport { normalizePlacement } from './normalize-placement';\nimport { Wrapper } from './wrapper';\nconst classPrefix = `adm-popover`;\nconst defaultProps = {\n  placement: 'top',\n  defaultVisible: false,\n  stopPropagation: ['click'],\n  getContainer: () => document.body,\n  mode: 'light'\n};\nexport const Popover = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const placement = normalizePlacement(props.placement);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: props.defaultVisible,\n    onChange: props.onVisibleChange\n  });\n  useImperativeHandle(ref, () => ({\n    show: () => setVisible(true),\n    hide: () => setVisible(false),\n    visible\n  }), [visible]);\n  const targetRef = useRef(null);\n  const floatingRef = useRef(null);\n  const arrowRef = useRef(null);\n  const floating = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.mode}`, {\n      [`${classPrefix}-hidden`]: !visible\n    }),\n    ref: floatingRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-arrow`,\n    ref: arrowRef\n  }, React.createElement(Arrow, {\n    className: `${classPrefix}-arrow-icon`\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-inner`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-inner-content`\n  }, props.content)))));\n  const [targetElement, setTargetElement] = useState(null);\n  function update() {\n    var _a, _b, _c;\n    return __awaiter(this, void 0, void 0, function* () {\n      const target = (_b = (_a = targetRef.current) === null || _a === void 0 ? void 0 : _a.element) !== null && _b !== void 0 ? _b : null;\n      const floating = floatingRef.current;\n      const arrowElement = arrowRef.current;\n      setTargetElement(target);\n      if (!target || !floating || !arrowElement) return;\n      const {\n        x,\n        y,\n        placement: realPlacement,\n        middlewareData\n      } = yield computePosition(target, floating, {\n        placement,\n        middleware: [offset(convertPx(12)), shift({\n          padding: convertPx(4),\n          crossAxis: false,\n          limiter: limitShift()\n        }), flip(), hide(), arrow({\n          element: arrowElement,\n          padding: convertPx(12)\n        })]\n      });\n      Object.assign(floating.style, {\n        left: `${x}px`,\n        top: `${y}px`\n      });\n      const side = realPlacement.split('-')[0];\n      const arrowSide = {\n        top: 'bottom',\n        right: 'left',\n        bottom: 'top',\n        left: 'right'\n      }[side];\n      const {\n        x: arrowX,\n        y: arrowY\n      } = (_c = middlewareData.arrow) !== null && _c !== void 0 ? _c : {};\n      Object.assign(arrowElement.style, {\n        left: arrowX != null ? `${arrowX}px` : '',\n        top: arrowY != null ? `${arrowY}px` : '',\n        right: '',\n        bottom: '',\n        [arrowSide]: 'calc(var(--arrow-size) * -1)'\n      });\n      const arrowRotate = {\n        top: '0deg',\n        bottom: '180deg',\n        left: '270deg',\n        right: '90deg'\n      }[side];\n      arrowElement.style.setProperty('--arrow-icon-rotate', arrowRotate);\n    });\n  }\n  useIsomorphicLayoutEffect(() => {\n    update();\n  });\n  useEffect(() => {\n    if (!targetElement) return;\n    if (!props.trigger) return;\n    function handleClick() {\n      setVisible(v => !v);\n    }\n    targetElement.addEventListener('click', handleClick);\n    return () => {\n      targetElement.removeEventListener('click', handleClick);\n    };\n  }, [targetElement, props.trigger]);\n  useEffect(() => {\n    const floatingElement = floatingRef.current;\n    if (!targetElement || !floatingElement || !visible) return;\n    return autoUpdate(targetElement, floatingElement, update, {\n      elementResize: typeof ResizeObserver !== 'undefined'\n    });\n  }, [targetElement, visible]);\n  useClickAway(() => {\n    if (!props.trigger) return;\n    setVisible(false);\n  }, [() => {\n    var _a;\n    return (_a = targetRef.current) === null || _a === void 0 ? void 0 : _a.element;\n  }, floatingRef], ['click', 'touchmove']);\n  const shouldRender = useShouldRender(visible, false, props.destroyOnHide);\n  return React.createElement(React.Fragment, null, React.createElement(Wrapper, {\n    ref: targetRef\n  }, props.children), shouldRender && renderToContainer(props.getContainer, floating));\n});", "map": {"version": 3, "names": ["__awaiter", "arrow", "autoUpdate", "computePosition", "flip", "hide", "limitShift", "offset", "shift", "useClickAway", "useIsomorphicLayoutEffect", "classNames", "React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "useState", "convertPx", "withNativeProps", "renderToContainer", "useShouldRender", "usePropsValue", "mergeProps", "withStopPropagation", "Arrow", "normalizePlacement", "Wrapper", "classPrefix", "defaultProps", "placement", "defaultVisible", "stopPropagation", "getContainer", "document", "body", "mode", "Popover", "p", "ref", "props", "visible", "setVisible", "value", "defaultValue", "onChange", "onVisibleChange", "show", "targetRef", "floatingRef", "arrowRef", "floating", "createElement", "className", "content", "targetElement", "setTargetElement", "update", "_a", "_b", "_c", "target", "current", "element", "arrowElement", "x", "y", "realPlacement", "middlewareData", "middleware", "padding", "crossAxis", "limiter", "Object", "assign", "style", "left", "top", "side", "split", "arrowSide", "right", "bottom", "arrowX", "arrowY", "arrowRotate", "setProperty", "trigger", "handleClick", "v", "addEventListener", "removeEventListener", "floatingElement", "elementResize", "ResizeObserver", "shouldRender", "destroyOnHide", "Fragment", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/popover/popover.js"], "sourcesContent": ["import { __awaiter } from \"tslib\";\nimport { arrow, autoUpdate, computePosition, flip, hide, limitShift, offset, shift } from '@floating-ui/dom';\nimport { useClickAway, useIsomorphicLayoutEffect } from 'ahooks';\nimport classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { convertPx } from '../../utils/convert-px';\nimport { withNativeProps } from '../../utils/native-props';\nimport { renderToContainer } from '../../utils/render-to-container';\nimport { useShouldRender } from '../../utils/should-render';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport { Arrow } from './arrow';\nimport { normalizePlacement } from './normalize-placement';\nimport { Wrapper } from './wrapper';\nconst classPrefix = `adm-popover`;\nconst defaultProps = {\n  placement: 'top',\n  defaultVisible: false,\n  stopPropagation: ['click'],\n  getContainer: () => document.body,\n  mode: 'light'\n};\nexport const Popover = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const placement = normalizePlacement(props.placement);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: props.defaultVisible,\n    onChange: props.onVisibleChange\n  });\n  useImperativeHandle(ref, () => ({\n    show: () => setVisible(true),\n    hide: () => setVisible(false),\n    visible\n  }), [visible]);\n  const targetRef = useRef(null);\n  const floatingRef = useRef(null);\n  const arrowRef = useRef(null);\n  const floating = withStopPropagation(props.stopPropagation, withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.mode}`, {\n      [`${classPrefix}-hidden`]: !visible\n    }),\n    ref: floatingRef\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-arrow`,\n    ref: arrowRef\n  }, React.createElement(Arrow, {\n    className: `${classPrefix}-arrow-icon`\n  })), React.createElement(\"div\", {\n    className: `${classPrefix}-inner`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-inner-content`\n  }, props.content)))));\n  const [targetElement, setTargetElement] = useState(null);\n  function update() {\n    var _a, _b, _c;\n    return __awaiter(this, void 0, void 0, function* () {\n      const target = (_b = (_a = targetRef.current) === null || _a === void 0 ? void 0 : _a.element) !== null && _b !== void 0 ? _b : null;\n      const floating = floatingRef.current;\n      const arrowElement = arrowRef.current;\n      setTargetElement(target);\n      if (!target || !floating || !arrowElement) return;\n      const {\n        x,\n        y,\n        placement: realPlacement,\n        middlewareData\n      } = yield computePosition(target, floating, {\n        placement,\n        middleware: [offset(convertPx(12)), shift({\n          padding: convertPx(4),\n          crossAxis: false,\n          limiter: limitShift()\n        }), flip(), hide(), arrow({\n          element: arrowElement,\n          padding: convertPx(12)\n        })]\n      });\n      Object.assign(floating.style, {\n        left: `${x}px`,\n        top: `${y}px`\n      });\n      const side = realPlacement.split('-')[0];\n      const arrowSide = {\n        top: 'bottom',\n        right: 'left',\n        bottom: 'top',\n        left: 'right'\n      }[side];\n      const {\n        x: arrowX,\n        y: arrowY\n      } = (_c = middlewareData.arrow) !== null && _c !== void 0 ? _c : {};\n      Object.assign(arrowElement.style, {\n        left: arrowX != null ? `${arrowX}px` : '',\n        top: arrowY != null ? `${arrowY}px` : '',\n        right: '',\n        bottom: '',\n        [arrowSide]: 'calc(var(--arrow-size) * -1)'\n      });\n      const arrowRotate = {\n        top: '0deg',\n        bottom: '180deg',\n        left: '270deg',\n        right: '90deg'\n      }[side];\n      arrowElement.style.setProperty('--arrow-icon-rotate', arrowRotate);\n    });\n  }\n  useIsomorphicLayoutEffect(() => {\n    update();\n  });\n  useEffect(() => {\n    if (!targetElement) return;\n    if (!props.trigger) return;\n    function handleClick() {\n      setVisible(v => !v);\n    }\n    targetElement.addEventListener('click', handleClick);\n    return () => {\n      targetElement.removeEventListener('click', handleClick);\n    };\n  }, [targetElement, props.trigger]);\n  useEffect(() => {\n    const floatingElement = floatingRef.current;\n    if (!targetElement || !floatingElement || !visible) return;\n    return autoUpdate(targetElement, floatingElement, update, {\n      elementResize: typeof ResizeObserver !== 'undefined'\n    });\n  }, [targetElement, visible]);\n  useClickAway(() => {\n    if (!props.trigger) return;\n    setVisible(false);\n  }, [() => {\n    var _a;\n    return (_a = targetRef.current) === null || _a === void 0 ? void 0 : _a.element;\n  }, floatingRef], ['click', 'touchmove']);\n  const shouldRender = useShouldRender(visible, false, props.destroyOnHide);\n  return React.createElement(React.Fragment, null, React.createElement(Wrapper, {\n    ref: targetRef\n  }, props.children), shouldRender && renderToContainer(props.getContainer, floating));\n});"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,KAAK,EAAEC,UAAU,EAAEC,eAAe,EAAEC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC5G,SAASC,YAAY,EAAEC,yBAAyB,QAAQ,QAAQ;AAChE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC3F,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,OAAO,QAAQ,WAAW;AACnC,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,KAAK;EAChBC,cAAc,EAAE,KAAK;EACrBC,eAAe,EAAE,CAAC,OAAO,CAAC;EAC1BC,YAAY,EAAEA,CAAA,KAAMC,QAAQ,CAACC,IAAI;EACjCC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,MAAMC,OAAO,GAAGxB,UAAU,CAAC,CAACyB,CAAC,EAAEC,GAAG,KAAK;EAC5C,MAAMC,KAAK,GAAGjB,UAAU,CAACM,YAAY,EAAES,CAAC,CAAC;EACzC,MAAMR,SAAS,GAAGJ,kBAAkB,CAACc,KAAK,CAACV,SAAS,CAAC;EACrD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGpB,aAAa,CAAC;IAC1CqB,KAAK,EAAEH,KAAK,CAACC,OAAO;IACpBG,YAAY,EAAEJ,KAAK,CAACT,cAAc;IAClCc,QAAQ,EAAEL,KAAK,CAACM;EAClB,CAAC,CAAC;EACF/B,mBAAmB,CAACwB,GAAG,EAAE,OAAO;IAC9BQ,IAAI,EAAEA,CAAA,KAAML,UAAU,CAAC,IAAI,CAAC;IAC5BrC,IAAI,EAAEA,CAAA,KAAMqC,UAAU,CAAC,KAAK,CAAC;IAC7BD;EACF,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACd,MAAMO,SAAS,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiC,WAAW,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMkC,QAAQ,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMmC,QAAQ,GAAG3B,mBAAmB,CAACgB,KAAK,CAACR,eAAe,EAAEb,eAAe,CAACqB,KAAK,EAAE5B,KAAK,CAACwC,aAAa,CAAC,KAAK,EAAE;IAC5GC,SAAS,EAAE1C,UAAU,CAACiB,WAAW,EAAE,GAAGA,WAAW,IAAIY,KAAK,CAACJ,IAAI,EAAE,EAAE;MACjE,CAAC,GAAGR,WAAW,SAAS,GAAG,CAACa;IAC9B,CAAC,CAAC;IACFF,GAAG,EAAEU;EACP,CAAC,EAAErC,KAAK,CAACwC,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGzB,WAAW,QAAQ;IACjCW,GAAG,EAAEW;EACP,CAAC,EAAEtC,KAAK,CAACwC,aAAa,CAAC3B,KAAK,EAAE;IAC5B4B,SAAS,EAAE,GAAGzB,WAAW;EAC3B,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACwC,aAAa,CAAC,KAAK,EAAE;IAC9BC,SAAS,EAAE,GAAGzB,WAAW;EAC3B,CAAC,EAAEhB,KAAK,CAACwC,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGzB,WAAW;EAC3B,CAAC,EAAEY,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,SAASwC,MAAMA,CAAA,EAAG;IAChB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,OAAO5D,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAClD,MAAM6D,MAAM,GAAG,CAACF,EAAE,GAAG,CAACD,EAAE,GAAGV,SAAS,CAACc,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;MACpI,MAAMR,QAAQ,GAAGF,WAAW,CAACa,OAAO;MACpC,MAAME,YAAY,GAAGd,QAAQ,CAACY,OAAO;MACrCN,gBAAgB,CAACK,MAAM,CAAC;MACxB,IAAI,CAACA,MAAM,IAAI,CAACV,QAAQ,IAAI,CAACa,YAAY,EAAE;MAC3C,MAAM;QACJC,CAAC;QACDC,CAAC;QACDpC,SAAS,EAAEqC,aAAa;QACxBC;MACF,CAAC,GAAG,MAAMjE,eAAe,CAAC0D,MAAM,EAAEV,QAAQ,EAAE;QAC1CrB,SAAS;QACTuC,UAAU,EAAE,CAAC9D,MAAM,CAACW,SAAS,CAAC,EAAE,CAAC,CAAC,EAAEV,KAAK,CAAC;UACxC8D,OAAO,EAAEpD,SAAS,CAAC,CAAC,CAAC;UACrBqD,SAAS,EAAE,KAAK;UAChBC,OAAO,EAAElE,UAAU,CAAC;QACtB,CAAC,CAAC,EAAEF,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,EAAEJ,KAAK,CAAC;UACxB8D,OAAO,EAAEC,YAAY;UACrBM,OAAO,EAAEpD,SAAS,CAAC,EAAE;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFuD,MAAM,CAACC,MAAM,CAACvB,QAAQ,CAACwB,KAAK,EAAE;QAC5BC,IAAI,EAAE,GAAGX,CAAC,IAAI;QACdY,GAAG,EAAE,GAAGX,CAAC;MACX,CAAC,CAAC;MACF,MAAMY,IAAI,GAAGX,aAAa,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAG;QAChBH,GAAG,EAAE,QAAQ;QACbI,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK;QACbN,IAAI,EAAE;MACR,CAAC,CAACE,IAAI,CAAC;MACP,MAAM;QACJb,CAAC,EAAEkB,MAAM;QACTjB,CAAC,EAAEkB;MACL,CAAC,GAAG,CAACxB,EAAE,GAAGQ,cAAc,CAACnE,KAAK,MAAM,IAAI,IAAI2D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;MACnEa,MAAM,CAACC,MAAM,CAACV,YAAY,CAACW,KAAK,EAAE;QAChCC,IAAI,EAAEO,MAAM,IAAI,IAAI,GAAG,GAAGA,MAAM,IAAI,GAAG,EAAE;QACzCN,GAAG,EAAEO,MAAM,IAAI,IAAI,GAAG,GAAGA,MAAM,IAAI,GAAG,EAAE;QACxCH,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACV,CAACF,SAAS,GAAG;MACf,CAAC,CAAC;MACF,MAAMK,WAAW,GAAG;QAClBR,GAAG,EAAE,MAAM;QACXK,MAAM,EAAE,QAAQ;QAChBN,IAAI,EAAE,QAAQ;QACdK,KAAK,EAAE;MACT,CAAC,CAACH,IAAI,CAAC;MACPd,YAAY,CAACW,KAAK,CAACW,WAAW,CAAC,qBAAqB,EAAED,WAAW,CAAC;IACpE,CAAC,CAAC;EACJ;EACA3E,yBAAyB,CAAC,MAAM;IAC9B+C,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACF3C,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,aAAa,EAAE;IACpB,IAAI,CAACf,KAAK,CAAC+C,OAAO,EAAE;IACpB,SAASC,WAAWA,CAAA,EAAG;MACrB9C,UAAU,CAAC+C,CAAC,IAAI,CAACA,CAAC,CAAC;IACrB;IACAlC,aAAa,CAACmC,gBAAgB,CAAC,OAAO,EAAEF,WAAW,CAAC;IACpD,OAAO,MAAM;MACXjC,aAAa,CAACoC,mBAAmB,CAAC,OAAO,EAAEH,WAAW,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAACjC,aAAa,EAAEf,KAAK,CAAC+C,OAAO,CAAC,CAAC;EAClCzE,SAAS,CAAC,MAAM;IACd,MAAM8E,eAAe,GAAG3C,WAAW,CAACa,OAAO;IAC3C,IAAI,CAACP,aAAa,IAAI,CAACqC,eAAe,IAAI,CAACnD,OAAO,EAAE;IACpD,OAAOvC,UAAU,CAACqD,aAAa,EAAEqC,eAAe,EAAEnC,MAAM,EAAE;MACxDoC,aAAa,EAAE,OAAOC,cAAc,KAAK;IAC3C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,aAAa,EAAEd,OAAO,CAAC,CAAC;EAC5BhC,YAAY,CAAC,MAAM;IACjB,IAAI,CAAC+B,KAAK,CAAC+C,OAAO,EAAE;IACpB7C,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,CAAC,MAAM;IACR,IAAIgB,EAAE;IACN,OAAO,CAACA,EAAE,GAAGV,SAAS,CAACc,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,OAAO;EACjF,CAAC,EAAEd,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;EACxC,MAAM8C,YAAY,GAAG1E,eAAe,CAACoB,OAAO,EAAE,KAAK,EAAED,KAAK,CAACwD,aAAa,CAAC;EACzE,OAAOpF,KAAK,CAACwC,aAAa,CAACxC,KAAK,CAACqF,QAAQ,EAAE,IAAI,EAAErF,KAAK,CAACwC,aAAa,CAACzB,OAAO,EAAE;IAC5EY,GAAG,EAAES;EACP,CAAC,EAAER,KAAK,CAAC0D,QAAQ,CAAC,EAAEH,YAAY,IAAI3E,iBAAiB,CAACoB,KAAK,CAACP,YAAY,EAAEkB,QAAQ,CAAC,CAAC;AACtF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}