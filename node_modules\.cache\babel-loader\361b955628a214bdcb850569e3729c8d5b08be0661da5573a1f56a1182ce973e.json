{"ast": null, "code": "import \"./virtual-input.css\";\nimport { VirtualInput } from './virtual-input';\nexport default VirtualInput;", "map": {"version": 3, "names": ["VirtualInput"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/virtual-input/index.js"], "sourcesContent": ["import \"./virtual-input.css\";\nimport { VirtualInput } from './virtual-input';\nexport default VirtualInput;"], "mappings": "AAAA,OAAO,qBAAqB;AAC5B,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}