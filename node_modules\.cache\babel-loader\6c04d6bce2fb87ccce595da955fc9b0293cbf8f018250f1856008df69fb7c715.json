{"ast": null, "code": "import * as React from 'react';\nimport canUseDom from \"../Dom/canUseDom\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nvar useInternalLayoutEffect = process.env.NODE_ENV !== 'test' && canUseDom() ? React.useLayoutEffect : React.useEffect;\nvar useLayoutEffect = function useLayoutEffect(callback, deps) {\n  var firstMountRef = React.useRef(true);\n  useInternalLayoutEffect(function () {\n    return callback(firstMountRef.current);\n  }, deps);\n\n  // We tell react that first mount has passed\n  useInternalLayoutEffect(function () {\n    firstMountRef.current = false;\n    return function () {\n      firstMountRef.current = true;\n    };\n  }, []);\n};\nexport var useLayoutUpdateEffect = function useLayoutUpdateEffect(callback, deps) {\n  useLayoutEffect(function (firstMount) {\n    if (!firstMount) {\n      return callback();\n    }\n  }, deps);\n};\nexport default useLayoutEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}