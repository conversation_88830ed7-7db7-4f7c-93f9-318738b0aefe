{"ast": null, "code": "import React, { forwardRef, useCallback, useMemo } from 'react';\nimport { useMemoizedFn } from 'ahooks';\nimport Picker from '../picker';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { convertDateToStringArray, convertStringArrayToDate, generateDatePickerColumns } from './date-picker-utils';\nimport { bound } from '../../utils/bound';\nimport useRenderLabel from '../date-picker-view/useRenderLabel';\nimport { TILL_NOW } from './util';\nconst thisYear = new Date().getFullYear();\nconst defaultProps = {\n  min: new Date(new Date().setFullYear(thisYear - 10)),\n  max: new Date(new Date().setFullYear(thisYear + 10)),\n  precision: 'day',\n  defaultValue: null\n};\nexport const DatePicker = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    renderLabel\n  } = props;\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const now = useMemo(() => new Date(), []);\n  const mergedRenderLabel = useRenderLabel(renderLabel);\n  const pickerValue = useMemo(() => {\n    let date = value !== null && value !== void 0 ? value : now;\n    if (date.tillNow) {\n      return [TILL_NOW];\n    }\n    date = new Date(bound(date.getTime(), props.min.getTime(), props.max.getTime()));\n    return convertDateToStringArray(date, props.precision);\n  }, [value, props.precision, props.min, props.max]);\n  const onConfirm = useCallback(val => {\n    const date = convertStringArrayToDate(val, props.precision);\n    setValue(date, true);\n  }, [setValue, props.precision]);\n  const onSelect = useMemoizedFn(val => {\n    var _a;\n    const date = convertStringArrayToDate(val, props.precision);\n    (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, date);\n  });\n  const columns = useCallback(selected => generateDatePickerColumns(selected, props.min, props.max, props.precision, mergedRenderLabel, props.filter, props.tillNow), [props.min, props.max, props.precision, mergedRenderLabel, props.tillNow]);\n  return withNativeProps(props, React.createElement(Picker, {\n    ref: ref,\n    columns: columns,\n    value: pickerValue,\n    onCancel: props.onCancel,\n    onClose: props.onClose,\n    closeOnMaskClick: props.closeOnMaskClick,\n    visible: props.visible,\n    confirmText: props.confirmText,\n    cancelText: props.cancelText,\n    onConfirm: onConfirm,\n    onSelect: onSelect,\n    getContainer: props.getContainer,\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    title: props.title,\n    stopPropagation: props.stopPropagation,\n    mouseWheel: props.mouseWheel,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, (_, actions) => {\n    var _a;\n    return (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, value, actions);\n  }));\n});", "map": {"version": 3, "names": ["React", "forwardRef", "useCallback", "useMemo", "useMemoizedFn", "Picker", "withNativeProps", "mergeProps", "usePropsValue", "convertDateToStringArray", "convertStringArrayToDate", "generateDatePickerColumns", "bound", "useRenderLabel", "TILL_NOW", "thisYear", "Date", "getFullYear", "defaultProps", "min", "setFullYear", "max", "precision", "defaultValue", "DatePicker", "p", "ref", "props", "renderLabel", "value", "setValue", "onChange", "v", "_a", "onConfirm", "call", "now", "mergedRenderLabel", "picker<PERSON><PERSON><PERSON>", "date", "tillNow", "getTime", "val", "onSelect", "columns", "selected", "filter", "createElement", "onCancel", "onClose", "closeOnMaskClick", "visible", "confirmText", "cancelText", "getContainer", "loading", "loadingContent", "afterShow", "afterClose", "onClick", "title", "stopPropagation", "mouseWheel", "destroyOnClose", "forceRender", "_", "actions", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/date-picker/date-picker.js"], "sourcesContent": ["import React, { forwardRef, useCallback, useMemo } from 'react';\nimport { useMemoizedFn } from 'ahooks';\nimport Picker from '../picker';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { convertDateToStringArray, convertStringArrayToDate, generateDatePickerColumns } from './date-picker-utils';\nimport { bound } from '../../utils/bound';\nimport useRenderLabel from '../date-picker-view/useRenderLabel';\nimport { TILL_NOW } from './util';\nconst thisYear = new Date().getFullYear();\nconst defaultProps = {\n  min: new Date(new Date().setFullYear(thisYear - 10)),\n  max: new Date(new Date().setFullYear(thisYear + 10)),\n  precision: 'day',\n  defaultValue: null\n};\nexport const DatePicker = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    renderLabel\n  } = props;\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const now = useMemo(() => new Date(), []);\n  const mergedRenderLabel = useRenderLabel(renderLabel);\n  const pickerValue = useMemo(() => {\n    let date = value !== null && value !== void 0 ? value : now;\n    if (date.tillNow) {\n      return [TILL_NOW];\n    }\n    date = new Date(bound(date.getTime(), props.min.getTime(), props.max.getTime()));\n    return convertDateToStringArray(date, props.precision);\n  }, [value, props.precision, props.min, props.max]);\n  const onConfirm = useCallback(val => {\n    const date = convertStringArrayToDate(val, props.precision);\n    setValue(date, true);\n  }, [setValue, props.precision]);\n  const onSelect = useMemoizedFn(val => {\n    var _a;\n    const date = convertStringArrayToDate(val, props.precision);\n    (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, date);\n  });\n  const columns = useCallback(selected => generateDatePickerColumns(selected, props.min, props.max, props.precision, mergedRenderLabel, props.filter, props.tillNow), [props.min, props.max, props.precision, mergedRenderLabel, props.tillNow]);\n  return withNativeProps(props, React.createElement(Picker, {\n    ref: ref,\n    columns: columns,\n    value: pickerValue,\n    onCancel: props.onCancel,\n    onClose: props.onClose,\n    closeOnMaskClick: props.closeOnMaskClick,\n    visible: props.visible,\n    confirmText: props.confirmText,\n    cancelText: props.cancelText,\n    onConfirm: onConfirm,\n    onSelect: onSelect,\n    getContainer: props.getContainer,\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    title: props.title,\n    stopPropagation: props.stopPropagation,\n    mouseWheel: props.mouseWheel,\n    destroyOnClose: props.destroyOnClose,\n    forceRender: props.forceRender\n  }, (_, actions) => {\n    var _a;\n    return (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, value, actions);\n  }));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAC/D,SAASC,aAAa,QAAQ,QAAQ;AACtC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,wBAAwB,EAAEC,wBAAwB,EAAEC,yBAAyB,QAAQ,qBAAqB;AACnH,SAASC,KAAK,QAAQ,mBAAmB;AACzC,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,SAASC,QAAQ,QAAQ,QAAQ;AACjC,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AACzC,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAE,IAAIH,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACI,WAAW,CAACL,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpDM,GAAG,EAAE,IAAIL,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACI,WAAW,CAACL,QAAQ,GAAG,EAAE,CAAC,CAAC;EACpDO,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,UAAU,GAAGvB,UAAU,CAAC,CAACwB,CAAC,EAAEC,GAAG,KAAK;EAC/C,MAAMC,KAAK,GAAGpB,UAAU,CAACW,YAAY,EAAEO,CAAC,CAAC;EACzC,MAAM;IACJG;EACF,CAAC,GAAGD,KAAK;EACT,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,aAAa,CAAC;IACtCqB,KAAK,EAAEF,KAAK,CAACE,KAAK;IAClBN,YAAY,EAAEI,KAAK,CAACJ,YAAY;IAChCQ,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIC,EAAE;MACN,IAAID,CAAC,KAAK,IAAI,EAAE;MAChB,CAACC,EAAE,GAAGN,KAAK,CAACO,SAAS,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,KAAK,EAAEK,CAAC,CAAC;IAC/E;EACF,CAAC,CAAC;EACF,MAAMI,GAAG,GAAGjC,OAAO,CAAC,MAAM,IAAIa,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC;EACzC,MAAMqB,iBAAiB,GAAGxB,cAAc,CAACe,WAAW,CAAC;EACrD,MAAMU,WAAW,GAAGnC,OAAO,CAAC,MAAM;IAChC,IAAIoC,IAAI,GAAGV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,GAAG;IAC3D,IAAIG,IAAI,CAACC,OAAO,EAAE;MAChB,OAAO,CAAC1B,QAAQ,CAAC;IACnB;IACAyB,IAAI,GAAG,IAAIvB,IAAI,CAACJ,KAAK,CAAC2B,IAAI,CAACE,OAAO,CAAC,CAAC,EAAEd,KAAK,CAACR,GAAG,CAACsB,OAAO,CAAC,CAAC,EAAEd,KAAK,CAACN,GAAG,CAACoB,OAAO,CAAC,CAAC,CAAC,CAAC;IAChF,OAAOhC,wBAAwB,CAAC8B,IAAI,EAAEZ,KAAK,CAACL,SAAS,CAAC;EACxD,CAAC,EAAE,CAACO,KAAK,EAAEF,KAAK,CAACL,SAAS,EAAEK,KAAK,CAACR,GAAG,EAAEQ,KAAK,CAACN,GAAG,CAAC,CAAC;EAClD,MAAMa,SAAS,GAAGhC,WAAW,CAACwC,GAAG,IAAI;IACnC,MAAMH,IAAI,GAAG7B,wBAAwB,CAACgC,GAAG,EAAEf,KAAK,CAACL,SAAS,CAAC;IAC3DQ,QAAQ,CAACS,IAAI,EAAE,IAAI,CAAC;EACtB,CAAC,EAAE,CAACT,QAAQ,EAAEH,KAAK,CAACL,SAAS,CAAC,CAAC;EAC/B,MAAMqB,QAAQ,GAAGvC,aAAa,CAACsC,GAAG,IAAI;IACpC,IAAIT,EAAE;IACN,MAAMM,IAAI,GAAG7B,wBAAwB,CAACgC,GAAG,EAAEf,KAAK,CAACL,SAAS,CAAC;IAC3D,CAACW,EAAE,GAAGN,KAAK,CAACgB,QAAQ,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,KAAK,EAAEY,IAAI,CAAC;EACjF,CAAC,CAAC;EACF,MAAMK,OAAO,GAAG1C,WAAW,CAAC2C,QAAQ,IAAIlC,yBAAyB,CAACkC,QAAQ,EAAElB,KAAK,CAACR,GAAG,EAAEQ,KAAK,CAACN,GAAG,EAAEM,KAAK,CAACL,SAAS,EAAEe,iBAAiB,EAAEV,KAAK,CAACmB,MAAM,EAAEnB,KAAK,CAACa,OAAO,CAAC,EAAE,CAACb,KAAK,CAACR,GAAG,EAAEQ,KAAK,CAACN,GAAG,EAAEM,KAAK,CAACL,SAAS,EAAEe,iBAAiB,EAAEV,KAAK,CAACa,OAAO,CAAC,CAAC;EAC9O,OAAOlC,eAAe,CAACqB,KAAK,EAAE3B,KAAK,CAAC+C,aAAa,CAAC1C,MAAM,EAAE;IACxDqB,GAAG,EAAEA,GAAG;IACRkB,OAAO,EAAEA,OAAO;IAChBf,KAAK,EAAES,WAAW;IAClBU,QAAQ,EAAErB,KAAK,CAACqB,QAAQ;IACxBC,OAAO,EAAEtB,KAAK,CAACsB,OAAO;IACtBC,gBAAgB,EAAEvB,KAAK,CAACuB,gBAAgB;IACxCC,OAAO,EAAExB,KAAK,CAACwB,OAAO;IACtBC,WAAW,EAAEzB,KAAK,CAACyB,WAAW;IAC9BC,UAAU,EAAE1B,KAAK,CAAC0B,UAAU;IAC5BnB,SAAS,EAAEA,SAAS;IACpBS,QAAQ,EAAEA,QAAQ;IAClBW,YAAY,EAAE3B,KAAK,CAAC2B,YAAY;IAChCC,OAAO,EAAE5B,KAAK,CAAC4B,OAAO;IACtBC,cAAc,EAAE7B,KAAK,CAAC6B,cAAc;IACpCC,SAAS,EAAE9B,KAAK,CAAC8B,SAAS;IAC1BC,UAAU,EAAE/B,KAAK,CAAC+B,UAAU;IAC5BC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;IACtBC,KAAK,EAAEjC,KAAK,CAACiC,KAAK;IAClBC,eAAe,EAAElC,KAAK,CAACkC,eAAe;IACtCC,UAAU,EAAEnC,KAAK,CAACmC,UAAU;IAC5BC,cAAc,EAAEpC,KAAK,CAACoC,cAAc;IACpCC,WAAW,EAAErC,KAAK,CAACqC;EACrB,CAAC,EAAE,CAACC,CAAC,EAAEC,OAAO,KAAK;IACjB,IAAIjC,EAAE;IACN,OAAO,CAACA,EAAE,GAAGN,KAAK,CAACwC,QAAQ,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACR,KAAK,EAAEE,KAAK,EAAEqC,OAAO,CAAC;EAClG,CAAC,CAAC,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}