{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\nvar useMutationObserver = function (callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n    if (!element) {\n      return;\n    }\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      observer === null || observer === void 0 ? void 0 : observer.disconnect();\n    };\n  }, [options], target);\n};\nexport default useMutationObserver;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}