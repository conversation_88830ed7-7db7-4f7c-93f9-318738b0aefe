{"ast": null, "code": "import { mergeProps } from '../../utils/with-default-props';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { toCSSLength } from '../../utils/to-css-length';\nconst classPrefix = `adm-grid`;\nexport const Grid = props => {\n  const style = {\n    '--columns': props.columns.toString()\n  };\n  const {\n    gap\n  } = props;\n  if (gap !== undefined) {\n    if (Array.isArray(gap)) {\n      style['--gap-horizontal'] = toCSSLength(gap[0]);\n      style['--gap-vertical'] = toCSSLength(gap[1]);\n    } else {\n      style['--gap'] = toCSSLength(gap);\n    }\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    style: style\n  }, props.children));\n};\nexport const GridItem = p => {\n  const props = mergeProps({\n    span: 1\n  }, p);\n  const itemStyle = {\n    '--item-span': props.span\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}-item`,\n    style: itemStyle,\n    onClick: props.onClick\n  }, props.children));\n};", "map": {"version": 3, "names": ["mergeProps", "React", "withNativeProps", "toCS<PERSON><PERSON>th", "classPrefix", "Grid", "props", "style", "columns", "toString", "gap", "undefined", "Array", "isArray", "createElement", "className", "children", "GridItem", "p", "span", "itemStyle", "onClick"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/grid/grid.js"], "sourcesContent": ["import { mergeProps } from '../../utils/with-default-props';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { toCSSLength } from '../../utils/to-css-length';\nconst classPrefix = `adm-grid`;\nexport const Grid = props => {\n  const style = {\n    '--columns': props.columns.toString()\n  };\n  const {\n    gap\n  } = props;\n  if (gap !== undefined) {\n    if (Array.isArray(gap)) {\n      style['--gap-horizontal'] = toCSSLength(gap[0]);\n      style['--gap-vertical'] = toCSSLength(gap[1]);\n    } else {\n      style['--gap'] = toCSSLength(gap);\n    }\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    style: style\n  }, props.children));\n};\nexport const GridItem = p => {\n  const props = mergeProps({\n    span: 1\n  }, p);\n  const itemStyle = {\n    '--item-span': props.span\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: `${classPrefix}-item`,\n    style: itemStyle,\n    onClick: props.onClick\n  }, props.children));\n};"], "mappings": "AAAA,SAASA,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,2BAA2B;AACvD,MAAMC,WAAW,GAAG,UAAU;AAC9B,OAAO,MAAMC,IAAI,GAAGC,KAAK,IAAI;EAC3B,MAAMC,KAAK,GAAG;IACZ,WAAW,EAAED,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC;EACtC,CAAC;EACD,MAAM;IACJC;EACF,CAAC,GAAGJ,KAAK;EACT,IAAII,GAAG,KAAKC,SAAS,EAAE;IACrB,IAAIC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;MACtBH,KAAK,CAAC,kBAAkB,CAAC,GAAGJ,WAAW,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/CH,KAAK,CAAC,gBAAgB,CAAC,GAAGJ,WAAW,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLH,KAAK,CAAC,OAAO,CAAC,GAAGJ,WAAW,CAACO,GAAG,CAAC;IACnC;EACF;EACA,OAAOR,eAAe,CAACI,KAAK,EAAEL,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEX,WAAW;IACtBG,KAAK,EAAEA;EACT,CAAC,EAAED,KAAK,CAACU,QAAQ,CAAC,CAAC;AACrB,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGC,CAAC,IAAI;EAC3B,MAAMZ,KAAK,GAAGN,UAAU,CAAC;IACvBmB,IAAI,EAAE;EACR,CAAC,EAAED,CAAC,CAAC;EACL,MAAME,SAAS,GAAG;IAChB,aAAa,EAAEd,KAAK,CAACa;EACvB,CAAC;EACD,OAAOjB,eAAe,CAACI,KAAK,EAAEL,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAE,GAAGX,WAAW,OAAO;IAChCG,KAAK,EAAEa,SAAS;IAChBC,OAAO,EAAEf,KAAK,CAACe;EACjB,CAAC,EAAEf,KAAK,CAACU,QAAQ,CAAC,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}