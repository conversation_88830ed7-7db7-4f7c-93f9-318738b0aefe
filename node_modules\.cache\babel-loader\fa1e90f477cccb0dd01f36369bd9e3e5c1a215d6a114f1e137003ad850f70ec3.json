{"ast": null, "code": "import React, { isValidElement, useRef } from 'react';\nimport classNames from 'classnames';\nimport { useSpring, animated } from '@react-spring/web';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { bound } from '../../utils/bound';\nimport { useThrottleFn, useIsomorphicLayoutEffect } from 'ahooks';\nimport { useMutationEffect } from '../../utils/use-mutation-effect';\nimport { useResizeEffect } from '../../utils/use-resize-effect';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { ShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-tabs`;\nexport const Tab = () => {\n  return null;\n};\nconst defaultProps = {\n  activeLineMode: 'auto',\n  stretch: true,\n  direction: 'ltr'\n};\nexport const Tabs = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const tabListContainerRef = useRef(null);\n  const activeLineRef = useRef(null);\n  const keyToIndexRecord = {};\n  let firstActiveKey = null;\n  const panes = [];\n  const isRTL = props.direction === 'rtl';\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    const length = panes.push(child);\n    keyToIndexRecord[key] = length - 1;\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const [{\n    x,\n    width\n  }, inkApi] = useSpring(() => ({\n    x: 0,\n    width: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    scrollLeft\n  }, scrollApi] = useSpring(() => ({\n    scrollLeft: 0,\n    config: {\n      tension: 300,\n      clamp: true\n    }\n  }));\n  const [{\n    leftMaskOpacity,\n    rightMaskOpacity\n  }, maskApi] = useSpring(() => ({\n    leftMaskOpacity: 0,\n    rightMaskOpacity: 0,\n    config: {\n      clamp: true\n    }\n  }));\n  function animate(immediate = false, fromMutation = false) {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const activeIndex = keyToIndexRecord[activeKey];\n    if (activeIndex === undefined) {\n      inkApi.start({\n        x: 0,\n        width: 0,\n        immediate: true\n      });\n      return;\n    }\n    const activeLine = activeLineRef.current;\n    if (!activeLine) return;\n    const activeTabWrapper = container.children.item(activeIndex + 1);\n    const activeTab = activeTabWrapper.children.item(0);\n    const activeTabLeft = activeTab.offsetLeft;\n    const activeTabWidth = activeTab.offsetWidth;\n    const activeTabWrapperLeft = activeTabWrapper.offsetLeft;\n    const activeTabWrapperWidth = activeTabWrapper.offsetWidth;\n    const containerWidth = container.offsetWidth;\n    const containerScrollWidth = container.scrollWidth;\n    const containerScrollLeft = container.scrollLeft;\n    const activeLineWidth = activeLine.offsetWidth;\n    let x = 0;\n    let width = 0;\n    if (props.activeLineMode === 'auto') {\n      x = activeTabLeft;\n      width = activeTabWidth;\n    } else if (props.activeLineMode === 'full') {\n      x = activeTabWrapperLeft;\n      width = activeTabWrapperWidth;\n    } else {\n      x = activeTabLeft + (activeTabWidth - activeLineWidth) / 2;\n    }\n    if (isRTL) {\n      /**\n       * In RTL mode, x equals the container width minus the x-coordinate of the current tab minus the width of the current tab.\n       * https://github.com/Fog3211/reproduce-codesandbox/blob/f0a3396a114cc00e88a51a67d3be60a746519b30/assets/images/antd_mobile_tabs_rtl_x.jpg?raw=true\n       */\n      const w = ['auto', 'full'].includes(props.activeLineMode) ? width : activeLineWidth;\n      x = -(containerWidth - x - w);\n    }\n    inkApi.start({\n      x,\n      width,\n      immediate\n    });\n    const maxScrollDistance = containerScrollWidth - containerWidth;\n    if (maxScrollDistance <= 0) return;\n    let nextScrollLeft = 0;\n    if (isRTL) {\n      /**\n       * 位移距离等于：activeTab的中心坐标距离容器中心坐标的距离，然后RTL取负数\n       * containerWidth / 2 - (activeTabLeft + (activeTabWidth - activeLineWidth) / 2) - activeLineWidth / 2,\n       */\n      nextScrollLeft = -bound(containerWidth / 2 - activeTabLeft + activeTabWidth / 2 - activeLineWidth, 0, maxScrollDistance);\n    } else {\n      nextScrollLeft = bound(activeTabLeft - (containerWidth - activeTabWidth) / 2, 0, maxScrollDistance);\n    }\n    if (!fromMutation || props.autoScroll !== false) {\n      scrollApi.start({\n        scrollLeft: nextScrollLeft,\n        from: {\n          scrollLeft: containerScrollLeft\n        },\n        immediate\n      });\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    animate(!x.isAnimating);\n  }, []);\n  useIsomorphicUpdateLayoutEffect(() => {\n    animate();\n  }, [activeKey, isRTL, props.activeLineMode]);\n  useResizeEffect(() => {\n    animate(!x.isAnimating);\n  }, tabListContainerRef);\n  useMutationEffect(() => {\n    animate(!x.isAnimating, true);\n  }, tabListContainerRef, {\n    subtree: true,\n    childList: true,\n    characterData: true\n  });\n  const {\n    run: updateMask\n  } = useThrottleFn((immediate = false) => {\n    const container = tabListContainerRef.current;\n    if (!container) return;\n    const scrollLeft = container.scrollLeft;\n    let showLeftMask = false;\n    let showRightMask = false;\n    if (isRTL) {\n      /**\n       * RTL模式下，只要滑动过，scrollLeft就再也回不到0（chrome是0.5）\n       * 所以要加round才能终止触发条件\n       * round(443.5) + 375 < 819\n       */\n      showLeftMask = Math.round(-scrollLeft) + container.offsetWidth < container.scrollWidth;\n      showRightMask = scrollLeft < 0;\n    } else {\n      showLeftMask = scrollLeft > 0;\n      showRightMask = scrollLeft + container.offsetWidth < container.scrollWidth;\n    }\n    maskApi.start({\n      leftMaskOpacity: showLeftMask ? 1 : 0,\n      rightMaskOpacity: showRightMask ? 1 : 0,\n      immediate\n    });\n  }, {\n    wait: 100,\n    trailing: true,\n    leading: true\n  });\n  useIsomorphicLayoutEffect(() => {\n    updateMask(true);\n  }, []);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    style: {\n      direction: props.direction\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-left`),\n    style: {\n      opacity: leftMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-header-mask`, `${classPrefix}-header-mask-right`),\n    style: {\n      opacity: rightMaskOpacity\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-tab-list`,\n    ref: tabListContainerRef,\n    scrollLeft: scrollLeft,\n    onScroll: updateMask,\n    role: 'tablist'\n  }, React.createElement(animated.div, {\n    ref: activeLineRef,\n    className: `${classPrefix}-tab-line`,\n    style: {\n      width: props.activeLineMode === 'fixed' ? 'var(--fixed-active-line-width, 30px)' : width,\n      x\n    }\n  }), panes.map(pane => withNativeProps(pane.props, React.createElement(\"div\", {\n    key: pane.key,\n    className: classNames(`${classPrefix}-tab-wrapper`, {\n      [`${classPrefix}-tab-wrapper-stretch`]: props.stretch\n    })\n  }, React.createElement(\"div\", {\n    onClick: () => {\n      const {\n        key\n      } = pane;\n      if (pane.props.disabled) return;\n      if (key === undefined || key === null) {\n        return;\n      }\n      setActiveKey(key.toString());\n    },\n    className: classNames(`${classPrefix}-tab`, {\n      [`${classPrefix}-tab-active`]: pane.key === activeKey,\n      [`${classPrefix}-tab-disabled`]: pane.props.disabled\n    }),\n    role: 'tab',\n    \"aria-selected\": pane.key === activeKey\n  }, pane.props.title)))))), panes.map(pane => {\n    if (pane.props.children === undefined) {\n      return null;\n    }\n    const active = pane.key === activeKey;\n    return React.createElement(ShouldRender, {\n      key: pane.key,\n      active: active,\n      forceRender: pane.props.forceRender,\n      destroyOnClose: pane.props.destroyOnClose\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`,\n      style: {\n        display: active ? 'block' : 'none'\n      }\n    }, pane.props.children));\n  })));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}