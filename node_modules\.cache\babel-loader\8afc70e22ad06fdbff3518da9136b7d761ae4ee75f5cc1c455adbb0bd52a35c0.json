{"ast": null, "code": "import React, { memo, useRef } from 'react';\nimport { useSpring, animated } from '@react-spring/web';\nimport { useDrag, useWheel } from '@use-gesture/react';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { bound } from '../../utils/bound';\nimport isEqual from 'react-fast-compare';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport { measureCSSLength } from '../../utils/measure-css-length';\nimport { supportsPassive } from '../../utils/supports-passive';\nimport classNames from 'classnames';\nconst classPrefix = `adm-picker-view`;\nexport const Wheel = memo(props => {\n  const {\n    value,\n    column,\n    renderLabel\n  } = props;\n  function onSelect(val) {\n    props.onSelect(val, props.index);\n  }\n  const [{\n    y\n  }, api] = useSpring(() => ({\n    from: {\n      y: 0\n    },\n    config: {\n      tension: 400,\n      mass: 0.8\n    }\n  }));\n  const draggingRef = useRef(false);\n  const rootRef = useRef(null);\n  const itemHeightMeasureRef = useRef(null);\n  const itemHeight = useRef(34);\n  useIsomorphicLayoutEffect(() => {\n    const itemHeightMeasure = itemHeightMeasureRef.current;\n    if (!itemHeightMeasure) return;\n    itemHeight.current = measureCSSLength(window.getComputedStyle(itemHeightMeasure).getPropertyValue('height'));\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (draggingRef.current) return;\n    if (value === null) return;\n    const targetIndex = column.findIndex(item => item.value === value);\n    if (targetIndex < 0) return;\n    const finalPosition = targetIndex * -itemHeight.current;\n    api.start({\n      y: finalPosition,\n      immediate: y.goal !== finalPosition\n    });\n  }, [value, column]);\n  useIsomorphicLayoutEffect(() => {\n    if (column.length === 0) {\n      if (value !== null) {\n        onSelect(null);\n      }\n    } else {\n      if (!column.some(item => item.value === value)) {\n        const firstItem = column[0];\n        onSelect(firstItem.value);\n      }\n    }\n  }, [column, value]);\n  function scrollSelect(index) {\n    const finalPosition = index * -itemHeight.current;\n    api.start({\n      y: finalPosition\n    });\n    const item = column[index];\n    if (!item) return;\n    onSelect(item.value);\n  }\n  const handleGestureState = state => {\n    const {\n      direction: [, direction],\n      distance: [, distance],\n      velocity: [, velocity],\n      offset: [, offset],\n      last\n    } = state;\n    return {\n      direction,\n      distance,\n      velocity,\n      offset,\n      last\n    };\n  };\n  const handleDrag = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      offset\n    } = handleGestureState(state);\n    if (last) {\n      draggingRef.current = false;\n      const position = offset + velocity * direction * 50;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = offset;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  const handleWheel = state => {\n    draggingRef.current = true;\n    const min = -((column.length - 1) * itemHeight.current);\n    const max = 0;\n    const {\n      direction,\n      last,\n      velocity,\n      distance\n    } = handleGestureState(state);\n    const whellDir = -direction; // 取反\n    const scrollY = y.get();\n    if (last) {\n      draggingRef.current = false;\n      const speed = velocity * whellDir * 50;\n      const position = scrollY + distance * whellDir + speed;\n      const boundNum = bound(position, min, max);\n      const targetIndex = -Math.round(boundNum / itemHeight.current);\n      scrollSelect(targetIndex);\n    } else {\n      const position = scrollY + distance * whellDir;\n      api.start({\n        y: rubberbandIfOutOfBounds(position, min, max, itemHeight.current * 50, 0.2)\n      });\n    }\n  };\n  useDrag(state => {\n    state.event.stopPropagation();\n    handleDrag(state);\n  }, {\n    axis: 'y',\n    from: () => [0, y.get()],\n    filterTaps: true,\n    pointer: {\n      touch: true\n    },\n    target: rootRef\n  });\n  useWheel(state => {\n    state.event.stopPropagation();\n    handleWheel(state);\n  }, {\n    target: props.mouseWheel ? rootRef : undefined,\n    axis: 'y',\n    from: () => [0, y.get()],\n    preventDefault: true,\n    eventOptions: supportsPassive ? {\n      passive: false\n    } : undefined\n  });\n  let selectedIndex = null;\n  function renderAccessible() {\n    if (selectedIndex === null) {\n      return null;\n    }\n    const current = column[selectedIndex];\n    const previousIndex = selectedIndex - 1;\n    const nextIndex = selectedIndex + 1;\n    const previous = column[previousIndex];\n    const next = column[nextIndex];\n    return React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-current`,\n      role: 'button',\n      \"aria-label\": current ? `当前选择的是：${current.label}` : '当前未选择'\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!previous) return;\n        scrollSelect(previousIndex);\n      },\n      role: previous ? 'button' : 'text',\n      \"aria-label\": !previous ? '没有上一项' : `选择上一项：${previous.label}`\n    }, \"-\"), React.createElement(\"div\", {\n      className: `${classPrefix}-column-accessible-button`,\n      onClick: () => {\n        if (!next) return;\n        scrollSelect(nextIndex);\n      },\n      role: next ? 'button' : 'text',\n      \"aria-label\": !next ? '没有下一项' : `选择下一项：${next.label}`\n    }, \"-\"));\n  }\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-column`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-item-height-measure`,\n    ref: itemHeightMeasureRef\n  }), React.createElement(animated.div, {\n    ref: rootRef,\n    style: {\n      translateY: y\n    },\n    className: `${classPrefix}-column-wheel`,\n    \"aria-hidden\": true\n  }, column.map((item, index) => {\n    var _a;\n    const selected = props.value === item.value;\n    if (selected) selectedIndex = index;\n    function handleClick() {\n      draggingRef.current = false;\n      scrollSelect(index);\n    }\n    return React.createElement(\"div\", {\n      key: (_a = item.key) !== null && _a !== void 0 ? _a : item.value,\n      \"data-selected\": selected,\n      className: classNames(`${classPrefix}-column-item`, {\n        [`${classPrefix}-column-item-active`]: selected\n      }),\n      onClick: handleClick,\n      \"aria-hidden\": !selected,\n      \"aria-label\": selected ? 'active' : ''\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-column-item-label`\n    }, renderLabel(item)));\n  })), renderAccessible());\n}, (prev, next) => {\n  if (prev.index !== next.index) return false;\n  if (prev.value !== next.value) return false;\n  if (prev.onSelect !== next.onSelect) return false;\n  if (prev.renderLabel !== next.renderLabel) return false;\n  if (prev.mouseWheel !== next.mouseWheel) return false;\n  if (!isEqual(prev.column, next.column)) return false;\n  return true;\n});\nWheel.displayName = 'Wheel';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}