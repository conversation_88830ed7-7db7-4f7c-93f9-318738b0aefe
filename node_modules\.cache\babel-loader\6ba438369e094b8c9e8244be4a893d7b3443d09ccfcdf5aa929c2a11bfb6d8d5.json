{"ast": null, "code": "import * as React from \"react\";\nfunction StarFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarFill-StarFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StarFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.845596,4.44900551 C26.6681364,4.87129848 27.3339158,5.56388467 27.7398638,6.41954369 L31.6608321,14.6841788 L40.4283838,16.0094747 C42.70793,16.354049 44.2873463,18.5557239 43.9561089,20.9270533 C43.8242084,21.8713291 43.3967517,22.7440386 42.7399141,23.4100762 L36.3956541,29.8431975 L37.8933307,38.9269105 C38.2827237,41.2886581 36.7579187,43.531608 34.4875834,43.9366794 C33.5835239,44.097981 32.6535618,43.9447589 31.8416658,43.5007338 L23.9997292,39.2119863 L16.1577926,43.5007338 C14.1189044,44.6157998 11.5971068,43.8003441 10.5251999,41.6793627 C10.0983609,40.8347767 9.95106936,39.8673708 10.1061277,38.9269105 L11.6038043,29.8431975 L5.25954433,23.4100762 C3.61004918,21.7374773 3.57629754,18.9905481 5.18415792,17.274638 C5.82441637,16.5913537 6.66334736,16.146686 7.57107463,16.0094747 L16.3386263,14.6841788 L20.2595946,6.41954369 C21.2790387,4.27075478 23.7799767,3.38851478 25.845596,4.44900551 Z\",\n    id: \"StarFill-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default StarFill;", "map": {"version": 3, "names": ["React", "StarFill", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/StarFill.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction StarFill(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarFill-StarFill\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"StarFill-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"StarFill-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25.845596,4.44900551 C26.6681364,4.87129848 27.3339158,5.56388467 27.7398638,6.41954369 L31.6608321,14.6841788 L40.4283838,16.0094747 C42.70793,16.354049 44.2873463,18.5557239 43.9561089,20.9270533 C43.8242084,21.8713291 43.3967517,22.7440386 42.7399141,23.4100762 L36.3956541,29.8431975 L37.8933307,38.9269105 C38.2827237,41.2886581 36.7579187,43.531608 34.4875834,43.9366794 C33.5835239,44.097981 32.6535618,43.9447589 31.8416658,43.5007338 L23.9997292,39.2119863 L16.1577926,43.5007338 C14.1189044,44.6157998 11.5971068,43.8003441 10.5251999,41.6793627 C10.0983609,40.8347767 9.95106936,39.8673708 10.1061277,38.9269105 L11.6038043,29.8431975 L5.25954433,23.4100762 C3.61004918,21.7374773 3.57629754,18.9905481 5.18415792,17.274638 C5.82441637,16.5913537 6.66334736,16.146686 7.57107463,16.0094747 L16.3386263,14.6841788 L20.2595946,6.41954369 C21.2790387,4.27075478 23.7799767,3.38851478 25.845596,4.44900551 Z\",\n    id: \"StarFill-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default StarFill;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mBAAmB;IACvBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,q5BAAq5B;IACx5BR,EAAE,EAAE,uBAAuB;IAC3BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}