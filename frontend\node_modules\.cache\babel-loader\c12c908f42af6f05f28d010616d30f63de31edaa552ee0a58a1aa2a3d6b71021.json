{"ast": null, "code": "import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nexport default function useCreation(factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n  return current.obj;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}