{"ast": null, "code": "/**\n * 数组打乱\n * @param array 任意数组\n * @returns any[] 打乱后的数组\n */\nexport function shuffle(array) {\n  const result = [...array];\n  for (let i = result.length; i > 0; i--) {\n    const j = Math.floor(Math.random() * i);\n    [result[i - 1], result[j]] = [result[j], result[i - 1]];\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}