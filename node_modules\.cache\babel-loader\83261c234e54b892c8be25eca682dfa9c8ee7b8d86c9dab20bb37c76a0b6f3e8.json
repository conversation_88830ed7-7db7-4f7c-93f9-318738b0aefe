{"ast": null, "code": "export const sleep = time => new Promise(resolve => setTimeout(resolve, time));", "map": {"version": 3, "names": ["sleep", "time", "Promise", "resolve", "setTimeout"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/sleep.js"], "sourcesContent": ["export const sleep = time => new Promise(resolve => setTimeout(resolve, time));"], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAGC,IAAI,IAAI,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}