{"ast": null, "code": "import React, { isValidElement } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Badge from '../badge';\nimport SafeArea from '../safe-area';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\n/* istanbul ignore next */\nexport const TabBarItem = () => {\n  return null;\n};\nconst classPrefix = `adm-tab-bar`;\nconst defaultProps = {\n  safeArea: false\n};\nexport const TabBar = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  let firstActiveKey = null;\n  const items = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    items.push(child);\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-wrap`\n  }, items.map(item => {\n    const active = item.key === activeKey;\n    function renderContent() {\n      const iconElement = item.props.icon && React.createElement(\"div\", {\n        className: `${classPrefix}-item-icon`\n      }, typeof item.props.icon === 'function' ? item.props.icon(active) : item.props.icon);\n      const titleElement = item.props.title && React.createElement(\"div\", {\n        className: classNames(`${classPrefix}-item-title`, Boolean(iconElement) && `${classPrefix}-item-title-with-icon`)\n      }, typeof item.props.title === 'function' ? item.props.title(active) : item.props.title);\n      if (iconElement) {\n        return React.createElement(React.Fragment, null, React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-icon-badge`\n        }, iconElement), titleElement);\n      } else if (titleElement) {\n        return React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-title-badge`\n        }, titleElement);\n      }\n      return null;\n    }\n    return withNativeProps(item.props, React.createElement(\"div\", {\n      key: item.key,\n      onClick: () => {\n        var _a, _b;\n        const {\n          key\n        } = item;\n        if (key === undefined || key === null) return;\n        setActiveKey(key.toString());\n        (_b = (_a = item.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a);\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-active`]: active\n      })\n    }, renderContent()));\n  })), props.safeArea && React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n};", "map": {"version": 3, "names": ["React", "isValidElement", "classNames", "withNativeProps", "mergeProps", "Badge", "SafeArea", "usePropsValue", "traverseReactNode", "TabBarItem", "classPrefix", "defaultProps", "safeArea", "TabBar", "p", "_a", "props", "firstActiveKey", "items", "children", "child", "index", "key", "push", "active<PERSON><PERSON>", "setActiveKey", "value", "defaultValue", "defaultActiveKey", "onChange", "v", "call", "createElement", "className", "map", "item", "active", "renderContent", "iconElement", "icon", "titleElement", "title", "Boolean", "Fragment", "content", "badge", "onClick", "_b", "undefined", "toString", "position"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/tab-bar/tab-bar.js"], "sourcesContent": ["import React, { isValidElement } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Badge from '../badge';\nimport SafeArea from '../safe-area';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\n/* istanbul ignore next */\nexport const TabBarItem = () => {\n  return null;\n};\nconst classPrefix = `adm-tab-bar`;\nconst defaultProps = {\n  safeArea: false\n};\nexport const TabBar = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  let firstActiveKey = null;\n  const items = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    items.push(child);\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-wrap`\n  }, items.map(item => {\n    const active = item.key === activeKey;\n    function renderContent() {\n      const iconElement = item.props.icon && React.createElement(\"div\", {\n        className: `${classPrefix}-item-icon`\n      }, typeof item.props.icon === 'function' ? item.props.icon(active) : item.props.icon);\n      const titleElement = item.props.title && React.createElement(\"div\", {\n        className: classNames(`${classPrefix}-item-title`, Boolean(iconElement) && `${classPrefix}-item-title-with-icon`)\n      }, typeof item.props.title === 'function' ? item.props.title(active) : item.props.title);\n      if (iconElement) {\n        return React.createElement(React.Fragment, null, React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-icon-badge`\n        }, iconElement), titleElement);\n      } else if (titleElement) {\n        return React.createElement(Badge, {\n          content: item.props.badge,\n          className: `${classPrefix}-title-badge`\n        }, titleElement);\n      }\n      return null;\n    }\n    return withNativeProps(item.props, React.createElement(\"div\", {\n      key: item.key,\n      onClick: () => {\n        var _a, _b;\n        const {\n          key\n        } = item;\n        if (key === undefined || key === null) return;\n        setActiveKey(key.toString());\n        (_b = (_a = item.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a);\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-active`]: active\n      })\n    }, renderContent()));\n  })), props.safeArea && React.createElement(SafeArea, {\n    position: 'bottom'\n  })));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,cAAc,QAAQ,OAAO;AAC7C,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE;AACA,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAC9B,OAAO,IAAI;AACb,CAAC;AACD,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGC,CAAC,IAAI;EACzB,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGZ,UAAU,CAACO,YAAY,EAAEG,CAAC,CAAC;EACzC,IAAIG,cAAc,GAAG,IAAI;EACzB,MAAMC,KAAK,GAAG,EAAE;EAChBV,iBAAiB,CAACQ,KAAK,CAACG,QAAQ,EAAE,CAACC,KAAK,EAAEC,KAAK,KAAK;IAClD,IAAI,CAACpB,cAAc,CAACmB,KAAK,CAAC,EAAE;IAC5B,MAAME,GAAG,GAAGF,KAAK,CAACE,GAAG;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC7B,IAAID,KAAK,KAAK,CAAC,EAAE;MACfJ,cAAc,GAAGK,GAAG;IACtB;IACAJ,KAAK,CAACK,IAAI,CAACH,KAAK,CAAC;EACnB,CAAC,CAAC;EACF,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGlB,aAAa,CAAC;IAC9CmB,KAAK,EAAEV,KAAK,CAACQ,SAAS;IACtBG,YAAY,EAAE,CAACZ,EAAE,GAAGC,KAAK,CAACY,gBAAgB,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,cAAc;IAC3FY,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIf,EAAE;MACN,IAAIe,CAAC,KAAK,IAAI,EAAE;MAChB,CAACf,EAAE,GAAGC,KAAK,CAACa,QAAQ,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAACf,KAAK,EAAEc,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC;EACF,OAAO3B,eAAe,CAACa,KAAK,EAAEhB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEvB;EACb,CAAC,EAAEV,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGvB,WAAW;EAC3B,CAAC,EAAEQ,KAAK,CAACgB,GAAG,CAACC,IAAI,IAAI;IACnB,MAAMC,MAAM,GAAGD,IAAI,CAACb,GAAG,KAAKE,SAAS;IACrC,SAASa,aAAaA,CAAA,EAAG;MACvB,MAAMC,WAAW,GAAGH,IAAI,CAACnB,KAAK,CAACuB,IAAI,IAAIvC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;QAChEC,SAAS,EAAE,GAAGvB,WAAW;MAC3B,CAAC,EAAE,OAAOyB,IAAI,CAACnB,KAAK,CAACuB,IAAI,KAAK,UAAU,GAAGJ,IAAI,CAACnB,KAAK,CAACuB,IAAI,CAACH,MAAM,CAAC,GAAGD,IAAI,CAACnB,KAAK,CAACuB,IAAI,CAAC;MACrF,MAAMC,YAAY,GAAGL,IAAI,CAACnB,KAAK,CAACyB,KAAK,IAAIzC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;QAClEC,SAAS,EAAE/B,UAAU,CAAC,GAAGQ,WAAW,aAAa,EAAEgC,OAAO,CAACJ,WAAW,CAAC,IAAI,GAAG5B,WAAW,uBAAuB;MAClH,CAAC,EAAE,OAAOyB,IAAI,CAACnB,KAAK,CAACyB,KAAK,KAAK,UAAU,GAAGN,IAAI,CAACnB,KAAK,CAACyB,KAAK,CAACL,MAAM,CAAC,GAAGD,IAAI,CAACnB,KAAK,CAACyB,KAAK,CAAC;MACxF,IAAIH,WAAW,EAAE;QACf,OAAOtC,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAAC2C,QAAQ,EAAE,IAAI,EAAE3C,KAAK,CAACgC,aAAa,CAAC3B,KAAK,EAAE;UAC1EuC,OAAO,EAAET,IAAI,CAACnB,KAAK,CAAC6B,KAAK;UACzBZ,SAAS,EAAE,GAAGvB,WAAW;QAC3B,CAAC,EAAE4B,WAAW,CAAC,EAAEE,YAAY,CAAC;MAChC,CAAC,MAAM,IAAIA,YAAY,EAAE;QACvB,OAAOxC,KAAK,CAACgC,aAAa,CAAC3B,KAAK,EAAE;UAChCuC,OAAO,EAAET,IAAI,CAACnB,KAAK,CAAC6B,KAAK;UACzBZ,SAAS,EAAE,GAAGvB,WAAW;QAC3B,CAAC,EAAE8B,YAAY,CAAC;MAClB;MACA,OAAO,IAAI;IACb;IACA,OAAOrC,eAAe,CAACgC,IAAI,CAACnB,KAAK,EAAEhB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;MAC5DV,GAAG,EAAEa,IAAI,CAACb,GAAG;MACbwB,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI/B,EAAE,EAAEgC,EAAE;QACV,MAAM;UACJzB;QACF,CAAC,GAAGa,IAAI;QACR,IAAIb,GAAG,KAAK0B,SAAS,IAAI1B,GAAG,KAAK,IAAI,EAAE;QACvCG,YAAY,CAACH,GAAG,CAAC2B,QAAQ,CAAC,CAAC,CAAC;QAC5B,CAACF,EAAE,GAAG,CAAChC,EAAE,GAAGoB,IAAI,CAACnB,KAAK,EAAE8B,OAAO,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChB,IAAI,CAAChB,EAAE,CAAC;MACnF,CAAC;MACDkB,SAAS,EAAE/B,UAAU,CAAC,GAAGQ,WAAW,OAAO,EAAE;QAC3C,CAAC,GAAGA,WAAW,cAAc,GAAG0B;MAClC,CAAC;IACH,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,EAAErB,KAAK,CAACJ,QAAQ,IAAIZ,KAAK,CAACgC,aAAa,CAAC1B,QAAQ,EAAE;IACnD4C,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}