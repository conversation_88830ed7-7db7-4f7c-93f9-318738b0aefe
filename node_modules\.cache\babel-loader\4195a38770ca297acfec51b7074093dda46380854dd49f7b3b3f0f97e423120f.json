{"ast": null, "code": "import debounce from 'lodash/debounce';\nfunction isNodeOrWeb() {\n  var freeGlobal = (typeof global === 'undefined' ? 'undefined' : typeof global) == 'object' && global && global.Object === Object && global;\n  var freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n  return freeGlobal || freeSelf;\n}\nif (!isNodeOrWeb()) {\n  global.Date = Date;\n}\nexport { debounce };", "map": {"version": 3, "names": ["debounce", "isNodeOrWeb", "freeGlobal", "global", "Object", "freeSelf", "self", "Date"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/lodash-polyfill.js"], "sourcesContent": ["import debounce from 'lodash/debounce';\nfunction isNodeOrWeb() {\n  var freeGlobal = (typeof global === 'undefined' ? 'undefined' : typeof global) == 'object' && global && global.Object === Object && global;\n  var freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n  return freeGlobal || freeSelf;\n}\nif (!isNodeOrWeb()) {\n  global.Date = Date;\n}\nexport { debounce };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,UAAU,GAAG,CAAC,OAAOC,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAKA,MAAM,IAAID,MAAM;EAC1I,IAAIE,QAAQ,GAAG,OAAOC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAIA,IAAI,CAACF,MAAM,KAAKA,MAAM,IAAIE,IAAI;EAChF,OAAOJ,UAAU,IAAIG,QAAQ;AAC/B;AACA,IAAI,CAACJ,WAAW,CAAC,CAAC,EAAE;EAClBE,MAAM,CAACI,IAAI,GAAGA,IAAI;AACpB;AACA,SAASP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}