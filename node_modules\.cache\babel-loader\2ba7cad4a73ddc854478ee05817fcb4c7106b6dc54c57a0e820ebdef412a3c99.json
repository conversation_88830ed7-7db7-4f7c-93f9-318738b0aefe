{"ast": null, "code": "import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-page-indicator`;\nconst defaultProps = {\n  color: 'primary',\n  direction: 'horizontal'\n};\nexport const PageIndicator = memo(p => {\n  const props = mergeProps(defaultProps, p);\n  const dots = [];\n  for (let i = 0; i < props.total; i++) {\n    dots.push(React.createElement(\"div\", {\n      key: i,\n      className: classNames(`${classPrefix}-dot`, {\n        [`${classPrefix}-dot-active`]: props.current === i\n      })\n    }));\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.direction}`, `${classPrefix}-color-${props.color}`)\n  }, dots));\n});", "map": {"version": 3, "names": ["React", "memo", "withNativeProps", "classNames", "mergeProps", "classPrefix", "defaultProps", "color", "direction", "PageIndicator", "p", "props", "dots", "i", "total", "push", "createElement", "key", "className", "current"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/page-indicator/page-indicator.js"], "sourcesContent": ["import React, { memo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-page-indicator`;\nconst defaultProps = {\n  color: 'primary',\n  direction: 'horizontal'\n};\nexport const PageIndicator = memo(p => {\n  const props = mergeProps(defaultProps, p);\n  const dots = [];\n  for (let i = 0; i < props.total; i++) {\n    dots.push(React.createElement(\"div\", {\n      key: i,\n      className: classNames(`${classPrefix}-dot`, {\n        [`${classPrefix}-dot-active`]: props.current === i\n      })\n    }));\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, `${classPrefix}-${props.direction}`, `${classPrefix}-color-${props.color}`)\n  }, dots));\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,QAAQ,OAAO;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGR,IAAI,CAACS,CAAC,IAAI;EACrC,MAAMC,KAAK,GAAGP,UAAU,CAACE,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAME,IAAI,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,KAAK,EAAED,CAAC,EAAE,EAAE;IACpCD,IAAI,CAACG,IAAI,CAACf,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;MACnCC,GAAG,EAAEJ,CAAC;MACNK,SAAS,EAAEf,UAAU,CAAC,GAAGE,WAAW,MAAM,EAAE;QAC1C,CAAC,GAAGA,WAAW,aAAa,GAAGM,KAAK,CAACQ,OAAO,KAAKN;MACnD,CAAC;IACH,CAAC,CAAC,CAAC;EACL;EACA,OAAOX,eAAe,CAACS,KAAK,EAAEX,KAAK,CAACgB,aAAa,CAAC,KAAK,EAAE;IACvDE,SAAS,EAAEf,UAAU,CAACE,WAAW,EAAE,GAAGA,WAAW,IAAIM,KAAK,CAACH,SAAS,EAAE,EAAE,GAAGH,WAAW,UAAUM,KAAK,CAACJ,KAAK,EAAE;EAC/G,CAAC,EAAEK,IAAI,CAAC,CAAC;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}