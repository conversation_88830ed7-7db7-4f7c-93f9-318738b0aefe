{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON><PERSON> from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ModalActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-modal-button', {\n      'adm-modal-button-primary': props.action.primary\n    }),\n    fill: props.action.primary ? 'solid' : 'none',\n    size: props.action.primary ? 'large' : 'middle',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};", "map": {"version": 3, "names": ["React", "classNames", "<PERSON><PERSON>", "withNativeProps", "ModalActionButton", "props", "action", "createElement", "key", "onClick", "onAction", "className", "primary", "fill", "size", "block", "color", "danger", "loading", "disabled", "text"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/modal/modal-action-button.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON><PERSON> from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ModalActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-modal-button', {\n      'adm-modal-button-primary': props.action.primary\n    }),\n    fill: props.action.primary ? 'solid' : 'none',\n    size: props.action.primary ? 'large' : 'middle',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACxC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAOF,eAAe,CAACE,KAAK,CAACC,MAAM,EAAEN,KAAK,CAACO,aAAa,CAACL,MAAM,EAAE;IAC/DM,GAAG,EAAEF,MAAM,CAACE,GAAG;IACfC,OAAO,EAAEJ,KAAK,CAACK,QAAQ;IACvBC,SAAS,EAAEV,UAAU,CAAC,kBAAkB,EAAE;MACxC,0BAA0B,EAAEI,KAAK,CAACC,MAAM,CAACM;IAC3C,CAAC,CAAC;IACFC,IAAI,EAAER,KAAK,CAACC,MAAM,CAACM,OAAO,GAAG,OAAO,GAAG,MAAM;IAC7CE,IAAI,EAAET,KAAK,CAACC,MAAM,CAACM,OAAO,GAAG,OAAO,GAAG,QAAQ;IAC/CG,KAAK,EAAE,IAAI;IACXC,KAAK,EAAEV,MAAM,CAACW,MAAM,GAAG,QAAQ,GAAG,SAAS;IAC3CC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAEb,MAAM,CAACa;EACnB,CAAC,EAAEb,MAAM,CAACc,IAAI,CAAC,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}