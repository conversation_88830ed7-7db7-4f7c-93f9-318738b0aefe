{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useMap(initialValue) {\n  var getInitValue = function () {\n    return new Map(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    map = _a[0],\n    setMap = _a[1];\n  var set = function (key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n  var setAll = function (newMap) {\n    setMap(new Map(newMap));\n  };\n  var remove = function (key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setMap(getInitValue());\n  };\n  var get = function (key) {\n    return map.get(key);\n  };\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\nexport default useMap;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}