{"ast": null, "code": "import { useRef } from 'react';\nvar useRetryPlugin = function (fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n    retryCount = _a.retryCount;\n  var timerRef = useRef();\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n  if (!retryCount) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n      triggerByRetry.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onError: function () {\n      countRef.current += 1;\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      countRef.current = 0;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\nexport default useRetryPlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}