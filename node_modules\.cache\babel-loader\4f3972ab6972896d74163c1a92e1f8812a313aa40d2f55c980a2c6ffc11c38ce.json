{"ast": null, "code": "import * as React from \"react\";\nfunction ScanningOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningOutline-ScanningOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ScanningOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.6,32 C6.8209139,32 7,32.1790861 7,32.4 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L15.6,41 C15.8209139,41 16,41.1790861 16,41.4 L16,43.6 C16,43.8209139 15.8209139,44 15.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,32.4 C4,32.1790861 4.1790861,32 4.4,32 L6.6,32 Z M43.6,32 C43.8209139,32 44,32.1790861 44,32.4 L44,38 C44,41.3137085 41.3137085,44 38,44 L32.4,44 C32.1790861,44 32,43.8209139 32,43.6 L32,41.4 C32,41.1790861 32.1790861,41 32.4,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,32.4 C41,32.1790861 41.1790861,32 41.4,32 L43.6,32 Z M43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L4.4,26 C4.1790861,26 4,25.8209139 4,25.6 L4,23.4 C4,23.1790861 4.1790861,23 4.4,23 L43.6,23 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,15.6 C44,15.8209139 43.8209139,16 43.6,16 L41.4,16 C41.1790861,16 41,15.8209139 41,15.6 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L32.4,7 C32.1790861,7 32,6.8209139 32,6.6 L32,4.4 C32,4.1790861 32.1790861,4 32.4,4 L38,4 Z M15.6,4 C15.8209139,4 16,4.1790861 16,4.4 L16,6.6 C16,6.8209139 15.8209139,7 15.6,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,15.6 C7,15.8209139 6.8209139,16 6.6,16 L4.4,16 C4.1790861,16 4,15.8209139 4,15.6 L4,10 C4,6.6862915 6.6862915,4 10,4 L15.6,4 Z\",\n    id: \"ScanningOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ScanningOutline;", "map": {"version": 3, "names": ["React", "ScanningOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ScanningOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ScanningOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningOutline-ScanningOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ScanningOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ScanningOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.6,32 C6.8209139,32 7,32.1790861 7,32.4 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L15.6,41 C15.8209139,41 16,41.1790861 16,41.4 L16,43.6 C16,43.8209139 15.8209139,44 15.6,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,32.4 C4,32.1790861 4.1790861,32 4.4,32 L6.6,32 Z M43.6,32 C43.8209139,32 44,32.1790861 44,32.4 L44,38 C44,41.3137085 41.3137085,44 38,44 L32.4,44 C32.1790861,44 32,43.8209139 32,43.6 L32,41.4 C32,41.1790861 32.1790861,41 32.4,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,32.4 C41,32.1790861 41.1790861,32 41.4,32 L43.6,32 Z M43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,25.6 C44,25.8209139 43.8209139,26 43.6,26 L4.4,26 C4.1790861,26 4,25.8209139 4,25.6 L4,23.4 C4,23.1790861 4.1790861,23 4.4,23 L43.6,23 Z M38,4 C41.3137085,4 44,6.6862915 44,10 L44,15.6 C44,15.8209139 43.8209139,16 43.6,16 L41.4,16 C41.1790861,16 41,15.8209139 41,15.6 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 L32.4,7 C32.1790861,7 32,6.8209139 32,6.6 L32,4.4 C32,4.1790861 32.1790861,4 32.4,4 L38,4 Z M15.6,4 C15.8209139,4 16,4.1790861 16,4.4 L16,6.6 C16,6.8209139 15.8209139,7 15.6,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,15.6 C7,15.8209139 6.8209139,16 6.6,16 L4.4,16 C4.1790861,16 4,15.8209139 4,15.6 L4,10 C4,6.6862915 6.6862915,4 10,4 L15.6,4 Z\",\n    id: \"ScanningOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ScanningOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,y1CAAy1C;IAC51CR,EAAE,EAAE,0CAA0C;IAC9CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}