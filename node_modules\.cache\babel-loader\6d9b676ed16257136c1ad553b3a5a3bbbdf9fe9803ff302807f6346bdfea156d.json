{"ast": null, "code": "import * as React from \"react\";\nfunction FolderOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FolderOutline-FolderOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FolderOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FolderOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.1428572,8.80002768 L41.1428572,15.5697136 C42.7866652,15.9924635 44,17.4792138 44,19.2500124 L44,37.2999994 C44,40.4480207 41.441625,43 38.2857143,43 L9.71428596,43 C6.55837502,43 4,40.4480207 4,37.2999994 L4,14.5 C4,12.7675773 5.17477678,11.2539243 6.85665177,10.8198247 L6.85665177,8.79999837 C6.85665145,6.70131658 8.56223211,5 10.6661741,5 L37.3328257,5 C39.4372768,5 41.1428572,6.70134535 41.1428572,8.80002768 Z M6.85719895,14.5000149 L6.85719895,37.3000172 C6.85719895,38.8093041 8.03656812,40.0568249 9.54687888,40.1452774 L9.7143394,40.1500181 L38.285735,40.1500181 C39.798644,40.1500181 41.0493568,38.9737473 41.1381112,37.4672193 L41.1428731,37.3000192 L41.1428731,19.2500322 C41.1428731,18.7685297 40.7815557,18.3631083 40.3020482,18.3066961 L40.1904952,18.3000314 L29.632427,18.3000314 L24.1829243,13.7704458 C24.046666,13.6572006 23.8814135,13.5841577 23.705782,13.5595458 L23.5724487,13.5500457 L7.80960969,13.5500457 C7.32677544,13.5500457 6.92038752,13.9105774 6.86389651,14.3888943 L6.85719895,14.5000149 Z M30.7486029,15.4500313 L38.2857458,15.4500313 L38.2857458,8.80004549 C38.2857458,8.31854301 37.9244279,7.91312156 37.4449199,7.85670937 L37.3333667,7.85004469 L10.6667151,7.85004469 C10.1838803,7.85004469 9.77749195,8.21057641 9.72100088,8.68889329 L9.71433423,8.8000433 L9.71433423,10.700045 L25.0343341,10.700045 L30.7486029,15.4500313 Z\",\n    id: \"FolderOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FolderOutline;", "map": {"version": 3, "names": ["React", "FolderOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/FolderOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FolderOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FolderOutline-FolderOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FolderOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FolderOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M41.1428572,8.80002768 L41.1428572,15.5697136 C42.7866652,15.9924635 44,17.4792138 44,19.2500124 L44,37.2999994 C44,40.4480207 41.441625,43 38.2857143,43 L9.71428596,43 C6.55837502,43 4,40.4480207 4,37.2999994 L4,14.5 C4,12.7675773 5.17477678,11.2539243 6.85665177,10.8198247 L6.85665177,8.79999837 C6.85665145,6.70131658 8.56223211,5 10.6661741,5 L37.3328257,5 C39.4372768,5 41.1428572,6.70134535 41.1428572,8.80002768 Z M6.85719895,14.5000149 L6.85719895,37.3000172 C6.85719895,38.8093041 8.03656812,40.0568249 9.54687888,40.1452774 L9.7143394,40.1500181 L38.285735,40.1500181 C39.798644,40.1500181 41.0493568,38.9737473 41.1381112,37.4672193 L41.1428731,37.3000192 L41.1428731,19.2500322 C41.1428731,18.7685297 40.7815557,18.3631083 40.3020482,18.3066961 L40.1904952,18.3000314 L29.632427,18.3000314 L24.1829243,13.7704458 C24.046666,13.6572006 23.8814135,13.5841577 23.705782,13.5595458 L23.5724487,13.5500457 L7.80960969,13.5500457 C7.32677544,13.5500457 6.92038752,13.9105774 6.86389651,14.3888943 L6.85719895,14.5000149 Z M30.7486029,15.4500313 L38.2857458,15.4500313 L38.2857458,8.80004549 C38.2857458,8.31854301 37.9244279,7.91312156 37.4449199,7.85670937 L37.3333667,7.85004469 L10.6667151,7.85004469 C10.1838803,7.85004469 9.77749195,8.21057641 9.72100088,8.68889329 L9.71433423,8.8000433 L9.71433423,10.700045 L25.0343341,10.700045 L30.7486029,15.4500313 Z\",\n    id: \"FolderOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FolderOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,01CAA01C;IAC71CR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}