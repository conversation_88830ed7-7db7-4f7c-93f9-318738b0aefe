{"ast": null, "code": "import { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport { depsEqual } from './depsEqual';\nvar useDeepCompareEffectWithTarget = function (effect, deps, target) {\n  var ref = useRef();\n  var signalRef = useRef(0);\n  if (!depsEqual(deps, ref.current)) {\n    signalRef.current += 1;\n  }\n  ref.current = deps;\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\nexport default useDeepCompareEffectWithTarget;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}