{"ast": null, "code": "import { __read, __values } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction handleResize() {\n  var e_1, _a;\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) return;\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1.return)) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\nvar listening = false;\nfunction calculate() {\n  var e_2, _a;\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nfunction useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n  var _a = __read(useState(info), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    if (!isBrowser) return;\n    // In React 18's StrictMode, useEffect perform twice, resize listener is remove, so handleResize is never perform.\n    // https://github.com/alibaba/hooks/issues/1910\n    if (!listening) {\n      window.addEventListener('resize', handleResize);\n    }\n    var subscriber = function () {\n      setState(info);\n    };\n    subscribers.add(subscriber);\n    return function () {\n      subscribers.delete(subscriber);\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}\nexport default useResponsive;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}