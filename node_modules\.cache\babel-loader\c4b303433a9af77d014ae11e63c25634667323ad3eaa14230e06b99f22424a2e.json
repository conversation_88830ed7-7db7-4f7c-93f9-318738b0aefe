{"ast": null, "code": "import * as React from \"react\";\nfunction GiftOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GiftOutline-GiftOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GiftOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"GiftOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.35714432,38.3552973 L7.35714432,22.2434408 C5.67521035,21.8137701 4.5,20.3149571 4.5,18.5988506 L4.5,13.8949232 C4.5,11.8166032 6.20558112,10.131794 8.30952403,10.131794 L21.2961816,10.131794 L18.2323722,7.105298 C18.0835181,6.95842036 18.0833842,6.72015204 18.2320725,6.57310905 L19.7142792,5.1098976 C19.8629752,4.96336749 20.1036785,4.96336749 20.2523732,5.1098976 L24.0238034,8.83539647 L27.7952337,5.1098976 C27.9439298,4.96336746 28.184633,4.96336746 28.3333277,5.10989762 L29.8152346,6.572813 C29.9640887,6.71969064 29.9642227,6.95795896 29.8155343,7.10500195 L26.7504698,10.1308547 L39.6904761,10.1317956 C41.7944188,10.1317955 43.5,11.8166048 43.5,13.8949243 L43.5,18.5988517 C43.5,20.3144618 42.3252227,21.8134283 40.6433469,22.2433159 L40.6433469,38.3553047 C40.6433469,41.4727817 38.0849708,44 34.9290587,44 L13.0714145,44 C9.91552047,44 7.35714432,41.4727743 7.35714432,38.3552973 Z M10.5952327,22.3619794 L10.5952327,38.5119811 C10.5952327,40.0212666 11.6430835,40.9068069 13.1069265,40.9952592 L13.2692348,41 L22.5,41 L22.5,22.3619794 L10.5952327,22.3619794 Z M25.4523759,22.3619794 L25.4523759,41 L34.6666647,41 C36.141751,41 37.3611961,40.1857096 37.4477316,38.679183 L37.4523759,38.511983 L37.4523759,22.3619794 L25.4523759,22.3619794 Z M8.30951107,12.9541687 C7.82667604,12.9541687 7.42028746,13.3112023 7.36379637,13.7848782 L7.3571297,13.8949497 L7.3571297,18.5988772 C7.3571297,19.0757079 7.71844817,19.4771957 8.19795597,19.5330605 L8.30950915,19.5396605 L22.5,19.5396605 L22.5,13 L8.30951107,12.9541687 Z M25.4523759,12.9541401 L25.4523759,19.5396318 L39.6904631,19.5396318 C40.1732982,19.5396318 40.5796867,19.1825982 40.6361778,18.7089223 L40.6428445,18.5988507 L40.6428445,13.8949233 C40.6428445,13.4180927 40.2815265,13.0166049 39.8020182,12.9607401 L39.6904651,12.9541401 L25.4523759,12.9541401 Z\",\n    id: \"GiftOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default GiftOutline;", "map": {"version": 3, "names": ["React", "GiftOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/GiftOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction GiftOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GiftOutline-GiftOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"GiftOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"GiftOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.35714432,38.3552973 L7.35714432,22.2434408 C5.67521035,21.8137701 4.5,20.3149571 4.5,18.5988506 L4.5,13.8949232 C4.5,11.8166032 6.20558112,10.131794 8.30952403,10.131794 L21.2961816,10.131794 L18.2323722,7.105298 C18.0835181,6.95842036 18.0833842,6.72015204 18.2320725,6.57310905 L19.7142792,5.1098976 C19.8629752,4.96336749 20.1036785,4.96336749 20.2523732,5.1098976 L24.0238034,8.83539647 L27.7952337,5.1098976 C27.9439298,4.96336746 28.184633,4.96336746 28.3333277,5.10989762 L29.8152346,6.572813 C29.9640887,6.71969064 29.9642227,6.95795896 29.8155343,7.10500195 L26.7504698,10.1308547 L39.6904761,10.1317956 C41.7944188,10.1317955 43.5,11.8166048 43.5,13.8949243 L43.5,18.5988517 C43.5,20.3144618 42.3252227,21.8134283 40.6433469,22.2433159 L40.6433469,38.3553047 C40.6433469,41.4727817 38.0849708,44 34.9290587,44 L13.0714145,44 C9.91552047,44 7.35714432,41.4727743 7.35714432,38.3552973 Z M10.5952327,22.3619794 L10.5952327,38.5119811 C10.5952327,40.0212666 11.6430835,40.9068069 13.1069265,40.9952592 L13.2692348,41 L22.5,41 L22.5,22.3619794 L10.5952327,22.3619794 Z M25.4523759,22.3619794 L25.4523759,41 L34.6666647,41 C36.141751,41 37.3611961,40.1857096 37.4477316,38.679183 L37.4523759,38.511983 L37.4523759,22.3619794 L25.4523759,22.3619794 Z M8.30951107,12.9541687 C7.82667604,12.9541687 7.42028746,13.3112023 7.36379637,13.7848782 L7.3571297,13.8949497 L7.3571297,18.5988772 C7.3571297,19.0757079 7.71844817,19.4771957 8.19795597,19.5330605 L8.30950915,19.5396605 L22.5,19.5396605 L22.5,13 L8.30951107,12.9541687 Z M25.4523759,12.9541401 L25.4523759,19.5396318 L39.6904631,19.5396318 C40.1732982,19.5396318 40.5796867,19.1825982 40.6361778,18.7089223 L40.6428445,18.5988507 L40.6428445,13.8949233 C40.6428445,13.4180927 40.2815265,13.0166049 39.8020182,12.9607401 L39.6904651,12.9541401 L25.4523759,12.9541401 Z\",\n    id: \"GiftOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default GiftOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,syDAAsyD;IACzyDR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}