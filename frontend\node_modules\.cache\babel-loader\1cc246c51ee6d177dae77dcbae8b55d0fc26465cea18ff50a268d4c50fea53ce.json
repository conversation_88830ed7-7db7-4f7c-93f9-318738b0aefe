{"ast": null, "code": "var cachePromise = new Map();\nvar getCachePromise = function (cacheKey) {\n  return cachePromise.get(cacheKey);\n};\nvar setCachePromise = function (cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise);\n  // no use promise.finally for compatibility\n  promise.then(function (res) {\n    cachePromise.delete(cacheKey);\n    return res;\n  }).catch(function () {\n    cachePromise.delete(cacheKey);\n  });\n};\nexport { getCachePromise, setCachePromise };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}