{"ast": null, "code": "import { canUseDom } from './can-use-dom';\nconst defaultRoot = canUseDom ? window : undefined;\nconst overflowStylePatterns = ['scroll', 'auto', 'overlay'];\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.nodeType === ELEMENT_NODE_TYPE;\n}\nexport function getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    if (node === document.body) {\n      return root;\n    }\n    const {\n      overflowY\n    } = window.getComputedStyle(node);\n    if (overflowStylePatterns.includes(overflowY) && node.scrollHeight > node.clientHeight) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}