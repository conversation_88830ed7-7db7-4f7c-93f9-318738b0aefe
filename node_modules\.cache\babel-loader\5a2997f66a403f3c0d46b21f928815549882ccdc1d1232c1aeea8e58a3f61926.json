{"ast": null, "code": "import * as React from \"react\";\nfunction ChatCheckOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatCheckOutline-ChatCheckOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatCheckOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatCheckOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M33.7782857,18 C33.9991996,18 34.1782857,18.1790861 34.1782857,18.4 C34.1782857,18.5060895 34.1361407,18.6078336 34.0611225,18.6828487 L23.4645144,29.2790094 C22.878728,29.8647958 21.9289805,29.8647958 21.3431941,29.2790094 L21.2662305,29.1971084 C21.1568964,29.1333873 21.0538947,29.0546811 20.9602034,28.9609899 L15.682734,23.6828241 C15.5265346,23.526604 15.5265513,23.273338 15.6827713,23.1171386 C15.7577843,23.0421355 15.8595179,23 15.9655953,23 L19.0763284,23 C19.1824068,23 19.2841412,23.0421363 19.3591544,23.1171406 L22.3713687,26.129 L30.3838446,18.1171463 C30.4588583,18.0421385 30.5605951,18 30.6666763,18 L33.7782857,18 Z\",\n    id: \"ChatCheckOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default ChatCheckOutline;", "map": {"version": 3, "names": ["React", "ChatCheckOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/ChatCheckOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction ChatCheckOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatCheckOutline-ChatCheckOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"ChatCheckOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"ChatCheckOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24,5 C35.0454654,5 44,13.5065417 44,23.9999335 C44,34.4933254 35.0454654,43.000129 24,43.000129 C20.5361038,43.0086689 17.1256565,42.1499296 14.0827434,40.5027329 L9.12272737,42.6506288 C8.20180797,43.0490167 7.13075643,42.6289802 6.73046083,41.7124519 C6.57362597,41.3533578 6.53805635,40.953324 6.6290919,40.5723965 L7.89727419,35.2705244 C5.44727043,32.1174388 4,28.2188348 4,23.999959 C4,13.5065671 12.9545346,5 24,5 Z M24.0000207,7.7142478 C14.4327303,7.7142478 6.7272871,15.0337886 6.7272871,23.9999335 C6.7272871,27.3746822 7.816377,30.5920198 9.81910787,33.2999345 L10.0545624,33.6102666 L10.851834,34.636263 L9.70274404,39.4387082 L14.2191094,37.4844293 L15.3900175,38.1213809 C17.9854744,39.5319012 20.9309429,40.2855624 24.0000207,40.2855624 C33.5672971,40.2855624 41.2727403,32.9660501 41.2727403,23.9999051 C41.2727403,15.0337601 33.5672971,7.7142478 24.0000207,7.7142478 Z M33.7782857,18 C33.9991996,18 34.1782857,18.1790861 34.1782857,18.4 C34.1782857,18.5060895 34.1361407,18.6078336 34.0611225,18.6828487 L23.4645144,29.2790094 C22.878728,29.8647958 21.9289805,29.8647958 21.3431941,29.2790094 L21.2662305,29.1971084 C21.1568964,29.1333873 21.0538947,29.0546811 20.9602034,28.9609899 L15.682734,23.6828241 C15.5265346,23.526604 15.5265513,23.273338 15.6827713,23.1171386 C15.7577843,23.0421355 15.8595179,23 15.9655953,23 L19.0763284,23 C19.1824068,23 19.2841412,23.0421363 19.3591544,23.1171406 L22.3713687,26.129 L30.3838446,18.1171463 C30.4588583,18.0421385 30.5605951,18 30.6666763,18 L33.7782857,18 Z\",\n    id: \"ChatCheckOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default ChatCheckOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,mCAAmC;IACvCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,+BAA+B;IACnCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,q/CAAq/C;IACx/CR,EAAE,EAAE,2CAA2C;IAC/CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}