{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useSize } from 'ahooks';\nimport React, { useRef } from 'react';\nimport { bound } from '../../utils/bound';\nimport * as mat from '../../utils/matrix';\nimport { rubberbandIfOutOfBounds } from '../../utils/rubberband';\nimport { useDragAndPinch } from '../../utils/use-drag-and-pinch';\nconst classPrefix = `adm-image-viewer`;\nexport const Slide = props => {\n  const {\n    dragLockRef,\n    maxZoom,\n    imageRender,\n    index\n  } = props;\n  const initialMartix = useRef([]);\n  const controlRef = useRef(null);\n  const imgRef = useRef(null);\n  const [{\n    matrix\n  }, api] = useSpring(() => ({\n    matrix: mat.create(),\n    config: {\n      tension: 200\n    }\n  }));\n  const controlSize = useSize(controlRef);\n  const imgSize = useSize(imgRef);\n  const pinchLockRef = useRef(false);\n  /**\n   * Calculate the min and max value of x and y\n   */\n  const getMinAndMax = nextMatrix => {\n    if (!controlSize || !imgSize) return {\n      x: {\n        position: 0,\n        minX: 0,\n        maxX: 0\n      },\n      y: {\n        position: 0,\n        minY: 0,\n        maxY: 0\n      }\n    };\n    const controlLeft = -controlSize.width / 2;\n    const controlTop = -controlSize.height / 2;\n    const imgLeft = -imgSize.width / 2;\n    const imgTop = -imgSize.height / 2;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const minX = controlLeft - (scaledImgWidth - controlSize.width);\n    const maxX = controlLeft;\n    const minY = controlTop - (scaledImgHeight - controlSize.height);\n    const maxY = controlTop;\n    const [x, y] = mat.apply(nextMatrix, [imgLeft, imgTop]);\n    return {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    };\n  };\n  /**\n   * Check if is reach the bound\n   */\n  const getReachBound = (position, min, max, buffer = 0) => {\n    return [position <= min - buffer, position >= max + buffer];\n  };\n  /**\n   * Limit the matrix in the bound\n   */\n  const boundMatrix = (nextMatrix, type, last = false) => {\n    if (!controlSize || !imgSize) return nextMatrix;\n    const zoom = mat.getScaleX(nextMatrix);\n    const scaledImgWidth = zoom * imgSize.width;\n    const scaledImgHeight = zoom * imgSize.height;\n    const {\n      x: {\n        position: x,\n        minX,\n        maxX\n      },\n      y: {\n        position: y,\n        minY,\n        maxY\n      }\n    } = getMinAndMax(nextMatrix);\n    if (type === 'translate') {\n      let boundedX = x;\n      let boundedY = y;\n      if (scaledImgWidth > controlSize.width) {\n        boundedX = last ? bound(x, minX, maxX) : rubberbandIfOutOfBounds(x, minX, maxX, zoom * 50);\n      } else {\n        boundedX = -scaledImgWidth / 2;\n      }\n      if (scaledImgHeight > controlSize.height) {\n        boundedY = last ? bound(y, minY, maxY) : rubberbandIfOutOfBounds(y, minY, maxY, zoom * 50);\n      } else {\n        boundedY = -scaledImgHeight / 2;\n      }\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    if (type === 'scale' && last) {\n      const [boundedX, boundedY] = [scaledImgWidth > controlSize.width ? bound(x, minX, maxX) : -scaledImgWidth / 2, scaledImgHeight > controlSize.height ? bound(y, minY, maxY) : -scaledImgHeight / 2];\n      return mat.translate(nextMatrix, boundedX - x, boundedY - y);\n    }\n    return nextMatrix;\n  };\n  useDragAndPinch({\n    onDrag: state => {\n      var _a;\n      if (state.first) {\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(matrix.get());\n        initialMartix.current = getReachBound(x, minX, maxX);\n        return;\n      }\n      if (state.pinching) return state.cancel();\n      if (state.tap && state.elapsedTime > 0 && state.elapsedTime < 1000) {\n        // 判断点击时间>0是为了过滤掉非正常操作，例如用户长按选择图片之后的取消操作（也是一次点击）\n        (_a = props.onTap) === null || _a === void 0 ? void 0 : _a.call(props);\n        return;\n      }\n      const currentZoom = mat.getScaleX(matrix.get());\n      if (dragLockRef) {\n        dragLockRef.current = currentZoom !== 1;\n      }\n      if (!pinchLockRef.current && currentZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n      } else {\n        const currentMatrix = matrix.get();\n        const offset = [state.offset[0] - mat.getTranslateX(currentMatrix), state.offset[1] - mat.getTranslateY(currentMatrix)];\n        const nextMatrix = mat.translate(currentMatrix, ...(state.last ? [offset[0] + state.velocity[0] * state.direction[0] * 200, offset[1] + state.velocity[1] * state.direction[1] * 200] : offset));\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'translate', state.last),\n          immediate: !state.last\n        });\n        const {\n          x: {\n            position: x,\n            minX,\n            maxX\n          }\n        } = getMinAndMax(nextMatrix);\n        if (state.last && initialMartix.current.some(i => i) && getReachBound(x, minX, maxX).some(i => i)) {\n          if (dragLockRef) {\n            dragLockRef.current = false;\n          }\n          api.start({\n            matrix: mat.create()\n          });\n        }\n      }\n    },\n    onPinch: state => {\n      var _a;\n      pinchLockRef.current = !state.last;\n      const [d] = state.offset;\n      if (d < 0) return;\n      let mergedMaxZoom;\n      if (maxZoom === 'auto') {\n        mergedMaxZoom = controlSize && imgSize ? Math.max(controlSize.height / imgSize.height, controlSize.width / imgSize.width) : 1;\n      } else {\n        mergedMaxZoom = maxZoom;\n      }\n      const nextZoom = state.last ? bound(d, 1, mergedMaxZoom) : d;\n      (_a = props.onZoomChange) === null || _a === void 0 ? void 0 : _a.call(props, nextZoom);\n      if (state.last && nextZoom <= 1) {\n        api.start({\n          matrix: mat.create()\n        });\n        if (dragLockRef) {\n          dragLockRef.current = false;\n        }\n      } else {\n        if (!controlSize) return;\n        const currentMatrix = matrix.get();\n        const currentZoom = mat.getScaleX(currentMatrix);\n        const originOffsetX = state.origin[0] - controlSize.width / 2;\n        const originOffsetY = state.origin[1] - controlSize.height / 2;\n        let nextMatrix = mat.translate(currentMatrix, -originOffsetX, -originOffsetY);\n        nextMatrix = mat.scale(nextMatrix, nextZoom / currentZoom);\n        nextMatrix = mat.translate(nextMatrix, originOffsetX, originOffsetY);\n        api.start({\n          matrix: boundMatrix(nextMatrix, 'scale', state.last),\n          immediate: !state.last\n        });\n        if (dragLockRef) {\n          dragLockRef.current = true;\n        }\n      }\n    }\n  }, {\n    target: controlRef,\n    drag: {\n      from: () => [mat.getTranslateX(matrix.get()), mat.getTranslateY(matrix.get())],\n      pointer: {\n        touch: true\n      }\n    },\n    pinch: {\n      from: () => [mat.getScaleX(matrix.get()), 0],\n      pointer: {\n        touch: true\n      }\n    }\n  });\n  const customRendering = typeof imageRender === 'function' && imageRender(props.image, {\n    index\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-slide`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-control`,\n    ref: controlRef\n  }, React.createElement(animated.div, {\n    className: `${classPrefix}-image-wrapper`,\n    style: {\n      matrix\n    }\n  }, customRendering ? customRendering : React.createElement(\"img\", {\n    ref: imgRef,\n    src: props.image,\n    draggable: false,\n    alt: props.image\n  }))));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}