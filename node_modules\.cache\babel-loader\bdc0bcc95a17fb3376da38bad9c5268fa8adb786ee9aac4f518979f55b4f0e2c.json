{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useDrag } from '@use-gesture/react';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { nearest } from '../../utils/nearest';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withStopPropagation } from '../../utils/with-stop-propagation';\nimport Button from '../button';\nconst classPrefix = `adm-swipe-action`;\nconst defaultProps = {\n  rightActions: [],\n  leftActions: [],\n  closeOnTouchOutside: true,\n  closeOnAction: true,\n  stopPropagation: []\n};\nexport const SwipeAction = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const rootRef = useRef(null);\n  const leftRef = useRef(null);\n  const rightRef = useRef(null);\n  function getWidth(ref) {\n    const element = ref.current;\n    if (!element) return 0;\n    return element.offsetWidth;\n  }\n  function getLeftWidth() {\n    return getWidth(leftRef);\n  }\n  function getRightWidth() {\n    return getWidth(rightRef);\n  }\n  const [{\n    x\n  }, api] = useSpring(() => ({\n    x: 0,\n    config: {\n      tension: 200,\n      friction: 30\n    }\n  }), []);\n  const draggingRef = useRef(false);\n  const dragCancelRef = useRef(null);\n  function forceCancelDrag() {\n    var _a;\n    (_a = dragCancelRef.current) === null || _a === void 0 ? void 0 : _a.call(dragCancelRef);\n    draggingRef.current = false;\n  }\n  const bind = useDrag(state => {\n    var _a;\n    dragCancelRef.current = state.cancel;\n    if (!state.intentional) return;\n    if (state.down) {\n      draggingRef.current = true;\n    }\n    if (!draggingRef.current) return;\n    const [offsetX] = state.offset;\n    if (state.last) {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      let position = offsetX + state.velocity[0] * state.direction[0] * 50;\n      if (offsetX > 0) {\n        position = Math.max(0, position);\n      } else if (offsetX < 0) {\n        position = Math.min(0, position);\n      } else {\n        position = 0;\n      }\n      const targetX = nearest([-rightWidth, 0, leftWidth], position);\n      api.start({\n        x: targetX\n      });\n      if (targetX !== 0) {\n        (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, targetX > 0 ? 'left' : 'right');\n      }\n      window.setTimeout(() => {\n        draggingRef.current = false;\n      });\n    } else {\n      api.start({\n        x: offsetX,\n        immediate: true\n      });\n    }\n  }, {\n    from: () => [x.get(), 0],\n    bounds: () => {\n      const leftWidth = getLeftWidth();\n      const rightWidth = getRightWidth();\n      return {\n        left: -rightWidth,\n        right: leftWidth\n      };\n    },\n    axis: 'x',\n    preventScroll: true,\n    pointer: {\n      touch: true\n    },\n    triggerAllEvents: true\n  });\n  const close = () => {\n    var _a;\n    api.start({\n      x: 0\n    });\n    forceCancelDrag();\n    (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n  };\n  useImperativeHandle(ref, () => ({\n    show: (side = 'right') => {\n      var _a;\n      if (side === 'right') {\n        api.start({\n          x: -getRightWidth()\n        });\n      } else if (side === 'left') {\n        api.start({\n          x: getLeftWidth()\n        });\n      }\n      (_a = p.onActionsReveal) === null || _a === void 0 ? void 0 : _a.call(p, side);\n    },\n    close\n  }));\n  useEffect(() => {\n    if (!props.closeOnTouchOutside) return;\n    function handle(e) {\n      if (x.get() === 0) {\n        return;\n      }\n      const root = rootRef.current;\n      if (root && !root.contains(e.target)) {\n        close();\n      }\n    }\n    document.addEventListener('touchstart', handle);\n    return () => {\n      document.removeEventListener('touchstart', handle);\n    };\n  }, [props.closeOnTouchOutside]);\n  function renderAction(action) {\n    var _a, _b;\n    const color = (_a = action.color) !== null && _a !== void 0 ? _a : 'light';\n    return React.createElement(Button, {\n      key: action.key,\n      className: `${classPrefix}-action-button`,\n      style: {\n        '--background-color': (_b = colorRecord[color]) !== null && _b !== void 0 ? _b : color\n      },\n      onClick: e => {\n        var _a, _b;\n        if (props.closeOnAction) {\n          close();\n        }\n        (_a = action.onClick) === null || _a === void 0 ? void 0 : _a.call(action, e);\n        (_b = props.onAction) === null || _b === void 0 ? void 0 : _b.call(props, action, e);\n      }\n    }, action.text);\n  }\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classPrefix\n  }, bind(), {\n    ref: rootRef,\n    onClickCapture: e => {\n      if (draggingRef.current) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }\n  }), React.createElement(animated.div, {\n    className: `${classPrefix}-track`,\n    style: {\n      x\n    }\n  }, withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-left`,\n    ref: leftRef\n  }, props.leftActions.map(renderAction))), React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    onClickCapture: e => {\n      if (x.goal !== 0) {\n        e.preventDefault();\n        e.stopPropagation();\n        close();\n      }\n    }\n  }, React.createElement(animated.div, {\n    style: {\n      pointerEvents: x.to(v => v !== 0 && x.goal !== 0 ? 'none' : 'auto')\n    }\n  }, props.children)), withStopPropagation(props.stopPropagation, React.createElement(\"div\", {\n    className: `${classPrefix}-actions ${classPrefix}-actions-right`,\n    ref: rightRef\n  }, props.rightActions.map(renderAction))))));\n});\nconst colorRecord = {\n  light: 'var(--adm-color-light)',\n  weak: 'var(--adm-color-weak)',\n  primary: 'var(--adm-color-primary)',\n  success: 'var(--adm-color-success)',\n  warning: 'var(--adm-color-warning)',\n  danger: 'var(--adm-color-danger)'\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}