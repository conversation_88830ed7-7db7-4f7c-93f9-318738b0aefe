{"ast": null, "code": "import { mergeProps } from '../../utils/with-default-props';\nimport React, { useState, useRef, useEffect } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { staged } from 'staged-components';\nimport { toCSSLength } from '../../utils/to-css-length';\nimport { LazyDetector } from './lazy-detector';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { ImageIcon } from './image-icon';\nimport { BrokenImageIcon } from './broken-image-icon';\nconst classPrefix = `adm-image`;\nconst defaultProps = {\n  fit: 'fill',\n  placeholder: React.createElement(\"div\", {\n    className: `${classPrefix}-tip`\n  }, React.createElement(ImageIcon, null)),\n  fallback: React.createElement(\"div\", {\n    className: `${classPrefix}-tip`\n  }, React.createElement(BrokenImageIcon, null)),\n  lazy: false,\n  draggable: false\n};\nexport const Image = staged(p => {\n  const props = mergeProps(defaultProps, p);\n  const [loaded, setLoaded] = useState(false);\n  const [failed, setFailed] = useState(false);\n  const ref = useRef(null);\n  const imgRef = useRef(null);\n  let src = props.src;\n  let srcSet = props.srcSet;\n  const [initialized, setInitialized] = useState(!props.lazy);\n  src = initialized ? props.src : undefined;\n  srcSet = initialized ? props.srcSet : undefined;\n  useIsomorphicUpdateLayoutEffect(() => {\n    setLoaded(false);\n    setFailed(false);\n  }, [src]);\n  useEffect(() => {\n    var _a;\n    // for nextjs ssr\n    if ((_a = imgRef.current) === null || _a === void 0 ? void 0 : _a.complete) {\n      setLoaded(true);\n    }\n  }, []);\n  function renderInner() {\n    if (failed) {\n      return React.createElement(React.Fragment, null, props.fallback);\n    }\n    const img = React.createElement(\"img\", {\n      ref: imgRef,\n      id: props.id,\n      className: `${classPrefix}-img`,\n      src: src,\n      alt: props.alt,\n      onClick: props.onClick,\n      onLoad: e => {\n        var _a;\n        setLoaded(true);\n        (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      },\n      onError: e => {\n        var _a;\n        setFailed(true);\n        (_a = props.onError) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      },\n      style: {\n        objectFit: props.fit,\n        display: loaded ? 'block' : 'none'\n      },\n      crossOrigin: props.crossOrigin,\n      decoding: props.decoding,\n      loading: props.loading,\n      referrerPolicy: props.referrerPolicy,\n      sizes: props.sizes,\n      srcSet: srcSet,\n      useMap: props.useMap,\n      draggable: props.draggable\n    });\n    return React.createElement(React.Fragment, null, !loaded && props.placeholder, img);\n  }\n  const style = {};\n  if (props.width) {\n    style['--width'] = toCSSLength(props.width);\n    style['width'] = toCSSLength(props.width);\n  }\n  if (props.height) {\n    style['--height'] = toCSSLength(props.height);\n    style['height'] = toCSSLength(props.height);\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    ref: ref,\n    className: classPrefix,\n    style: style,\n    onClick: props.onContainerClick\n  }, props.lazy && !initialized && React.createElement(LazyDetector, {\n    onActive: () => {\n      setInitialized(true);\n    }\n  }), renderInner()));\n});", "map": {"version": 3, "names": ["mergeProps", "React", "useState", "useRef", "useEffect", "withNativeProps", "staged", "toCS<PERSON><PERSON>th", "LazyDetector", "useIsomorphicUpdateLayoutEffect", "ImageIcon", "BrokenImageIcon", "classPrefix", "defaultProps", "fit", "placeholder", "createElement", "className", "fallback", "lazy", "draggable", "Image", "p", "props", "loaded", "setLoaded", "failed", "setFailed", "ref", "imgRef", "src", "srcSet", "initialized", "setInitialized", "undefined", "_a", "current", "complete", "renderInner", "Fragment", "img", "id", "alt", "onClick", "onLoad", "e", "call", "onError", "style", "objectFit", "display", "crossOrigin", "decoding", "loading", "referrerPolicy", "sizes", "useMap", "width", "height", "onContainerClick", "onActive"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/image/image.js"], "sourcesContent": ["import { mergeProps } from '../../utils/with-default-props';\nimport React, { useState, useRef, useEffect } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { staged } from 'staged-components';\nimport { toCSSLength } from '../../utils/to-css-length';\nimport { LazyDetector } from './lazy-detector';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { ImageIcon } from './image-icon';\nimport { BrokenImageIcon } from './broken-image-icon';\nconst classPrefix = `adm-image`;\nconst defaultProps = {\n  fit: 'fill',\n  placeholder: React.createElement(\"div\", {\n    className: `${classPrefix}-tip`\n  }, React.createElement(ImageIcon, null)),\n  fallback: React.createElement(\"div\", {\n    className: `${classPrefix}-tip`\n  }, React.createElement(BrokenImageIcon, null)),\n  lazy: false,\n  draggable: false\n};\nexport const Image = staged(p => {\n  const props = mergeProps(defaultProps, p);\n  const [loaded, setLoaded] = useState(false);\n  const [failed, setFailed] = useState(false);\n  const ref = useRef(null);\n  const imgRef = useRef(null);\n  let src = props.src;\n  let srcSet = props.srcSet;\n  const [initialized, setInitialized] = useState(!props.lazy);\n  src = initialized ? props.src : undefined;\n  srcSet = initialized ? props.srcSet : undefined;\n  useIsomorphicUpdateLayoutEffect(() => {\n    setLoaded(false);\n    setFailed(false);\n  }, [src]);\n  useEffect(() => {\n    var _a;\n    // for nextjs ssr\n    if ((_a = imgRef.current) === null || _a === void 0 ? void 0 : _a.complete) {\n      setLoaded(true);\n    }\n  }, []);\n  function renderInner() {\n    if (failed) {\n      return React.createElement(React.Fragment, null, props.fallback);\n    }\n    const img = React.createElement(\"img\", {\n      ref: imgRef,\n      id: props.id,\n      className: `${classPrefix}-img`,\n      src: src,\n      alt: props.alt,\n      onClick: props.onClick,\n      onLoad: e => {\n        var _a;\n        setLoaded(true);\n        (_a = props.onLoad) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      },\n      onError: e => {\n        var _a;\n        setFailed(true);\n        (_a = props.onError) === null || _a === void 0 ? void 0 : _a.call(props, e);\n      },\n      style: {\n        objectFit: props.fit,\n        display: loaded ? 'block' : 'none'\n      },\n      crossOrigin: props.crossOrigin,\n      decoding: props.decoding,\n      loading: props.loading,\n      referrerPolicy: props.referrerPolicy,\n      sizes: props.sizes,\n      srcSet: srcSet,\n      useMap: props.useMap,\n      draggable: props.draggable\n    });\n    return React.createElement(React.Fragment, null, !loaded && props.placeholder, img);\n  }\n  const style = {};\n  if (props.width) {\n    style['--width'] = toCSSLength(props.width);\n    style['width'] = toCSSLength(props.width);\n  }\n  if (props.height) {\n    style['--height'] = toCSSLength(props.height);\n    style['height'] = toCSSLength(props.height);\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    ref: ref,\n    className: classPrefix,\n    style: style,\n    onClick: props.onContainerClick\n  }, props.lazy && !initialized && React.createElement(LazyDetector, {\n    onActive: () => {\n      setInitialized(true);\n    }\n  }), renderInner()));\n});"], "mappings": "AAAA,SAASA,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,+BAA+B,QAAQ,iDAAiD;AACjG,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,eAAe,QAAQ,qBAAqB;AACrD,MAAMC,WAAW,GAAG,WAAW;AAC/B,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAE,MAAM;EACXC,WAAW,EAAEd,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAE,GAAGL,WAAW;EAC3B,CAAC,EAAEX,KAAK,CAACe,aAAa,CAACN,SAAS,EAAE,IAAI,CAAC,CAAC;EACxCQ,QAAQ,EAAEjB,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACnCC,SAAS,EAAE,GAAGL,WAAW;EAC3B,CAAC,EAAEX,KAAK,CAACe,aAAa,CAACL,eAAe,EAAE,IAAI,CAAC,CAAC;EAC9CQ,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE;AACb,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGf,MAAM,CAACgB,CAAC,IAAI;EAC/B,MAAMC,KAAK,GAAGvB,UAAU,CAACa,YAAY,EAAES,CAAC,CAAC;EACzC,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM0B,GAAG,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACxB,MAAM0B,MAAM,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAI2B,GAAG,GAAGP,KAAK,CAACO,GAAG;EACnB,IAAIC,MAAM,GAAGR,KAAK,CAACQ,MAAM;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAACqB,KAAK,CAACJ,IAAI,CAAC;EAC3DW,GAAG,GAAGE,WAAW,GAAGT,KAAK,CAACO,GAAG,GAAGI,SAAS;EACzCH,MAAM,GAAGC,WAAW,GAAGT,KAAK,CAACQ,MAAM,GAAGG,SAAS;EAC/CzB,+BAA+B,CAAC,MAAM;IACpCgB,SAAS,CAAC,KAAK,CAAC;IAChBE,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,EAAE,CAACG,GAAG,CAAC,CAAC;EACT1B,SAAS,CAAC,MAAM;IACd,IAAI+B,EAAE;IACN;IACA,IAAI,CAACA,EAAE,GAAGN,MAAM,CAACO,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,EAAE;MAC1EZ,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,SAASa,WAAWA,CAAA,EAAG;IACrB,IAAIZ,MAAM,EAAE;MACV,OAAOzB,KAAK,CAACe,aAAa,CAACf,KAAK,CAACsC,QAAQ,EAAE,IAAI,EAAEhB,KAAK,CAACL,QAAQ,CAAC;IAClE;IACA,MAAMsB,GAAG,GAAGvC,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;MACrCY,GAAG,EAAEC,MAAM;MACXY,EAAE,EAAElB,KAAK,CAACkB,EAAE;MACZxB,SAAS,EAAE,GAAGL,WAAW,MAAM;MAC/BkB,GAAG,EAAEA,GAAG;MACRY,GAAG,EAAEnB,KAAK,CAACmB,GAAG;MACdC,OAAO,EAAEpB,KAAK,CAACoB,OAAO;MACtBC,MAAM,EAAEC,CAAC,IAAI;QACX,IAAIV,EAAE;QACNV,SAAS,CAAC,IAAI,CAAC;QACf,CAACU,EAAE,GAAGZ,KAAK,CAACqB,MAAM,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,IAAI,CAACvB,KAAK,EAAEsB,CAAC,CAAC;MAC5E,CAAC;MACDE,OAAO,EAAEF,CAAC,IAAI;QACZ,IAAIV,EAAE;QACNR,SAAS,CAAC,IAAI,CAAC;QACf,CAACQ,EAAE,GAAGZ,KAAK,CAACwB,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,IAAI,CAACvB,KAAK,EAAEsB,CAAC,CAAC;MAC7E,CAAC;MACDG,KAAK,EAAE;QACLC,SAAS,EAAE1B,KAAK,CAACT,GAAG;QACpBoC,OAAO,EAAE1B,MAAM,GAAG,OAAO,GAAG;MAC9B,CAAC;MACD2B,WAAW,EAAE5B,KAAK,CAAC4B,WAAW;MAC9BC,QAAQ,EAAE7B,KAAK,CAAC6B,QAAQ;MACxBC,OAAO,EAAE9B,KAAK,CAAC8B,OAAO;MACtBC,cAAc,EAAE/B,KAAK,CAAC+B,cAAc;MACpCC,KAAK,EAAEhC,KAAK,CAACgC,KAAK;MAClBxB,MAAM,EAAEA,MAAM;MACdyB,MAAM,EAAEjC,KAAK,CAACiC,MAAM;MACpBpC,SAAS,EAAEG,KAAK,CAACH;IACnB,CAAC,CAAC;IACF,OAAOnB,KAAK,CAACe,aAAa,CAACf,KAAK,CAACsC,QAAQ,EAAE,IAAI,EAAE,CAACf,MAAM,IAAID,KAAK,CAACR,WAAW,EAAEyB,GAAG,CAAC;EACrF;EACA,MAAMQ,KAAK,GAAG,CAAC,CAAC;EAChB,IAAIzB,KAAK,CAACkC,KAAK,EAAE;IACfT,KAAK,CAAC,SAAS,CAAC,GAAGzC,WAAW,CAACgB,KAAK,CAACkC,KAAK,CAAC;IAC3CT,KAAK,CAAC,OAAO,CAAC,GAAGzC,WAAW,CAACgB,KAAK,CAACkC,KAAK,CAAC;EAC3C;EACA,IAAIlC,KAAK,CAACmC,MAAM,EAAE;IAChBV,KAAK,CAAC,UAAU,CAAC,GAAGzC,WAAW,CAACgB,KAAK,CAACmC,MAAM,CAAC;IAC7CV,KAAK,CAAC,QAAQ,CAAC,GAAGzC,WAAW,CAACgB,KAAK,CAACmC,MAAM,CAAC;EAC7C;EACA,OAAOrD,eAAe,CAACkB,KAAK,EAAEtB,KAAK,CAACe,aAAa,CAAC,KAAK,EAAE;IACvDY,GAAG,EAAEA,GAAG;IACRX,SAAS,EAAEL,WAAW;IACtBoC,KAAK,EAAEA,KAAK;IACZL,OAAO,EAAEpB,KAAK,CAACoC;EACjB,CAAC,EAAEpC,KAAK,CAACJ,IAAI,IAAI,CAACa,WAAW,IAAI/B,KAAK,CAACe,aAAa,CAACR,YAAY,EAAE;IACjEoD,QAAQ,EAAEA,CAAA,KAAM;MACd3B,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,CAAC,EAAEK,WAAW,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}