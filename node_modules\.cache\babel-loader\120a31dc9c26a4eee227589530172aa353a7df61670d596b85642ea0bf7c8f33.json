{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\nimport classNames from 'classnames';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { fillRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport { Context } from \"./context\";\nimport DomWrapper from \"./DomWrapper\";\nimport useStatus from \"./hooks/useStatus\";\nimport { isActive } from \"./hooks/useStepQueue\";\nimport { STATUS_NONE, STEP_PREPARE, STEP_START } from \"./interface\";\nimport { getTransitionName, supportTransition } from \"./util/motion\";\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nexport function genCSSMotion(config) {\n  var transitionSupport = config;\n  if (_typeof(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = React.useContext(Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = useRef();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = useRef();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : findDOMNode(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = useStatus(supportMotion, visible, getDomElement, props),\n      _useStatus2 = _slicedToArray(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = React.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = React.useCallback(function (node) {\n      nodeRef.current = node;\n      fillRef(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = _objectSpread(_objectSpread({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children(_objectSpread({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if (isActive(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = getTransitionName(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children(_objectSpread(_objectSpread({}, mergedProps), {}, {\n        className: classNames(getTransitionName(motionName, status), _defineProperty(_defineProperty({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if (/*#__PURE__*/React.isValidElement(motionChildren) && supportRef(motionChildren)) {\n      var originNodeRef = getNodeRef(motionChildren);\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/React.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/React.createElement(DomWrapper, {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\nexport default genCSSMotion(supportTransition);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}