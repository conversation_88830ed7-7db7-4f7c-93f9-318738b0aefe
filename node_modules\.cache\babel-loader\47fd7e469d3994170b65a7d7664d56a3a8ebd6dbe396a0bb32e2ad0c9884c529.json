{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport * as ReactDOM from 'react-dom';\n// Let compiler not to search module usage\nconst fullClone = Object.assign({}, ReactDOM);\nconst {\n  version,\n  render: reactRender,\n  unmountComponentAtNode\n} = fullClone;\nlet createRoot;\ntry {\n  const mainVersion = Number((version || '').split('.')[0]);\n  if (mainVersion >= 18 && fullClone.createRoot) {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    createRoot = fullClone.createRoot;\n  }\n} catch (e) {\n  // Do nothing;\n}\nfunction toggleWarning(skip) {\n  const {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n  } = fullClone;\n  if (__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && typeof __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === 'object') {\n    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = skip;\n  }\n}\nconst MARK = '__antd_mobile_root__';\nfunction legacyRender(node, container) {\n  reactRender(node, container);\n}\nfunction concurrentRender(node, container) {\n  toggleWarning(true);\n  const root = container[MARK] || createRoot(container);\n  toggleWarning(false);\n  root.render(node);\n  container[MARK] = root;\n}\nexport function render(node, container) {\n  if (createRoot) {\n    concurrentRender(node, container);\n    return;\n  }\n  legacyRender(node, container);\n}\n// ========================== Unmount =========================\nfunction legacyUnmount(container) {\n  return unmountComponentAtNode(container);\n}\nfunction concurrentUnmount(container) {\n  return __awaiter(this, void 0, void 0, function* () {\n    // Delay to unmount to avoid React 18 sync warning\n    return Promise.resolve().then(() => {\n      var _a;\n      (_a = container[MARK]) === null || _a === void 0 ? void 0 : _a.unmount();\n      delete container[MARK];\n    });\n  });\n}\nexport function unmount(container) {\n  if (createRoot) {\n    return concurrentUnmount(container);\n  }\n  return legacyUnmount(container);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}