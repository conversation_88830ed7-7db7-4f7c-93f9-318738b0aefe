{"ast": null, "code": "import { useMemoizedFn } from 'ahooks';\nimport { useEffect } from 'react';\nexport function observe(element, options, callback) {\n  if (element && typeof MutationObserver !== 'undefined') {\n    let observer = new MutationObserver(() => {\n      callback();\n    });\n    observer.observe(element, options);\n    // Return cleanup function\n    return () => {\n      if (observer) {\n        observer.disconnect();\n        observer = null;\n      }\n    };\n  }\n  return () => {};\n}\nexport function useMutationEffect(effect, targetRef, options) {\n  const fn = useMemoizedFn(effect);\n  useEffect(() => {\n    const cleanup = observe(targetRef.current, options, fn);\n    return cleanup;\n  }, [targetRef]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}