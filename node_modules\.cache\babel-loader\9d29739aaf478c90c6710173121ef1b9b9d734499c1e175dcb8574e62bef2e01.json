{"ast": null, "code": "export function attachPropertiesToComponent(component, properties) {\n  const ret = component;\n  for (const key in properties) {\n    if (properties.hasOwnProperty(key)) {\n      ret[key] = properties[key];\n    }\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["attachPropertiesToComponent", "component", "properties", "ret", "key", "hasOwnProperty"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/attach-properties-to-component.js"], "sourcesContent": ["export function attachPropertiesToComponent(component, properties) {\n  const ret = component;\n  for (const key in properties) {\n    if (properties.hasOwnProperty(key)) {\n      ret[key] = properties[key];\n    }\n  }\n  return ret;\n}"], "mappings": "AAAA,OAAO,SAASA,2BAA2BA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACjE,MAAMC,GAAG,GAAGF,SAAS;EACrB,KAAK,MAAMG,GAAG,IAAIF,UAAU,EAAE;IAC5B,IAAIA,UAAU,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MAClCD,GAAG,CAACC,GAAG,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;IAC5B;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}