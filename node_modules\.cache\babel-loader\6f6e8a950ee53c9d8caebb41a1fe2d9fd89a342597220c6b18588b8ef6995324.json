{"ast": null, "code": "import * as React from \"react\";\nfunction VideoOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"VideoOutline-VideoOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"VideoOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"VideoOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M31.4166854,7 C34.2296701,7 36.5809622,9.13001776 36.8845657,11.9243836 L41.6671403,11.3912215 C43.3137645,11.2073882 44.7976435,12.3932145 44.9814769,14.0398387 C44.9938161,14.1503628 45,14.2614876 45,14.3726984 L45,34.7947111 C45,36.4515654 43.6568542,37.7947111 42,37.7947111 C41.8887892,37.7947111 41.7776644,37.7885272 41.6671403,37.776188 L36.8638765,37.2394434 C36.4880108,39.9526752 34.1742642,42 31.4166857,42 L8.5,42 C5.4624388,42 3,39.525783 3,36.4736864 L3,12.5263128 C3,9.47421697 5.46243451,7 8.5,7 L31.4166854,7 Z M31,10.0143494 L9,10.0143494 C7.40231912,10.0143494 6.09633912,11.2632694 6.00509269,12.8380766 L6,13.0143494 L6,36.1530602 C6,37.7507411 7.24891996,39.0567211 8.82372721,39.1479675 L9,39.1530602 L31,39.1530602 C32.5976809,39.1530602 33.9036609,37.9041402 33.9949073,36.329333 L34,36.1530602 L34,13.0143494 C34,11.3574951 32.6568542,10.0143494 31,10.0143494 Z M40.9509667,14.2159785 L40.8348361,14.2286048 L36.898,14.888 L36.897,34.279 L40.8348361,34.9388048 C40.8894159,34.947945 40.9446602,34.9525387 41,34.9525387 C41.5128358,34.9525387 41.9355072,34.5664985 41.9932723,34.0691598 L42,33.9525387 L42,15.2148709 C42,15.1595311 41.9954063,15.1042867 41.9862661,15.049707 C41.9015641,14.5439143 41.4510157,14.1908078 40.9509667,14.2159785 Z M29.5,13.0286988 C30.3284266,13.0286988 31,13.7034844 31,14.5358735 C31,15.3682625 30.3284266,16.0430482 29.5,16.0430482 C28.6715734,16.0430482 28,15.3682625 28,14.5358735 C28,13.7034844 28.6715734,13.0286988 29.5,13.0286988 Z\",\n    id: \"VideoOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default VideoOutline;", "map": {"version": 3, "names": ["React", "VideoOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/VideoOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction VideoOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"VideoOutline-VideoOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"VideoOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"VideoOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M31.4166854,7 C34.2296701,7 36.5809622,9.13001776 36.8845657,11.9243836 L41.6671403,11.3912215 C43.3137645,11.2073882 44.7976435,12.3932145 44.9814769,14.0398387 C44.9938161,14.1503628 45,14.2614876 45,14.3726984 L45,34.7947111 C45,36.4515654 43.6568542,37.7947111 42,37.7947111 C41.8887892,37.7947111 41.7776644,37.7885272 41.6671403,37.776188 L36.8638765,37.2394434 C36.4880108,39.9526752 34.1742642,42 31.4166857,42 L8.5,42 C5.4624388,42 3,39.525783 3,36.4736864 L3,12.5263128 C3,9.47421697 5.46243451,7 8.5,7 L31.4166854,7 Z M31,10.0143494 L9,10.0143494 C7.40231912,10.0143494 6.09633912,11.2632694 6.00509269,12.8380766 L6,13.0143494 L6,36.1530602 C6,37.7507411 7.24891996,39.0567211 8.82372721,39.1479675 L9,39.1530602 L31,39.1530602 C32.5976809,39.1530602 33.9036609,37.9041402 33.9949073,36.329333 L34,36.1530602 L34,13.0143494 C34,11.3574951 32.6568542,10.0143494 31,10.0143494 Z M40.9509667,14.2159785 L40.8348361,14.2286048 L36.898,14.888 L36.897,34.279 L40.8348361,34.9388048 C40.8894159,34.947945 40.9446602,34.9525387 41,34.9525387 C41.5128358,34.9525387 41.9355072,34.5664985 41.9932723,34.0691598 L42,33.9525387 L42,15.2148709 C42,15.1595311 41.9954063,15.1042867 41.9862661,15.049707 C41.9015641,14.5439143 41.4510157,14.1908078 40.9509667,14.2159785 Z M29.5,13.0286988 C30.3284266,13.0286988 31,13.7034844 31,14.5358735 C31,15.3682625 30.3284266,16.0430482 29.5,16.0430482 C28.6715734,16.0430482 28,15.3682625 28,14.5358735 C28,13.7034844 28.6715734,13.0286988 29.5,13.0286988 Z\",\n    id: \"VideoOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default VideoOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,09CAA09C;IAC79CR,EAAE,EAAE,uCAAuC;IAC3CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}