{"ast": null, "code": "import * as React from \"react\";\nfunction UnorderedListOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnorderedListOutline-UnorderedListOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnorderedListOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UnorderedListOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.5,32 C10.9852814,32 13,34.0147186 13,36.5 C13,38.9852814 10.9852814,41 8.5,41 C6.01471863,41 4,38.9852814 4,36.5 C4,34.0147186 6.01471863,32 8.5,32 Z M8.5,35 C7.67157288,35 7,35.6715729 7,36.5 C7,37.3284271 7.67157288,38 8.5,38 C9.32842712,38 10,37.3284271 10,36.5 C10,35.6715729 9.32842712,35 8.5,35 Z M43.6,35 C43.8209139,35 44,35.1790861 44,35.4 L44,37.6 C44,37.8209139 43.8209139,38 43.6,38 L16.4,38 C16.1790861,38 16,37.8209139 16,37.6 L16,35.4 C16,35.1790861 16.1790861,35 16.4,35 L43.6,35 Z M8.5,19 C10.9852814,19 13,21.0147186 13,23.5 C13,25.9852814 10.9852814,28 8.5,28 C6.01471863,28 4,25.9852814 4,23.5 C4,21.0147186 6.01471863,19 8.5,19 Z M8.5,22 C7.67157288,22 7,22.6715729 7,23.5 C7,24.3284271 7.67157288,25 8.5,25 C9.32842712,25 10,24.3284271 10,23.5 C10,22.6715729 9.32842712,22 8.5,22 Z M43.6,22 C43.8209139,22 44,22.1790861 44,22.4 L44,24.6 C44,24.8209139 43.8209139,25 43.6,25 L16.4,25 C16.1790861,25 16,24.8209139 16,24.6 L16,22.4 C16,22.1790861 16.1790861,22 16.4,22 L43.6,22 Z M8.5,6 C10.9852814,6 13,8.01471863 13,10.5 C13,12.9852814 10.9852814,15 8.5,15 C6.01471863,15 4,12.9852814 4,10.5 C4,8.01471863 6.01471863,6 8.5,6 Z M8.5,9 C7.67157288,9 7,9.67157288 7,10.5 C7,11.3284271 7.67157288,12 8.5,12 C9.32842712,12 10,11.3284271 10,10.5 C10,9.67157288 9.32842712,9 8.5,9 Z M43.6,9 C43.8209139,9 44,9.1790861 44,9.4 L44,11.6 C44,11.8209139 43.8209139,12 43.6,12 L16.4,12 C16.1790861,12 16,11.8209139 16,11.6 L16,9.4 C16,9.1790861 16.1790861,9 16.4,9 L43.6,9 Z\",\n    id: \"UnorderedListOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default UnorderedListOutline;", "map": {"version": 3, "names": ["React", "UnorderedListOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/UnorderedListOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction UnorderedListOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnorderedListOutline-UnorderedListOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"UnorderedListOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"UnorderedListOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M8.5,32 C10.9852814,32 13,34.0147186 13,36.5 C13,38.9852814 10.9852814,41 8.5,41 C6.01471863,41 4,38.9852814 4,36.5 C4,34.0147186 6.01471863,32 8.5,32 Z M8.5,35 C7.67157288,35 7,35.6715729 7,36.5 C7,37.3284271 7.67157288,38 8.5,38 C9.32842712,38 10,37.3284271 10,36.5 C10,35.6715729 9.32842712,35 8.5,35 Z M43.6,35 C43.8209139,35 44,35.1790861 44,35.4 L44,37.6 C44,37.8209139 43.8209139,38 43.6,38 L16.4,38 C16.1790861,38 16,37.8209139 16,37.6 L16,35.4 C16,35.1790861 16.1790861,35 16.4,35 L43.6,35 Z M8.5,19 C10.9852814,19 13,21.0147186 13,23.5 C13,25.9852814 10.9852814,28 8.5,28 C6.01471863,28 4,25.9852814 4,23.5 C4,21.0147186 6.01471863,19 8.5,19 Z M8.5,22 C7.67157288,22 7,22.6715729 7,23.5 C7,24.3284271 7.67157288,25 8.5,25 C9.32842712,25 10,24.3284271 10,23.5 C10,22.6715729 9.32842712,22 8.5,22 Z M43.6,22 C43.8209139,22 44,22.1790861 44,22.4 L44,24.6 C44,24.8209139 43.8209139,25 43.6,25 L16.4,25 C16.1790861,25 16,24.8209139 16,24.6 L16,22.4 C16,22.1790861 16.1790861,22 16.4,22 L43.6,22 Z M8.5,6 C10.9852814,6 13,8.01471863 13,10.5 C13,12.9852814 10.9852814,15 8.5,15 C6.01471863,15 4,12.9852814 4,10.5 C4,8.01471863 6.01471863,6 8.5,6 Z M8.5,9 C7.67157288,9 7,9.67157288 7,10.5 C7,11.3284271 7.67157288,12 8.5,12 C9.32842712,12 10,11.3284271 10,10.5 C10,9.67157288 9.32842712,9 8.5,9 Z M43.6,9 C43.8209139,9 44,9.1790861 44,9.4 L44,11.6 C44,11.8209139 43.8209139,12 43.6,12 L16.4,12 C16.1790861,12 16,11.8209139 16,11.6 L16,9.4 C16,9.1790861 16.1790861,9 16.4,9 L43.6,9 Z\",\n    id: \"UnorderedListOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default UnorderedListOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EACnC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2CAA2C;IAC/CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,mCAAmC;IACvCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,+8CAA+8C;IACl9CR,EAAE,EAAE,+CAA+C;IACnDG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}