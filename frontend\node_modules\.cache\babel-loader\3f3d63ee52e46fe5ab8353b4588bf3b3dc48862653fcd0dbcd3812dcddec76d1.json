{"ast": null, "code": "var listeners = {};\nvar trigger = function (key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\nvar subscribe = function (key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\nexport { trigger, subscribe };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}