{"ast": null, "code": "import isEqual from 'react-fast-compare';\nexport var depsEqual = function (aDeps, bDeps) {\n  if (aDeps === void 0) {\n    aDeps = [];\n  }\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n  return isEqual(aDeps, bDeps);\n};", "map": {"version": 3, "names": ["isEqual", "depsEqual", "aDeps", "bDeps"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/depsEqual.js"], "sourcesContent": ["import isEqual from 'react-fast-compare';\nexport var depsEqual = function (aDeps, bDeps) {\n  if (aDeps === void 0) {\n    aDeps = [];\n  }\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n  return isEqual(aDeps, bDeps);\n};"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAO,IAAIC,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAE;EAC7C,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,EAAE;EACZ;EACA,IAAIC,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,EAAE;EACZ;EACA,OAAOH,OAAO,CAACE,KAAK,EAAEC,KAAK,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}