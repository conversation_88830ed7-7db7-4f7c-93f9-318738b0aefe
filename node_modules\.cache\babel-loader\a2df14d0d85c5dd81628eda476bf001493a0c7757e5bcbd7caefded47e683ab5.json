{"ast": null, "code": "import * as React from \"react\";\nfunction SendOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SendOutline-SendOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SendOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SendOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24.9998666,4.39999998 L24.9991334,6.59900002 C24.9988997,6.81985136 24.8198514,6.99885187 24.599,6.99902667 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L40.9990267,23.4 C40.9989853,23.1790861 41.1780594,22.9999881 41.3989733,22.9999733 C41.3989822,22.9999733 41.3989911,22.9999733 41.399,23 L43.6,23 C43.8209139,23 44,22.8209139 44,22.6 L44,17.4 C44,17.1790861 43.8209139,17 43.6,17 L41.4,17 C41.1791022,16.9999587 41.0000228,16.8208978 40.9999587,16.6 L40.9991652,8.914 L40.9991652,8.914 L26.5653182,23.3488752 C26.4091241,23.5051006 26.1558581,23.5051096 25.9996429,23.3489054 C25.9996395,23.3489021 25.9996362,23.3488987 25.9996429,23.3488853 L24.4440079,21.7932504 C24.2878173,21.6370323 24.2878112,21.3837769 24.4439942,21.2275512 L38.6701652,7 L38.6701652,7 L31.4,7 C31.1790861,7 31,6.8209139 31,6.6 L31,4.4 C31,4.1790861 31.1790861,4 31.4,4 L42.5,4 L42.5,4 C43.3284271,4 44,4.67157288 44,5.5 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L24.6,4 C24.8209139,3.99986664 25,4.17895274 25,4.39986664 C25,4.3999111 25,4.39995556 24.9998666,4.39999998 Z\",\n    id: \"SendOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default SendOutline;", "map": {"version": 3, "names": ["React", "SendOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/SendOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction SendOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SendOutline-SendOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"SendOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"SendOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M24.9998666,4.39999998 L24.9991334,6.59900002 C24.9988997,6.81985136 24.8198514,6.99885187 24.599,6.99902667 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L40.9990267,23.4 C40.9989853,23.1790861 41.1780594,22.9999881 41.3989733,22.9999733 C41.3989822,22.9999733 41.3989911,22.9999733 41.399,23 L43.6,23 C43.8209139,23 44,22.8209139 44,22.6 L44,17.4 C44,17.1790861 43.8209139,17 43.6,17 L41.4,17 C41.1791022,16.9999587 41.0000228,16.8208978 40.9999587,16.6 L40.9991652,8.914 L40.9991652,8.914 L26.5653182,23.3488752 C26.4091241,23.5051006 26.1558581,23.5051096 25.9996429,23.3489054 C25.9996395,23.3489021 25.9996362,23.3488987 25.9996429,23.3488853 L24.4440079,21.7932504 C24.2878173,21.6370323 24.2878112,21.3837769 24.4439942,21.2275512 L38.6701652,7 L38.6701652,7 L31.4,7 C31.1790861,7 31,6.8209139 31,6.6 L31,4.4 C31,4.1790861 31.1790861,4 31.4,4 L42.5,4 L42.5,4 C43.3284271,4 44,4.67157288 44,5.5 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L24.6,4 C24.8209139,3.99986664 25,4.17895274 25,4.39986664 C25,4.3999111 25,4.39995556 24.9998666,4.39999998 Z\",\n    id: \"SendOutline-\\u8DEF\\u5F84\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default SendOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yBAAyB;IAC7BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,uxCAAuxC;IAC1xCR,EAAE,EAAE,0BAA0B;IAC9BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}