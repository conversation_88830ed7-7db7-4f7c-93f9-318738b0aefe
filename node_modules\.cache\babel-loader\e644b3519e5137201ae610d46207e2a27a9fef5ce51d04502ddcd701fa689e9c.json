{"ast": null, "code": "import { useMemo } from 'react';\nimport isEqual from 'react-fast-compare';\nimport memoize from 'nano-memoize';\nexport function useCascaderValueExtend(options, fieldNames) {\n  const {\n    valueName,\n    childrenName\n  } = fieldNames;\n  const generateItems = useMemo(() => {\n    return memoize(val => {\n      const ret = [];\n      let currentOptions = options;\n      for (const v of val) {\n        const target = currentOptions.find(option => option[valueName] === v);\n        if (!target) {\n          break;\n        }\n        ret.push(target);\n        if (!target[childrenName]) break;\n        currentOptions = target[childrenName];\n      }\n      return ret;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  const generateIsLeaf = useMemo(() => {\n    return memoize(val => {\n      const children = val.reduce((currentOptions, v) => {\n        var _a;\n        return ((_a = currentOptions.find(option => option[valueName] === v)) === null || _a === void 0 ? void 0 : _a[childrenName]) || [];\n      }, options);\n      return children.length === 0;\n    }, {\n      equals: isEqual\n    });\n  }, [options]);\n  function generateValueExtend(val) {\n    return {\n      get items() {\n        return generateItems(val);\n      },\n      get isLeaf() {\n        return generateIsLeaf(val);\n      }\n    };\n  }\n  return generateValueExtend;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}