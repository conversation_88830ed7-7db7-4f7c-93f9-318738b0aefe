{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useSet(initialValue) {\n  var getInitValue = function () {\n    return new Set(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    set = _a[0],\n    setSet = _a[1];\n  var add = function (key) {\n    if (set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.add(key);\n      return temp;\n    });\n  };\n  var remove = function (key) {\n    if (!set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setSet(getInitValue());\n  };\n  return [set, {\n    add: useMemoizedFn(add),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useSet;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}