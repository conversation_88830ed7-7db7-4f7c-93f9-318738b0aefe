{"ast": null, "code": "import \"./dropdown.css\";\nimport Dropdown from './dropdown';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport Item from './item';\nexport default attachPropertiesToComponent(Dropdown, {\n  Item\n});", "map": {"version": 3, "names": ["Dropdown", "attachPropertiesToComponent", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/dropdown/index.js"], "sourcesContent": ["import \"./dropdown.css\";\nimport Dropdown from './dropdown';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport Item from './item';\nexport default attachPropertiesToComponent(Dropdown, {\n  Item\n});"], "mappings": "AAAA,OAAO,gBAAgB;AACvB,OAAOA,QAAQ,MAAM,YAAY;AACjC,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,OAAOC,IAAI,MAAM,QAAQ;AACzB,eAAeD,2BAA2B,CAACD,QAAQ,EAAE;EACnDE;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}