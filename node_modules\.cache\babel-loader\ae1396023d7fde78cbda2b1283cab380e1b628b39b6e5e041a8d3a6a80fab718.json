{"ast": null, "code": "import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Space from '../space';\nimport Grid from '../grid';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { CheckMark } from './check-mark';\nimport { useConfig } from '../config-provider';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-selector`;\nconst defaultProps = {\n  multiple: false,\n  defaultValue: [],\n  showCheckMark: true\n};\nexport const Selector = p => {\n  const props = mergeProps(defaultProps, p);\n  const [labelName, valueName,, disabledName] = useFieldNames(props.fieldNames);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: val => {\n      var _a;\n      const extend = {\n        get items() {\n          return props.options.filter(option => val.includes(option[valueName]));\n        }\n      };\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n    }\n  });\n  const {\n    locale\n  } = useConfig();\n  const items = props.options.map(option => {\n    const active = (value || []).includes(option[valueName]);\n    const disabled = option[disabledName] || props.disabled;\n    const itemCls = classNames(`${classPrefix}-item`, {\n      [`${classPrefix}-item-active`]: active && !props.multiple,\n      [`${classPrefix}-item-multiple-active`]: active && props.multiple,\n      [`${classPrefix}-item-disabled`]: disabled\n    });\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      className: itemCls,\n      onClick: () => {\n        if (disabled) {\n          return;\n        }\n        if (props.multiple) {\n          const val = active ? value.filter(v => v !== option[valueName]) : [...value, option[valueName]];\n          setValue(val);\n        } else {\n          const val = active ? [] : [option[valueName]];\n          setValue(val);\n        }\n      },\n      role: 'option',\n      \"aria-selected\": active && !props.multiple || active && props.multiple\n    }, option[labelName], option.description && React.createElement(\"div\", {\n      className: `${classPrefix}-item-description`\n    }, option.description), active && props.showCheckMark && React.createElement(\"div\", {\n      className: `${classPrefix}-check-mark-wrapper`\n    }, React.createElement(CheckMark, null)));\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    role: 'listbox',\n    \"aria-label\": locale.Selector.name\n  }, props.columns ? React.createElement(Grid, {\n    columns: props.columns\n  }, items) : React.createElement(Space, {\n    wrap: true\n  }, items)));\n};", "map": {"version": 3, "names": ["classNames", "React", "withNativeProps", "mergeProps", "Space", "Grid", "usePropsValue", "CheckMark", "useConfig", "useFieldNames", "classPrefix", "defaultProps", "multiple", "defaultValue", "showCheckMark", "Selector", "p", "props", "labelName", "valueName", "<PERSON><PERSON><PERSON>", "fieldNames", "value", "setValue", "onChange", "val", "_a", "extend", "items", "options", "filter", "option", "includes", "call", "locale", "map", "active", "disabled", "itemCls", "createElement", "key", "className", "onClick", "v", "role", "description", "name", "columns", "wrap"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/selector/selector.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Space from '../space';\nimport Grid from '../grid';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { CheckMark } from './check-mark';\nimport { useConfig } from '../config-provider';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-selector`;\nconst defaultProps = {\n  multiple: false,\n  defaultValue: [],\n  showCheckMark: true\n};\nexport const Selector = p => {\n  const props = mergeProps(defaultProps, p);\n  const [labelName, valueName,, disabledName] = useFieldNames(props.fieldNames);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: val => {\n      var _a;\n      const extend = {\n        get items() {\n          return props.options.filter(option => val.includes(option[valueName]));\n        }\n      };\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n    }\n  });\n  const {\n    locale\n  } = useConfig();\n  const items = props.options.map(option => {\n    const active = (value || []).includes(option[valueName]);\n    const disabled = option[disabledName] || props.disabled;\n    const itemCls = classNames(`${classPrefix}-item`, {\n      [`${classPrefix}-item-active`]: active && !props.multiple,\n      [`${classPrefix}-item-multiple-active`]: active && props.multiple,\n      [`${classPrefix}-item-disabled`]: disabled\n    });\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      className: itemCls,\n      onClick: () => {\n        if (disabled) {\n          return;\n        }\n        if (props.multiple) {\n          const val = active ? value.filter(v => v !== option[valueName]) : [...value, option[valueName]];\n          setValue(val);\n        } else {\n          const val = active ? [] : [option[valueName]];\n          setValue(val);\n        }\n      },\n      role: 'option',\n      \"aria-selected\": active && !props.multiple || active && props.multiple\n    }, option[labelName], option.description && React.createElement(\"div\", {\n      className: `${classPrefix}-item-description`\n    }, option.description), active && props.showCheckMark && React.createElement(\"div\", {\n      className: `${classPrefix}-check-mark-wrapper`\n    }, React.createElement(CheckMark, null)));\n  });\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix,\n    role: 'listbox',\n    \"aria-label\": locale.Selector.name\n  }, props.columns ? React.createElement(Grid, {\n    columns: props.columns\n  }, items) : React.createElement(Space, {\n    wrap: true\n  }, items)));\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,OAAOC,KAAK,MAAM,UAAU;AAC5B,OAAOC,IAAI,MAAM,SAAS;AAC1B,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,WAAW,GAAG,cAAc;AAClC,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE,EAAE;EAChBC,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGC,CAAC,IAAI;EAC3B,MAAMC,KAAK,GAAGd,UAAU,CAACQ,YAAY,EAAEK,CAAC,CAAC;EACzC,MAAM,CAACE,SAAS,EAAEC,SAAS,GAAGC,YAAY,CAAC,GAAGX,aAAa,CAACQ,KAAK,CAACI,UAAU,CAAC;EAC7E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,aAAa,CAAC;IACtCgB,KAAK,EAAEL,KAAK,CAACK,KAAK;IAClBT,YAAY,EAAEI,KAAK,CAACJ,YAAY;IAChCW,QAAQ,EAAEC,GAAG,IAAI;MACf,IAAIC,EAAE;MACN,MAAMC,MAAM,GAAG;QACb,IAAIC,KAAKA,CAAA,EAAG;UACV,OAAOX,KAAK,CAACY,OAAO,CAACC,MAAM,CAACC,MAAM,IAAIN,GAAG,CAACO,QAAQ,CAACD,MAAM,CAACZ,SAAS,CAAC,CAAC,CAAC;QACxE;MACF,CAAC;MACD,CAACO,EAAE,GAAGT,KAAK,CAACO,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAAChB,KAAK,EAAEQ,GAAG,EAAEE,MAAM,CAAC;IACxF;EACF,CAAC,CAAC;EACF,MAAM;IACJO;EACF,CAAC,GAAG1B,SAAS,CAAC,CAAC;EACf,MAAMoB,KAAK,GAAGX,KAAK,CAACY,OAAO,CAACM,GAAG,CAACJ,MAAM,IAAI;IACxC,MAAMK,MAAM,GAAG,CAACd,KAAK,IAAI,EAAE,EAAEU,QAAQ,CAACD,MAAM,CAACZ,SAAS,CAAC,CAAC;IACxD,MAAMkB,QAAQ,GAAGN,MAAM,CAACX,YAAY,CAAC,IAAIH,KAAK,CAACoB,QAAQ;IACvD,MAAMC,OAAO,GAAGtC,UAAU,CAAC,GAAGU,WAAW,OAAO,EAAE;MAChD,CAAC,GAAGA,WAAW,cAAc,GAAG0B,MAAM,IAAI,CAACnB,KAAK,CAACL,QAAQ;MACzD,CAAC,GAAGF,WAAW,uBAAuB,GAAG0B,MAAM,IAAInB,KAAK,CAACL,QAAQ;MACjE,CAAC,GAAGF,WAAW,gBAAgB,GAAG2B;IACpC,CAAC,CAAC;IACF,OAAOpC,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;MAChCC,GAAG,EAAET,MAAM,CAACZ,SAAS,CAAC;MACtBsB,SAAS,EAAEH,OAAO;MAClBI,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIL,QAAQ,EAAE;UACZ;QACF;QACA,IAAIpB,KAAK,CAACL,QAAQ,EAAE;UAClB,MAAMa,GAAG,GAAGW,MAAM,GAAGd,KAAK,CAACQ,MAAM,CAACa,CAAC,IAAIA,CAAC,KAAKZ,MAAM,CAACZ,SAAS,CAAC,CAAC,GAAG,CAAC,GAAGG,KAAK,EAAES,MAAM,CAACZ,SAAS,CAAC,CAAC;UAC/FI,QAAQ,CAACE,GAAG,CAAC;QACf,CAAC,MAAM;UACL,MAAMA,GAAG,GAAGW,MAAM,GAAG,EAAE,GAAG,CAACL,MAAM,CAACZ,SAAS,CAAC,CAAC;UAC7CI,QAAQ,CAACE,GAAG,CAAC;QACf;MACF,CAAC;MACDmB,IAAI,EAAE,QAAQ;MACd,eAAe,EAAER,MAAM,IAAI,CAACnB,KAAK,CAACL,QAAQ,IAAIwB,MAAM,IAAInB,KAAK,CAACL;IAChE,CAAC,EAAEmB,MAAM,CAACb,SAAS,CAAC,EAAEa,MAAM,CAACc,WAAW,IAAI5C,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;MACrEE,SAAS,EAAE,GAAG/B,WAAW;IAC3B,CAAC,EAAEqB,MAAM,CAACc,WAAW,CAAC,EAAET,MAAM,IAAInB,KAAK,CAACH,aAAa,IAAIb,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;MAClFE,SAAS,EAAE,GAAG/B,WAAW;IAC3B,CAAC,EAAET,KAAK,CAACsC,aAAa,CAAChC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC;EACF,OAAOL,eAAe,CAACe,KAAK,EAAEhB,KAAK,CAACsC,aAAa,CAAC,KAAK,EAAE;IACvDE,SAAS,EAAE/B,WAAW;IACtBkC,IAAI,EAAE,SAAS;IACf,YAAY,EAAEV,MAAM,CAACnB,QAAQ,CAAC+B;EAChC,CAAC,EAAE7B,KAAK,CAAC8B,OAAO,GAAG9C,KAAK,CAACsC,aAAa,CAAClC,IAAI,EAAE;IAC3C0C,OAAO,EAAE9B,KAAK,CAAC8B;EACjB,CAAC,EAAEnB,KAAK,CAAC,GAAG3B,KAAK,CAACsC,aAAa,CAACnC,KAAK,EAAE;IACrC4C,IAAI,EAAE;EACR,CAAC,EAAEpB,KAAK,CAAC,CAAC,CAAC;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}