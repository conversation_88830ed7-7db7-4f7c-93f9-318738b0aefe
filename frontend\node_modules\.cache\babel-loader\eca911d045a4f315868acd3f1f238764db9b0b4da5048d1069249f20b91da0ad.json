{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafInterval = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n  var start = Date.now();\n  var handle = {\n    id: 0\n  };\n  var loop = function () {\n    var current = Date.now();\n    if (current - start >= delay) {\n      callback();\n      start = Date.now();\n    }\n    handle.id = requestAnimationFrame(loop);\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafInterval = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafInterval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}