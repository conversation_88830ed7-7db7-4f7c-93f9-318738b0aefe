{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nfunction useDebounce(value, options) {\n  var _a = __read(useState(value), 2),\n    debounced = _a[0],\n    setDebounced = _a[1];\n  var run = useDebounceFn(function () {\n    setDebounced(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return debounced;\n}\nexport default useDebounce;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}