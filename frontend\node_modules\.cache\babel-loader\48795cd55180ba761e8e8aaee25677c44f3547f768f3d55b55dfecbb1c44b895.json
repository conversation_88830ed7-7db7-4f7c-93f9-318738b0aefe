{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useAntdTable = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var form = options.form,\n    _b = options.defaultType,\n    defaultType = _b === void 0 ? 'simple' : _b,\n    defaultParams = options.defaultParams,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    _d = options.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    _e = options.ready,\n    ready = _e === void 0 ? true : _e,\n    rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n  var result = usePagination(service, __assign(__assign({\n    ready: ready,\n    manual: true\n  }, rest), {\n    onSuccess: function () {\n      var _a;\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runSuccessRef.current = true;\n      (_a = rest.onSuccess) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([rest], __read(args), false));\n    }\n  }));\n  var _f = result.params,\n    params = _f === void 0 ? [] : _f,\n    run = result.run;\n  var cacheFormTableData = params[2] || {};\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n    type = _g[0],\n    setType = _g[1];\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var runSuccessRef = useRef(false);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);\n  // get current active field values\n  var getActiveFieldValues = function () {\n    if (!form) {\n      return {};\n    }\n    // antd 4\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    }\n    // antd 3\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n  var validateFields = function () {\n    if (!form) {\n      return Promise.resolve({});\n    }\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue);\n    // antd 4\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    }\n    // antd 3\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n  var restoreForm = function () {\n    if (!form) {\n      return;\n    }\n    // antd v4\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    }\n    // antd v3\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n  var changeType = function () {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n  var _submit = function (initPagination) {\n    if (!ready) {\n      return;\n    }\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        }\n        // record all form data\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);\n        // @ts-ignore\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      }).catch(function (err) {\n        return err;\n      });\n    });\n  };\n  var reset = function () {\n    var _a, _b;\n    if (form) {\n      form.resetFields();\n    }\n    _submit(__assign(__assign({}, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}), {\n      pageSize: options.defaultPageSize || ((_b = (_a = options.defaultParams) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.pageSize) || 10,\n      current: 1\n    }));\n  };\n  var submit = function (e) {\n    var _a, _b, _c;\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n    _submit(runSuccessRef.current ? undefined : __assign({\n      pageSize: options.defaultPageSize || ((_c = (_b = options.defaultParams) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.pageSize) || 10,\n      current: 1\n    }, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}));\n  };\n  var onTableChange = function (pagination, filters, sorter, extra) {\n    var _a = __read(params || []),\n      oldPaginationParams = _a[0],\n      restParams = _a.slice(1);\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter,\n      extra: extra\n    })], __read(restParams), false));\n  };\n  // init\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm();\n      // @ts-ignore\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n    if (ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      if (!manual) {\n        _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n      }\n    }\n  }, []);\n  // change search type, restore form data\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n    restoreForm();\n  }, [type]);\n  // refresh & ready change on the same time\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      if (form) {\n        form.resetFields();\n      }\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!ready) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\nexport default useAntdTable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}