{"ast": null, "code": "import React from 'react';\nimport { findDOMNode } from 'react-dom';\nexport class <PERSON>rapper extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.element = null;\n  }\n  componentDidMount() {\n    this.componentDidUpdate();\n  }\n  componentDidUpdate() {\n    // eslint-disable-next-line\n    const node = findDOMNode(this);\n    if (node instanceof Element) {\n      this.element = node;\n    } else {\n      this.element = null;\n    }\n  }\n  render() {\n    return React.Children.only(this.props.children);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}