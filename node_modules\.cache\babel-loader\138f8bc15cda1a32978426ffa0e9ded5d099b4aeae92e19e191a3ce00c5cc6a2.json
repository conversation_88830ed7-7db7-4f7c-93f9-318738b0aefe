{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nfunction useEventTarget(options) {\n  var _a = options || {},\n    initialValue = _a.initialValue,\n    transformer = _a.transformer;\n  var _b = __read(useState(initialValue), 2),\n    value = _b[0],\n    setValue = _b[1];\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    }\n    // no transformer => U and T should be the same\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\nexport default useEventTarget;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}