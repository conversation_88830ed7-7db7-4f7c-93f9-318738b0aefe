{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nimport { Cascader } from './cascader';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(Cascader, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: (val, extend) => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "renderToBody", "<PERSON>r", "prompt", "props", "Promise", "resolve", "Wrapper", "visible", "setVisible", "createElement", "Object", "assign", "onConfirm", "val", "extend", "_a", "call", "onClose", "afterClose", "unmount"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascader/prompt.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nimport { Cascader } from './cascader';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(Cascader, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: (val, extend) => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5B,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAAC,MAAM;QACdU,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;MACN,OAAOX,KAAK,CAACY,aAAa,CAACR,QAAQ,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,KAAK,EAAE;QAC5DI,OAAO,EAAEA,OAAO;QAChBK,SAAS,EAAEA,CAACC,GAAG,EAAEC,MAAM,KAAK;UAC1B,IAAIC,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACS,SAAS,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,EAAEU,GAAG,EAAEC,MAAM,CAAC;UACvFT,OAAO,CAACQ,GAAG,CAAC;QACd,CAAC;QACDI,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIF,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACc,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,CAAC;UACxEK,UAAU,CAAC,KAAK,CAAC;UACjBH,OAAO,CAAC,IAAI,CAAC;QACf,CAAC;QACDa,UAAU,EAAEA,CAAA,KAAM;UAChB,IAAIH,EAAE;UACN,CAACA,EAAE,GAAGZ,KAAK,CAACe,UAAU,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACb,KAAK,CAAC;UAC3EgB,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC;IACD,MAAMA,OAAO,GAAGnB,YAAY,CAACH,KAAK,CAACY,aAAa,CAACH,OAAO,EAAE,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}