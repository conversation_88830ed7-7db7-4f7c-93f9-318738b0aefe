{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\nvar useThrottlePlugin = function (fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n    throttleLeading = _a.throttleLeading,\n    throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef();\n  var options = {};\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options);\n      // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n  if (!throttleWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useThrottlePlugin;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "throttle", "useEffect", "useRef", "useThrottlePlugin", "fetchInstance", "_a", "throttleWait", "throttleLeading", "throttleTrailing", "throttledRef", "options", "undefined", "leading", "trailing", "_originRunAsync_1", "runAsync", "bind", "current", "callback", "args", "_i", "arguments", "length", "Promise", "resolve", "reject", "call", "apply", "then", "catch", "cancel", "onCancel"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\nvar useThrottlePlugin = function (fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n    throttleLeading = _a.throttleLeading,\n    throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef();\n  var options = {};\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options);\n      // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n  if (!throttleWait) {\n    return {};\n  }\n  return {\n    onCancel: function () {\n      var _a;\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useThrottlePlugin;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EACnD,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAY;IAChCC,eAAe,GAAGF,EAAE,CAACE,eAAe;IACpCC,gBAAgB,GAAGH,EAAE,CAACG,gBAAgB;EACxC,IAAIC,YAAY,GAAGP,MAAM,CAAC,CAAC;EAC3B,IAAIQ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIH,eAAe,KAAKI,SAAS,EAAE;IACjCD,OAAO,CAACE,OAAO,GAAGL,eAAe;EACnC;EACA,IAAIC,gBAAgB,KAAKG,SAAS,EAAE;IAClCD,OAAO,CAACG,QAAQ,GAAGL,gBAAgB;EACrC;EACAP,SAAS,CAAC,YAAY;IACpB,IAAIK,YAAY,EAAE;MAChB,IAAIQ,iBAAiB,GAAGV,aAAa,CAACW,QAAQ,CAACC,IAAI,CAACZ,aAAa,CAAC;MAClEK,YAAY,CAACQ,OAAO,GAAGjB,QAAQ,CAAC,UAAUkB,QAAQ,EAAE;QAClDA,QAAQ,CAAC,CAAC;MACZ,CAAC,EAAEZ,YAAY,EAAEI,OAAO,CAAC;MACzB;MACA;MACAN,aAAa,CAACW,QAAQ,GAAG,YAAY;QACnC,IAAII,IAAI,GAAG,EAAE;QACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;UAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;QAC1B;QACA,OAAO,IAAIG,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;UAC5C,IAAIpB,EAAE;UACN,CAACA,EAAE,GAAGI,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,IAAI,CAACjB,YAAY,EAAE,YAAY;YACjGK,iBAAiB,CAACa,KAAK,CAAC,KAAK,CAAC,EAAE5B,aAAa,CAAC,EAAE,EAAED,MAAM,CAACqB,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAACS,IAAI,CAACJ,OAAO,CAAC,CAACK,KAAK,CAACJ,MAAM,CAAC;UACrG,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACD,OAAO,YAAY;QACjB,IAAIpB,EAAE;QACND,aAAa,CAACW,QAAQ,GAAGD,iBAAiB;QAC1C,CAACT,EAAE,GAAGI,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,MAAM,CAAC,CAAC;MAC9E,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;EACrD,IAAI,CAACF,YAAY,EAAE;IACjB,OAAO,CAAC,CAAC;EACX;EACA,OAAO;IACLyB,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpB,IAAI1B,EAAE;MACN,CAACA,EAAE,GAAGI,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,MAAM,CAAC,CAAC;IAC9E;EACF,CAAC;AACH,CAAC;AACD,eAAe3B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}