{"ast": null, "code": "import dayjs from 'dayjs';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';\nimport isLeapYear from 'dayjs/plugin/isLeapYear';\nimport { TILL_NOW } from './util';\ndayjs.extend(isoWeek);\ndayjs.extend(isoWeeksInYear);\ndayjs.extend(isLeapYear);\nconst precisionRankRecord = {\n  year: 0,\n  month: 1,\n  day: 2,\n  hour: 3,\n  minute: 4,\n  second: 5\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter, tillNow) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const minMonth = min.getMonth() + 1;\n  const minDay = min.getDate();\n  const minHour = min.getHours();\n  const minMinute = min.getMinutes();\n  const minSecond = min.getSeconds();\n  const maxYear = max.getFullYear();\n  const maxMonth = max.getMonth() + 1;\n  const maxDay = max.getDate();\n  const maxHour = max.getHours();\n  const maxMinute = max.getMinutes();\n  const maxSecond = max.getSeconds();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const firstDayInSelectedMonth = dayjs(convertStringArrayToDate([selected[0], selected[1], '1']));\n  const selectedMonth = parseInt(selected[1]);\n  const selectedDay = parseInt(selected[2]);\n  const selectedHour = parseInt(selected[3]);\n  const selectedMinute = parseInt(selected[4]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const isInMinMonth = isInMinYear && selectedMonth === minMonth;\n  const isInMaxMonth = isInMaxYear && selectedMonth === maxMonth;\n  const isInMinDay = isInMinMonth && selectedDay === minDay;\n  const isInMaxDay = isInMaxMonth && selectedDay === maxDay;\n  const isInMinHour = isInMinDay && selectedHour === minHour;\n  const isInMaxHour = isInMaxDay && selectedHour === maxHour;\n  const isInMinMinute = isInMinHour && selectedMinute === minMinute;\n  const isInMaxMinute = isInMaxHour && selectedMinute === maxMinute;\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.month) {\n    const lower = isInMinYear ? minMonth : 1;\n    const upper = isInMaxYear ? maxMonth : 12;\n    const months = generateColumn(lower, upper, 'month');\n    ret.push(months.map(v => ({\n      label: renderLabel('month', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.day) {\n    const lower = isInMinMonth ? minDay : 1;\n    const upper = isInMaxMonth ? maxDay : firstDayInSelectedMonth.daysInMonth();\n    const days = generateColumn(lower, upper, 'day');\n    ret.push(days.map(v => ({\n      label: renderLabel('day', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.hour) {\n    const lower = isInMinDay ? minHour : 0;\n    const upper = isInMaxDay ? maxHour : 23;\n    const hours = generateColumn(lower, upper, 'hour');\n    ret.push(hours.map(v => ({\n      label: renderLabel('hour', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.minute) {\n    const lower = isInMinHour ? minMinute : 0;\n    const upper = isInMaxHour ? maxMinute : 59;\n    const minutes = generateColumn(lower, upper, 'minute');\n    ret.push(minutes.map(v => ({\n      label: renderLabel('minute', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.second) {\n    const lower = isInMinMinute ? minSecond : 0;\n    const upper = isInMaxMinute ? maxSecond : 59;\n    const seconds = generateColumn(lower, upper, 'second');\n    ret.push(seconds.map(v => ({\n      label: renderLabel('second', v),\n      value: v.toString()\n    })));\n  }\n  // Till Now\n  if (tillNow) {\n    ret[0].push({\n      label: renderLabel('now', null),\n      value: TILL_NOW\n    });\n    if (TILL_NOW === (selected === null || selected === void 0 ? void 0 : selected[0])) {\n      for (let i = 1; i < ret.length; i += 1) {\n        ret[i] = [];\n      }\n    }\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  return [date.getFullYear().toString(), (date.getMonth() + 1).toString(), date.getDate().toString(), date.getHours().toString(), date.getMinutes().toString(), date.getSeconds().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b, _c, _d, _e, _f;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const monthString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const dateString = (_c = value[2]) !== null && _c !== void 0 ? _c : '1';\n  const hourString = (_d = value[3]) !== null && _d !== void 0 ? _d : '0';\n  const minuteString = (_e = value[4]) !== null && _e !== void 0 ? _e : '0';\n  const secondString = (_f = value[5]) !== null && _f !== void 0 ? _f : '0';\n  return new Date(parseInt(yearString), parseInt(monthString) - 1, parseInt(dateString), parseInt(hourString), parseInt(minuteString), parseInt(secondString));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}