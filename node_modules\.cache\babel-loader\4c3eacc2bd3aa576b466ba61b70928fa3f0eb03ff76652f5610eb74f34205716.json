{"ast": null, "code": "import * as React from 'react';\nexport default function useMemo(getValue, condition, shouldUpdate) {\n  var cacheRef = React.useRef({});\n  if (!('value' in cacheRef.current) || shouldUpdate(cacheRef.current.condition, condition)) {\n    cacheRef.current.value = getValue();\n    cacheRef.current.condition = condition;\n  }\n  return cacheRef.current.value;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}