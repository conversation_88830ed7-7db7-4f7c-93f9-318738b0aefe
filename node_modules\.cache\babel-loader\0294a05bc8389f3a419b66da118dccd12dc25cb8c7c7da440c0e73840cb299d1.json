{"ast": null, "code": "import classNames from 'classnames';\nimport dayjs from 'dayjs';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore';\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { convertPageToDayjs, convertValueToRange } from './convert';\nimport useSyncScroll from './useSyncScroll';\ndayjs.extend(isoWeek);\ndayjs.extend(isSameOrBefore);\nconst classPrefix = 'adm-calendar-picker-view';\nexport const Context = React.createContext({\n  visible: false\n});\nconst defaultProps = {\n  weekStartsOn: 'Sunday',\n  defaultValue: null,\n  allowClear: true,\n  usePopup: true,\n  selectionMode: 'single'\n};\nexport const CalendarPickerView = forwardRef((p, ref) => {\n  var _a;\n  const bodyRef = useRef(null);\n  const today = dayjs();\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const markItems = [...locale.Calendar.markItems];\n  if (props.weekStartsOn === 'Sunday') {\n    const item = markItems.pop();\n    if (item) markItems.unshift(item);\n  }\n  const [dateRange, setDateRange] = usePropsValue({\n    value: props.value === undefined ? undefined : convertValueToRange(props.selectionMode, props.value),\n    defaultValue: convertValueToRange(props.selectionMode, props.defaultValue),\n    onChange: v => {\n      var _a, _b;\n      if (props.selectionMode === 'single') {\n        (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v ? v[0] : null);\n      } else if (props.selectionMode === 'range') {\n        (_b = props.onChange) === null || _b === void 0 ? void 0 : _b.call(props, v);\n      }\n    }\n  });\n  const [intermediate, setIntermediate] = useState(false);\n  const [current, setCurrent] = useState(() => dayjs(dateRange ? dateRange[0] : today).date(1));\n  const onDateChange = v => {\n    if (v) {\n      setCurrent(dayjs(v[0]).date(1));\n    }\n    setDateRange(v);\n  };\n  const showHeader = props.title !== false;\n  // =============================== Scroll ===============================\n  const context = useContext(Context);\n  const scrollTo = useSyncScroll(current, context.visible, bodyRef);\n  // ============================== Boundary ==============================\n  // 记录默认的 min 和 max，并在外部的值超出边界时自动扩充\n  const [defaultMin, setDefaultMin] = useState(current);\n  const [defaultMax, setDefaultMax] = useState(() => current.add(6, 'month'));\n  useEffect(() => {\n    if (dateRange) {\n      const [startDate, endDate] = dateRange;\n      if (!props.min && startDate && dayjs(startDate).isBefore(defaultMin)) {\n        setDefaultMin(dayjs(startDate).date(1));\n      }\n      if (!props.max && endDate && dayjs(endDate).isAfter(defaultMax)) {\n        setDefaultMax(dayjs(endDate).endOf('month'));\n      }\n    }\n  }, [dateRange]);\n  const maxDay = useMemo(() => props.max ? dayjs(props.max) : defaultMax, [props.max, defaultMax]);\n  const minDay = useMemo(() => props.min ? dayjs(props.min) : defaultMin, [props.min, defaultMin]);\n  // ================================ Refs ================================\n  useImperativeHandle(ref, () => ({\n    jumpTo: pageOrPageGenerator => {\n      let page;\n      if (typeof pageOrPageGenerator === 'function') {\n        page = pageOrPageGenerator({\n          year: current.year(),\n          month: current.month() + 1\n        });\n      } else {\n        page = pageOrPageGenerator;\n      }\n      const next = convertPageToDayjs(page);\n      setCurrent(next);\n      scrollTo(next);\n    },\n    jumpToToday: () => {\n      const next = dayjs().date(1);\n      setCurrent(next);\n      scrollTo(next);\n    },\n    getDateRange: () => dateRange\n  }));\n  // =============================== Render ===============================\n  const header = React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, (_a = props.title) !== null && _a !== void 0 ? _a : locale.Calendar.title));\n  function renderBody() {\n    var _a;\n    const cells = [];\n    let monthIterator = minDay;\n    // 遍历月份\n    while (monthIterator.isSameOrBefore(maxDay, 'month')) {\n      const year = monthIterator.year();\n      const month = monthIterator.month() + 1;\n      const renderMap = {\n        year,\n        month\n      };\n      const yearMonth = `${year}-${month}`;\n      // 获取需要预先填充的空格，如果是 7 天则不需要填充\n      const presetEmptyCellCount = props.weekStartsOn === 'Monday' ? monthIterator.date(1).isoWeekday() - 1 : monthIterator.date(1).isoWeekday();\n      const presetEmptyCells = presetEmptyCellCount == 7 ? null : Array(presetEmptyCellCount).fill(null).map((_, index) => React.createElement(\"div\", {\n        key: index,\n        className: `${classPrefix}-cell`\n      }));\n      cells.push(React.createElement(\"div\", {\n        key: yearMonth,\n        \"data-year-month\": yearMonth\n      }, React.createElement(\"div\", {\n        className: `${classPrefix}-title`\n      }, (_a = locale.Calendar.yearAndMonth) === null || _a === void 0 ? void 0 : _a.replace(/\\${(.*?)}/g, (_, variable) => {\n        var _a;\n        return (_a = renderMap[variable]) === null || _a === void 0 ? void 0 : _a.toString();\n      })), React.createElement(\"div\", {\n        className: `${classPrefix}-cells`\n      }, presetEmptyCells, Array(monthIterator.daysInMonth()).fill(null).map((_, index) => {\n        const d = monthIterator.date(index + 1);\n        let isSelect = false;\n        let isBegin = false;\n        let isEnd = false;\n        let isSelectRowBegin = false;\n        let isSelectRowEnd = false;\n        if (dateRange) {\n          const [begin, end] = dateRange;\n          isBegin = d.isSame(begin, 'day');\n          isEnd = d.isSame(end, 'day');\n          isSelect = isBegin || isEnd || d.isAfter(begin, 'day') && d.isBefore(end, 'day');\n          if (isSelect) {\n            isSelectRowBegin = (cells.length % 7 === 0 || d.isSame(d.startOf('month'), 'day')) && !isBegin;\n            isSelectRowEnd = (cells.length % 7 === 6 || d.isSame(d.endOf('month'), 'day')) && !isEnd;\n          }\n        }\n        const disabled = props.shouldDisableDate ? props.shouldDisableDate(d.toDate()) : maxDay && d.isAfter(maxDay, 'day') || minDay && d.isBefore(minDay, 'day');\n        const renderTop = () => {\n          var _a;\n          if (props.renderTop === false) return null;\n          const contentWrapper = content => React.createElement(\"div\", {\n            className: `${classPrefix}-cell-top`\n          }, content);\n          const top = (_a = props.renderTop) === null || _a === void 0 ? void 0 : _a.call(props, d.toDate());\n          if (top) {\n            return contentWrapper(top);\n          }\n          if (props.selectionMode === 'range') {\n            if (isBegin) {\n              return contentWrapper(locale.Calendar.start);\n            }\n            if (isEnd) {\n              return contentWrapper(locale.Calendar.end);\n            }\n          }\n          if (d.isSame(today, 'day') && !isSelect) {\n            return contentWrapper(locale.Calendar.today);\n          }\n          return contentWrapper(null);\n        };\n        const renderBottom = () => {\n          var _a;\n          if (props.renderBottom === false) return null;\n          return React.createElement(\"div\", {\n            className: `${classPrefix}-cell-bottom`\n          }, (_a = props.renderBottom) === null || _a === void 0 ? void 0 : _a.call(props, d.toDate()));\n        };\n        return React.createElement(\"div\", {\n          key: d.valueOf(),\n          className: classNames(`${classPrefix}-cell`, {\n            [`${classPrefix}-cell-today`]: d.isSame(today, 'day'),\n            [`${classPrefix}-cell-selected`]: isSelect,\n            [`${classPrefix}-cell-selected-begin`]: isBegin,\n            [`${classPrefix}-cell-selected-end`]: isEnd,\n            [`${classPrefix}-cell-selected-row-begin`]: isSelectRowBegin,\n            [`${classPrefix}-cell-selected-row-end`]: isSelectRowEnd,\n            [`${classPrefix}-cell-disabled`]: !!disabled\n          }),\n          onClick: () => {\n            if (!props.selectionMode) return;\n            if (disabled) return;\n            const date = d.toDate();\n            function shouldClear() {\n              if (!props.allowClear) return false;\n              if (!dateRange) return false;\n              const [begin, end] = dateRange;\n              return d.isSame(begin, 'date') && d.isSame(end, 'day');\n            }\n            if (props.selectionMode === 'single') {\n              if (props.allowClear && shouldClear()) {\n                onDateChange(null);\n                return;\n              }\n              onDateChange([date, date]);\n            } else if (props.selectionMode === 'range') {\n              if (!dateRange) {\n                onDateChange([date, date]);\n                setIntermediate(true);\n                return;\n              }\n              if (shouldClear()) {\n                onDateChange(null);\n                setIntermediate(false);\n                return;\n              }\n              if (intermediate) {\n                const another = dateRange[0];\n                onDateChange(another > date ? [date, another] : [another, date]);\n                setIntermediate(false);\n              } else {\n                onDateChange([date, date]);\n                setIntermediate(true);\n              }\n            }\n          }\n        }, renderTop(), React.createElement(\"div\", {\n          className: `${classPrefix}-cell-date`\n        }, props.renderDate ? props.renderDate(d.toDate()) : d.date()), renderBottom());\n      }))));\n      monthIterator = monthIterator.add(1, 'month');\n    }\n    return cells;\n  }\n  const body = React.createElement(\"div\", {\n    className: `${classPrefix}-body`,\n    ref: bodyRef\n  }, renderBody());\n  const mark = React.createElement(\"div\", {\n    className: `${classPrefix}-mark`\n  }, markItems.map((item, index) => React.createElement(\"div\", {\n    key: index,\n    className: `${classPrefix}-mark-cell`\n  }, item)));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, showHeader && header, mark, body));\n});", "map": {"version": 3, "names": ["classNames", "dayjs", "isoWeek", "isSameOrBefore", "React", "forwardRef", "useContext", "useEffect", "useImperativeHandle", "useMemo", "useRef", "useState", "withNativeProps", "usePropsValue", "mergeProps", "useConfig", "convertPageToDayjs", "convertValueToRange", "useSyncScroll", "extend", "classPrefix", "Context", "createContext", "visible", "defaultProps", "weekStartsOn", "defaultValue", "allowClear", "usePopup", "selectionMode", "CalendarPickerView", "p", "ref", "_a", "bodyRef", "today", "props", "locale", "markItems", "Calendar", "item", "pop", "unshift", "date<PERSON><PERSON><PERSON>", "setDateRange", "value", "undefined", "onChange", "v", "_b", "call", "intermediate", "setIntermediate", "current", "setCurrent", "date", "onDateChange", "showHeader", "title", "context", "scrollTo", "defaultMin", "setDefaultMin", "defaultMax", "setDefaultMax", "add", "startDate", "endDate", "min", "isBefore", "max", "isAfter", "endOf", "maxDay", "minDay", "jumpTo", "pageOrPageGenerator", "page", "year", "month", "next", "jumpT<PERSON><PERSON>oday", "getDateRange", "header", "createElement", "className", "renderBody", "cells", "monthIterator", "renderMap", "yearMonth", "presetEmptyCellCount", "isoWeekday", "presetEmpty<PERSON>ells", "Array", "fill", "map", "_", "index", "key", "push", "yearAndMonth", "replace", "variable", "toString", "daysInMonth", "d", "isSelect", "isBegin", "isEnd", "isSelectRowBegin", "isSelectRowEnd", "begin", "end", "isSame", "length", "startOf", "disabled", "shouldDisableDate", "toDate", "renderTop", "contentWrapper", "content", "top", "start", "renderBottom", "valueOf", "onClick", "shouldClear", "another", "renderDate", "body", "mark"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/calendar-picker-view/calendar-picker-view.js"], "sourcesContent": ["import classNames from 'classnames';\nimport dayjs from 'dayjs';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isSameOrBefore from 'dayjs/plugin/isSameOrBefore';\nimport React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { convertPageToDayjs, convertValueToRange } from './convert';\nimport useSyncScroll from './useSyncScroll';\ndayjs.extend(isoWeek);\ndayjs.extend(isSameOrBefore);\nconst classPrefix = 'adm-calendar-picker-view';\nexport const Context = React.createContext({\n  visible: false\n});\nconst defaultProps = {\n  weekStartsOn: 'Sunday',\n  defaultValue: null,\n  allowClear: true,\n  usePopup: true,\n  selectionMode: 'single'\n};\nexport const CalendarPickerView = forwardRef((p, ref) => {\n  var _a;\n  const bodyRef = useRef(null);\n  const today = dayjs();\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const markItems = [...locale.Calendar.markItems];\n  if (props.weekStartsOn === 'Sunday') {\n    const item = markItems.pop();\n    if (item) markItems.unshift(item);\n  }\n  const [dateRange, setDateRange] = usePropsValue({\n    value: props.value === undefined ? undefined : convertValueToRange(props.selectionMode, props.value),\n    defaultValue: convertValueToRange(props.selectionMode, props.defaultValue),\n    onChange: v => {\n      var _a, _b;\n      if (props.selectionMode === 'single') {\n        (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v ? v[0] : null);\n      } else if (props.selectionMode === 'range') {\n        (_b = props.onChange) === null || _b === void 0 ? void 0 : _b.call(props, v);\n      }\n    }\n  });\n  const [intermediate, setIntermediate] = useState(false);\n  const [current, setCurrent] = useState(() => dayjs(dateRange ? dateRange[0] : today).date(1));\n  const onDateChange = v => {\n    if (v) {\n      setCurrent(dayjs(v[0]).date(1));\n    }\n    setDateRange(v);\n  };\n  const showHeader = props.title !== false;\n  // =============================== Scroll ===============================\n  const context = useContext(Context);\n  const scrollTo = useSyncScroll(current, context.visible, bodyRef);\n  // ============================== Boundary ==============================\n  // 记录默认的 min 和 max，并在外部的值超出边界时自动扩充\n  const [defaultMin, setDefaultMin] = useState(current);\n  const [defaultMax, setDefaultMax] = useState(() => current.add(6, 'month'));\n  useEffect(() => {\n    if (dateRange) {\n      const [startDate, endDate] = dateRange;\n      if (!props.min && startDate && dayjs(startDate).isBefore(defaultMin)) {\n        setDefaultMin(dayjs(startDate).date(1));\n      }\n      if (!props.max && endDate && dayjs(endDate).isAfter(defaultMax)) {\n        setDefaultMax(dayjs(endDate).endOf('month'));\n      }\n    }\n  }, [dateRange]);\n  const maxDay = useMemo(() => props.max ? dayjs(props.max) : defaultMax, [props.max, defaultMax]);\n  const minDay = useMemo(() => props.min ? dayjs(props.min) : defaultMin, [props.min, defaultMin]);\n  // ================================ Refs ================================\n  useImperativeHandle(ref, () => ({\n    jumpTo: pageOrPageGenerator => {\n      let page;\n      if (typeof pageOrPageGenerator === 'function') {\n        page = pageOrPageGenerator({\n          year: current.year(),\n          month: current.month() + 1\n        });\n      } else {\n        page = pageOrPageGenerator;\n      }\n      const next = convertPageToDayjs(page);\n      setCurrent(next);\n      scrollTo(next);\n    },\n    jumpToToday: () => {\n      const next = dayjs().date(1);\n      setCurrent(next);\n      scrollTo(next);\n    },\n    getDateRange: () => dateRange\n  }));\n  // =============================== Render ===============================\n  const header = React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, (_a = props.title) !== null && _a !== void 0 ? _a : locale.Calendar.title));\n  function renderBody() {\n    var _a;\n    const cells = [];\n    let monthIterator = minDay;\n    // 遍历月份\n    while (monthIterator.isSameOrBefore(maxDay, 'month')) {\n      const year = monthIterator.year();\n      const month = monthIterator.month() + 1;\n      const renderMap = {\n        year,\n        month\n      };\n      const yearMonth = `${year}-${month}`;\n      // 获取需要预先填充的空格，如果是 7 天则不需要填充\n      const presetEmptyCellCount = props.weekStartsOn === 'Monday' ? monthIterator.date(1).isoWeekday() - 1 : monthIterator.date(1).isoWeekday();\n      const presetEmptyCells = presetEmptyCellCount == 7 ? null : Array(presetEmptyCellCount).fill(null).map((_, index) => React.createElement(\"div\", {\n        key: index,\n        className: `${classPrefix}-cell`\n      }));\n      cells.push(React.createElement(\"div\", {\n        key: yearMonth,\n        \"data-year-month\": yearMonth\n      }, React.createElement(\"div\", {\n        className: `${classPrefix}-title`\n      }, (_a = locale.Calendar.yearAndMonth) === null || _a === void 0 ? void 0 : _a.replace(/\\${(.*?)}/g, (_, variable) => {\n        var _a;\n        return (_a = renderMap[variable]) === null || _a === void 0 ? void 0 : _a.toString();\n      })), React.createElement(\"div\", {\n        className: `${classPrefix}-cells`\n      }, presetEmptyCells, Array(monthIterator.daysInMonth()).fill(null).map((_, index) => {\n        const d = monthIterator.date(index + 1);\n        let isSelect = false;\n        let isBegin = false;\n        let isEnd = false;\n        let isSelectRowBegin = false;\n        let isSelectRowEnd = false;\n        if (dateRange) {\n          const [begin, end] = dateRange;\n          isBegin = d.isSame(begin, 'day');\n          isEnd = d.isSame(end, 'day');\n          isSelect = isBegin || isEnd || d.isAfter(begin, 'day') && d.isBefore(end, 'day');\n          if (isSelect) {\n            isSelectRowBegin = (cells.length % 7 === 0 || d.isSame(d.startOf('month'), 'day')) && !isBegin;\n            isSelectRowEnd = (cells.length % 7 === 6 || d.isSame(d.endOf('month'), 'day')) && !isEnd;\n          }\n        }\n        const disabled = props.shouldDisableDate ? props.shouldDisableDate(d.toDate()) : maxDay && d.isAfter(maxDay, 'day') || minDay && d.isBefore(minDay, 'day');\n        const renderTop = () => {\n          var _a;\n          if (props.renderTop === false) return null;\n          const contentWrapper = content => React.createElement(\"div\", {\n            className: `${classPrefix}-cell-top`\n          }, content);\n          const top = (_a = props.renderTop) === null || _a === void 0 ? void 0 : _a.call(props, d.toDate());\n          if (top) {\n            return contentWrapper(top);\n          }\n          if (props.selectionMode === 'range') {\n            if (isBegin) {\n              return contentWrapper(locale.Calendar.start);\n            }\n            if (isEnd) {\n              return contentWrapper(locale.Calendar.end);\n            }\n          }\n          if (d.isSame(today, 'day') && !isSelect) {\n            return contentWrapper(locale.Calendar.today);\n          }\n          return contentWrapper(null);\n        };\n        const renderBottom = () => {\n          var _a;\n          if (props.renderBottom === false) return null;\n          return React.createElement(\"div\", {\n            className: `${classPrefix}-cell-bottom`\n          }, (_a = props.renderBottom) === null || _a === void 0 ? void 0 : _a.call(props, d.toDate()));\n        };\n        return React.createElement(\"div\", {\n          key: d.valueOf(),\n          className: classNames(`${classPrefix}-cell`, {\n            [`${classPrefix}-cell-today`]: d.isSame(today, 'day'),\n            [`${classPrefix}-cell-selected`]: isSelect,\n            [`${classPrefix}-cell-selected-begin`]: isBegin,\n            [`${classPrefix}-cell-selected-end`]: isEnd,\n            [`${classPrefix}-cell-selected-row-begin`]: isSelectRowBegin,\n            [`${classPrefix}-cell-selected-row-end`]: isSelectRowEnd,\n            [`${classPrefix}-cell-disabled`]: !!disabled\n          }),\n          onClick: () => {\n            if (!props.selectionMode) return;\n            if (disabled) return;\n            const date = d.toDate();\n            function shouldClear() {\n              if (!props.allowClear) return false;\n              if (!dateRange) return false;\n              const [begin, end] = dateRange;\n              return d.isSame(begin, 'date') && d.isSame(end, 'day');\n            }\n            if (props.selectionMode === 'single') {\n              if (props.allowClear && shouldClear()) {\n                onDateChange(null);\n                return;\n              }\n              onDateChange([date, date]);\n            } else if (props.selectionMode === 'range') {\n              if (!dateRange) {\n                onDateChange([date, date]);\n                setIntermediate(true);\n                return;\n              }\n              if (shouldClear()) {\n                onDateChange(null);\n                setIntermediate(false);\n                return;\n              }\n              if (intermediate) {\n                const another = dateRange[0];\n                onDateChange(another > date ? [date, another] : [another, date]);\n                setIntermediate(false);\n              } else {\n                onDateChange([date, date]);\n                setIntermediate(true);\n              }\n            }\n          }\n        }, renderTop(), React.createElement(\"div\", {\n          className: `${classPrefix}-cell-date`\n        }, props.renderDate ? props.renderDate(d.toDate()) : d.date()), renderBottom());\n      }))));\n      monthIterator = monthIterator.add(1, 'month');\n    }\n    return cells;\n  }\n  const body = React.createElement(\"div\", {\n    className: `${classPrefix}-body`,\n    ref: bodyRef\n  }, renderBody());\n  const mark = React.createElement(\"div\", {\n    className: `${classPrefix}-mark`\n  }, markItems.map((item, index) => React.createElement(\"div\", {\n    key: index,\n    className: `${classPrefix}-mark-cell`\n  }, item)));\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, showHeader && header, mark, body));\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChH,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,WAAW;AACnE,OAAOC,aAAa,MAAM,iBAAiB;AAC3CjB,KAAK,CAACkB,MAAM,CAACjB,OAAO,CAAC;AACrBD,KAAK,CAACkB,MAAM,CAAChB,cAAc,CAAC;AAC5B,MAAMiB,WAAW,GAAG,0BAA0B;AAC9C,OAAO,MAAMC,OAAO,GAAGjB,KAAK,CAACkB,aAAa,CAAC;EACzCC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,QAAQ;EACtBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,MAAMC,kBAAkB,GAAGzB,UAAU,CAAC,CAAC0B,CAAC,EAAEC,GAAG,KAAK;EACvD,IAAIC,EAAE;EACN,MAAMC,OAAO,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMyB,KAAK,GAAGlC,KAAK,CAAC,CAAC;EACrB,MAAMmC,KAAK,GAAGtB,UAAU,CAACU,YAAY,EAAEO,CAAC,CAAC;EACzC,MAAM;IACJM;EACF,CAAC,GAAGtB,SAAS,CAAC,CAAC;EACf,MAAMuB,SAAS,GAAG,CAAC,GAAGD,MAAM,CAACE,QAAQ,CAACD,SAAS,CAAC;EAChD,IAAIF,KAAK,CAACX,YAAY,KAAK,QAAQ,EAAE;IACnC,MAAMe,IAAI,GAAGF,SAAS,CAACG,GAAG,CAAC,CAAC;IAC5B,IAAID,IAAI,EAAEF,SAAS,CAACI,OAAO,CAACF,IAAI,CAAC;EACnC;EACA,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG/B,aAAa,CAAC;IAC9CgC,KAAK,EAAET,KAAK,CAACS,KAAK,KAAKC,SAAS,GAAGA,SAAS,GAAG7B,mBAAmB,CAACmB,KAAK,CAACP,aAAa,EAAEO,KAAK,CAACS,KAAK,CAAC;IACpGnB,YAAY,EAAET,mBAAmB,CAACmB,KAAK,CAACP,aAAa,EAAEO,KAAK,CAACV,YAAY,CAAC;IAC1EqB,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIf,EAAE,EAAEgB,EAAE;MACV,IAAIb,KAAK,CAACP,aAAa,KAAK,QAAQ,EAAE;QACpC,CAACI,EAAE,GAAGG,KAAK,CAACW,QAAQ,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAACd,KAAK,EAAEY,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAC5F,CAAC,MAAM,IAAIZ,KAAK,CAACP,aAAa,KAAK,OAAO,EAAE;QAC1C,CAACoB,EAAE,GAAGb,KAAK,CAACW,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACd,KAAK,EAAEY,CAAC,CAAC;MAC9E;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,MAAMV,KAAK,CAAC0C,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGR,KAAK,CAAC,CAACoB,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7F,MAAMC,YAAY,GAAGR,CAAC,IAAI;IACxB,IAAIA,CAAC,EAAE;MACLM,UAAU,CAACrD,KAAK,CAAC+C,CAAC,CAAC,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC;IACjC;IACAX,YAAY,CAACI,CAAC,CAAC;EACjB,CAAC;EACD,MAAMS,UAAU,GAAGrB,KAAK,CAACsB,KAAK,KAAK,KAAK;EACxC;EACA,MAAMC,OAAO,GAAGrD,UAAU,CAACe,OAAO,CAAC;EACnC,MAAMuC,QAAQ,GAAG1C,aAAa,CAACmC,OAAO,EAAEM,OAAO,CAACpC,OAAO,EAAEW,OAAO,CAAC;EACjE;EACA;EACA,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC0C,OAAO,CAAC;EACrD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,MAAM0C,OAAO,CAACY,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3E1D,SAAS,CAAC,MAAM;IACd,IAAIoC,SAAS,EAAE;MACb,MAAM,CAACuB,SAAS,EAAEC,OAAO,CAAC,GAAGxB,SAAS;MACtC,IAAI,CAACP,KAAK,CAACgC,GAAG,IAAIF,SAAS,IAAIjE,KAAK,CAACiE,SAAS,CAAC,CAACG,QAAQ,CAACR,UAAU,CAAC,EAAE;QACpEC,aAAa,CAAC7D,KAAK,CAACiE,SAAS,CAAC,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC;MACzC;MACA,IAAI,CAACnB,KAAK,CAACkC,GAAG,IAAIH,OAAO,IAAIlE,KAAK,CAACkE,OAAO,CAAC,CAACI,OAAO,CAACR,UAAU,CAAC,EAAE;QAC/DC,aAAa,CAAC/D,KAAK,CAACkE,OAAO,CAAC,CAACK,KAAK,CAAC,OAAO,CAAC,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EACf,MAAM8B,MAAM,GAAGhE,OAAO,CAAC,MAAM2B,KAAK,CAACkC,GAAG,GAAGrE,KAAK,CAACmC,KAAK,CAACkC,GAAG,CAAC,GAAGP,UAAU,EAAE,CAAC3B,KAAK,CAACkC,GAAG,EAAEP,UAAU,CAAC,CAAC;EAChG,MAAMW,MAAM,GAAGjE,OAAO,CAAC,MAAM2B,KAAK,CAACgC,GAAG,GAAGnE,KAAK,CAACmC,KAAK,CAACgC,GAAG,CAAC,GAAGP,UAAU,EAAE,CAACzB,KAAK,CAACgC,GAAG,EAAEP,UAAU,CAAC,CAAC;EAChG;EACArD,mBAAmB,CAACwB,GAAG,EAAE,OAAO;IAC9B2C,MAAM,EAAEC,mBAAmB,IAAI;MAC7B,IAAIC,IAAI;MACR,IAAI,OAAOD,mBAAmB,KAAK,UAAU,EAAE;QAC7CC,IAAI,GAAGD,mBAAmB,CAAC;UACzBE,IAAI,EAAEzB,OAAO,CAACyB,IAAI,CAAC,CAAC;UACpBC,KAAK,EAAE1B,OAAO,CAAC0B,KAAK,CAAC,CAAC,GAAG;QAC3B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLF,IAAI,GAAGD,mBAAmB;MAC5B;MACA,MAAMI,IAAI,GAAGhE,kBAAkB,CAAC6D,IAAI,CAAC;MACrCvB,UAAU,CAAC0B,IAAI,CAAC;MAChBpB,QAAQ,CAACoB,IAAI,CAAC;IAChB,CAAC;IACDC,WAAW,EAAEA,CAAA,KAAM;MACjB,MAAMD,IAAI,GAAG/E,KAAK,CAAC,CAAC,CAACsD,IAAI,CAAC,CAAC,CAAC;MAC5BD,UAAU,CAAC0B,IAAI,CAAC;MAChBpB,QAAQ,CAACoB,IAAI,CAAC;IAChB,CAAC;IACDE,YAAY,EAAEA,CAAA,KAAMvC;EACtB,CAAC,CAAC,CAAC;EACH;EACA,MAAMwC,MAAM,GAAG/E,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IACxCC,SAAS,EAAE,GAAGjE,WAAW;EAC3B,CAAC,EAAEhB,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGjE,WAAW;EAC3B,CAAC,EAAE,CAACa,EAAE,GAAGG,KAAK,CAACsB,KAAK,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGI,MAAM,CAACE,QAAQ,CAACmB,KAAK,CAAC,CAAC;EAC9E,SAAS4B,UAAUA,CAAA,EAAG;IACpB,IAAIrD,EAAE;IACN,MAAMsD,KAAK,GAAG,EAAE;IAChB,IAAIC,aAAa,GAAGd,MAAM;IAC1B;IACA,OAAOc,aAAa,CAACrF,cAAc,CAACsE,MAAM,EAAE,OAAO,CAAC,EAAE;MACpD,MAAMK,IAAI,GAAGU,aAAa,CAACV,IAAI,CAAC,CAAC;MACjC,MAAMC,KAAK,GAAGS,aAAa,CAACT,KAAK,CAAC,CAAC,GAAG,CAAC;MACvC,MAAMU,SAAS,GAAG;QAChBX,IAAI;QACJC;MACF,CAAC;MACD,MAAMW,SAAS,GAAG,GAAGZ,IAAI,IAAIC,KAAK,EAAE;MACpC;MACA,MAAMY,oBAAoB,GAAGvD,KAAK,CAACX,YAAY,KAAK,QAAQ,GAAG+D,aAAa,CAACjC,IAAI,CAAC,CAAC,CAAC,CAACqC,UAAU,CAAC,CAAC,GAAG,CAAC,GAAGJ,aAAa,CAACjC,IAAI,CAAC,CAAC,CAAC,CAACqC,UAAU,CAAC,CAAC;MAC1I,MAAMC,gBAAgB,GAAGF,oBAAoB,IAAI,CAAC,GAAG,IAAI,GAAGG,KAAK,CAACH,oBAAoB,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK9F,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QAC9Ie,GAAG,EAAED,KAAK;QACVb,SAAS,EAAE,GAAGjE,WAAW;MAC3B,CAAC,CAAC,CAAC;MACHmE,KAAK,CAACa,IAAI,CAAChG,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QACpCe,GAAG,EAAET,SAAS;QACd,iBAAiB,EAAEA;MACrB,CAAC,EAAEtF,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QAC5BC,SAAS,EAAE,GAAGjE,WAAW;MAC3B,CAAC,EAAE,CAACa,EAAE,GAAGI,MAAM,CAACE,QAAQ,CAAC8D,YAAY,MAAM,IAAI,IAAIpE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqE,OAAO,CAAC,YAAY,EAAE,CAACL,CAAC,EAAEM,QAAQ,KAAK;QACpH,IAAItE,EAAE;QACN,OAAO,CAACA,EAAE,GAAGwD,SAAS,CAACc,QAAQ,CAAC,MAAM,IAAI,IAAItE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuE,QAAQ,CAAC,CAAC;MACtF,CAAC,CAAC,CAAC,EAAEpG,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;QAC9BC,SAAS,EAAE,GAAGjE,WAAW;MAC3B,CAAC,EAAEyE,gBAAgB,EAAEC,KAAK,CAACN,aAAa,CAACiB,WAAW,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;QACnF,MAAMQ,CAAC,GAAGlB,aAAa,CAACjC,IAAI,CAAC2C,KAAK,GAAG,CAAC,CAAC;QACvC,IAAIS,QAAQ,GAAG,KAAK;QACpB,IAAIC,OAAO,GAAG,KAAK;QACnB,IAAIC,KAAK,GAAG,KAAK;QACjB,IAAIC,gBAAgB,GAAG,KAAK;QAC5B,IAAIC,cAAc,GAAG,KAAK;QAC1B,IAAIpE,SAAS,EAAE;UACb,MAAM,CAACqE,KAAK,EAAEC,GAAG,CAAC,GAAGtE,SAAS;UAC9BiE,OAAO,GAAGF,CAAC,CAACQ,MAAM,CAACF,KAAK,EAAE,KAAK,CAAC;UAChCH,KAAK,GAAGH,CAAC,CAACQ,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC;UAC5BN,QAAQ,GAAGC,OAAO,IAAIC,KAAK,IAAIH,CAAC,CAACnC,OAAO,CAACyC,KAAK,EAAE,KAAK,CAAC,IAAIN,CAAC,CAACrC,QAAQ,CAAC4C,GAAG,EAAE,KAAK,CAAC;UAChF,IAAIN,QAAQ,EAAE;YACZG,gBAAgB,GAAG,CAACvB,KAAK,CAAC4B,MAAM,GAAG,CAAC,KAAK,CAAC,IAAIT,CAAC,CAACQ,MAAM,CAACR,CAAC,CAACU,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,KAAK,CAACR,OAAO;YAC9FG,cAAc,GAAG,CAACxB,KAAK,CAAC4B,MAAM,GAAG,CAAC,KAAK,CAAC,IAAIT,CAAC,CAACQ,MAAM,CAACR,CAAC,CAAClC,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,KAAK,CAACqC,KAAK;UAC1F;QACF;QACA,MAAMQ,QAAQ,GAAGjF,KAAK,CAACkF,iBAAiB,GAAGlF,KAAK,CAACkF,iBAAiB,CAACZ,CAAC,CAACa,MAAM,CAAC,CAAC,CAAC,GAAG9C,MAAM,IAAIiC,CAAC,CAACnC,OAAO,CAACE,MAAM,EAAE,KAAK,CAAC,IAAIC,MAAM,IAAIgC,CAAC,CAACrC,QAAQ,CAACK,MAAM,EAAE,KAAK,CAAC;QAC1J,MAAM8C,SAAS,GAAGA,CAAA,KAAM;UACtB,IAAIvF,EAAE;UACN,IAAIG,KAAK,CAACoF,SAAS,KAAK,KAAK,EAAE,OAAO,IAAI;UAC1C,MAAMC,cAAc,GAAGC,OAAO,IAAItH,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;YAC3DC,SAAS,EAAE,GAAGjE,WAAW;UAC3B,CAAC,EAAEsG,OAAO,CAAC;UACX,MAAMC,GAAG,GAAG,CAAC1F,EAAE,GAAGG,KAAK,CAACoF,SAAS,MAAM,IAAI,IAAIvF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAACd,KAAK,EAAEsE,CAAC,CAACa,MAAM,CAAC,CAAC,CAAC;UAClG,IAAII,GAAG,EAAE;YACP,OAAOF,cAAc,CAACE,GAAG,CAAC;UAC5B;UACA,IAAIvF,KAAK,CAACP,aAAa,KAAK,OAAO,EAAE;YACnC,IAAI+E,OAAO,EAAE;cACX,OAAOa,cAAc,CAACpF,MAAM,CAACE,QAAQ,CAACqF,KAAK,CAAC;YAC9C;YACA,IAAIf,KAAK,EAAE;cACT,OAAOY,cAAc,CAACpF,MAAM,CAACE,QAAQ,CAAC0E,GAAG,CAAC;YAC5C;UACF;UACA,IAAIP,CAAC,CAACQ,MAAM,CAAC/E,KAAK,EAAE,KAAK,CAAC,IAAI,CAACwE,QAAQ,EAAE;YACvC,OAAOc,cAAc,CAACpF,MAAM,CAACE,QAAQ,CAACJ,KAAK,CAAC;UAC9C;UACA,OAAOsF,cAAc,CAAC,IAAI,CAAC;QAC7B,CAAC;QACD,MAAMI,YAAY,GAAGA,CAAA,KAAM;UACzB,IAAI5F,EAAE;UACN,IAAIG,KAAK,CAACyF,YAAY,KAAK,KAAK,EAAE,OAAO,IAAI;UAC7C,OAAOzH,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;YAChCC,SAAS,EAAE,GAAGjE,WAAW;UAC3B,CAAC,EAAE,CAACa,EAAE,GAAGG,KAAK,CAACyF,YAAY,MAAM,IAAI,IAAI5F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAACd,KAAK,EAAEsE,CAAC,CAACa,MAAM,CAAC,CAAC,CAAC,CAAC;QAC/F,CAAC;QACD,OAAOnH,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;UAChCe,GAAG,EAAEO,CAAC,CAACoB,OAAO,CAAC,CAAC;UAChBzC,SAAS,EAAErF,UAAU,CAAC,GAAGoB,WAAW,OAAO,EAAE;YAC3C,CAAC,GAAGA,WAAW,aAAa,GAAGsF,CAAC,CAACQ,MAAM,CAAC/E,KAAK,EAAE,KAAK,CAAC;YACrD,CAAC,GAAGf,WAAW,gBAAgB,GAAGuF,QAAQ;YAC1C,CAAC,GAAGvF,WAAW,sBAAsB,GAAGwF,OAAO;YAC/C,CAAC,GAAGxF,WAAW,oBAAoB,GAAGyF,KAAK;YAC3C,CAAC,GAAGzF,WAAW,0BAA0B,GAAG0F,gBAAgB;YAC5D,CAAC,GAAG1F,WAAW,wBAAwB,GAAG2F,cAAc;YACxD,CAAC,GAAG3F,WAAW,gBAAgB,GAAG,CAAC,CAACiG;UACtC,CAAC,CAAC;UACFU,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI,CAAC3F,KAAK,CAACP,aAAa,EAAE;YAC1B,IAAIwF,QAAQ,EAAE;YACd,MAAM9D,IAAI,GAAGmD,CAAC,CAACa,MAAM,CAAC,CAAC;YACvB,SAASS,WAAWA,CAAA,EAAG;cACrB,IAAI,CAAC5F,KAAK,CAACT,UAAU,EAAE,OAAO,KAAK;cACnC,IAAI,CAACgB,SAAS,EAAE,OAAO,KAAK;cAC5B,MAAM,CAACqE,KAAK,EAAEC,GAAG,CAAC,GAAGtE,SAAS;cAC9B,OAAO+D,CAAC,CAACQ,MAAM,CAACF,KAAK,EAAE,MAAM,CAAC,IAAIN,CAAC,CAACQ,MAAM,CAACD,GAAG,EAAE,KAAK,CAAC;YACxD;YACA,IAAI7E,KAAK,CAACP,aAAa,KAAK,QAAQ,EAAE;cACpC,IAAIO,KAAK,CAACT,UAAU,IAAIqG,WAAW,CAAC,CAAC,EAAE;gBACrCxE,YAAY,CAAC,IAAI,CAAC;gBAClB;cACF;cACAA,YAAY,CAAC,CAACD,IAAI,EAAEA,IAAI,CAAC,CAAC;YAC5B,CAAC,MAAM,IAAInB,KAAK,CAACP,aAAa,KAAK,OAAO,EAAE;cAC1C,IAAI,CAACc,SAAS,EAAE;gBACda,YAAY,CAAC,CAACD,IAAI,EAAEA,IAAI,CAAC,CAAC;gBAC1BH,eAAe,CAAC,IAAI,CAAC;gBACrB;cACF;cACA,IAAI4E,WAAW,CAAC,CAAC,EAAE;gBACjBxE,YAAY,CAAC,IAAI,CAAC;gBAClBJ,eAAe,CAAC,KAAK,CAAC;gBACtB;cACF;cACA,IAAID,YAAY,EAAE;gBAChB,MAAM8E,OAAO,GAAGtF,SAAS,CAAC,CAAC,CAAC;gBAC5Ba,YAAY,CAACyE,OAAO,GAAG1E,IAAI,GAAG,CAACA,IAAI,EAAE0E,OAAO,CAAC,GAAG,CAACA,OAAO,EAAE1E,IAAI,CAAC,CAAC;gBAChEH,eAAe,CAAC,KAAK,CAAC;cACxB,CAAC,MAAM;gBACLI,YAAY,CAAC,CAACD,IAAI,EAAEA,IAAI,CAAC,CAAC;gBAC1BH,eAAe,CAAC,IAAI,CAAC;cACvB;YACF;UACF;QACF,CAAC,EAAEoE,SAAS,CAAC,CAAC,EAAEpH,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;UACzCC,SAAS,EAAE,GAAGjE,WAAW;QAC3B,CAAC,EAAEgB,KAAK,CAAC8F,UAAU,GAAG9F,KAAK,CAAC8F,UAAU,CAACxB,CAAC,CAACa,MAAM,CAAC,CAAC,CAAC,GAAGb,CAAC,CAACnD,IAAI,CAAC,CAAC,CAAC,EAAEsE,YAAY,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC,CAAC,CAAC,CAAC;MACLrC,aAAa,GAAGA,aAAa,CAACvB,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;IAC/C;IACA,OAAOsB,KAAK;EACd;EACA,MAAM4C,IAAI,GAAG/H,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAE,GAAGjE,WAAW,OAAO;IAChCY,GAAG,EAAEE;EACP,CAAC,EAAEoD,UAAU,CAAC,CAAC,CAAC;EAChB,MAAM8C,IAAI,GAAGhI,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IACtCC,SAAS,EAAE,GAAGjE,WAAW;EAC3B,CAAC,EAAEkB,SAAS,CAAC0D,GAAG,CAAC,CAACxD,IAAI,EAAE0D,KAAK,KAAK9F,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IAC3De,GAAG,EAAED,KAAK;IACVb,SAAS,EAAE,GAAGjE,WAAW;EAC3B,CAAC,EAAEoB,IAAI,CAAC,CAAC,CAAC;EACV,OAAO5B,eAAe,CAACwB,KAAK,EAAEhC,KAAK,CAACgF,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEjE;EACb,CAAC,EAAEqC,UAAU,IAAI0B,MAAM,EAAEiD,IAAI,EAAED,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}