{"ast": null, "code": "export function generateIntArray(from, to) {\n  const array = [];\n  for (let i = from; i <= to; i++) {\n    array.push(i);\n  }\n  return array;\n}", "map": {"version": 3, "names": ["generateIntArray", "from", "to", "array", "i", "push"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/generate-int-array.js"], "sourcesContent": ["export function generateIntArray(from, to) {\n  const array = [];\n  for (let i = from; i <= to; i++) {\n    array.push(i);\n  }\n  return array;\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,EAAE,EAAE;EACzC,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAGH,IAAI,EAAEG,CAAC,IAAIF,EAAE,EAAEE,CAAC,EAAE,EAAE;IAC/BD,KAAK,CAACE,IAAI,CAACD,CAAC,CAAC;EACf;EACA,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}