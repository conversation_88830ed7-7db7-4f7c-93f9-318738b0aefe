{"ast": null, "code": "import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-tag`;\nconst colorRecord = {\n  default: 'var(--adm-color-text-secondary, #666666)',\n  primary: 'var(--adm-color-primary, #1677ff)',\n  success: 'var(--adm-color-success, #00b578)',\n  warning: 'var(--adm-color-warning, #ff8f1f)',\n  danger: 'var(--adm-color-danger, #ff3141)'\n};\nconst defaultProps = {\n  color: 'default',\n  fill: 'solid',\n  round: false\n};\nexport const Tag = p => {\n  var _a;\n  const props = mergeProps(defaultProps, p);\n  const color = (_a = colorRecord[props.color]) !== null && _a !== void 0 ? _a : props.color;\n  const style = {\n    '--border-color': color,\n    '--text-color': props.fill === 'outline' ? color : '#ffffff',\n    '--background-color': props.fill === 'outline' ? 'transparent' : color\n  };\n  return withNativeProps(props, React.createElement(\"span\", {\n    style: style,\n    onClick: props.onClick,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-round`]: props.round\n    })\n  }, props.children));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}