{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport classNames from 'classnames';\nconst classPrefix = `adm-result-page-card`;\nexport const ResultPageCard = props => {\n  return withNativeProps(props, React.createElement('div', {\n    className: classNames(`${classPrefix}`)\n  }, props.children));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}