{"ast": null, "code": "import { __assign } from \"tslib\";\nexport var fieldAdapter = function (field) {\n  return {\n    getFieldInstance: function (name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function (fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function (result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function (dataIndex, order) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function (filterParams) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}