{"ast": null, "code": "import \"./divider.css\";\nimport { Divider } from './divider';\nexport default Divider;", "map": {"version": 3, "names": ["Divider"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/divider/index.js"], "sourcesContent": ["import \"./divider.css\";\nimport { Divider } from './divider';\nexport default Divider;"], "mappings": "AAAA,OAAO,eAAe;AACtB,SAASA,OAAO,QAAQ,WAAW;AACnC,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}