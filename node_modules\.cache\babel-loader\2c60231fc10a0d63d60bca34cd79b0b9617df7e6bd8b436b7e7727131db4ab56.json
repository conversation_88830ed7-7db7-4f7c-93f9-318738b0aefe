{"ast": null, "code": "import * as React from \"react\";\nfunction MailOpenOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailOpenOutline-MailOpenOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailOpenOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MailOpenOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36,4 C37.9778155,4 39.6203643,5.43544767 39.9426676,7.3213643 L40.1521169,7.397518 L40.4016756,7.49998484 L40.7010174,7.64088849 L40.9229172,7.75886114 L41.2103992,7.93023277 L41.4685764,8.10359489 L41.6487222,8.23654502 L41.8596383,8.40599995 L42.0939224,8.61364948 L42.3482912,8.8656909 L42.6096058,9.15899345 L42.7970438,9.39548388 L42.9950139,9.67471806 L43.1500089,9.91968306 L43.2940156,10.1737733 L43.3980208,10.3774535 L43.4880155,10.5710669 L43.6120779,10.873017 C43.6506755,10.9748146 43.6865101,11.077707 43.7195801,11.181831 C43.7528859,11.2867281 43.7832591,11.3923833 43.8107652,11.4991897 C43.8581057,11.6830033 43.8969673,11.8703081 43.9268861,12.0605821 C43.9466638,12.1863118 43.9625713,12.3137089 43.9744223,12.442281 C43.9913436,12.6258069 44,12.8118925 44,13 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,13 C4,12.8118705 4.00865842,12.6257632 4.02559849,12.442055 C4.03743935,12.3136114 4.05335138,12.1861967 4.07319184,12.0600865 C4.1030415,11.8702746 4.14188803,11.6830419 4.18922718,11.4992195 L4.23853436,11.3194576 L4.3060467,11.1030472 L4.37456106,10.9085888 L4.46482202,10.680405 L4.57180595,10.4403766 L4.71097334,10.164467 L4.87599118,9.87671624 L5.06561426,9.58556874 L5.23246926,9.35659776 L5.36658853,9.18771182 L5.57453401,8.94837275 L5.84363605,8.67279192 L6.0127184,8.51643602 L6.20476326,8.35259498 L6.43649902,8.17235931 L6.56793277,8.0778882 L6.8218693,7.90989918 L7.12759631,7.7309474 L7.32963388,7.62551869 L7.55087239,7.52098106 L7.81448692,7.41047018 L8.05818167,7.3211851 C8.37963568,5.43544767 10.0221845,4 12,4 L36,4 Z M41,38 L41,16.499 L24.1789023,24.9105383 C24.0662821,24.9668551 23.9337179,24.9668551 23.8210977,24.9105383 L7,16.499 L7,16.499 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 Z M36,7 L12,7 C11.4871642,7 11.0644928,7.38604019 11.0067277,7.88337887 L11,8 L11,14.8987694 C11,15.0502871 11.0856109,15.1887988 11.2211366,15.2565513 L23.8211228,21.5555752 C23.9337295,21.6118699 24.0662684,21.6118658 24.1788717,21.5555642 L36.7788854,15.2555573 C36.914399,15.1878005 37,15.0492952 37,14.8977864 L37,8 L37,8 C37,7.48716416 36.6139598,7.06449284 36.1166211,7.00672773 L36,7 Z M7.99923004,10.7645829 L7.9385071,10.8204839 L7.75500972,11.00997 L7.62991137,11.1605664 L7.48976276,11.3566031 L7.38180755,11.5342964 L7.28970216,11.7121763 L7.26295405,11.7699483 L7.18817859,11.9518852 L7.12642482,12.1355294 C7.08993805,12.2569843 7.06094096,12.3816803 7.03997011,12.5090808 C7.02298239,12.6122833 7.01126152,12.7172605 7.00509269,12.8237272 L7,13 L7,13.146 L8,13.645 L7.99923004,10.7645829 Z M40,13.644 L41,13.145 L41,13 L40.9952298,12.8293849 L40.9810892,12.6612729 L40.9593377,12.5048943 L40.9210998,12.3135442 L40.8709336,12.1267849 L40.8249512,11.9877969 L40.7738959,11.8553512 L40.709736,11.7109967 L40.6170467,11.5322559 L40.5133161,11.361306 L40.416615,11.2220361 L40.2982483,11.071666 L40.2334083,10.9969894 L40.1078401,10.865284 L40.0014394,10.7651827 L40,13.644 Z\",\n    id: \"MailOpenOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default MailOpenOutline;", "map": {"version": 3, "names": ["React", "MailOpenOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/MailOpenOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction MailOpenOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailOpenOutline-MailOpenOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"MailOpenOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"MailOpenOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M36,4 C37.9778155,4 39.6203643,5.43544767 39.9426676,7.3213643 L40.1521169,7.397518 L40.4016756,7.49998484 L40.7010174,7.64088849 L40.9229172,7.75886114 L41.2103992,7.93023277 L41.4685764,8.10359489 L41.6487222,8.23654502 L41.8596383,8.40599995 L42.0939224,8.61364948 L42.3482912,8.8656909 L42.6096058,9.15899345 L42.7970438,9.39548388 L42.9950139,9.67471806 L43.1500089,9.91968306 L43.2940156,10.1737733 L43.3980208,10.3774535 L43.4880155,10.5710669 L43.6120779,10.873017 C43.6506755,10.9748146 43.6865101,11.077707 43.7195801,11.181831 C43.7528859,11.2867281 43.7832591,11.3923833 43.8107652,11.4991897 C43.8581057,11.6830033 43.8969673,11.8703081 43.9268861,12.0605821 C43.9466638,12.1863118 43.9625713,12.3137089 43.9744223,12.442281 C43.9913436,12.6258069 44,12.8118925 44,13 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,13 C4,12.8118705 4.00865842,12.6257632 4.02559849,12.442055 C4.03743935,12.3136114 4.05335138,12.1861967 4.07319184,12.0600865 C4.1030415,11.8702746 4.14188803,11.6830419 4.18922718,11.4992195 L4.23853436,11.3194576 L4.3060467,11.1030472 L4.37456106,10.9085888 L4.46482202,10.680405 L4.57180595,10.4403766 L4.71097334,10.164467 L4.87599118,9.87671624 L5.06561426,9.58556874 L5.23246926,9.35659776 L5.36658853,9.18771182 L5.57453401,8.94837275 L5.84363605,8.67279192 L6.0127184,8.51643602 L6.20476326,8.35259498 L6.43649902,8.17235931 L6.56793277,8.0778882 L6.8218693,7.90989918 L7.12759631,7.7309474 L7.32963388,7.62551869 L7.55087239,7.52098106 L7.81448692,7.41047018 L8.05818167,7.3211851 C8.37963568,5.43544767 10.0221845,4 12,4 L36,4 Z M41,38 L41,16.499 L24.1789023,24.9105383 C24.0662821,24.9668551 23.9337179,24.9668551 23.8210977,24.9105383 L7,16.499 L7,16.499 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 Z M36,7 L12,7 C11.4871642,7 11.0644928,7.38604019 11.0067277,7.88337887 L11,8 L11,14.8987694 C11,15.0502871 11.0856109,15.1887988 11.2211366,15.2565513 L23.8211228,21.5555752 C23.9337295,21.6118699 24.0662684,21.6118658 24.1788717,21.5555642 L36.7788854,15.2555573 C36.914399,15.1878005 37,15.0492952 37,14.8977864 L37,8 L37,8 C37,7.48716416 36.6139598,7.06449284 36.1166211,7.00672773 L36,7 Z M7.99923004,10.7645829 L7.9385071,10.8204839 L7.75500972,11.00997 L7.62991137,11.1605664 L7.48976276,11.3566031 L7.38180755,11.5342964 L7.28970216,11.7121763 L7.26295405,11.7699483 L7.18817859,11.9518852 L7.12642482,12.1355294 C7.08993805,12.2569843 7.06094096,12.3816803 7.03997011,12.5090808 C7.02298239,12.6122833 7.01126152,12.7172605 7.00509269,12.8237272 L7,13 L7,13.146 L8,13.645 L7.99923004,10.7645829 Z M40,13.644 L41,13.145 L41,13 L40.9952298,12.8293849 L40.9810892,12.6612729 L40.9593377,12.5048943 L40.9210998,12.3135442 L40.8709336,12.1267849 L40.8249512,11.9877969 L40.7738959,11.8553512 L40.709736,11.7109967 L40.6170467,11.5322559 L40.5133161,11.361306 L40.416615,11.2220361 L40.2982483,11.071666 L40.2334083,10.9969894 L40.1078401,10.865284 L40.0014394,10.7651827 L40,13.644 Z\",\n    id: \"MailOpenOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default MailOpenOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,iCAAiC;IACrCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,w/FAAw/F;IAC3/FR,EAAE,EAAE,8BAA8B;IAClCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}