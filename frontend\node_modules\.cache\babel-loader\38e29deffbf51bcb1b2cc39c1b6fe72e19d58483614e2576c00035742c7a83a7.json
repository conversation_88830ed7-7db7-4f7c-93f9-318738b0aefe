{"ast": null, "code": "import { useMemo } from 'react';\nexport const useFieldNames = (fieldNames = {}) => {\n  const fields = useMemo(() => {\n    const {\n      label = 'label',\n      value = 'value',\n      disabled = 'disabled',\n      children = 'children'\n    } = fieldNames;\n    return [label, value, children, disabled];\n  }, [JSON.stringify(fieldNames)]);\n  return fields;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}