{"ast": null, "code": "import * as React from \"react\";\nfunction AppOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppOutline-AppOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AppOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38.6673097,11.5277497 L27.3505965,4.90817356 C25.2828181,3.69727548 22.7306145,3.69727548 20.6583507,4.90817356 L9.33715206,11.5277497 C7.2693737,12.7386478 6,14.9675973 6,17.3804238 L6,30.6195762 C6,33.0324027 7.27385912,35.265837 9.33715206,36.4722503 L20.6538653,43.0918264 C22.7261291,44.3027245 25.2738473,44.3027245 27.3461111,43.0918264 L38.6628243,36.4722503 C40.7306027,35.265837 41.9999882,33.0324027 41.9999882,30.6195762 L41.9999882,17.3849086 C42.0044618,14.9675973 40.7350881,12.7386478 38.6673097,11.5277497 Z M8.62397037,17.3804238 C8.62397037,15.8914677 9.40443335,14.5191165 10.673807,13.7746384 L21.9591223,7.16851665 C23.2240106,6.42852338 24.7804512,6.42852338 26.0453394,7.16851665 L32.4908872,10.9447247 L12.9479147,22.3764996 L8.62397037,19.8470681 L8.62397037,17.3804238 Z M23.7398339,19.0622267 L32.4729455,24.1704227 L24.2466863,28.9826214 L15.5135746,23.8744254 L23.7398339,19.0622267 L23.7398339,19.0622267 Z M10.673807,34.1984527 C9.40443335,33.4584595 8.62397037,32.0816235 8.62397037,30.5926673 L8.62397037,22.8474044 L21.6810263,30.485032 L13.0017397,35.5618343 L10.673807,34.1984527 L10.673807,34.1984527 Z M39.3804914,30.5926673 C39.3804914,32.0816235 38.6000284,33.4539747 37.3306547,34.1984527 L26.0453394,40.8045745 C24.7804512,41.5445678 23.2240106,41.5445678 21.9591223,40.8045745 L15.5629142,37.0597601 L35.0386054,25.6683485 L39.3804914,28.2067496 L39.3804914,30.5926673 L39.3804914,30.5926673 Z M39.3804914,25.2108981 L35.6486224,23.0267967 L26.3054938,17.5643009 L35.0565471,12.4471353 L37.3306547,13.7791232 C38.6000284,14.5191165 39.3804914,15.8959525 39.3804914,17.3849086 L39.3804914,25.2108981 L39.3804914,25.2108981 Z\",\n    id: \"AppOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AppOutline;", "map": {"version": 3, "names": ["React", "AppOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/AppOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AppOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppOutline-AppOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AppOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AppOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38.6673097,11.5277497 L27.3505965,4.90817356 C25.2828181,3.69727548 22.7306145,3.69727548 20.6583507,4.90817356 L9.33715206,11.5277497 C7.2693737,12.7386478 6,14.9675973 6,17.3804238 L6,30.6195762 C6,33.0324027 7.27385912,35.265837 9.33715206,36.4722503 L20.6538653,43.0918264 C22.7261291,44.3027245 25.2738473,44.3027245 27.3461111,43.0918264 L38.6628243,36.4722503 C40.7306027,35.265837 41.9999882,33.0324027 41.9999882,30.6195762 L41.9999882,17.3849086 C42.0044618,14.9675973 40.7350881,12.7386478 38.6673097,11.5277497 Z M8.62397037,17.3804238 C8.62397037,15.8914677 9.40443335,14.5191165 10.673807,13.7746384 L21.9591223,7.16851665 C23.2240106,6.42852338 24.7804512,6.42852338 26.0453394,7.16851665 L32.4908872,10.9447247 L12.9479147,22.3764996 L8.62397037,19.8470681 L8.62397037,17.3804238 Z M23.7398339,19.0622267 L32.4729455,24.1704227 L24.2466863,28.9826214 L15.5135746,23.8744254 L23.7398339,19.0622267 L23.7398339,19.0622267 Z M10.673807,34.1984527 C9.40443335,33.4584595 8.62397037,32.0816235 8.62397037,30.5926673 L8.62397037,22.8474044 L21.6810263,30.485032 L13.0017397,35.5618343 L10.673807,34.1984527 L10.673807,34.1984527 Z M39.3804914,30.5926673 C39.3804914,32.0816235 38.6000284,33.4539747 37.3306547,34.1984527 L26.0453394,40.8045745 C24.7804512,41.5445678 23.2240106,41.5445678 21.9591223,40.8045745 L15.5629142,37.0597601 L35.0386054,25.6683485 L39.3804914,28.2067496 L39.3804914,30.5926673 L39.3804914,30.5926673 Z M39.3804914,25.2108981 L35.6486224,23.0267967 L26.3054938,17.5643009 L35.0565471,12.4471353 L37.3306547,13.7791232 C38.6000284,14.5191165 39.3804914,15.8959525 39.3804914,17.3849086 L39.3804914,25.2108981 L39.3804914,25.2108981 Z\",\n    id: \"AppOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AppOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,uBAAuB;IAC3BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,soDAAsoD;IACzoDR,EAAE,EAAE,yBAAyB;IAC7BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}