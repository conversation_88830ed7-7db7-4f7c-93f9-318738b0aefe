{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON>ton from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const DialogActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-dialog-button', {\n      'adm-dialog-button-bold': action.bold\n    }),\n    fill: 'none',\n    shape: 'rectangular',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}