{"ast": null, "code": "/**\n * 相機拍照策略實現\n * 根據不同的運行環境提供相應的拍照功能\n */\n\nimport { Toast } from 'antd-mobile';\n\n/**\n * 基礎相機策略抽象類\n */\nclass BaseCameraStrategy {\n  constructor() {\n    this.stream = null;\n    this.isActive = false;\n    this.callbacks = {};\n  }\n\n  /**\n   * 設置事件回調\n   */\n  setCallbacks(callbacks) {\n    this.callbacks = {\n      ...this.callbacks,\n      ...callbacks\n    };\n  }\n\n  /**\n   * 觸發事件回調\n   */\n  emit(event, data) {\n    if (this.callbacks[event]) {\n      this.callbacks[event](data);\n    }\n  }\n\n  /**\n   * 啟動相機 - 子類需要實現\n   */\n  async startCamera(constraints) {\n    throw new Error('startCamera method must be implemented');\n  }\n\n  /**\n   * 停止相機\n   */\n  stopCamera() {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    this.isActive = false;\n    this.emit('cameraStop');\n  }\n\n  /**\n   * 拍照 - 子類需要實現\n   */\n  async takePhoto() {\n    throw new Error('takePhoto method must be implemented');\n  }\n\n  /**\n   * 切換攝像頭\n   */\n  async switchCamera() {\n    // 默認實現，子類可以覆蓋\n    console.warn('Camera switching not implemented for this strategy');\n  }\n\n  /**\n   * 獲取相機狀態\n   */\n  getStatus() {\n    return {\n      isActive: this.isActive,\n      hasStream: !!this.stream\n    };\n  }\n}\n\n/**\n * Web端相機策略 - 使用Modal模式\n */\nexport class WebCameraStrategy extends BaseCameraStrategy {\n  constructor() {\n    super();\n    this.videoElement = null;\n    this.canvasElement = null;\n  }\n\n  /**\n   * 設置視頻和畫布元素\n   */\n  setElements(videoElement, canvasElement) {\n    this.videoElement = videoElement;\n    this.canvasElement = canvasElement;\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(constraints = {}) {\n    try {\n      const defaultConstraints = {\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1280\n          },\n          height: {\n            ideal: 720\n          }\n        }\n      };\n      const finalConstraints = {\n        ...defaultConstraints,\n        ...constraints\n      };\n      console.log('Web相機啟動中...', {\n        constraints: finalConstraints\n      });\n      this.stream = await navigator.mediaDevices.getUserMedia(finalConstraints);\n      if (this.videoElement) {\n        this.videoElement.srcObject = this.stream;\n\n        // 等待視頻元素準備就緒\n        await new Promise((resolve, reject) => {\n          const timeout = setTimeout(() => {\n            reject(new Error('視頻加載超時'));\n          }, 5000);\n          const onLoadedData = () => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            console.log('Web相機視頻加載完成', {\n              videoWidth: this.videoElement.videoWidth,\n              videoHeight: this.videoElement.videoHeight\n            });\n            resolve();\n          };\n          const onError = error => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            reject(error);\n          };\n          this.videoElement.addEventListener('loadeddata', onLoadedData);\n          this.videoElement.addEventListener('error', onError);\n        });\n      }\n      this.isActive = true;\n      this.emit('cameraStart', {\n        stream: this.stream\n      });\n      console.log('Web相機啟動成功');\n      return this.stream;\n    } catch (error) {\n      console.error('Web相機啟動失敗:', error);\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 檢查視頻是否準備就緒\n   */\n  isVideoReady() {\n    if (!this.videoElement) return false;\n    const video = this.videoElement;\n    return video.readyState >= 2 &&\n    // HAVE_CURRENT_DATA\n    video.videoWidth > 0 && video.videoHeight > 0 && !video.paused && !video.ended;\n  }\n\n  /**\n   * 等待視頻準備就緒\n   */\n  async waitForVideoReady(timeout = 5000) {\n    return new Promise((resolve, reject) => {\n      if (this.isVideoReady()) {\n        resolve();\n        return;\n      }\n      const startTime = Date.now();\n      const checkReady = () => {\n        if (this.isVideoReady()) {\n          resolve();\n        } else if (Date.now() - startTime > timeout) {\n          reject(new Error('視頻準備超時'));\n        } else {\n          setTimeout(checkReady, 100);\n        }\n      };\n\n      // 監聽視頻事件\n      if (this.videoElement) {\n        const onReady = () => {\n          if (this.isVideoReady()) {\n            this.videoElement.removeEventListener('loadeddata', onReady);\n            this.videoElement.removeEventListener('canplay', onReady);\n            resolve();\n          }\n        };\n        this.videoElement.addEventListener('loadeddata', onReady);\n        this.videoElement.addEventListener('canplay', onReady);\n      }\n      checkReady();\n    });\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    if (!this.videoElement || !this.canvasElement || !this.isActive) {\n      throw new Error('相機未準備就緒');\n    }\n    try {\n      // 等待視頻準備就緒\n      await this.waitForVideoReady();\n      const video = this.videoElement;\n      const canvas = this.canvasElement;\n      const context = canvas.getContext('2d');\n\n      // 檢查視頻尺寸\n      if (video.videoWidth === 0 || video.videoHeight === 0) {\n        throw new Error('視頻尺寸無效');\n      }\n\n      // 設置畫布尺寸\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      console.log(`Web拍照 - 視頻尺寸: ${video.videoWidth}x${video.videoHeight}`);\n\n      // 設置高質量渲染\n      context.imageSmoothingEnabled = true;\n      context.imageSmoothingQuality = 'high';\n\n      // 輕微圖像增強（適度，保持自然）\n      if (video.videoWidth >= 1280 && video.videoHeight >= 720) {\n        context.filter = 'contrast(1.04) brightness(1.02) saturate(1.02)';\n      }\n\n      // 繪製視頻幀到畫布\n      context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n      // 轉換為Blob，使用高質量設置\n      return new Promise((resolve, reject) => {\n        canvas.toBlob(blob => {\n          if (blob) {\n            const file = new File([blob], 'web-photo-enhanced.jpg', {\n              type: 'image/jpeg'\n            });\n            console.log(`Web拍照成功 - 文件大小: ${blob.size} bytes`);\n            this.emit('photoTaken', {\n              file,\n              blob\n            });\n            resolve({\n              file,\n              blob\n            });\n          } else {\n            reject(new Error('無法生成照片'));\n          }\n        }, 'image/jpeg', 0.92); // 稍微提高質量\n      });\n    } catch (error) {\n      console.error('Web拍照失敗:', error);\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n}\n\n/**\n * 移動端相機策略 - 全屏模式\n */\nexport class MobileCameraStrategy extends BaseCameraStrategy {\n  constructor() {\n    super();\n    this.videoElement = null;\n    this.canvasElement = null;\n    this.currentFacingMode = 'environment'; // 默認後置攝像頭\n    this.supportsFacingMode = true;\n  }\n\n  /**\n   * 設置視頻和畫布元素\n   */\n  setElements(videoElement, canvasElement) {\n    this.videoElement = videoElement;\n    this.canvasElement = canvasElement;\n  }\n\n  /**\n   * 獲取最佳相機約束\n   */\n  getBestCameraConstraints() {\n    // 嘗試獲取設備支持的最高解析度\n    const highResConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: {\n          ideal: 3840,\n          min: 1920\n        },\n        // 4K優先，最低1080p\n        height: {\n          ideal: 2160,\n          min: 1080\n        },\n        frameRate: {\n          ideal: 30,\n          min: 15\n        },\n        aspectRatio: {\n          ideal: 16 / 9\n        }\n      }\n    };\n    const standardConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: {\n          ideal: 2560,\n          min: 1280\n        },\n        // 2K優先，最低720p\n        height: {\n          ideal: 1440,\n          min: 720\n        },\n        frameRate: {\n          ideal: 30,\n          min: 15\n        }\n      }\n    };\n    const fallbackConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: {\n          ideal: 1920,\n          min: 1280\n        },\n        height: {\n          ideal: 1080,\n          min: 720\n        }\n      }\n    };\n    return {\n      highResConstraints,\n      standardConstraints,\n      fallbackConstraints\n    };\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(constraints = {}) {\n    try {\n      const {\n        highResConstraints,\n        standardConstraints,\n        fallbackConstraints\n      } = this.getBestCameraConstraints();\n\n      // 如果用戶提供了自定義約束，使用用戶約束\n      if (constraints.video) {\n        const finalConstraints = {\n          video: {\n            ...standardConstraints.video,\n            ...constraints.video\n          }\n        };\n        return await this.attemptCameraStart(finalConstraints);\n      }\n\n      // 嘗試不同解析度級別\n      const constraintLevels = [{\n        name: '4K/高解析度',\n        constraints: highResConstraints\n      }, {\n        name: '2K/標準解析度',\n        constraints: standardConstraints\n      }, {\n        name: '1080p/備用解析度',\n        constraints: fallbackConstraints\n      }];\n      for (const level of constraintLevels) {\n        try {\n          console.log(`移動端嘗試${level.name}相機啟動...`);\n          return await this.attemptCameraStart(level.constraints);\n        } catch (error) {\n          console.warn(`${level.name}啟動失敗，嘗試下一級別:`, error.message);\n          continue;\n        }\n      }\n\n      // 如果所有預設都失敗，嘗試基本約束\n      throw new Error('所有解析度級別都無法啟動相機');\n    } catch (error) {\n      console.error('移動端相機啟動失敗:', error);\n\n      // 如果指定的攝像頭模式失敗，嘗試基本模式\n      if (error.name === 'OverconstrainedError' && this.currentFacingMode !== 'user') {\n        try {\n          this.currentFacingMode = 'user';\n          return await this.startCamera({\n            video: {\n              facingMode: 'user'\n            }\n          });\n        } catch (fallbackError) {\n          this.supportsFacingMode = false;\n          return await this.startCamera({\n            video: true\n          });\n        }\n      }\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 嘗試啟動相機的核心方法\n   */\n  async attemptCameraStart(finalConstraints) {\n    try {\n      console.log('移動端相機啟動中...', {\n        constraints: finalConstraints\n      });\n      this.stream = await navigator.mediaDevices.getUserMedia(finalConstraints);\n\n      // 記錄實際獲得的解析度\n      if (this.stream) {\n        const videoTrack = this.stream.getVideoTracks()[0];\n        if (videoTrack) {\n          const settings = videoTrack.getSettings();\n          console.log('移動端相機實際解析度:', {\n            width: settings.width,\n            height: settings.height,\n            frameRate: settings.frameRate,\n            facingMode: settings.facingMode\n          });\n        }\n      }\n      if (this.videoElement) {\n        this.videoElement.srcObject = this.stream;\n\n        // 移動端優化：設置視頻屬性\n        this.videoElement.setAttribute('playsinline', true);\n        this.videoElement.setAttribute('webkit-playsinline', true);\n        this.videoElement.muted = true;\n\n        // 等待視頻元素準備就緒\n        await new Promise((resolve, reject) => {\n          const timeout = setTimeout(() => {\n            reject(new Error('移動端視頻加載超時'));\n          }, 8000); // 移動端給更多時間\n\n          const onLoadedData = () => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            console.log('移動端相機視頻加載完成', {\n              videoWidth: this.videoElement.videoWidth,\n              videoHeight: this.videoElement.videoHeight,\n              facingMode: this.currentFacingMode\n            });\n            resolve();\n          };\n          const onError = error => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            reject(error);\n          };\n          this.videoElement.addEventListener('loadeddata', onLoadedData);\n          this.videoElement.addEventListener('error', onError);\n        });\n      }\n      this.isActive = true;\n      this.emit('cameraStart', {\n        stream: this.stream,\n        facingMode: this.currentFacingMode\n      });\n      return this.stream;\n    } catch (error) {\n      console.error('移動端相機啟動失敗:', error);\n\n      // 如果指定的攝像頭模式失敗，嘗試基本模式\n      if (error.name === 'OverconstrainedError' && this.currentFacingMode !== 'user') {\n        try {\n          this.currentFacingMode = 'user';\n          return await this.startCamera({\n            video: {\n              facingMode: 'user'\n            }\n          });\n        } catch (fallbackError) {\n          this.supportsFacingMode = false;\n          return await this.startCamera({\n            video: true\n          });\n        }\n      }\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 切換前後攝像頭\n   */\n  async switchCamera() {\n    if (!this.supportsFacingMode) {\n      Toast.show({\n        content: '此設備不支持切換攝像頭',\n        position: 'center'\n      });\n      return;\n    }\n    try {\n      // 停止當前流\n      this.stopCamera();\n\n      // 切換攝像頭模式\n      this.currentFacingMode = this.currentFacingMode === 'environment' ? 'user' : 'environment';\n\n      // 重新啟動相機\n      await this.startCamera();\n      this.emit('cameraSwitch', {\n        facingMode: this.currentFacingMode\n      });\n      Toast.show({\n        content: `已切換到${this.currentFacingMode === 'environment' ? '後置' : '前置'}攝像頭`,\n        position: 'center'\n      });\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n      Toast.show({\n        content: '切換攝像頭失敗，請重試',\n        position: 'center'\n      });\n      this.emit('cameraSwitchError', error);\n    }\n  }\n\n  /**\n   * 檢查視頻是否準備就緒\n   */\n  isVideoReady() {\n    if (!this.videoElement) return false;\n    const video = this.videoElement;\n    return video.readyState >= 2 &&\n    // HAVE_CURRENT_DATA\n    video.videoWidth > 0 && video.videoHeight > 0 && !video.paused && !video.ended;\n  }\n\n  /**\n   * 等待視頻準備就緒並確保對焦穩定\n   */\n  async waitForVideoReady() {\n    const video = this.videoElement;\n    if (!video) {\n      throw new Error('視頻元素不存在');\n    }\n    return new Promise((resolve, reject) => {\n      const timeout = setTimeout(() => {\n        reject(new Error('視頻準備超時'));\n      }, 10000);\n      const checkReady = () => {\n        if (video.readyState >= 2 && video.videoWidth > 0 && video.videoHeight > 0) {\n          clearTimeout(timeout);\n          resolve();\n        } else {\n          setTimeout(checkReady, 50);\n        }\n      };\n      checkReady();\n    });\n  }\n\n  /**\n   * 確保拍攝穩定性\n   */\n  async ensureStability() {\n    // 等待設備穩定（減少手震影響）\n    await new Promise(resolve => setTimeout(resolve, 300));\n\n    // 檢查視頻流是否穩定\n    const video = this.videoElement;\n    if (video && video.srcObject) {\n      const tracks = video.srcObject.getVideoTracks();\n      if (tracks.length > 0) {\n        const track = tracks[0];\n        const settings = track.getSettings();\n\n        // 記錄當前設置\n        console.log('拍攝穩定性檢查:', {\n          width: settings.width,\n          height: settings.height,\n          frameRate: settings.frameRate,\n          readyState: video.readyState\n        });\n      }\n    }\n  }\n\n  /**\n   * 優化畫布渲染設置\n   */\n  optimizeCanvasRendering(context, videoWidth, videoHeight) {\n    // 啟用高質量渲染\n    context.imageSmoothingEnabled = true;\n    context.imageSmoothingQuality = 'high';\n\n    // 針對高解析度進行優化\n    if (videoWidth >= 2560 || videoHeight >= 1440) {\n      // 高解析度時使用更精細的渲染設置\n      context.globalCompositeOperation = 'source-over';\n\n      // 輕微增強對比度和銳化，有利於OCR識別\n      context.filter = 'contrast(1.08) brightness(1.03) saturate(1.05) sepia(0) hue-rotate(0deg)';\n    } else if (videoWidth >= 1920 || videoHeight >= 1080) {\n      // 中等解析度優化\n      context.filter = 'contrast(1.06) brightness(1.02) saturate(1.03)';\n    } else {\n      // 標準解析度優化\n      context.filter = 'contrast(1.04) brightness(1.01) saturate(1.02)';\n    }\n  }\n\n  /**\n   * 高級圖像後處理\n   */\n  async postProcessImage(canvas, context) {\n    // 創建臨時畫布進行圖像處理\n    const tempCanvas = document.createElement('canvas');\n    const tempContext = tempCanvas.getContext('2d');\n    tempCanvas.width = canvas.width;\n    tempCanvas.height = canvas.height;\n\n    // 複製原圖像\n    tempContext.drawImage(canvas, 0, 0);\n\n    // 獲取圖像數據\n    const imageData = tempContext.getImageData(0, 0, canvas.width, canvas.height);\n    const data = imageData.data;\n\n    // 輕微銳化處理（增強文字邊緣）\n    const sharpenKernel = [0, -0.2, 0, -0.2, 1.8, -0.2, 0, -0.2, 0];\n\n    // 應用銳化濾鏡（僅對高解析度圖像）\n    if (canvas.width >= 1920 && canvas.height >= 1080) {\n      this.applySharpenFilter(data, canvas.width, canvas.height, sharpenKernel);\n    }\n\n    // 輕微對比度增強\n    for (let i = 0; i < data.length; i += 4) {\n      // 增強對比度，但保持自然度\n      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.15 + 128)); // R\n      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.15 + 128)); // G\n      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.15 + 128)); // B\n    }\n\n    // 將處理後的數據重新繪製到畫布\n    tempContext.putImageData(imageData, 0, 0);\n    context.clearRect(0, 0, canvas.width, canvas.height);\n    context.drawImage(tempCanvas, 0, 0);\n    return canvas;\n  }\n\n  /**\n   * 應用銳化濾鏡\n   */\n  applySharpenFilter(data, width, height, kernel) {\n    const tempData = new Uint8ClampedArray(data);\n    for (let y = 1; y < height - 1; y++) {\n      for (let x = 1; x < width - 1; x++) {\n        for (let c = 0; c < 3; c++) {\n          // RGB通道\n          let sum = 0;\n          for (let ky = -1; ky <= 1; ky++) {\n            for (let kx = -1; kx <= 1; kx++) {\n              const idx = ((y + ky) * width + (x + kx)) * 4 + c;\n              sum += tempData[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n            }\n          }\n          const idx = (y * width + x) * 4 + c;\n          data[idx] = Math.min(255, Math.max(0, sum));\n        }\n      }\n    }\n  }\n\n  /**\n   * 獲取最佳圖片質量設置\n   */\n  getOptimalImageQuality(videoWidth, videoHeight) {\n    const totalPixels = videoWidth * videoHeight;\n\n    // 根據解析度動態調整質量和壓縮策略\n    if (totalPixels >= 3840 * 2160) {\n      // 4K及以上\n      return {\n        quality: 0.98,\n        // 最高質量\n        maxFileSize: 8 * 1024 * 1024,\n        // 8MB\n        compressionLevel: 'minimal'\n      };\n    } else if (totalPixels >= 2560 * 1440) {\n      // 2K\n      return {\n        quality: 0.97,\n        maxFileSize: 6 * 1024 * 1024,\n        // 6MB\n        compressionLevel: 'low'\n      };\n    } else if (totalPixels >= 1920 * 1080) {\n      // 1080p\n      return {\n        quality: 0.96,\n        maxFileSize: 4 * 1024 * 1024,\n        // 4MB\n        compressionLevel: 'medium'\n      };\n    } else {\n      // 720p及以下\n      return {\n        quality: 0.95,\n        maxFileSize: 2 * 1024 * 1024,\n        // 2MB\n        compressionLevel: 'standard'\n      };\n    }\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    if (!this.videoElement || !this.canvasElement || !this.isActive) {\n      throw new Error('相機未準備就緒');\n    }\n    try {\n      // 確保拍攝穩定性\n      await this.ensureStability();\n\n      // 等待視頻準備就緒\n      await this.waitForVideoReady();\n      const video = this.videoElement;\n      const canvas = this.canvasElement;\n      const context = canvas.getContext('2d');\n\n      // 檢查視頻尺寸\n      if (video.videoWidth === 0 || video.videoHeight === 0) {\n        throw new Error('視頻尺寸無效');\n      }\n\n      // 設置畫布尺寸為視頻的實際尺寸\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n      console.log(`移動端拍照 - 視頻尺寸: ${video.videoWidth}x${video.videoHeight}, 攝像頭: ${this.currentFacingMode}`);\n\n      // 優化畫布渲染設置\n      this.optimizeCanvasRendering(context, video.videoWidth, video.videoHeight);\n\n      // 繪製視頻幀到畫布\n      context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n      // 進行圖像後處理\n      await this.postProcessImage(canvas, context);\n\n      // 獲取最佳圖片質量設置\n      const qualitySettings = this.getOptimalImageQuality(video.videoWidth, video.videoHeight);\n      console.log(`移動端拍照質量設置:`, qualitySettings);\n\n      // 移動端優化：動態質量壓縮\n      return new Promise((resolve, reject) => {\n        canvas.toBlob(blob => {\n          if (blob) {\n            // 檢查文件大小，如果超過限制則降低質量重新壓縮\n            if (blob.size > qualitySettings.maxFileSize && qualitySettings.quality > 0.85) {\n              console.log(`文件過大 (${blob.size} bytes)，重新壓縮...`);\n\n              // 降低質量重新壓縮\n              const reducedQuality = Math.max(0.85, qualitySettings.quality - 0.1);\n              canvas.toBlob(reducedBlob => {\n                if (reducedBlob) {\n                  const file = new File([reducedBlob], 'mobile-photo-enhanced.jpg', {\n                    type: 'image/jpeg'\n                  });\n                  console.log(`移動端拍照成功 (重新壓縮) - 文件大小: ${reducedBlob.size} bytes, 質量: ${reducedQuality}, 攝像頭: ${this.currentFacingMode}`);\n                  this.emit('photoTaken', {\n                    file,\n                    blob: reducedBlob,\n                    facingMode: this.currentFacingMode\n                  });\n                  resolve({\n                    file,\n                    blob: reducedBlob,\n                    facingMode: this.currentFacingMode\n                  });\n                } else {\n                  reject(new Error('重新壓縮失敗'));\n                }\n              }, 'image/jpeg', reducedQuality);\n            } else {\n              const file = new File([blob], 'mobile-photo-enhanced.jpg', {\n                type: 'image/jpeg'\n              });\n              console.log(`移動端拍照成功 - 文件大小: ${blob.size} bytes, 質量: ${qualitySettings.quality}, 攝像頭: ${this.currentFacingMode}`);\n              this.emit('photoTaken', {\n                file,\n                blob,\n                facingMode: this.currentFacingMode\n              });\n              resolve({\n                file,\n                blob,\n                facingMode: this.currentFacingMode\n              });\n            }\n          } else {\n            reject(new Error('無法生成照片'));\n          }\n        }, 'image/jpeg', qualitySettings.quality);\n      });\n    } catch (error) {\n      console.error('移動端拍照失敗:', error);\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 獲取當前攝像頭模式\n   */\n  getCurrentFacingMode() {\n    return this.currentFacingMode;\n  }\n\n  /**\n   * 檢查是否支持攝像頭切換\n   */\n  supportsCameraSwitch() {\n    return this.supportsFacingMode;\n  }\n}\n\n/**\n * 策略工廠函數\n */\nexport const createCameraStrategy = mode => {\n  switch (mode) {\n    case 'web':\n      return new WebCameraStrategy();\n    case 'mobile':\n      return new MobileCameraStrategy();\n    default:\n      throw new Error(`不支持的相機模式: ${mode}`);\n  }\n};\nexport default {\n  WebCameraStrategy,\n  MobileCameraStrategy,\n  createCameraStrategy\n};", "map": {"version": 3, "names": ["Toast", "BaseCameraStrategy", "constructor", "stream", "isActive", "callbacks", "setCallbacks", "emit", "event", "data", "startCamera", "constraints", "Error", "stopCamera", "getTracks", "for<PERSON>ach", "track", "stop", "<PERSON><PERSON><PERSON><PERSON>", "switchCamera", "console", "warn", "getStatus", "hasStream", "WebCameraStrategy", "videoElement", "canvasElement", "setElements", "defaultConstraints", "video", "facingMode", "width", "ideal", "height", "finalConstraints", "log", "navigator", "mediaDevices", "getUserMedia", "srcObject", "Promise", "resolve", "reject", "timeout", "setTimeout", "onLoadedData", "clearTimeout", "removeEventListener", "onError", "videoWidth", "videoHeight", "error", "addEventListener", "isVideoReady", "readyState", "paused", "ended", "waitForVideoReady", "startTime", "Date", "now", "checkReady", "onReady", "canvas", "context", "getContext", "imageSmoothingEnabled", "imageSmoothingQuality", "filter", "drawImage", "toBlob", "blob", "file", "File", "type", "size", "MobileCameraStrategy", "currentFacingMode", "supportsFacingMode", "getBestCameraConstraints", "highResConstraints", "min", "frameRate", "aspectRatio", "standardConstraints", "fallbackConstraints", "attemptCameraStart", "constraintLevels", "name", "level", "message", "fallback<PERSON><PERSON>r", "videoTrack", "getVideoTracks", "settings", "getSettings", "setAttribute", "muted", "show", "content", "position", "ensureStability", "tracks", "length", "optimizeCanvasRendering", "globalCompositeOperation", "postProcessImage", "tempCanvas", "document", "createElement", "tempContext", "imageData", "getImageData", "sharpen<PERSON><PERSON>l", "applySharpenFilter", "i", "Math", "max", "putImageData", "clearRect", "kernel", "tempData", "Uint8ClampedArray", "y", "x", "c", "sum", "ky", "kx", "idx", "getOptimalImageQuality", "totalPixels", "quality", "maxFileSize", "compressionLevel", "qualitySettings", "reducedQuality", "reducedBlob", "getCurrentFacingMode", "supportsCameraSwitch", "createCameraStrategy", "mode"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v3/src/utils/cameraStrategies.js"], "sourcesContent": ["/**\n * 相機拍照策略實現\n * 根據不同的運行環境提供相應的拍照功能\n */\n\nimport { Toast } from 'antd-mobile';\n\n/**\n * 基礎相機策略抽象類\n */\nclass BaseCameraStrategy {\n  constructor() {\n    this.stream = null;\n    this.isActive = false;\n    this.callbacks = {};\n  }\n\n  /**\n   * 設置事件回調\n   */\n  setCallbacks(callbacks) {\n    this.callbacks = { ...this.callbacks, ...callbacks };\n  }\n\n  /**\n   * 觸發事件回調\n   */\n  emit(event, data) {\n    if (this.callbacks[event]) {\n      this.callbacks[event](data);\n    }\n  }\n\n  /**\n   * 啟動相機 - 子類需要實現\n   */\n  async startCamera(constraints) {\n    throw new Error('startCamera method must be implemented');\n  }\n\n  /**\n   * 停止相機\n   */\n  stopCamera() {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    this.isActive = false;\n    this.emit('cameraStop');\n  }\n\n  /**\n   * 拍照 - 子類需要實現\n   */\n  async takePhoto() {\n    throw new Error('takePhoto method must be implemented');\n  }\n\n  /**\n   * 切換攝像頭\n   */\n  async switchCamera() {\n    // 默認實現，子類可以覆蓋\n    console.warn('Camera switching not implemented for this strategy');\n  }\n\n  /**\n   * 獲取相機狀態\n   */\n  getStatus() {\n    return {\n      isActive: this.isActive,\n      hasStream: !!this.stream\n    };\n  }\n}\n\n/**\n * Web端相機策略 - 使用Modal模式\n */\nexport class WebCameraStrategy extends BaseCameraStrategy {\n  constructor() {\n    super();\n    this.videoElement = null;\n    this.canvasElement = null;\n  }\n\n  /**\n   * 設置視頻和畫布元素\n   */\n  setElements(videoElement, canvasElement) {\n    this.videoElement = videoElement;\n    this.canvasElement = canvasElement;\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(constraints = {}) {\n    try {\n      const defaultConstraints = {\n        video: {\n          facingMode: 'environment',\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        }\n      };\n\n      const finalConstraints = {\n        ...defaultConstraints,\n        ...constraints\n      };\n\n      console.log('Web相機啟動中...', { constraints: finalConstraints });\n\n      this.stream = await navigator.mediaDevices.getUserMedia(finalConstraints);\n\n      if (this.videoElement) {\n        this.videoElement.srcObject = this.stream;\n\n        // 等待視頻元素準備就緒\n        await new Promise((resolve, reject) => {\n          const timeout = setTimeout(() => {\n            reject(new Error('視頻加載超時'));\n          }, 5000);\n\n          const onLoadedData = () => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            console.log('Web相機視頻加載完成', {\n              videoWidth: this.videoElement.videoWidth,\n              videoHeight: this.videoElement.videoHeight\n            });\n            resolve();\n          };\n\n          const onError = (error) => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            reject(error);\n          };\n\n          this.videoElement.addEventListener('loadeddata', onLoadedData);\n          this.videoElement.addEventListener('error', onError);\n        });\n      }\n\n      this.isActive = true;\n      this.emit('cameraStart', { stream: this.stream });\n\n      console.log('Web相機啟動成功');\n      return this.stream;\n    } catch (error) {\n      console.error('Web相機啟動失敗:', error);\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 檢查視頻是否準備就緒\n   */\n  isVideoReady() {\n    if (!this.videoElement) return false;\n\n    const video = this.videoElement;\n    return video.readyState >= 2 && // HAVE_CURRENT_DATA\n           video.videoWidth > 0 &&\n           video.videoHeight > 0 &&\n           !video.paused &&\n           !video.ended;\n  }\n\n  /**\n   * 等待視頻準備就緒\n   */\n  async waitForVideoReady(timeout = 5000) {\n    return new Promise((resolve, reject) => {\n      if (this.isVideoReady()) {\n        resolve();\n        return;\n      }\n\n      const startTime = Date.now();\n      const checkReady = () => {\n        if (this.isVideoReady()) {\n          resolve();\n        } else if (Date.now() - startTime > timeout) {\n          reject(new Error('視頻準備超時'));\n        } else {\n          setTimeout(checkReady, 100);\n        }\n      };\n\n      // 監聽視頻事件\n      if (this.videoElement) {\n        const onReady = () => {\n          if (this.isVideoReady()) {\n            this.videoElement.removeEventListener('loadeddata', onReady);\n            this.videoElement.removeEventListener('canplay', onReady);\n            resolve();\n          }\n        };\n\n        this.videoElement.addEventListener('loadeddata', onReady);\n        this.videoElement.addEventListener('canplay', onReady);\n      }\n\n      checkReady();\n    });\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    if (!this.videoElement || !this.canvasElement || !this.isActive) {\n      throw new Error('相機未準備就緒');\n    }\n\n    try {\n      // 等待視頻準備就緒\n      await this.waitForVideoReady();\n\n      const video = this.videoElement;\n      const canvas = this.canvasElement;\n      const context = canvas.getContext('2d');\n\n      // 檢查視頻尺寸\n      if (video.videoWidth === 0 || video.videoHeight === 0) {\n        throw new Error('視頻尺寸無效');\n      }\n\n      // 設置畫布尺寸\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n\n      console.log(`Web拍照 - 視頻尺寸: ${video.videoWidth}x${video.videoHeight}`);\n\n      // 設置高質量渲染\n      context.imageSmoothingEnabled = true;\n      context.imageSmoothingQuality = 'high';\n      \n      // 輕微圖像增強（適度，保持自然）\n      if (video.videoWidth >= 1280 && video.videoHeight >= 720) {\n        context.filter = 'contrast(1.04) brightness(1.02) saturate(1.02)';\n      }\n\n      // 繪製視頻幀到畫布\n      context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n      // 轉換為Blob，使用高質量設置\n      return new Promise((resolve, reject) => {\n        canvas.toBlob((blob) => {\n          if (blob) {\n            const file = new File([blob], 'web-photo-enhanced.jpg', { type: 'image/jpeg' });\n            console.log(`Web拍照成功 - 文件大小: ${blob.size} bytes`);\n            this.emit('photoTaken', { file, blob });\n            resolve({ file, blob });\n          } else {\n            reject(new Error('無法生成照片'));\n          }\n        }, 'image/jpeg', 0.92); // 稍微提高質量\n      });\n    } catch (error) {\n      console.error('Web拍照失敗:', error);\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n}\n\n/**\n * 移動端相機策略 - 全屏模式\n */\nexport class MobileCameraStrategy extends BaseCameraStrategy {\n  constructor() {\n    super();\n    this.videoElement = null;\n    this.canvasElement = null;\n    this.currentFacingMode = 'environment'; // 默認後置攝像頭\n    this.supportsFacingMode = true;\n  }\n\n  /**\n   * 設置視頻和畫布元素\n   */\n  setElements(videoElement, canvasElement) {\n    this.videoElement = videoElement;\n    this.canvasElement = canvasElement;\n  }\n\n  /**\n   * 獲取最佳相機約束\n   */\n  getBestCameraConstraints() {\n    // 嘗試獲取設備支持的最高解析度\n    const highResConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: { ideal: 3840, min: 1920 }, // 4K優先，最低1080p\n        height: { ideal: 2160, min: 1080 },\n        frameRate: { ideal: 30, min: 15 },\n        aspectRatio: { ideal: 16/9 }\n      }\n    };\n\n    const standardConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: { ideal: 2560, min: 1280 }, // 2K優先，最低720p\n        height: { ideal: 1440, min: 720 },\n        frameRate: { ideal: 30, min: 15 }\n      }\n    };\n\n    const fallbackConstraints = {\n      video: {\n        facingMode: this.currentFacingMode,\n        width: { ideal: 1920, min: 1280 },\n        height: { ideal: 1080, min: 720 }\n      }\n    };\n\n    return { highResConstraints, standardConstraints, fallbackConstraints };\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(constraints = {}) {\n    try {\n      const { highResConstraints, standardConstraints, fallbackConstraints } = this.getBestCameraConstraints();\n\n      // 如果用戶提供了自定義約束，使用用戶約束\n      if (constraints.video) {\n        const finalConstraints = {\n          video: {\n            ...standardConstraints.video,\n            ...constraints.video\n          }\n        };\n        return await this.attemptCameraStart(finalConstraints);\n      }\n\n      // 嘗試不同解析度級別\n      const constraintLevels = [\n        { name: '4K/高解析度', constraints: highResConstraints },\n        { name: '2K/標準解析度', constraints: standardConstraints },\n        { name: '1080p/備用解析度', constraints: fallbackConstraints }\n      ];\n\n      for (const level of constraintLevels) {\n        try {\n          console.log(`移動端嘗試${level.name}相機啟動...`);\n          return await this.attemptCameraStart(level.constraints);\n        } catch (error) {\n          console.warn(`${level.name}啟動失敗，嘗試下一級別:`, error.message);\n          continue;\n        }\n      }\n\n      // 如果所有預設都失敗，嘗試基本約束\n      throw new Error('所有解析度級別都無法啟動相機');\n\n    } catch (error) {\n      console.error('移動端相機啟動失敗:', error);\n\n      // 如果指定的攝像頭模式失敗，嘗試基本模式\n      if (error.name === 'OverconstrainedError' && this.currentFacingMode !== 'user') {\n        try {\n          this.currentFacingMode = 'user';\n          return await this.startCamera({ video: { facingMode: 'user' } });\n        } catch (fallbackError) {\n          this.supportsFacingMode = false;\n          return await this.startCamera({ video: true });\n        }\n      }\n\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 嘗試啟動相機的核心方法\n   */\n  async attemptCameraStart(finalConstraints) {\n    try {\n      console.log('移動端相機啟動中...', { constraints: finalConstraints });\n\n      this.stream = await navigator.mediaDevices.getUserMedia(finalConstraints);\n\n      // 記錄實際獲得的解析度\n      if (this.stream) {\n        const videoTrack = this.stream.getVideoTracks()[0];\n        if (videoTrack) {\n          const settings = videoTrack.getSettings();\n          console.log('移動端相機實際解析度:', {\n            width: settings.width,\n            height: settings.height,\n            frameRate: settings.frameRate,\n            facingMode: settings.facingMode\n          });\n        }\n      }\n      \n      if (this.videoElement) {\n        this.videoElement.srcObject = this.stream;\n\n        // 移動端優化：設置視頻屬性\n        this.videoElement.setAttribute('playsinline', true);\n        this.videoElement.setAttribute('webkit-playsinline', true);\n        this.videoElement.muted = true;\n\n        // 等待視頻元素準備就緒\n        await new Promise((resolve, reject) => {\n          const timeout = setTimeout(() => {\n            reject(new Error('移動端視頻加載超時'));\n          }, 8000); // 移動端給更多時間\n\n          const onLoadedData = () => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            console.log('移動端相機視頻加載完成', {\n              videoWidth: this.videoElement.videoWidth,\n              videoHeight: this.videoElement.videoHeight,\n              facingMode: this.currentFacingMode\n            });\n            resolve();\n          };\n\n          const onError = (error) => {\n            clearTimeout(timeout);\n            this.videoElement.removeEventListener('loadeddata', onLoadedData);\n            this.videoElement.removeEventListener('error', onError);\n            reject(error);\n          };\n\n          this.videoElement.addEventListener('loadeddata', onLoadedData);\n          this.videoElement.addEventListener('error', onError);\n        });\n      }\n\n      this.isActive = true;\n      this.emit('cameraStart', { \n        stream: this.stream, \n        facingMode: this.currentFacingMode \n      });\n      \n      return this.stream;\n    } catch (error) {\n      console.error('移動端相機啟動失敗:', error);\n      \n      // 如果指定的攝像頭模式失敗，嘗試基本模式\n      if (error.name === 'OverconstrainedError' && this.currentFacingMode !== 'user') {\n        try {\n          this.currentFacingMode = 'user';\n          return await this.startCamera({ video: { facingMode: 'user' } });\n        } catch (fallbackError) {\n          this.supportsFacingMode = false;\n          return await this.startCamera({ video: true });\n        }\n      }\n      \n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 切換前後攝像頭\n   */\n  async switchCamera() {\n    if (!this.supportsFacingMode) {\n      Toast.show({\n        content: '此設備不支持切換攝像頭',\n        position: 'center'\n      });\n      return;\n    }\n\n    try {\n      // 停止當前流\n      this.stopCamera();\n      \n      // 切換攝像頭模式\n      this.currentFacingMode = this.currentFacingMode === 'environment' ? 'user' : 'environment';\n      \n      // 重新啟動相機\n      await this.startCamera();\n      \n      this.emit('cameraSwitch', { facingMode: this.currentFacingMode });\n      \n      Toast.show({\n        content: `已切換到${this.currentFacingMode === 'environment' ? '後置' : '前置'}攝像頭`,\n        position: 'center'\n      });\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n      Toast.show({\n        content: '切換攝像頭失敗，請重試',\n        position: 'center'\n      });\n      this.emit('cameraSwitchError', error);\n    }\n  }\n\n  /**\n   * 檢查視頻是否準備就緒\n   */\n  isVideoReady() {\n    if (!this.videoElement) return false;\n\n    const video = this.videoElement;\n    return video.readyState >= 2 && // HAVE_CURRENT_DATA\n           video.videoWidth > 0 &&\n           video.videoHeight > 0 &&\n           !video.paused &&\n           !video.ended;\n  }\n\n  /**\n   * 等待視頻準備就緒並確保對焦穩定\n   */\n  async waitForVideoReady() {\n    const video = this.videoElement;\n    if (!video) {\n      throw new Error('視頻元素不存在');\n    }\n\n    return new Promise((resolve, reject) => {\n      const timeout = setTimeout(() => {\n        reject(new Error('視頻準備超時'));\n      }, 10000);\n\n      const checkReady = () => {\n        if (video.readyState >= 2 && video.videoWidth > 0 && video.videoHeight > 0) {\n          clearTimeout(timeout);\n          resolve();\n        } else {\n          setTimeout(checkReady, 50);\n        }\n      };\n\n      checkReady();\n    });\n  }\n\n  /**\n   * 確保拍攝穩定性\n   */\n  async ensureStability() {\n    // 等待設備穩定（減少手震影響）\n    await new Promise(resolve => setTimeout(resolve, 300));\n    \n    // 檢查視頻流是否穩定\n    const video = this.videoElement;\n    if (video && video.srcObject) {\n      const tracks = video.srcObject.getVideoTracks();\n      if (tracks.length > 0) {\n        const track = tracks[0];\n        const settings = track.getSettings();\n        \n        // 記錄當前設置\n        console.log('拍攝穩定性檢查:', {\n          width: settings.width,\n          height: settings.height,\n          frameRate: settings.frameRate,\n          readyState: video.readyState\n        });\n      }\n    }\n  }\n\n  /**\n   * 優化畫布渲染設置\n   */\n  optimizeCanvasRendering(context, videoWidth, videoHeight) {\n    // 啟用高質量渲染\n    context.imageSmoothingEnabled = true;\n    context.imageSmoothingQuality = 'high';\n\n    // 針對高解析度進行優化\n    if (videoWidth >= 2560 || videoHeight >= 1440) {\n      // 高解析度時使用更精細的渲染設置\n      context.globalCompositeOperation = 'source-over';\n      \n      // 輕微增強對比度和銳化，有利於OCR識別\n      context.filter = 'contrast(1.08) brightness(1.03) saturate(1.05) sepia(0) hue-rotate(0deg)';\n    } else if (videoWidth >= 1920 || videoHeight >= 1080) {\n      // 中等解析度優化\n      context.filter = 'contrast(1.06) brightness(1.02) saturate(1.03)';\n    } else {\n      // 標準解析度優化\n      context.filter = 'contrast(1.04) brightness(1.01) saturate(1.02)';\n    }\n  }\n\n  /**\n   * 高級圖像後處理\n   */\n  async postProcessImage(canvas, context) {\n    // 創建臨時畫布進行圖像處理\n    const tempCanvas = document.createElement('canvas');\n    const tempContext = tempCanvas.getContext('2d');\n    \n    tempCanvas.width = canvas.width;\n    tempCanvas.height = canvas.height;\n    \n    // 複製原圖像\n    tempContext.drawImage(canvas, 0, 0);\n    \n    // 獲取圖像數據\n    const imageData = tempContext.getImageData(0, 0, canvas.width, canvas.height);\n    const data = imageData.data;\n    \n    // 輕微銳化處理（增強文字邊緣）\n    const sharpenKernel = [\n      0, -0.2, 0,\n      -0.2, 1.8, -0.2,\n      0, -0.2, 0\n    ];\n    \n    // 應用銳化濾鏡（僅對高解析度圖像）\n    if (canvas.width >= 1920 && canvas.height >= 1080) {\n      this.applySharpenFilter(data, canvas.width, canvas.height, sharpenKernel);\n    }\n    \n    // 輕微對比度增強\n    for (let i = 0; i < data.length; i += 4) {\n      // 增強對比度，但保持自然度\n      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.15 + 128));     // R\n      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.15 + 128)); // G\n      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.15 + 128)); // B\n    }\n    \n    // 將處理後的數據重新繪製到畫布\n    tempContext.putImageData(imageData, 0, 0);\n    context.clearRect(0, 0, canvas.width, canvas.height);\n    context.drawImage(tempCanvas, 0, 0);\n    \n    return canvas;\n  }\n\n  /**\n   * 應用銳化濾鏡\n   */\n  applySharpenFilter(data, width, height, kernel) {\n    const tempData = new Uint8ClampedArray(data);\n    \n    for (let y = 1; y < height - 1; y++) {\n      for (let x = 1; x < width - 1; x++) {\n        for (let c = 0; c < 3; c++) { // RGB通道\n          let sum = 0;\n          for (let ky = -1; ky <= 1; ky++) {\n            for (let kx = -1; kx <= 1; kx++) {\n              const idx = ((y + ky) * width + (x + kx)) * 4 + c;\n              sum += tempData[idx] * kernel[(ky + 1) * 3 + (kx + 1)];\n            }\n          }\n          const idx = (y * width + x) * 4 + c;\n          data[idx] = Math.min(255, Math.max(0, sum));\n        }\n      }\n    }\n  }\n\n  /**\n   * 獲取最佳圖片質量設置\n   */\n  getOptimalImageQuality(videoWidth, videoHeight) {\n    const totalPixels = videoWidth * videoHeight;\n\n    // 根據解析度動態調整質量和壓縮策略\n    if (totalPixels >= 3840 * 2160) { // 4K及以上\n      return {\n        quality: 0.98, // 最高質量\n        maxFileSize: 8 * 1024 * 1024, // 8MB\n        compressionLevel: 'minimal'\n      };\n    } else if (totalPixels >= 2560 * 1440) { // 2K\n      return {\n        quality: 0.97,\n        maxFileSize: 6 * 1024 * 1024, // 6MB\n        compressionLevel: 'low'\n      };\n    } else if (totalPixels >= 1920 * 1080) { // 1080p\n      return {\n        quality: 0.96,\n        maxFileSize: 4 * 1024 * 1024, // 4MB\n        compressionLevel: 'medium'\n      };\n    } else { // 720p及以下\n      return {\n        quality: 0.95,\n        maxFileSize: 2 * 1024 * 1024, // 2MB\n        compressionLevel: 'standard'\n      };\n    }\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    if (!this.videoElement || !this.canvasElement || !this.isActive) {\n      throw new Error('相機未準備就緒');\n    }\n\n    try {\n      // 確保拍攝穩定性\n      await this.ensureStability();\n      \n      // 等待視頻準備就緒\n      await this.waitForVideoReady();\n\n      const video = this.videoElement;\n      const canvas = this.canvasElement;\n      const context = canvas.getContext('2d');\n\n      // 檢查視頻尺寸\n      if (video.videoWidth === 0 || video.videoHeight === 0) {\n        throw new Error('視頻尺寸無效');\n      }\n\n      // 設置畫布尺寸為視頻的實際尺寸\n      canvas.width = video.videoWidth;\n      canvas.height = video.videoHeight;\n\n      console.log(`移動端拍照 - 視頻尺寸: ${video.videoWidth}x${video.videoHeight}, 攝像頭: ${this.currentFacingMode}`);\n\n      // 優化畫布渲染設置\n      this.optimizeCanvasRendering(context, video.videoWidth, video.videoHeight);\n\n      // 繪製視頻幀到畫布\n      context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n      // 進行圖像後處理\n      await this.postProcessImage(canvas, context);\n\n      // 獲取最佳圖片質量設置\n      const qualitySettings = this.getOptimalImageQuality(video.videoWidth, video.videoHeight);\n\n      console.log(`移動端拍照質量設置:`, qualitySettings);\n\n      // 移動端優化：動態質量壓縮\n      return new Promise((resolve, reject) => {\n        canvas.toBlob((blob) => {\n          if (blob) {\n            // 檢查文件大小，如果超過限制則降低質量重新壓縮\n            if (blob.size > qualitySettings.maxFileSize && qualitySettings.quality > 0.85) {\n              console.log(`文件過大 (${blob.size} bytes)，重新壓縮...`);\n\n              // 降低質量重新壓縮\n              const reducedQuality = Math.max(0.85, qualitySettings.quality - 0.1);\n              canvas.toBlob((reducedBlob) => {\n                if (reducedBlob) {\n                  const file = new File([reducedBlob], 'mobile-photo-enhanced.jpg', { type: 'image/jpeg' });\n                  console.log(`移動端拍照成功 (重新壓縮) - 文件大小: ${reducedBlob.size} bytes, 質量: ${reducedQuality}, 攝像頭: ${this.currentFacingMode}`);\n                  this.emit('photoTaken', { file, blob: reducedBlob, facingMode: this.currentFacingMode });\n                  resolve({ file, blob: reducedBlob, facingMode: this.currentFacingMode });\n                } else {\n                  reject(new Error('重新壓縮失敗'));\n                }\n              }, 'image/jpeg', reducedQuality);\n            } else {\n              const file = new File([blob], 'mobile-photo-enhanced.jpg', { type: 'image/jpeg' });\n              console.log(`移動端拍照成功 - 文件大小: ${blob.size} bytes, 質量: ${qualitySettings.quality}, 攝像頭: ${this.currentFacingMode}`);\n              this.emit('photoTaken', { file, blob, facingMode: this.currentFacingMode });\n              resolve({ file, blob, facingMode: this.currentFacingMode });\n            }\n          } else {\n            reject(new Error('無法生成照片'));\n          }\n        }, 'image/jpeg', qualitySettings.quality);\n      });\n    } catch (error) {\n      console.error('移動端拍照失敗:', error);\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 獲取當前攝像頭模式\n   */\n  getCurrentFacingMode() {\n    return this.currentFacingMode;\n  }\n\n  /**\n   * 檢查是否支持攝像頭切換\n   */\n  supportsCameraSwitch() {\n    return this.supportsFacingMode;\n  }\n}\n\n/**\n * 策略工廠函數\n */\nexport const createCameraStrategy = (mode) => {\n  switch (mode) {\n    case 'web':\n      return new WebCameraStrategy();\n    case 'mobile':\n      return new MobileCameraStrategy();\n    default:\n      throw new Error(`不支持的相機模式: ${mode}`);\n  }\n};\n\nexport default {\n  WebCameraStrategy,\n  MobileCameraStrategy,\n  createCameraStrategy\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,KAAK,QAAQ,aAAa;;AAEnC;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;EACEC,YAAYA,CAACD,SAAS,EAAE;IACtB,IAAI,CAACA,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGA;IAAU,CAAC;EACtD;;EAEA;AACF;AACA;EACEE,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAChB,IAAI,IAAI,CAACJ,SAAS,CAACG,KAAK,CAAC,EAAE;MACzB,IAAI,CAACH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;IAC7B;EACF;;EAEA;AACF;AACA;EACE,MAAMC,WAAWA,CAACC,WAAW,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;EAC3D;;EAEA;AACF;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACW,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACtD,IAAI,CAACd,MAAM,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACG,IAAI,CAAC,YAAY,CAAC;EACzB;;EAEA;AACF;AACA;EACE,MAAMW,SAASA,CAAA,EAAG;IAChB,MAAM,IAAIN,KAAK,CAAC,sCAAsC,CAAC;EACzD;;EAEA;AACF;AACA;EACE,MAAMO,YAAYA,CAAA,EAAG;IACnB;IACAC,OAAO,CAACC,IAAI,CAAC,oDAAoD,CAAC;EACpE;;EAEA;AACF;AACA;EACEC,SAASA,CAAA,EAAG;IACV,OAAO;MACLlB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBmB,SAAS,EAAE,CAAC,CAAC,IAAI,CAACpB;IACpB,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMqB,iBAAiB,SAASvB,kBAAkB,CAAC;EACxDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACuB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;EAC3B;;EAEA;AACF;AACA;EACEC,WAAWA,CAACF,YAAY,EAAEC,aAAa,EAAE;IACvC,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACpC;;EAEA;AACF;AACA;EACE,MAAMhB,WAAWA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI;MACF,MAAMiB,kBAAkB,GAAG;QACzBC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC;UACtBC,MAAM,EAAE;YAAED,KAAK,EAAE;UAAI;QACvB;MACF,CAAC;MAED,MAAME,gBAAgB,GAAG;QACvB,GAAGN,kBAAkB;QACrB,GAAGjB;MACL,CAAC;MAEDS,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE;QAAExB,WAAW,EAAEuB;MAAiB,CAAC,CAAC;MAE7D,IAAI,CAAC/B,MAAM,GAAG,MAAMiC,SAAS,CAACC,YAAY,CAACC,YAAY,CAACJ,gBAAgB,CAAC;MAEzE,IAAI,IAAI,CAACT,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAACc,SAAS,GAAG,IAAI,CAACpC,MAAM;;QAEzC;QACA,MAAM,IAAIqC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACrC,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;YAC/BF,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;UAC7B,CAAC,EAAE,IAAI,CAAC;UAER,MAAMiC,YAAY,GAAGA,CAAA,KAAM;YACzBC,YAAY,CAACH,OAAO,CAAC;YACrB,IAAI,CAAClB,YAAY,CAACsB,mBAAmB,CAAC,YAAY,EAAEF,YAAY,CAAC;YACjE,IAAI,CAACpB,YAAY,CAACsB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;YACvD5B,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE;cACzBc,UAAU,EAAE,IAAI,CAACxB,YAAY,CAACwB,UAAU;cACxCC,WAAW,EAAE,IAAI,CAACzB,YAAY,CAACyB;YACjC,CAAC,CAAC;YACFT,OAAO,CAAC,CAAC;UACX,CAAC;UAED,MAAMO,OAAO,GAAIG,KAAK,IAAK;YACzBL,YAAY,CAACH,OAAO,CAAC;YACrB,IAAI,CAAClB,YAAY,CAACsB,mBAAmB,CAAC,YAAY,EAAEF,YAAY,CAAC;YACjE,IAAI,CAACpB,YAAY,CAACsB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;YACvDN,MAAM,CAACS,KAAK,CAAC;UACf,CAAC;UAED,IAAI,CAAC1B,YAAY,CAAC2B,gBAAgB,CAAC,YAAY,EAAEP,YAAY,CAAC;UAC9D,IAAI,CAACpB,YAAY,CAAC2B,gBAAgB,CAAC,OAAO,EAAEJ,OAAO,CAAC;QACtD,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC5C,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACG,IAAI,CAAC,aAAa,EAAE;QAAEJ,MAAM,EAAE,IAAI,CAACA;MAAO,CAAC,CAAC;MAEjDiB,OAAO,CAACe,GAAG,CAAC,WAAW,CAAC;MACxB,OAAO,IAAI,CAAChC,MAAM;IACpB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC5C,IAAI,CAAC,aAAa,EAAE4C,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEE,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC5B,YAAY,EAAE,OAAO,KAAK;IAEpC,MAAMI,KAAK,GAAG,IAAI,CAACJ,YAAY;IAC/B,OAAOI,KAAK,CAACyB,UAAU,IAAI,CAAC;IAAI;IACzBzB,KAAK,CAACoB,UAAU,GAAG,CAAC,IACpBpB,KAAK,CAACqB,WAAW,GAAG,CAAC,IACrB,CAACrB,KAAK,CAAC0B,MAAM,IACb,CAAC1B,KAAK,CAAC2B,KAAK;EACrB;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAACd,OAAO,GAAG,IAAI,EAAE;IACtC,OAAO,IAAIH,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI,IAAI,CAACW,YAAY,CAAC,CAAC,EAAE;QACvBZ,OAAO,CAAC,CAAC;QACT;MACF;MAEA,MAAMiB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMC,UAAU,GAAGA,CAAA,KAAM;QACvB,IAAI,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;UACvBZ,OAAO,CAAC,CAAC;QACX,CAAC,MAAM,IAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,GAAGf,OAAO,EAAE;UAC3CD,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC,MAAM;UACLgC,UAAU,CAACiB,UAAU,EAAE,GAAG,CAAC;QAC7B;MACF,CAAC;;MAED;MACA,IAAI,IAAI,CAACpC,YAAY,EAAE;QACrB,MAAMqC,OAAO,GAAGA,CAAA,KAAM;UACpB,IAAI,IAAI,CAACT,YAAY,CAAC,CAAC,EAAE;YACvB,IAAI,CAAC5B,YAAY,CAACsB,mBAAmB,CAAC,YAAY,EAAEe,OAAO,CAAC;YAC5D,IAAI,CAACrC,YAAY,CAACsB,mBAAmB,CAAC,SAAS,EAAEe,OAAO,CAAC;YACzDrB,OAAO,CAAC,CAAC;UACX;QACF,CAAC;QAED,IAAI,CAAChB,YAAY,CAAC2B,gBAAgB,CAAC,YAAY,EAAEU,OAAO,CAAC;QACzD,IAAI,CAACrC,YAAY,CAAC2B,gBAAgB,CAAC,SAAS,EAAEU,OAAO,CAAC;MACxD;MAEAD,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAM3C,SAASA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACO,YAAY,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IAAI,CAACtB,QAAQ,EAAE;MAC/D,MAAM,IAAIQ,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,IAAI;MACF;MACA,MAAM,IAAI,CAAC6C,iBAAiB,CAAC,CAAC;MAE9B,MAAM5B,KAAK,GAAG,IAAI,CAACJ,YAAY;MAC/B,MAAMsC,MAAM,GAAG,IAAI,CAACrC,aAAa;MACjC,MAAMsC,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;MAEvC;MACA,IAAIpC,KAAK,CAACoB,UAAU,KAAK,CAAC,IAAIpB,KAAK,CAACqB,WAAW,KAAK,CAAC,EAAE;QACrD,MAAM,IAAItC,KAAK,CAAC,QAAQ,CAAC;MAC3B;;MAEA;MACAmD,MAAM,CAAChC,KAAK,GAAGF,KAAK,CAACoB,UAAU;MAC/Bc,MAAM,CAAC9B,MAAM,GAAGJ,KAAK,CAACqB,WAAW;MAEjC9B,OAAO,CAACe,GAAG,CAAC,iBAAiBN,KAAK,CAACoB,UAAU,IAAIpB,KAAK,CAACqB,WAAW,EAAE,CAAC;;MAErE;MACAc,OAAO,CAACE,qBAAqB,GAAG,IAAI;MACpCF,OAAO,CAACG,qBAAqB,GAAG,MAAM;;MAEtC;MACA,IAAItC,KAAK,CAACoB,UAAU,IAAI,IAAI,IAAIpB,KAAK,CAACqB,WAAW,IAAI,GAAG,EAAE;QACxDc,OAAO,CAACI,MAAM,GAAG,gDAAgD;MACnE;;MAEA;MACAJ,OAAO,CAACK,SAAS,CAACxC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEkC,MAAM,CAAChC,KAAK,EAAEgC,MAAM,CAAC9B,MAAM,CAAC;;MAE3D;MACA,OAAO,IAAIO,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCqB,MAAM,CAACO,MAAM,CAAEC,IAAI,IAAK;UACtB,IAAIA,IAAI,EAAE;YACR,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,wBAAwB,EAAE;cAAEG,IAAI,EAAE;YAAa,CAAC,CAAC;YAC/EtD,OAAO,CAACe,GAAG,CAAC,mBAAmBoC,IAAI,CAACI,IAAI,QAAQ,CAAC;YACjD,IAAI,CAACpE,IAAI,CAAC,YAAY,EAAE;cAAEiE,IAAI;cAAED;YAAK,CAAC,CAAC;YACvC9B,OAAO,CAAC;cAAE+B,IAAI;cAAED;YAAK,CAAC,CAAC;UACzB,CAAC,MAAM;YACL7B,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;UAC7B;QACF,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAC5C,IAAI,CAAC,YAAY,EAAE4C,KAAK,CAAC;MAC9B,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMyB,oBAAoB,SAAS3E,kBAAkB,CAAC;EAC3DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACuB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACmD,iBAAiB,GAAG,aAAa,CAAC,CAAC;IACxC,IAAI,CAACC,kBAAkB,GAAG,IAAI;EAChC;;EAEA;AACF;AACA;EACEnD,WAAWA,CAACF,YAAY,EAAEC,aAAa,EAAE;IACvC,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACpC;;EAEA;AACF;AACA;EACEqD,wBAAwBA,CAAA,EAAG;IACzB;IACA,MAAMC,kBAAkB,GAAG;MACzBnD,KAAK,EAAE;QACLC,UAAU,EAAE,IAAI,CAAC+C,iBAAiB;QAClC9C,KAAK,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAK,CAAC;QAAE;QACnChD,MAAM,EAAE;UAAED,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAK,CAAC;QAClCC,SAAS,EAAE;UAAElD,KAAK,EAAE,EAAE;UAAEiD,GAAG,EAAE;QAAG,CAAC;QACjCE,WAAW,EAAE;UAAEnD,KAAK,EAAE,EAAE,GAAC;QAAE;MAC7B;IACF,CAAC;IAED,MAAMoD,mBAAmB,GAAG;MAC1BvD,KAAK,EAAE;QACLC,UAAU,EAAE,IAAI,CAAC+C,iBAAiB;QAClC9C,KAAK,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAK,CAAC;QAAE;QACnChD,MAAM,EAAE;UAAED,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAI,CAAC;QACjCC,SAAS,EAAE;UAAElD,KAAK,EAAE,EAAE;UAAEiD,GAAG,EAAE;QAAG;MAClC;IACF,CAAC;IAED,MAAMI,mBAAmB,GAAG;MAC1BxD,KAAK,EAAE;QACLC,UAAU,EAAE,IAAI,CAAC+C,iBAAiB;QAClC9C,KAAK,EAAE;UAAEC,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAK,CAAC;QACjChD,MAAM,EAAE;UAAED,KAAK,EAAE,IAAI;UAAEiD,GAAG,EAAE;QAAI;MAClC;IACF,CAAC;IAED,OAAO;MAAED,kBAAkB;MAAEI,mBAAmB;MAAEC;IAAoB,CAAC;EACzE;;EAEA;AACF;AACA;EACE,MAAM3E,WAAWA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE;IAClC,IAAI;MACF,MAAM;QAAEqE,kBAAkB;QAAEI,mBAAmB;QAAEC;MAAoB,CAAC,GAAG,IAAI,CAACN,wBAAwB,CAAC,CAAC;;MAExG;MACA,IAAIpE,WAAW,CAACkB,KAAK,EAAE;QACrB,MAAMK,gBAAgB,GAAG;UACvBL,KAAK,EAAE;YACL,GAAGuD,mBAAmB,CAACvD,KAAK;YAC5B,GAAGlB,WAAW,CAACkB;UACjB;QACF,CAAC;QACD,OAAO,MAAM,IAAI,CAACyD,kBAAkB,CAACpD,gBAAgB,CAAC;MACxD;;MAEA;MACA,MAAMqD,gBAAgB,GAAG,CACvB;QAAEC,IAAI,EAAE,SAAS;QAAE7E,WAAW,EAAEqE;MAAmB,CAAC,EACpD;QAAEQ,IAAI,EAAE,UAAU;QAAE7E,WAAW,EAAEyE;MAAoB,CAAC,EACtD;QAAEI,IAAI,EAAE,aAAa;QAAE7E,WAAW,EAAE0E;MAAoB,CAAC,CAC1D;MAED,KAAK,MAAMI,KAAK,IAAIF,gBAAgB,EAAE;QACpC,IAAI;UACFnE,OAAO,CAACe,GAAG,CAAC,QAAQsD,KAAK,CAACD,IAAI,SAAS,CAAC;UACxC,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,KAAK,CAAC9E,WAAW,CAAC;QACzD,CAAC,CAAC,OAAOwC,KAAK,EAAE;UACd/B,OAAO,CAACC,IAAI,CAAC,GAAGoE,KAAK,CAACD,IAAI,cAAc,EAAErC,KAAK,CAACuC,OAAO,CAAC;UACxD;QACF;MACF;;MAEA;MACA,MAAM,IAAI9E,KAAK,CAAC,gBAAgB,CAAC;IAEnC,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;MAElC;MACA,IAAIA,KAAK,CAACqC,IAAI,KAAK,sBAAsB,IAAI,IAAI,CAACX,iBAAiB,KAAK,MAAM,EAAE;QAC9E,IAAI;UACF,IAAI,CAACA,iBAAiB,GAAG,MAAM;UAC/B,OAAO,MAAM,IAAI,CAACnE,WAAW,CAAC;YAAEmB,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAO;UAAE,CAAC,CAAC;QAClE,CAAC,CAAC,OAAO6D,aAAa,EAAE;UACtB,IAAI,CAACb,kBAAkB,GAAG,KAAK;UAC/B,OAAO,MAAM,IAAI,CAACpE,WAAW,CAAC;YAAEmB,KAAK,EAAE;UAAK,CAAC,CAAC;QAChD;MACF;MAEA,IAAI,CAACtB,IAAI,CAAC,aAAa,EAAE4C,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMmC,kBAAkBA,CAACpD,gBAAgB,EAAE;IACzC,IAAI;MACFd,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE;QAAExB,WAAW,EAAEuB;MAAiB,CAAC,CAAC;MAE7D,IAAI,CAAC/B,MAAM,GAAG,MAAMiC,SAAS,CAACC,YAAY,CAACC,YAAY,CAACJ,gBAAgB,CAAC;;MAEzE;MACA,IAAI,IAAI,CAAC/B,MAAM,EAAE;QACf,MAAMyF,UAAU,GAAG,IAAI,CAACzF,MAAM,CAAC0F,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAID,UAAU,EAAE;UACd,MAAME,QAAQ,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;UACzC3E,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE;YACzBJ,KAAK,EAAE+D,QAAQ,CAAC/D,KAAK;YACrBE,MAAM,EAAE6D,QAAQ,CAAC7D,MAAM;YACvBiD,SAAS,EAAEY,QAAQ,CAACZ,SAAS;YAC7BpD,UAAU,EAAEgE,QAAQ,CAAChE;UACvB,CAAC,CAAC;QACJ;MACF;MAEA,IAAI,IAAI,CAACL,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,CAACc,SAAS,GAAG,IAAI,CAACpC,MAAM;;QAEzC;QACA,IAAI,CAACsB,YAAY,CAACuE,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC;QACnD,IAAI,CAACvE,YAAY,CAACuE,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC;QAC1D,IAAI,CAACvE,YAAY,CAACwE,KAAK,GAAG,IAAI;;QAE9B;QACA,MAAM,IAAIzD,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACrC,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;YAC/BF,MAAM,CAAC,IAAI9B,KAAK,CAAC,WAAW,CAAC,CAAC;UAChC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAEV,MAAMiC,YAAY,GAAGA,CAAA,KAAM;YACzBC,YAAY,CAACH,OAAO,CAAC;YACrB,IAAI,CAAClB,YAAY,CAACsB,mBAAmB,CAAC,YAAY,EAAEF,YAAY,CAAC;YACjE,IAAI,CAACpB,YAAY,CAACsB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;YACvD5B,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE;cACzBc,UAAU,EAAE,IAAI,CAACxB,YAAY,CAACwB,UAAU;cACxCC,WAAW,EAAE,IAAI,CAACzB,YAAY,CAACyB,WAAW;cAC1CpB,UAAU,EAAE,IAAI,CAAC+C;YACnB,CAAC,CAAC;YACFpC,OAAO,CAAC,CAAC;UACX,CAAC;UAED,MAAMO,OAAO,GAAIG,KAAK,IAAK;YACzBL,YAAY,CAACH,OAAO,CAAC;YACrB,IAAI,CAAClB,YAAY,CAACsB,mBAAmB,CAAC,YAAY,EAAEF,YAAY,CAAC;YACjE,IAAI,CAACpB,YAAY,CAACsB,mBAAmB,CAAC,OAAO,EAAEC,OAAO,CAAC;YACvDN,MAAM,CAACS,KAAK,CAAC;UACf,CAAC;UAED,IAAI,CAAC1B,YAAY,CAAC2B,gBAAgB,CAAC,YAAY,EAAEP,YAAY,CAAC;UAC9D,IAAI,CAACpB,YAAY,CAAC2B,gBAAgB,CAAC,OAAO,EAAEJ,OAAO,CAAC;QACtD,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC5C,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACG,IAAI,CAAC,aAAa,EAAE;QACvBJ,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB2B,UAAU,EAAE,IAAI,CAAC+C;MACnB,CAAC,CAAC;MAEF,OAAO,IAAI,CAAC1E,MAAM;IACpB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;MAElC;MACA,IAAIA,KAAK,CAACqC,IAAI,KAAK,sBAAsB,IAAI,IAAI,CAACX,iBAAiB,KAAK,MAAM,EAAE;QAC9E,IAAI;UACF,IAAI,CAACA,iBAAiB,GAAG,MAAM;UAC/B,OAAO,MAAM,IAAI,CAACnE,WAAW,CAAC;YAAEmB,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAO;UAAE,CAAC,CAAC;QAClE,CAAC,CAAC,OAAO6D,aAAa,EAAE;UACtB,IAAI,CAACb,kBAAkB,GAAG,KAAK;UAC/B,OAAO,MAAM,IAAI,CAACpE,WAAW,CAAC;YAAEmB,KAAK,EAAE;UAAK,CAAC,CAAC;QAChD;MACF;MAEA,IAAI,CAACtB,IAAI,CAAC,aAAa,EAAE4C,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMhC,YAAYA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC2D,kBAAkB,EAAE;MAC5B9E,KAAK,CAACkG,IAAI,CAAC;QACTC,OAAO,EAAE,aAAa;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF;MACA,IAAI,CAACvF,UAAU,CAAC,CAAC;;MAEjB;MACA,IAAI,CAACgE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa;;MAE1F;MACA,MAAM,IAAI,CAACnE,WAAW,CAAC,CAAC;MAExB,IAAI,CAACH,IAAI,CAAC,cAAc,EAAE;QAAEuB,UAAU,EAAE,IAAI,CAAC+C;MAAkB,CAAC,CAAC;MAEjE7E,KAAK,CAACkG,IAAI,CAAC;QACTC,OAAO,EAAE,OAAO,IAAI,CAACtB,iBAAiB,KAAK,aAAa,GAAG,IAAI,GAAG,IAAI,KAAK;QAC3EuB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCnD,KAAK,CAACkG,IAAI,CAAC;QACTC,OAAO,EAAE,aAAa;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAAC7F,IAAI,CAAC,mBAAmB,EAAE4C,KAAK,CAAC;IACvC;EACF;;EAEA;AACF;AACA;EACEE,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC5B,YAAY,EAAE,OAAO,KAAK;IAEpC,MAAMI,KAAK,GAAG,IAAI,CAACJ,YAAY;IAC/B,OAAOI,KAAK,CAACyB,UAAU,IAAI,CAAC;IAAI;IACzBzB,KAAK,CAACoB,UAAU,GAAG,CAAC,IACpBpB,KAAK,CAACqB,WAAW,GAAG,CAAC,IACrB,CAACrB,KAAK,CAAC0B,MAAM,IACb,CAAC1B,KAAK,CAAC2B,KAAK;EACrB;;EAEA;AACF;AACA;EACE,MAAMC,iBAAiBA,CAAA,EAAG;IACxB,MAAM5B,KAAK,GAAG,IAAI,CAACJ,YAAY;IAC/B,IAAI,CAACI,KAAK,EAAE;MACV,MAAM,IAAIjB,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,OAAO,IAAI4B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC/BF,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC7B,CAAC,EAAE,KAAK,CAAC;MAET,MAAMiD,UAAU,GAAGA,CAAA,KAAM;QACvB,IAAIhC,KAAK,CAACyB,UAAU,IAAI,CAAC,IAAIzB,KAAK,CAACoB,UAAU,GAAG,CAAC,IAAIpB,KAAK,CAACqB,WAAW,GAAG,CAAC,EAAE;UAC1EJ,YAAY,CAACH,OAAO,CAAC;UACrBF,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACLG,UAAU,CAACiB,UAAU,EAAE,EAAE,CAAC;QAC5B;MACF,CAAC;MAEDA,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMwC,eAAeA,CAAA,EAAG;IACtB;IACA,MAAM,IAAI7D,OAAO,CAACC,OAAO,IAAIG,UAAU,CAACH,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,MAAMZ,KAAK,GAAG,IAAI,CAACJ,YAAY;IAC/B,IAAII,KAAK,IAAIA,KAAK,CAACU,SAAS,EAAE;MAC5B,MAAM+D,MAAM,GAAGzE,KAAK,CAACU,SAAS,CAACsD,cAAc,CAAC,CAAC;MAC/C,IAAIS,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMvF,KAAK,GAAGsF,MAAM,CAAC,CAAC,CAAC;QACvB,MAAMR,QAAQ,GAAG9E,KAAK,CAAC+E,WAAW,CAAC,CAAC;;QAEpC;QACA3E,OAAO,CAACe,GAAG,CAAC,UAAU,EAAE;UACtBJ,KAAK,EAAE+D,QAAQ,CAAC/D,KAAK;UACrBE,MAAM,EAAE6D,QAAQ,CAAC7D,MAAM;UACvBiD,SAAS,EAAEY,QAAQ,CAACZ,SAAS;UAC7B5B,UAAU,EAAEzB,KAAK,CAACyB;QACpB,CAAC,CAAC;MACJ;IACF;EACF;;EAEA;AACF;AACA;EACEkD,uBAAuBA,CAACxC,OAAO,EAAEf,UAAU,EAAEC,WAAW,EAAE;IACxD;IACAc,OAAO,CAACE,qBAAqB,GAAG,IAAI;IACpCF,OAAO,CAACG,qBAAqB,GAAG,MAAM;;IAEtC;IACA,IAAIlB,UAAU,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI,EAAE;MAC7C;MACAc,OAAO,CAACyC,wBAAwB,GAAG,aAAa;;MAEhD;MACAzC,OAAO,CAACI,MAAM,GAAG,0EAA0E;IAC7F,CAAC,MAAM,IAAInB,UAAU,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI,EAAE;MACpD;MACAc,OAAO,CAACI,MAAM,GAAG,gDAAgD;IACnE,CAAC,MAAM;MACL;MACAJ,OAAO,CAACI,MAAM,GAAG,gDAAgD;IACnE;EACF;;EAEA;AACF;AACA;EACE,MAAMsC,gBAAgBA,CAAC3C,MAAM,EAAEC,OAAO,EAAE;IACtC;IACA,MAAM2C,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACnD,MAAMC,WAAW,GAAGH,UAAU,CAAC1C,UAAU,CAAC,IAAI,CAAC;IAE/C0C,UAAU,CAAC5E,KAAK,GAAGgC,MAAM,CAAChC,KAAK;IAC/B4E,UAAU,CAAC1E,MAAM,GAAG8B,MAAM,CAAC9B,MAAM;;IAEjC;IACA6E,WAAW,CAACzC,SAAS,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEnC;IACA,MAAMgD,SAAS,GAAGD,WAAW,CAACE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEjD,MAAM,CAAChC,KAAK,EAAEgC,MAAM,CAAC9B,MAAM,CAAC;IAC7E,MAAMxB,IAAI,GAAGsG,SAAS,CAACtG,IAAI;;IAE3B;IACA,MAAMwG,aAAa,GAAG,CACpB,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EACV,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EACf,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CACX;;IAED;IACA,IAAIlD,MAAM,CAAChC,KAAK,IAAI,IAAI,IAAIgC,MAAM,CAAC9B,MAAM,IAAI,IAAI,EAAE;MACjD,IAAI,CAACiF,kBAAkB,CAACzG,IAAI,EAAEsD,MAAM,CAAChC,KAAK,EAAEgC,MAAM,CAAC9B,MAAM,EAAEgF,aAAa,CAAC;IAC3E;;IAEA;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1G,IAAI,CAAC8F,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;MACvC;MACA1G,IAAI,CAAC0G,CAAC,CAAC,GAAGC,IAAI,CAACnC,GAAG,CAAC,GAAG,EAAEmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC5G,IAAI,CAAC0G,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAK;MACxE1G,IAAI,CAAC0G,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACnC,GAAG,CAAC,GAAG,EAAEmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC5G,IAAI,CAAC0G,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5E1G,IAAI,CAAC0G,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACnC,GAAG,CAAC,GAAG,EAAEmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC5G,IAAI,CAAC0G,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9E;;IAEA;IACAL,WAAW,CAACQ,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACzC/C,OAAO,CAACuD,SAAS,CAAC,CAAC,EAAE,CAAC,EAAExD,MAAM,CAAChC,KAAK,EAAEgC,MAAM,CAAC9B,MAAM,CAAC;IACpD+B,OAAO,CAACK,SAAS,CAACsC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;IAEnC,OAAO5C,MAAM;EACf;;EAEA;AACF;AACA;EACEmD,kBAAkBA,CAACzG,IAAI,EAAEsB,KAAK,EAAEE,MAAM,EAAEuF,MAAM,EAAE;IAC9C,MAAMC,QAAQ,GAAG,IAAIC,iBAAiB,CAACjH,IAAI,CAAC;IAE5C,KAAK,IAAIkH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,MAAM,GAAG,CAAC,EAAE0F,CAAC,EAAE,EAAE;MACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,KAAK,GAAG,CAAC,EAAE6F,CAAC,EAAE,EAAE;QAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAAE;UAC5B,IAAIC,GAAG,GAAG,CAAC;UACX,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;YAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;cAC/B,MAAMC,GAAG,GAAG,CAAC,CAACN,CAAC,GAAGI,EAAE,IAAIhG,KAAK,IAAI6F,CAAC,GAAGI,EAAE,CAAC,IAAI,CAAC,GAAGH,CAAC;cACjDC,GAAG,IAAIL,QAAQ,CAACQ,GAAG,CAAC,GAAGT,MAAM,CAAC,CAACO,EAAE,GAAG,CAAC,IAAI,CAAC,IAAIC,EAAE,GAAG,CAAC,CAAC,CAAC;YACxD;UACF;UACA,MAAMC,GAAG,GAAG,CAACN,CAAC,GAAG5F,KAAK,GAAG6F,CAAC,IAAI,CAAC,GAAGC,CAAC;UACnCpH,IAAI,CAACwH,GAAG,CAAC,GAAGb,IAAI,CAACnC,GAAG,CAAC,GAAG,EAAEmC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAES,GAAG,CAAC,CAAC;QAC7C;MACF;IACF;EACF;;EAEA;AACF;AACA;EACEI,sBAAsBA,CAACjF,UAAU,EAAEC,WAAW,EAAE;IAC9C,MAAMiF,WAAW,GAAGlF,UAAU,GAAGC,WAAW;;IAE5C;IACA,IAAIiF,WAAW,IAAI,IAAI,GAAG,IAAI,EAAE;MAAE;MAChC,OAAO;QACLC,OAAO,EAAE,IAAI;QAAE;QACfC,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QAAE;QAC9BC,gBAAgB,EAAE;MACpB,CAAC;IACH,CAAC,MAAM,IAAIH,WAAW,IAAI,IAAI,GAAG,IAAI,EAAE;MAAE;MACvC,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QAAE;QAC9BC,gBAAgB,EAAE;MACpB,CAAC;IACH,CAAC,MAAM,IAAIH,WAAW,IAAI,IAAI,GAAG,IAAI,EAAE;MAAE;MACvC,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QAAE;QAC9BC,gBAAgB,EAAE;MACpB,CAAC;IACH,CAAC,MAAM;MAAE;MACP,OAAO;QACLF,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QAAE;QAC9BC,gBAAgB,EAAE;MACpB,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMpH,SAASA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACO,YAAY,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IAAI,CAACtB,QAAQ,EAAE;MAC/D,MAAM,IAAIQ,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,IAAI;MACF;MACA,MAAM,IAAI,CAACyF,eAAe,CAAC,CAAC;;MAE5B;MACA,MAAM,IAAI,CAAC5C,iBAAiB,CAAC,CAAC;MAE9B,MAAM5B,KAAK,GAAG,IAAI,CAACJ,YAAY;MAC/B,MAAMsC,MAAM,GAAG,IAAI,CAACrC,aAAa;MACjC,MAAMsC,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;;MAEvC;MACA,IAAIpC,KAAK,CAACoB,UAAU,KAAK,CAAC,IAAIpB,KAAK,CAACqB,WAAW,KAAK,CAAC,EAAE;QACrD,MAAM,IAAItC,KAAK,CAAC,QAAQ,CAAC;MAC3B;;MAEA;MACAmD,MAAM,CAAChC,KAAK,GAAGF,KAAK,CAACoB,UAAU;MAC/Bc,MAAM,CAAC9B,MAAM,GAAGJ,KAAK,CAACqB,WAAW;MAEjC9B,OAAO,CAACe,GAAG,CAAC,iBAAiBN,KAAK,CAACoB,UAAU,IAAIpB,KAAK,CAACqB,WAAW,UAAU,IAAI,CAAC2B,iBAAiB,EAAE,CAAC;;MAErG;MACA,IAAI,CAAC2B,uBAAuB,CAACxC,OAAO,EAAEnC,KAAK,CAACoB,UAAU,EAAEpB,KAAK,CAACqB,WAAW,CAAC;;MAE1E;MACAc,OAAO,CAACK,SAAS,CAACxC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEkC,MAAM,CAAChC,KAAK,EAAEgC,MAAM,CAAC9B,MAAM,CAAC;;MAE3D;MACA,MAAM,IAAI,CAACyE,gBAAgB,CAAC3C,MAAM,EAAEC,OAAO,CAAC;;MAE5C;MACA,MAAMuE,eAAe,GAAG,IAAI,CAACL,sBAAsB,CAACrG,KAAK,CAACoB,UAAU,EAAEpB,KAAK,CAACqB,WAAW,CAAC;MAExF9B,OAAO,CAACe,GAAG,CAAC,YAAY,EAAEoG,eAAe,CAAC;;MAE1C;MACA,OAAO,IAAI/F,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCqB,MAAM,CAACO,MAAM,CAAEC,IAAI,IAAK;UACtB,IAAIA,IAAI,EAAE;YACR;YACA,IAAIA,IAAI,CAACI,IAAI,GAAG4D,eAAe,CAACF,WAAW,IAAIE,eAAe,CAACH,OAAO,GAAG,IAAI,EAAE;cAC7EhH,OAAO,CAACe,GAAG,CAAC,SAASoC,IAAI,CAACI,IAAI,iBAAiB,CAAC;;cAEhD;cACA,MAAM6D,cAAc,GAAGpB,IAAI,CAACC,GAAG,CAAC,IAAI,EAAEkB,eAAe,CAACH,OAAO,GAAG,GAAG,CAAC;cACpErE,MAAM,CAACO,MAAM,CAAEmE,WAAW,IAAK;gBAC7B,IAAIA,WAAW,EAAE;kBACf,MAAMjE,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACgE,WAAW,CAAC,EAAE,2BAA2B,EAAE;oBAAE/D,IAAI,EAAE;kBAAa,CAAC,CAAC;kBACzFtD,OAAO,CAACe,GAAG,CAAC,0BAA0BsG,WAAW,CAAC9D,IAAI,eAAe6D,cAAc,UAAU,IAAI,CAAC3D,iBAAiB,EAAE,CAAC;kBACtH,IAAI,CAACtE,IAAI,CAAC,YAAY,EAAE;oBAAEiE,IAAI;oBAAED,IAAI,EAAEkE,WAAW;oBAAE3G,UAAU,EAAE,IAAI,CAAC+C;kBAAkB,CAAC,CAAC;kBACxFpC,OAAO,CAAC;oBAAE+B,IAAI;oBAAED,IAAI,EAAEkE,WAAW;oBAAE3G,UAAU,EAAE,IAAI,CAAC+C;kBAAkB,CAAC,CAAC;gBAC1E,CAAC,MAAM;kBACLnC,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC7B;cACF,CAAC,EAAE,YAAY,EAAE4H,cAAc,CAAC;YAClC,CAAC,MAAM;cACL,MAAMhE,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,2BAA2B,EAAE;gBAAEG,IAAI,EAAE;cAAa,CAAC,CAAC;cAClFtD,OAAO,CAACe,GAAG,CAAC,mBAAmBoC,IAAI,CAACI,IAAI,eAAe4D,eAAe,CAACH,OAAO,UAAU,IAAI,CAACvD,iBAAiB,EAAE,CAAC;cACjH,IAAI,CAACtE,IAAI,CAAC,YAAY,EAAE;gBAAEiE,IAAI;gBAAED,IAAI;gBAAEzC,UAAU,EAAE,IAAI,CAAC+C;cAAkB,CAAC,CAAC;cAC3EpC,OAAO,CAAC;gBAAE+B,IAAI;gBAAED,IAAI;gBAAEzC,UAAU,EAAE,IAAI,CAAC+C;cAAkB,CAAC,CAAC;YAC7D;UACF,CAAC,MAAM;YACLnC,MAAM,CAAC,IAAI9B,KAAK,CAAC,QAAQ,CAAC,CAAC;UAC7B;QACF,CAAC,EAAE,YAAY,EAAE2H,eAAe,CAACH,OAAO,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAC5C,IAAI,CAAC,YAAY,EAAE4C,KAAK,CAAC;MAC9B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEuF,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC7D,iBAAiB;EAC/B;;EAEA;AACF;AACA;EACE8D,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC7D,kBAAkB;EAChC;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAM8D,oBAAoB,GAAIC,IAAI,IAAK;EAC5C,QAAQA,IAAI;IACV,KAAK,KAAK;MACR,OAAO,IAAIrH,iBAAiB,CAAC,CAAC;IAChC,KAAK,QAAQ;MACX,OAAO,IAAIoD,oBAAoB,CAAC,CAAC;IACnC;MACE,MAAM,IAAIhE,KAAK,CAAC,aAAaiI,IAAI,EAAE,CAAC;EACxC;AACF,CAAC;AAED,eAAe;EACbrH,iBAAiB;EACjBoD,oBAAoB;EACpBgE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}