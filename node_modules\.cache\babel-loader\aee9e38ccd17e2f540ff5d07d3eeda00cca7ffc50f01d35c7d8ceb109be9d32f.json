{"ast": null, "code": "import { useState } from 'react';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nexport function useInnerVisible(outerVisible) {\n  const [innerVisible, setInnerVisible] = useState(outerVisible);\n  useIsomorphicLayoutEffect(() => {\n    setInnerVisible(outerVisible);\n  }, [outerVisible]);\n  return innerVisible;\n}", "map": {"version": 3, "names": ["useState", "useIsomorphicLayoutEffect", "useInnerVisible", "outerVisible", "innerVisible", "setInnerVisible"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/use-inner-visible.js"], "sourcesContent": ["import { useState } from 'react';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nexport function useInnerVisible(outerVisible) {\n  const [innerVisible, setInnerVisible] = useState(outerVisible);\n  useIsomorphicLayoutEffect(() => {\n    setInnerVisible(outerVisible);\n  }, [outerVisible]);\n  return innerVisible;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,yBAAyB,QAAQ,QAAQ;AAClD,OAAO,SAASC,eAAeA,CAACC,YAAY,EAAE;EAC5C,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGL,QAAQ,CAACG,YAAY,CAAC;EAC9DF,yBAAyB,CAAC,MAAM;IAC9BI,eAAe,CAACF,YAAY,CAAC;EAC/B,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB,OAAOC,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}