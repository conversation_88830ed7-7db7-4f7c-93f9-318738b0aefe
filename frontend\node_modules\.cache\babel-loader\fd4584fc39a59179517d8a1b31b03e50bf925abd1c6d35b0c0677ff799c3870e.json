{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"./useEvent\";\n/**\n * Same as React.useState but will always get latest state.\n * This is useful when React merge multiple state updates into one.\n * e.g. onTransitionEnd trigger multiple event at once will be merged state update in React.\n */\nexport default function useSyncState(defaultValue) {\n  var _React$useReducer = React.useReducer(function (x) {\n      return x + 1;\n    }, 0),\n    _React$useReducer2 = _slicedToArray(_React$useReducer, 2),\n    forceUpdate = _React$useReducer2[1];\n  var currentValueRef = React.useRef(defaultValue);\n  var getValue = useEvent(function () {\n    return currentValueRef.current;\n  });\n  var setValue = useEvent(function (updater) {\n    currentValueRef.current = typeof updater === 'function' ? updater(currentValueRef.current) : updater;\n    forceUpdate();\n  });\n  return [getValue, setValue];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}