{"ast": null, "code": "import { SearchOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport Button from '../button';\nimport { useConfig } from '../config-provider';\nimport Input from '../input';\nconst classPrefix = `adm-search-bar`;\nconst defaultProps = {\n  clearable: true,\n  onlyShowClearWhenFocus: false,\n  showCancelButton: false,\n  defaultValue: '',\n  clearOnCancel: true\n};\nexport const SearchBar = forwardRef((props, ref) => {\n  const {\n    locale,\n    searchBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, {\n    cancelText: locale.common.cancel\n  }, props);\n  const searchIcon = mergeProp(React.createElement(SearchOutline, null), componentConfig.searchIcon, props.icon, props.searchIcon);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const [hasFocus, setHasFocus] = useState(false);\n  const inputRef = useRef(null);\n  const composingRef = useRef(false);\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n    },\n    focus: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      var _a, _b;\n      return (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement) !== null && _b !== void 0 ? _b : null;\n    }\n  }));\n  const renderCancelButton = () => {\n    let isShowCancel;\n    if (typeof mergedProps.showCancelButton === 'function') {\n      isShowCancel = mergedProps.showCancelButton(hasFocus, value);\n    } else {\n      isShowCancel = mergedProps.showCancelButton && hasFocus;\n    }\n    return isShowCancel && React.createElement(\"div\", {\n      className: `${classPrefix}-suffix`\n    }, React.createElement(Button, {\n      fill: 'none',\n      className: `${classPrefix}-cancel-button`,\n      onClick: () => {\n        var _a, _b, _c;\n        if (mergedProps.clearOnCancel) {\n          (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n        }\n        (_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n        (_c = mergedProps.onCancel) === null || _c === void 0 ? void 0 : _c.call(mergedProps);\n      },\n      onMouseDown: e => {\n        e.preventDefault();\n      }\n    }, mergedProps.cancelText));\n  };\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-active`]: hasFocus\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-input-box`\n  }, searchIcon && React.createElement(\"div\", {\n    className: `${classPrefix}-input-box-icon`\n  }, searchIcon), React.createElement(Input, {\n    ref: inputRef,\n    className: classNames(`${classPrefix}-input`, {\n      [`${classPrefix}-input-without-icon`]: !searchIcon\n    }),\n    value: value,\n    onChange: setValue,\n    maxLength: mergedProps.maxLength,\n    autoFocus: mergedProps.autoFocus,\n    placeholder: mergedProps.placeholder,\n    clearable: mergedProps.clearable,\n    onlyShowClearWhenFocus: mergedProps.onlyShowClearWhenFocus,\n    onFocus: e => {\n      var _a;\n      setHasFocus(true);\n      (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onBlur: e => {\n      var _a;\n      setHasFocus(false);\n      (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onClear: mergedProps.onClear,\n    type: 'search',\n    enterKeyHint: 'search',\n    onEnterPress: () => {\n      var _a, _b;\n      if (!composingRef.current) {\n        (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n        (_b = mergedProps.onSearch) === null || _b === void 0 ? void 0 : _b.call(mergedProps, value);\n      }\n    },\n    \"aria-label\": locale.SearchBar.name,\n    onCompositionStart: e => {\n      var _a;\n      composingRef.current = true;\n      (_a = mergedProps.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      composingRef.current = false;\n      (_a = mergedProps.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    }\n  })), renderCancelButton()));\n});", "map": {"version": 3, "names": ["SearchOutline", "classNames", "React", "forwardRef", "useImperativeHandle", "useRef", "useState", "withNativeProps", "usePropsValue", "mergeProp", "mergeProps", "<PERSON><PERSON>", "useConfig", "Input", "classPrefix", "defaultProps", "clearable", "onlyShowClearWhenFocus", "showCancelButton", "defaultValue", "clearOnCancel", "SearchBar", "props", "ref", "locale", "searchBar", "componentConfig", "mergedProps", "cancelText", "common", "cancel", "searchIcon", "createElement", "icon", "value", "setValue", "hasFocus", "setHasFocus", "inputRef", "composingRef", "clear", "_a", "current", "focus", "blur", "nativeElement", "_b", "renderCancelButton", "isShowCancel", "className", "fill", "onClick", "_c", "onCancel", "call", "onMouseDown", "e", "preventDefault", "onChange", "max<PERSON><PERSON><PERSON>", "autoFocus", "placeholder", "onFocus", "onBlur", "onClear", "type", "enterKeyHint", "onEnterPress", "onSearch", "name", "onCompositionStart", "onCompositionEnd"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/search-bar/search-bar.js"], "sourcesContent": ["import { SearchOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport Button from '../button';\nimport { useConfig } from '../config-provider';\nimport Input from '../input';\nconst classPrefix = `adm-search-bar`;\nconst defaultProps = {\n  clearable: true,\n  onlyShowClearWhenFocus: false,\n  showCancelButton: false,\n  defaultValue: '',\n  clearOnCancel: true\n};\nexport const SearchBar = forwardRef((props, ref) => {\n  const {\n    locale,\n    searchBar: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, {\n    cancelText: locale.common.cancel\n  }, props);\n  const searchIcon = mergeProp(React.createElement(SearchOutline, null), componentConfig.searchIcon, props.icon, props.searchIcon);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const [hasFocus, setHasFocus] = useState(false);\n  const inputRef = useRef(null);\n  const composingRef = useRef(false);\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n    },\n    focus: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      return (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      var _a, _b;\n      return (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement) !== null && _b !== void 0 ? _b : null;\n    }\n  }));\n  const renderCancelButton = () => {\n    let isShowCancel;\n    if (typeof mergedProps.showCancelButton === 'function') {\n      isShowCancel = mergedProps.showCancelButton(hasFocus, value);\n    } else {\n      isShowCancel = mergedProps.showCancelButton && hasFocus;\n    }\n    return isShowCancel && React.createElement(\"div\", {\n      className: `${classPrefix}-suffix`\n    }, React.createElement(Button, {\n      fill: 'none',\n      className: `${classPrefix}-cancel-button`,\n      onClick: () => {\n        var _a, _b, _c;\n        if (mergedProps.clearOnCancel) {\n          (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.clear();\n        }\n        (_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n        (_c = mergedProps.onCancel) === null || _c === void 0 ? void 0 : _c.call(mergedProps);\n      },\n      onMouseDown: e => {\n        e.preventDefault();\n      }\n    }, mergedProps.cancelText));\n  };\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-active`]: hasFocus\n    })\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-input-box`\n  }, searchIcon && React.createElement(\"div\", {\n    className: `${classPrefix}-input-box-icon`\n  }, searchIcon), React.createElement(Input, {\n    ref: inputRef,\n    className: classNames(`${classPrefix}-input`, {\n      [`${classPrefix}-input-without-icon`]: !searchIcon\n    }),\n    value: value,\n    onChange: setValue,\n    maxLength: mergedProps.maxLength,\n    autoFocus: mergedProps.autoFocus,\n    placeholder: mergedProps.placeholder,\n    clearable: mergedProps.clearable,\n    onlyShowClearWhenFocus: mergedProps.onlyShowClearWhenFocus,\n    onFocus: e => {\n      var _a;\n      setHasFocus(true);\n      (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onBlur: e => {\n      var _a;\n      setHasFocus(false);\n      (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onClear: mergedProps.onClear,\n    type: 'search',\n    enterKeyHint: 'search',\n    onEnterPress: () => {\n      var _a, _b;\n      if (!composingRef.current) {\n        (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n        (_b = mergedProps.onSearch) === null || _b === void 0 ? void 0 : _b.call(mergedProps, value);\n      }\n    },\n    \"aria-label\": locale.SearchBar.name,\n    onCompositionStart: e => {\n      var _a;\n      composingRef.current = true;\n      (_a = mergedProps.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      composingRef.current = false;\n      (_a = mergedProps.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(mergedProps, e);\n    }\n  })), renderCancelButton()));\n});"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAChF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,UAAU;AAC5B,MAAMC,WAAW,GAAG,gBAAgB;AACpC,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE,IAAI;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE,EAAE;EAChBC,aAAa,EAAE;AACjB,CAAC;AACD,OAAO,MAAMC,SAAS,GAAGlB,UAAU,CAAC,CAACmB,KAAK,EAAEC,GAAG,KAAK;EAClD,MAAM;IACJC,MAAM;IACNC,SAAS,EAAEC,eAAe,GAAG,CAAC;EAChC,CAAC,GAAGd,SAAS,CAAC,CAAC;EACf,MAAMe,WAAW,GAAGjB,UAAU,CAACK,YAAY,EAAEW,eAAe,EAAE;IAC5DE,UAAU,EAAEJ,MAAM,CAACK,MAAM,CAACC;EAC5B,CAAC,EAAER,KAAK,CAAC;EACT,MAAMS,UAAU,GAAGtB,SAAS,CAACP,KAAK,CAAC8B,aAAa,CAAChC,aAAa,EAAE,IAAI,CAAC,EAAE0B,eAAe,CAACK,UAAU,EAAET,KAAK,CAACW,IAAI,EAAEX,KAAK,CAACS,UAAU,CAAC;EAChI,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,aAAa,CAACmB,WAAW,CAAC;EACpD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMgC,QAAQ,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkC,YAAY,GAAGlC,MAAM,CAAC,KAAK,CAAC;EAClCD,mBAAmB,CAACmB,GAAG,EAAE,OAAO;IAC9BiB,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,KAAK,CAAC,CAAC;IAChF,CAAC;IACDG,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIF,EAAE;MACN,OAAO,CAACA,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK,CAAC,CAAC;IAChF,CAAC;IACDC,IAAI,EAAEA,CAAA,KAAM;MACV,IAAIH,EAAE;MACN,OAAO,CAACA,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;IAC/E,CAAC;IACD,IAAIC,aAAaA,CAAA,EAAG;MAClB,IAAIJ,EAAE,EAAEK,EAAE;MACV,OAAO,CAACA,EAAE,GAAG,CAACL,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,aAAa,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IACnI;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIC,YAAY;IAChB,IAAI,OAAOrB,WAAW,CAACT,gBAAgB,KAAK,UAAU,EAAE;MACtD8B,YAAY,GAAGrB,WAAW,CAACT,gBAAgB,CAACkB,QAAQ,EAAEF,KAAK,CAAC;IAC9D,CAAC,MAAM;MACLc,YAAY,GAAGrB,WAAW,CAACT,gBAAgB,IAAIkB,QAAQ;IACzD;IACA,OAAOY,YAAY,IAAI9C,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;MAChDiB,SAAS,EAAE,GAAGnC,WAAW;IAC3B,CAAC,EAAEZ,KAAK,CAAC8B,aAAa,CAACrB,MAAM,EAAE;MAC7BuC,IAAI,EAAE,MAAM;MACZD,SAAS,EAAE,GAAGnC,WAAW,gBAAgB;MACzCqC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIV,EAAE,EAAEK,EAAE,EAAEM,EAAE;QACd,IAAIzB,WAAW,CAACP,aAAa,EAAE;UAC7B,CAACqB,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,KAAK,CAAC,CAAC;QACzE;QACA,CAACM,EAAE,GAAGR,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,IAAI,CAAC,CAAC;QACtE,CAACQ,EAAE,GAAGzB,WAAW,CAAC0B,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC3B,WAAW,CAAC;MACvF,CAAC;MACD4B,WAAW,EAAEC,CAAC,IAAI;QAChBA,CAAC,CAACC,cAAc,CAAC,CAAC;MACpB;IACF,CAAC,EAAE9B,WAAW,CAACC,UAAU,CAAC,CAAC;EAC7B,CAAC;EACD,OAAOrB,eAAe,CAACoB,WAAW,EAAEzB,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC7DiB,SAAS,EAAEhD,UAAU,CAACa,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,SAAS,GAAGsB;IAC7B,CAAC;EACH,CAAC,EAAElC,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC5BiB,SAAS,EAAE,GAAGnC,WAAW;EAC3B,CAAC,EAAEiB,UAAU,IAAI7B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IAC1CiB,SAAS,EAAE,GAAGnC,WAAW;EAC3B,CAAC,EAAEiB,UAAU,CAAC,EAAE7B,KAAK,CAAC8B,aAAa,CAACnB,KAAK,EAAE;IACzCU,GAAG,EAAEe,QAAQ;IACbW,SAAS,EAAEhD,UAAU,CAAC,GAAGa,WAAW,QAAQ,EAAE;MAC5C,CAAC,GAAGA,WAAW,qBAAqB,GAAG,CAACiB;IAC1C,CAAC,CAAC;IACFG,KAAK,EAAEA,KAAK;IACZwB,QAAQ,EAAEvB,QAAQ;IAClBwB,SAAS,EAAEhC,WAAW,CAACgC,SAAS;IAChCC,SAAS,EAAEjC,WAAW,CAACiC,SAAS;IAChCC,WAAW,EAAElC,WAAW,CAACkC,WAAW;IACpC7C,SAAS,EAAEW,WAAW,CAACX,SAAS;IAChCC,sBAAsB,EAAEU,WAAW,CAACV,sBAAsB;IAC1D6C,OAAO,EAAEN,CAAC,IAAI;MACZ,IAAIf,EAAE;MACNJ,WAAW,CAAC,IAAI,CAAC;MACjB,CAACI,EAAE,GAAGd,WAAW,CAACmC,OAAO,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC3B,WAAW,EAAE6B,CAAC,CAAC;IACzF,CAAC;IACDO,MAAM,EAAEP,CAAC,IAAI;MACX,IAAIf,EAAE;MACNJ,WAAW,CAAC,KAAK,CAAC;MAClB,CAACI,EAAE,GAAGd,WAAW,CAACoC,MAAM,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC3B,WAAW,EAAE6B,CAAC,CAAC;IACxF,CAAC;IACDQ,OAAO,EAAErC,WAAW,CAACqC,OAAO;IAC5BC,IAAI,EAAE,QAAQ;IACdC,YAAY,EAAE,QAAQ;IACtBC,YAAY,EAAEA,CAAA,KAAM;MAClB,IAAI1B,EAAE,EAAEK,EAAE;MACV,IAAI,CAACP,YAAY,CAACG,OAAO,EAAE;QACzB,CAACD,EAAE,GAAGH,QAAQ,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;QACtE,CAACE,EAAE,GAAGnB,WAAW,CAACyC,QAAQ,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,IAAI,CAAC3B,WAAW,EAAEO,KAAK,CAAC;MAC9F;IACF,CAAC;IACD,YAAY,EAAEV,MAAM,CAACH,SAAS,CAACgD,IAAI;IACnCC,kBAAkB,EAAEd,CAAC,IAAI;MACvB,IAAIf,EAAE;MACNF,YAAY,CAACG,OAAO,GAAG,IAAI;MAC3B,CAACD,EAAE,GAAGd,WAAW,CAAC2C,kBAAkB,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC3B,WAAW,EAAE6B,CAAC,CAAC;IACpG,CAAC;IACDe,gBAAgB,EAAEf,CAAC,IAAI;MACrB,IAAIf,EAAE;MACNF,YAAY,CAACG,OAAO,GAAG,KAAK;MAC5B,CAACD,EAAE,GAAGd,WAAW,CAAC4C,gBAAgB,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAC3B,WAAW,EAAE6B,CAAC,CAAC;IAClG;EACF,CAAC,CAAC,CAAC,EAAET,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}