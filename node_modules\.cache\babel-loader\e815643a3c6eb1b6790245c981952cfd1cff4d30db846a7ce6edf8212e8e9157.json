{"ast": null, "code": "import { DownFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useShouldRender } from '../../utils/should-render';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { IconContext } from './context';\nconst classPrefix = `adm-dropdown-item`;\nconst Item = props => {\n  const {\n    dropdown: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const {\n    active,\n    highlight,\n    onClick,\n    title\n  } = mergedProps;\n  const cls = classNames(classPrefix, {\n    [`${classPrefix}-active`]: active,\n    [`${classPrefix}-highlight`]: highlight !== null && highlight !== void 0 ? highlight : active\n  });\n  const contextArrowIcon = React.useContext(IconContext);\n  const mergedArrowIcon = mergeProp(React.createElement(DownFill, null), contextArrowIcon, mergedProps.arrow, mergedProps.arrowIcon);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: cls,\n    onClick: onClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-title-text`\n  }, title), React.createElement(\"span\", {\n    className: classNames(`${classPrefix}-title-arrow`, {\n      [`${classPrefix}-title-arrow-active`]: active\n    })\n  }, mergedArrowIcon))));\n};\nexport default Item;\nexport const ItemChildrenWrap = props => {\n  const {\n    active = false\n  } = props;\n  const shouldRender = useShouldRender(active, props.forceRender, props.destroyOnClose);\n  const cls = classNames(`${classPrefix}-content`, {\n    [`${classPrefix}-content-hidden`]: !active\n  });\n  return shouldRender ? React.createElement(\"div\", {\n    className: cls,\n    onClick: props.onClick\n  }, props.children) : null;\n};", "map": {"version": 3, "names": ["DownFill", "classNames", "React", "withNativeProps", "useShouldRender", "mergeProp", "mergeProps", "useConfig", "IconContext", "classPrefix", "<PERSON><PERSON>", "props", "dropdown", "componentConfig", "mergedProps", "active", "highlight", "onClick", "title", "cls", "contextArrowIcon", "useContext", "mergedArrowIcon", "createElement", "arrow", "arrowIcon", "className", "ItemC<PERSON>dren<PERSON>", "shouldRender", "forceRender", "destroyOnClose", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/dropdown/item.js"], "sourcesContent": ["import { DownFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useShouldRender } from '../../utils/should-render';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport { IconContext } from './context';\nconst classPrefix = `adm-dropdown-item`;\nconst Item = props => {\n  const {\n    dropdown: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const {\n    active,\n    highlight,\n    onClick,\n    title\n  } = mergedProps;\n  const cls = classNames(classPrefix, {\n    [`${classPrefix}-active`]: active,\n    [`${classPrefix}-highlight`]: highlight !== null && highlight !== void 0 ? highlight : active\n  });\n  const contextArrowIcon = React.useContext(IconContext);\n  const mergedArrowIcon = mergeProp(React.createElement(DownFill, null), contextArrowIcon, mergedProps.arrow, mergedProps.arrowIcon);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: cls,\n    onClick: onClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, React.createElement(\"span\", {\n    className: `${classPrefix}-title-text`\n  }, title), React.createElement(\"span\", {\n    className: classNames(`${classPrefix}-title-arrow`, {\n      [`${classPrefix}-title-arrow-active`]: active\n    })\n  }, mergedArrowIcon))));\n};\nexport default Item;\nexport const ItemChildrenWrap = props => {\n  const {\n    active = false\n  } = props;\n  const shouldRender = useShouldRender(active, props.forceRender, props.destroyOnClose);\n  const cls = classNames(`${classPrefix}-content`, {\n    [`${classPrefix}-content-hidden`]: !active\n  });\n  return shouldRender ? React.createElement(\"div\", {\n    className: cls,\n    onClick: props.onClick\n  }, props.children) : null;\n};"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,WAAW,QAAQ,WAAW;AACvC,MAAMC,WAAW,GAAG,mBAAmB;AACvC,MAAMC,IAAI,GAAGC,KAAK,IAAI;EACpB,MAAM;IACJC,QAAQ,EAAEC,eAAe,GAAG,CAAC;EAC/B,CAAC,GAAGN,SAAS,CAAC,CAAC;EACf,MAAMO,WAAW,GAAGR,UAAU,CAACO,eAAe,EAAEF,KAAK,CAAC;EACtD,MAAM;IACJI,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGJ,WAAW;EACf,MAAMK,GAAG,GAAGlB,UAAU,CAACQ,WAAW,EAAE;IAClC,CAAC,GAAGA,WAAW,SAAS,GAAGM,MAAM;IACjC,CAAC,GAAGN,WAAW,YAAY,GAAGO,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGD;EACzF,CAAC,CAAC;EACF,MAAMK,gBAAgB,GAAGlB,KAAK,CAACmB,UAAU,CAACb,WAAW,CAAC;EACtD,MAAMc,eAAe,GAAGjB,SAAS,CAACH,KAAK,CAACqB,aAAa,CAACvB,QAAQ,EAAE,IAAI,CAAC,EAAEoB,gBAAgB,EAAEN,WAAW,CAACU,KAAK,EAAEV,WAAW,CAACW,SAAS,CAAC;EAClI,OAAOtB,eAAe,CAACQ,KAAK,EAAET,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IACvDG,SAAS,EAAEP,GAAG;IACdF,OAAO,EAAEA;EACX,CAAC,EAAEf,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC5BG,SAAS,EAAE,GAAGjB,WAAW;EAC3B,CAAC,EAAEP,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;IAC7BG,SAAS,EAAE,GAAGjB,WAAW;EAC3B,CAAC,EAAES,KAAK,CAAC,EAAEhB,KAAK,CAACqB,aAAa,CAAC,MAAM,EAAE;IACrCG,SAAS,EAAEzB,UAAU,CAAC,GAAGQ,WAAW,cAAc,EAAE;MAClD,CAAC,GAAGA,WAAW,qBAAqB,GAAGM;IACzC,CAAC;EACH,CAAC,EAAEO,eAAe,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC;AACD,eAAeZ,IAAI;AACnB,OAAO,MAAMiB,gBAAgB,GAAGhB,KAAK,IAAI;EACvC,MAAM;IACJI,MAAM,GAAG;EACX,CAAC,GAAGJ,KAAK;EACT,MAAMiB,YAAY,GAAGxB,eAAe,CAACW,MAAM,EAAEJ,KAAK,CAACkB,WAAW,EAAElB,KAAK,CAACmB,cAAc,CAAC;EACrF,MAAMX,GAAG,GAAGlB,UAAU,CAAC,GAAGQ,WAAW,UAAU,EAAE;IAC/C,CAAC,GAAGA,WAAW,iBAAiB,GAAG,CAACM;EACtC,CAAC,CAAC;EACF,OAAOa,YAAY,GAAG1B,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC/CG,SAAS,EAAEP,GAAG;IACdF,OAAO,EAAEN,KAAK,CAACM;EACjB,CAAC,EAAEN,KAAK,CAACoB,QAAQ,CAAC,GAAG,IAAI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}