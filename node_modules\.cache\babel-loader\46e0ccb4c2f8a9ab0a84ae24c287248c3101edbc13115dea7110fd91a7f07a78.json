{"ast": null, "code": "import \"./side-bar.css\";\nimport { SideBar, SideBarItem } from './side-bar';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(SideBar, {\n  Item: SideBarItem\n});", "map": {"version": 3, "names": ["SideBar", "SideBarItem", "attachPropertiesToComponent", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/side-bar/index.js"], "sourcesContent": ["import \"./side-bar.css\";\nimport { SideBar, SideBarItem } from './side-bar';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nexport default attachPropertiesToComponent(SideBar, {\n  Item: SideBarItem\n});"], "mappings": "AAAA,OAAO,gBAAgB;AACvB,SAASA,OAAO,EAAEC,WAAW,QAAQ,YAAY;AACjD,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,eAAeA,2BAA2B,CAACF,OAAO,EAAE;EAClDG,IAAI,EAAEF;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}