{"ast": null, "code": "import * as React from 'react';\nimport { useRef } from 'react';\nimport { animationEndName, transitionEndName } from \"../util/motion\";\nexport default (function (onInternalMotionEnd) {\n  var cacheElementRef = useRef();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(transitionEndName, onInternalMotionEnd);\n      element.addEventListener(animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  React.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}