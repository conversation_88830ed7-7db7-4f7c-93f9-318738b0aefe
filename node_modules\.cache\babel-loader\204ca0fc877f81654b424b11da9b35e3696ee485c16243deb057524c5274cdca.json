{"ast": null, "code": "import { PickerView } from './picker-view';\nimport \"./picker-view.css\";\nexport default PickerView;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/picker-view/index.js"], "sourcesContent": ["import { PickerView } from './picker-view';\nimport \"./picker-view.css\";\nexport default PickerView;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,mBAAmB;AAC1B,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}