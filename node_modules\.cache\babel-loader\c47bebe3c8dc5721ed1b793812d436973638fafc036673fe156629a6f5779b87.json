{"ast": null, "code": "// 找到树的深度\nexport function getTreeDeep(treeData, childrenName = 'children') {\n  const walker = tree => {\n    let deep = 0;\n    tree.forEach(item => {\n      if (item[childrenName]) {\n        deep = Math.max(deep, walker(item[childrenName]) + 1);\n      } else {\n        deep = Math.max(deep, 1);\n      }\n    });\n    return deep;\n  };\n  return walker(treeData);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}