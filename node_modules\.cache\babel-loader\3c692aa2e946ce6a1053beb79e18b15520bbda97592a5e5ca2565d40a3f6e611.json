{"ast": null, "code": "import \"./mask.css\";\nimport { Mask } from './mask';\nexport default Mask;", "map": {"version": 3, "names": ["Mask"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/mask/index.js"], "sourcesContent": ["import \"./mask.css\";\nimport { Mask } from './mask';\nexport default Mask;"], "mappings": "AAAA,OAAO,YAAY;AACnB,SAASA,IAAI,QAAQ,QAAQ;AAC7B,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}