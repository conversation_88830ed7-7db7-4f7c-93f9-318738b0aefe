{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-step`;\nexport const Step = props => {\n  const {\n    title,\n    description,\n    icon,\n    status = 'wait'\n  } = props;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, `${classPrefix}-status-${status}`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-indicator`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon-container`\n  }, icon)), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), !!description && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description))));\n};", "map": {"version": 3, "names": ["React", "classNames", "withNativeProps", "classPrefix", "Step", "props", "title", "description", "icon", "status", "createElement", "className"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/steps/step.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nconst classPrefix = `adm-step`;\nexport const Step = props => {\n  const {\n    title,\n    description,\n    icon,\n    status = 'wait'\n  } = props;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(`${classPrefix}`, `${classPrefix}-status-${status}`)\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-indicator`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-icon-container`\n  }, icon)), React.createElement(\"div\", {\n    className: `${classPrefix}-content`\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-title`\n  }, title), !!description && React.createElement(\"div\", {\n    className: `${classPrefix}-description`\n  }, description))));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,MAAMC,WAAW,GAAG,UAAU;AAC9B,OAAO,MAAMC,IAAI,GAAGC,KAAK,IAAI;EAC3B,MAAM;IACJC,KAAK;IACLC,WAAW;IACXC,IAAI;IACJC,MAAM,GAAG;EACX,CAAC,GAAGJ,KAAK;EACT,OAAOH,eAAe,CAACG,KAAK,EAAEL,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEV,UAAU,CAAC,GAAGE,WAAW,EAAE,EAAE,GAAGA,WAAW,WAAWM,MAAM,EAAE;EAC3E,CAAC,EAAET,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEH,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEK,IAAI,CAAC,CAAC,EAAER,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACpCC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEH,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEG,KAAK,CAAC,EAAE,CAAC,CAACC,WAAW,IAAIP,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACrDC,SAAS,EAAE,GAAGR,WAAW;EAC3B,CAAC,EAAEI,WAAW,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}