{"ast": null, "code": "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function () {\n      return true;\n    };\n  }\n  var _a = __read(useRafState(), 2),\n    position = _a[0],\n    setPosition = _a[1];\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var updatePosition = function () {\n      var newPosition;\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\nexport default useScroll;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}