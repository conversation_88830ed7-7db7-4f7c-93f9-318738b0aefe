{"ast": null, "code": "import { ConfigProvider } from './config-provider';\nexport { useConfig, setDefaultConfig, getDefaultConfig } from './config-provider';\nexport default ConfigProvider;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useConfig", "setDefaultConfig", "getDefaultConfig"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/config-provider/index.js"], "sourcesContent": ["import { ConfigProvider } from './config-provider';\nexport { useConfig, setDefaultConfig, getDefaultConfig } from './config-provider';\nexport default ConfigProvider;"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAClD,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,mBAAmB;AACjF,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}