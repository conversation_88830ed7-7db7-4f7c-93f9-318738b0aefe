{"ast": null, "code": "import \"./safe-area.css\";\nimport { SafeArea } from './safe-area';\nexport default SafeArea;", "map": {"version": 3, "names": ["SafeArea"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/safe-area/index.js"], "sourcesContent": ["import \"./safe-area.css\";\nimport { SafeArea } from './safe-area';\nexport default SafeArea;"], "mappings": "AAAA,OAAO,iBAAiB;AACxB,SAASA,QAAQ,QAAQ,aAAa;AACtC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}