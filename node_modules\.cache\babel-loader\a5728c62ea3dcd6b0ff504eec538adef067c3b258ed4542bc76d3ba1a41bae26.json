{"ast": null, "code": "import { useMemo } from 'react';\nimport { withCache } from '../../utils/with-cache';\nexport function generateColumnsExtend(rawColumns, val) {\n  const columns = withCache(() => {\n    const c = typeof rawColumns === 'function' ? rawColumns(val) : rawColumns;\n    return c.map(column => column.map(item => typeof item === 'string' ? {\n      label: item,\n      value: item\n    } : item));\n  });\n  const items = withCache(() => {\n    return val.map((v, index) => {\n      var _a;\n      const column = columns()[index];\n      if (!column) return null;\n      return (_a = column.find(item => item.value === v)) !== null && _a !== void 0 ? _a : null;\n    });\n  });\n  const extend = {\n    get columns() {\n      return columns();\n    },\n    get items() {\n      return items();\n    }\n  };\n  return extend;\n}\nexport function useColumnsExtend(rawColumns, value) {\n  return useMemo(() => generateColumnsExtend(rawColumns, value), [rawColumns, value]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}