{"ast": null, "code": "import { __assign } from \"tslib\";\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\nvar useFusionTable = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\nexport default useFusionTable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}