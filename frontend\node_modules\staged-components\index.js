"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.staged = void 0;
const react_1 = __importDefault(require("react"));
function processNext(next) {
    if (typeof next === 'function') {
        return (react_1.default.createElement(Stage, { stage: next }));
    }
    else {
        return next;
    }
}
function Stage(props) {
    const next = props.stage();
    return processNext(next);
}
function staged(stage) {
    return function Staged(props, ref) {
        const next = stage(props, ref);
        return processNext(next);
    };
}
exports.staged = staged;
