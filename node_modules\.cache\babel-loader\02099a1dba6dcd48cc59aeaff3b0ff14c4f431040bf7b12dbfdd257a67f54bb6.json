{"ast": null, "code": "import { useEffect, useRef, useState } from 'react';\nexport function useRefState(initialState) {\n  const [state, setState] = useState(initialState);\n  const ref = useRef(state);\n  useEffect(() => {\n    ref.current = state;\n  }, [state]);\n  return [state, setState, ref];\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useRefState", "initialState", "state", "setState", "ref", "current"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/use-ref-state.js"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nexport function useRefState(initialState) {\n  const [state, setState] = useState(initialState);\n  const ref = useRef(state);\n  useEffect(() => {\n    ref.current = state;\n  }, [state]);\n  return [state, setState, ref];\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAO,SAASC,WAAWA,CAACC,YAAY,EAAE;EACxC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGJ,QAAQ,CAACE,YAAY,CAAC;EAChD,MAAMG,GAAG,GAAGN,MAAM,CAACI,KAAK,CAAC;EACzBL,SAAS,CAAC,MAAM;IACdO,GAAG,CAACC,OAAO,GAAGH,KAAK;EACrB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAO,CAACA,KAAK,EAAEC,QAAQ,EAAEC,GAAG,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}