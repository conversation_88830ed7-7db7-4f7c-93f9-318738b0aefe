{"ast": null, "code": "import \"./index-bar.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Panel } from './panel';\nimport { IndexBar } from './index-bar';\nexport default attachPropertiesToComponent(IndexBar, {\n  Panel\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Panel", "IndexBar"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/index-bar/index.js"], "sourcesContent": ["import \"./index-bar.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Panel } from './panel';\nimport { IndexBar } from './index-bar';\nexport default attachPropertiesToComponent(IndexBar, {\n  Panel\n});"], "mappings": "AAAA,OAAO,iBAAiB;AACxB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,QAAQ,QAAQ,aAAa;AACtC,eAAeF,2BAA2B,CAACE,QAAQ,EAAE;EACnDD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}