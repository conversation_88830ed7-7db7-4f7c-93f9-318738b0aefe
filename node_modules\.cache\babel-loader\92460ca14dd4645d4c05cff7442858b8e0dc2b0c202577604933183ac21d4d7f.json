{"ast": null, "code": "import { useIsomorphicLayoutEffect } from 'ahooks';\nimport { CloseCircleFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = 'adm-virtual-input';\nconst defaultProps = {\n  defaultValue: ''\n};\nexport const VirtualInput = forwardRef((props, ref) => {\n  const {\n    locale,\n    input: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const rootRef = useRef(null);\n  const contentRef = useRef(null);\n  const [hasFocus, setHasFocus] = useState(false);\n  const clearIcon = mergeProp(React.createElement(CloseCircleFill, null), componentConfig.clearIcon, props.clearIcon);\n  function scrollToEnd() {\n    const root = rootRef.current;\n    if (!root) return;\n    if (document.activeElement !== root) {\n      return;\n    }\n    const content = contentRef.current;\n    if (!content) return;\n    content.scrollLeft = content.clientWidth;\n  }\n  useIsomorphicLayoutEffect(() => {\n    scrollToEnd();\n  }, [value]);\n  useEffect(() => {\n    if (hasFocus) {\n      scrollToEnd();\n    }\n  }, [hasFocus]);\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    }\n  }));\n  function onFocus() {\n    var _a;\n    setHasFocus(true);\n    (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  function onBlur() {\n    var _a;\n    setHasFocus(false);\n    (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  const keyboard = mergedProps.keyboard;\n  const keyboardElement = keyboard && React.cloneElement(keyboard, {\n    onInput: v => {\n      var _a, _b;\n      setValue(value + v);\n      (_b = (_a = keyboard.props).onInput) === null || _b === void 0 ? void 0 : _b.call(_a, v);\n    },\n    onDelete: () => {\n      var _a, _b;\n      setValue(value.slice(0, -1));\n      (_b = (_a = keyboard.props).onDelete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    },\n    visible: hasFocus,\n    onClose: () => {\n      var _a, _b, _c, _d;\n      const activeElement = document.activeElement;\n      // Long press makes `activeElement` to be the child of rootRef\n      // We will trigger blur on the child element instead\n      if (activeElement && ((_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.contains(activeElement))) {\n        activeElement.blur();\n      } else {\n        (_b = rootRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n      }\n      (_d = (_c = keyboard.props).onClose) === null || _d === void 0 ? void 0 : _d.call(_c);\n    },\n    getContainer: null\n  });\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    ref: rootRef,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-disabled`]: mergedProps.disabled\n    }),\n    tabIndex: mergedProps.disabled ? undefined : 0,\n    role: 'textbox',\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onClick: mergedProps.onClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    ref: contentRef,\n    \"aria-disabled\": mergedProps.disabled,\n    \"aria-label\": mergedProps.placeholder\n  }, value, React.createElement(\"div\", {\n    className: `${classPrefix}-caret-container`\n  }, hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-caret`\n  }))), mergedProps.clearable && !!value && hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-clear`,\n    onClick: e => {\n      var _a;\n      e.stopPropagation();\n      setValue('');\n      (_a = mergedProps.onClear) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    },\n    role: 'button',\n    \"aria-label\": locale.Input.clear\n  }, clearIcon), [undefined, null, ''].includes(value) && React.createElement(\"div\", {\n    className: `${classPrefix}-placeholder`\n  }, mergedProps.placeholder), keyboardElement));\n});", "map": {"version": 3, "names": ["useIsomorphicLayoutEffect", "CloseCircleFill", "classNames", "React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "useState", "withNativeProps", "usePropsValue", "mergeProp", "mergeProps", "useConfig", "classPrefix", "defaultProps", "defaultValue", "VirtualInput", "props", "ref", "locale", "input", "componentConfig", "mergedProps", "value", "setValue", "rootRef", "contentRef", "hasFocus", "setHasFocus", "clearIcon", "createElement", "scrollToEnd", "root", "current", "document", "activeElement", "content", "scrollLeft", "clientWidth", "focus", "_a", "blur", "onFocus", "call", "onBlur", "keyboard", "keyboardElement", "cloneElement", "onInput", "v", "_b", "onDelete", "slice", "visible", "onClose", "_c", "_d", "contains", "getContainer", "className", "disabled", "tabIndex", "undefined", "role", "onClick", "placeholder", "clearable", "e", "stopPropagation", "onClear", "Input", "clear", "includes"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/virtual-input/virtual-input.js"], "sourcesContent": ["import { useIsomorphicLayoutEffect } from 'ahooks';\nimport { CloseCircleFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = 'adm-virtual-input';\nconst defaultProps = {\n  defaultValue: ''\n};\nexport const VirtualInput = forwardRef((props, ref) => {\n  const {\n    locale,\n    input: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const rootRef = useRef(null);\n  const contentRef = useRef(null);\n  const [hasFocus, setHasFocus] = useState(false);\n  const clearIcon = mergeProp(React.createElement(CloseCircleFill, null), componentConfig.clearIcon, props.clearIcon);\n  function scrollToEnd() {\n    const root = rootRef.current;\n    if (!root) return;\n    if (document.activeElement !== root) {\n      return;\n    }\n    const content = contentRef.current;\n    if (!content) return;\n    content.scrollLeft = content.clientWidth;\n  }\n  useIsomorphicLayoutEffect(() => {\n    scrollToEnd();\n  }, [value]);\n  useEffect(() => {\n    if (hasFocus) {\n      scrollToEnd();\n    }\n  }, [hasFocus]);\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    }\n  }));\n  function onFocus() {\n    var _a;\n    setHasFocus(true);\n    (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  function onBlur() {\n    var _a;\n    setHasFocus(false);\n    (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  const keyboard = mergedProps.keyboard;\n  const keyboardElement = keyboard && React.cloneElement(keyboard, {\n    onInput: v => {\n      var _a, _b;\n      setValue(value + v);\n      (_b = (_a = keyboard.props).onInput) === null || _b === void 0 ? void 0 : _b.call(_a, v);\n    },\n    onDelete: () => {\n      var _a, _b;\n      setValue(value.slice(0, -1));\n      (_b = (_a = keyboard.props).onDelete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    },\n    visible: hasFocus,\n    onClose: () => {\n      var _a, _b, _c, _d;\n      const activeElement = document.activeElement;\n      // Long press makes `activeElement` to be the child of rootRef\n      // We will trigger blur on the child element instead\n      if (activeElement && ((_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.contains(activeElement))) {\n        activeElement.blur();\n      } else {\n        (_b = rootRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n      }\n      (_d = (_c = keyboard.props).onClose) === null || _d === void 0 ? void 0 : _d.call(_c);\n    },\n    getContainer: null\n  });\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    ref: rootRef,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-disabled`]: mergedProps.disabled\n    }),\n    tabIndex: mergedProps.disabled ? undefined : 0,\n    role: 'textbox',\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onClick: mergedProps.onClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    ref: contentRef,\n    \"aria-disabled\": mergedProps.disabled,\n    \"aria-label\": mergedProps.placeholder\n  }, value, React.createElement(\"div\", {\n    className: `${classPrefix}-caret-container`\n  }, hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-caret`\n  }))), mergedProps.clearable && !!value && hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-clear`,\n    onClick: e => {\n      var _a;\n      e.stopPropagation();\n      setValue('');\n      (_a = mergedProps.onClear) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    },\n    role: 'button',\n    \"aria-label\": locale.Input.clear\n  }, clearIcon), [undefined, null, ''].includes(value) && React.createElement(\"div\", {\n    className: `${classPrefix}-placeholder`\n  }, mergedProps.placeholder), keyboardElement));\n});"], "mappings": "AAAA,SAASA,yBAAyB,QAAQ,QAAQ;AAClD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC3F,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,MAAMC,WAAW,GAAG,mBAAmB;AACvC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,YAAY,GAAGb,UAAU,CAAC,CAACc,KAAK,EAAEC,GAAG,KAAK;EACrD,MAAM;IACJC,MAAM;IACNC,KAAK,EAAEC,eAAe,GAAG,CAAC;EAC5B,CAAC,GAAGT,SAAS,CAAC,CAAC;EACf,MAAMU,WAAW,GAAGX,UAAU,CAACG,YAAY,EAAEO,eAAe,EAAEJ,KAAK,CAAC;EACpE,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGf,aAAa,CAACa,WAAW,CAAC;EACpD,MAAMG,OAAO,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMoB,UAAU,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMsB,SAAS,GAAGnB,SAAS,CAACR,KAAK,CAAC4B,aAAa,CAAC9B,eAAe,EAAE,IAAI,CAAC,EAAEqB,eAAe,CAACQ,SAAS,EAAEZ,KAAK,CAACY,SAAS,CAAC;EACnH,SAASE,WAAWA,CAAA,EAAG;IACrB,MAAMC,IAAI,GAAGP,OAAO,CAACQ,OAAO;IAC5B,IAAI,CAACD,IAAI,EAAE;IACX,IAAIE,QAAQ,CAACC,aAAa,KAAKH,IAAI,EAAE;MACnC;IACF;IACA,MAAMI,OAAO,GAAGV,UAAU,CAACO,OAAO;IAClC,IAAI,CAACG,OAAO,EAAE;IACdA,OAAO,CAACC,UAAU,GAAGD,OAAO,CAACE,WAAW;EAC1C;EACAvC,yBAAyB,CAAC,MAAM;IAC9BgC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACR,KAAK,CAAC,CAAC;EACXnB,SAAS,CAAC,MAAM;IACd,IAAIuB,QAAQ,EAAE;MACZI,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EACdtB,mBAAmB,CAACa,GAAG,EAAE,OAAO;IAC9BqB,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGf,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,KAAK,CAAC,CAAC;IACxE,CAAC;IACDE,IAAI,EAAEA,CAAA,KAAM;MACV,IAAID,EAAE;MACN,CAACA,EAAE,GAAGf,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC,CAAC;IACvE;EACF,CAAC,CAAC,CAAC;EACH,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIF,EAAE;IACNZ,WAAW,CAAC,IAAI,CAAC;IACjB,CAACY,EAAE,GAAGlB,WAAW,CAACoB,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACrB,WAAW,CAAC;EACtF;EACA,SAASsB,MAAMA,CAAA,EAAG;IAChB,IAAIJ,EAAE;IACNZ,WAAW,CAAC,KAAK,CAAC;IAClB,CAACY,EAAE,GAAGlB,WAAW,CAACsB,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACrB,WAAW,CAAC;EACrF;EACA,MAAMuB,QAAQ,GAAGvB,WAAW,CAACuB,QAAQ;EACrC,MAAMC,eAAe,GAAGD,QAAQ,IAAI3C,KAAK,CAAC6C,YAAY,CAACF,QAAQ,EAAE;IAC/DG,OAAO,EAAEC,CAAC,IAAI;MACZ,IAAIT,EAAE,EAAEU,EAAE;MACV1B,QAAQ,CAACD,KAAK,GAAG0B,CAAC,CAAC;MACnB,CAACC,EAAE,GAAG,CAACV,EAAE,GAAGK,QAAQ,CAAC5B,KAAK,EAAE+B,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,IAAI,CAACH,EAAE,EAAES,CAAC,CAAC;IAC1F,CAAC;IACDE,QAAQ,EAAEA,CAAA,KAAM;MACd,IAAIX,EAAE,EAAEU,EAAE;MACV1B,QAAQ,CAACD,KAAK,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B,CAACF,EAAE,GAAG,CAACV,EAAE,GAAGK,QAAQ,CAAC5B,KAAK,EAAEkC,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,IAAI,CAACH,EAAE,CAAC;IACxF,CAAC;IACDa,OAAO,EAAE1B,QAAQ;IACjB2B,OAAO,EAAEA,CAAA,KAAM;MACb,IAAId,EAAE,EAAEU,EAAE,EAAEK,EAAE,EAAEC,EAAE;MAClB,MAAMrB,aAAa,GAAGD,QAAQ,CAACC,aAAa;MAC5C;MACA;MACA,IAAIA,aAAa,KAAK,CAACK,EAAE,GAAGf,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,QAAQ,CAACtB,aAAa,CAAC,CAAC,EAAE;QAC7GA,aAAa,CAACM,IAAI,CAAC,CAAC;MACtB,CAAC,MAAM;QACL,CAACS,EAAE,GAAGzB,OAAO,CAACQ,OAAO,MAAM,IAAI,IAAIiB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACT,IAAI,CAAC,CAAC;MACvE;MACA,CAACe,EAAE,GAAG,CAACD,EAAE,GAAGV,QAAQ,CAAC5B,KAAK,EAAEqC,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACb,IAAI,CAACY,EAAE,CAAC;IACvF,CAAC;IACDG,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAOlD,eAAe,CAACc,WAAW,EAAEpB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC7DZ,GAAG,EAAEO,OAAO;IACZkC,SAAS,EAAE1D,UAAU,CAACY,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,WAAW,GAAGS,WAAW,CAACsC;IAC3C,CAAC,CAAC;IACFC,QAAQ,EAAEvC,WAAW,CAACsC,QAAQ,GAAGE,SAAS,GAAG,CAAC;IAC9CC,IAAI,EAAE,SAAS;IACfrB,OAAO,EAAEA,OAAO;IAChBE,MAAM,EAAEA,MAAM;IACdoB,OAAO,EAAE1C,WAAW,CAAC0C;EACvB,CAAC,EAAE9D,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC5B6B,SAAS,EAAE,GAAG9C,WAAW,UAAU;IACnCK,GAAG,EAAEQ,UAAU;IACf,eAAe,EAAEJ,WAAW,CAACsC,QAAQ;IACrC,YAAY,EAAEtC,WAAW,CAAC2C;EAC5B,CAAC,EAAE1C,KAAK,EAAErB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACnC6B,SAAS,EAAE,GAAG9C,WAAW;EAC3B,CAAC,EAAEc,QAAQ,IAAIzB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACxC6B,SAAS,EAAE,GAAG9C,WAAW;EAC3B,CAAC,CAAC,CAAC,CAAC,EAAES,WAAW,CAAC4C,SAAS,IAAI,CAAC,CAAC3C,KAAK,IAAII,QAAQ,IAAIzB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IAC/E6B,SAAS,EAAE,GAAG9C,WAAW,QAAQ;IACjCmD,OAAO,EAAEG,CAAC,IAAI;MACZ,IAAI3B,EAAE;MACN2B,CAAC,CAACC,eAAe,CAAC,CAAC;MACnB5C,QAAQ,CAAC,EAAE,CAAC;MACZ,CAACgB,EAAE,GAAGlB,WAAW,CAAC+C,OAAO,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACrB,WAAW,CAAC;IACtF,CAAC;IACDyC,IAAI,EAAE,QAAQ;IACd,YAAY,EAAE5C,MAAM,CAACmD,KAAK,CAACC;EAC7B,CAAC,EAAE1C,SAAS,CAAC,EAAE,CAACiC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAACU,QAAQ,CAACjD,KAAK,CAAC,IAAIrB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACjF6B,SAAS,EAAE,GAAG9C,WAAW;EAC3B,CAAC,EAAES,WAAW,CAAC2C,WAAW,CAAC,EAAEnB,eAAe,CAAC,CAAC;AAChD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}