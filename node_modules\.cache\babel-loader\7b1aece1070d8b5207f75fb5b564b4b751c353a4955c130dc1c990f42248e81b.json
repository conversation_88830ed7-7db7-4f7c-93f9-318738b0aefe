{"ast": null, "code": "import { useIsomorphicLayoutEffect } from 'ahooks';\nimport { CloseCircleFill } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nconst classPrefix = 'adm-virtual-input';\nconst defaultProps = {\n  defaultValue: ''\n};\nexport const VirtualInput = forwardRef((props, ref) => {\n  const {\n    locale,\n    input: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(defaultProps, componentConfig, props);\n  const [value, setValue] = usePropsValue(mergedProps);\n  const rootRef = useRef(null);\n  const contentRef = useRef(null);\n  const [hasFocus, setHasFocus] = useState(false);\n  const clearIcon = mergeProp(React.createElement(CloseCircleFill, null), componentConfig.clearIcon, props.clearIcon);\n  function scrollToEnd() {\n    const root = rootRef.current;\n    if (!root) return;\n    if (document.activeElement !== root) {\n      return;\n    }\n    const content = contentRef.current;\n    if (!content) return;\n    content.scrollLeft = content.clientWidth;\n  }\n  useIsomorphicLayoutEffect(() => {\n    scrollToEnd();\n  }, [value]);\n  useEffect(() => {\n    if (hasFocus) {\n      scrollToEnd();\n    }\n  }, [hasFocus]);\n  useImperativeHandle(ref, () => ({\n    focus: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    }\n  }));\n  function onFocus() {\n    var _a;\n    setHasFocus(true);\n    (_a = mergedProps.onFocus) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  function onBlur() {\n    var _a;\n    setHasFocus(false);\n    (_a = mergedProps.onBlur) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n  }\n  const keyboard = mergedProps.keyboard;\n  const keyboardElement = keyboard && React.cloneElement(keyboard, {\n    onInput: v => {\n      var _a, _b;\n      setValue(value + v);\n      (_b = (_a = keyboard.props).onInput) === null || _b === void 0 ? void 0 : _b.call(_a, v);\n    },\n    onDelete: () => {\n      var _a, _b;\n      setValue(value.slice(0, -1));\n      (_b = (_a = keyboard.props).onDelete) === null || _b === void 0 ? void 0 : _b.call(_a);\n    },\n    visible: hasFocus,\n    onClose: () => {\n      var _a, _b, _c, _d;\n      const activeElement = document.activeElement;\n      // Long press makes `activeElement` to be the child of rootRef\n      // We will trigger blur on the child element instead\n      if (activeElement && ((_a = rootRef.current) === null || _a === void 0 ? void 0 : _a.contains(activeElement))) {\n        activeElement.blur();\n      } else {\n        (_b = rootRef.current) === null || _b === void 0 ? void 0 : _b.blur();\n      }\n      (_d = (_c = keyboard.props).onClose) === null || _d === void 0 ? void 0 : _d.call(_c);\n    },\n    getContainer: null\n  });\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    ref: rootRef,\n    className: classNames(classPrefix, {\n      [`${classPrefix}-disabled`]: mergedProps.disabled\n    }),\n    tabIndex: mergedProps.disabled ? undefined : 0,\n    role: 'textbox',\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onClick: mergedProps.onClick\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-content`,\n    ref: contentRef,\n    \"aria-disabled\": mergedProps.disabled,\n    \"aria-label\": mergedProps.placeholder\n  }, value, React.createElement(\"div\", {\n    className: `${classPrefix}-caret-container`\n  }, hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-caret`\n  }))), mergedProps.clearable && !!value && hasFocus && React.createElement(\"div\", {\n    className: `${classPrefix}-clear`,\n    onClick: e => {\n      var _a;\n      e.stopPropagation();\n      setValue('');\n      (_a = mergedProps.onClear) === null || _a === void 0 ? void 0 : _a.call(mergedProps);\n    },\n    role: 'button',\n    \"aria-label\": locale.Input.clear\n  }, clearIcon), [undefined, null, ''].includes(value) && React.createElement(\"div\", {\n    className: `${classPrefix}-placeholder`\n  }, mergedProps.placeholder), keyboardElement));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}