{"ast": null, "code": "import React from 'react';\nimport { renderImperatively } from '../../utils/render-imperatively';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { InternalToast } from './toast';\nlet currentHandler = null;\nlet currentTimeout = null;\nconst defaultProps = {\n  duration: 2000,\n  position: 'center',\n  maskClickable: true\n};\nconst ToastInner = props => React.createElement(InternalToast, Object.assign({}, props));\nexport function show(p) {\n  var _a;\n  const props = mergeProps(defaultProps, typeof p === 'string' ? {\n    content: p\n  } : p);\n  const element = React.createElement(ToastInner, Object.assign({}, props, {\n    onClose: () => {\n      currentHandler = null;\n    }\n  }));\n  if (currentHandler) {\n    if ((_a = currentHandler.isRendered) === null || _a === void 0 ? void 0 : _a.call(currentHandler)) {\n      currentHandler.replace(element);\n    } else {\n      currentHandler.close();\n      currentHandler = renderImperatively(element);\n    }\n  } else {\n    currentHandler = renderImperatively(element);\n  }\n  if (currentTimeout) {\n    window.clearTimeout(currentTimeout);\n  }\n  if (props.duration !== 0) {\n    currentTimeout = window.setTimeout(() => {\n      clear();\n    }, props.duration);\n  }\n  return currentHandler;\n}\nexport function clear() {\n  currentHandler === null || currentHandler === void 0 ? void 0 : currentHandler.close();\n  currentHandler = null;\n}\nexport function config(val) {\n  if (val.duration !== undefined) {\n    defaultProps.duration = val.duration;\n  }\n  if (val.position !== undefined) {\n    defaultProps.position = val.position;\n  }\n  if (val.maskClickable !== undefined) {\n    defaultProps.maskClickable = val.maskClickable;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}