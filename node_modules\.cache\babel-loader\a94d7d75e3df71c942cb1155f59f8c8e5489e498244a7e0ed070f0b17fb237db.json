{"ast": null, "code": "const record = {\n  'topLeft': 'top-start',\n  'topRight': 'top-end',\n  'bottomLeft': 'bottom-start',\n  'bottomRight': 'bottom-end',\n  'leftTop': 'left-start',\n  'leftBottom': 'left-end',\n  'rightTop': 'right-start',\n  'rightBottom': 'right-end'\n};\nexport function normalizePlacement(placement) {\n  var _a;\n  return (_a = record[placement]) !== null && _a !== void 0 ? _a : placement;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}