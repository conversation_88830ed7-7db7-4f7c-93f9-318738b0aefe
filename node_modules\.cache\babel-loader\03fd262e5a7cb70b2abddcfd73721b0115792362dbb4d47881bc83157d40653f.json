{"ast": null, "code": "export function undefinedFallback(...items) {\n  let i;\n  for (i = 0; i < items.length; i++) {\n    if (items[i] !== undefined) break;\n  }\n  return items[i];\n}", "map": {"version": 3, "names": ["undefined<PERSON><PERSON><PERSON>", "items", "i", "length", "undefined"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/undefined-fallback.js"], "sourcesContent": ["export function undefinedFallback(...items) {\n  let i;\n  for (i = 0; i < items.length; i++) {\n    if (items[i] !== undefined) break;\n  }\n  return items[i];\n}"], "mappings": "AAAA,OAAO,SAASA,iBAAiBA,CAAC,GAAGC,KAAK,EAAE;EAC1C,IAAIC,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAID,KAAK,CAACC,CAAC,CAAC,KAAKE,SAAS,EAAE;EAC9B;EACA,OAAOH,KAAK,CAACC,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}