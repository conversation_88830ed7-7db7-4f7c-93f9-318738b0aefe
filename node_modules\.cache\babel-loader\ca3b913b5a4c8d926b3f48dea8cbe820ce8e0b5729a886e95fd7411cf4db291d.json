{"ast": null, "code": "import \"./popover.css\";\nimport \"./popover-menu.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { PopoverMenu } from './popover-menu';\nimport { Popover } from './popover';\nexport default attachPropertiesToComponent(Popover, {\n  Menu: PopoverMenu\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "PopoverMenu", "Popover", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/popover/index.js"], "sourcesContent": ["import \"./popover.css\";\nimport \"./popover-menu.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { PopoverMenu } from './popover-menu';\nimport { Popover } from './popover';\nexport default attachPropertiesToComponent(Popover, {\n  Menu: PopoverMenu\n});"], "mappings": "AAAA,OAAO,eAAe;AACtB,OAAO,oBAAoB;AAC3B,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,QAAQ,WAAW;AACnC,eAAeF,2BAA2B,CAACE,OAAO,EAAE;EAClDC,IAAI,EAAEF;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}