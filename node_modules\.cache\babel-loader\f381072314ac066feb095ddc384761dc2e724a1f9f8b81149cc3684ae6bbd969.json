{"ast": null, "code": "import { useRef } from 'react';\nimport { depsEqual } from '../utils/depsEqual';\nexport var createDeepCompareEffect = function (hook) {\n  return function (effect, deps) {\n    var ref = useRef();\n    var signalRef = useRef(0);\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      signalRef.current += 1;\n    }\n    ref.current = deps;\n    hook(effect, [signalRef.current]);\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}