{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = '/api/v1';\n\n// 創建axios實例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000\n  // 移除默認Content-Type，讓瀏覽器根據請求內容自動設置\n});\n\n// 請求攔截器\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log('API請求:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url, config.data);\n  return config;\n}, error => {\n  console.error('請求攔截器錯誤:', error);\n  return Promise.reject(error);\n});\n\n// 響應攔截器\napi.interceptors.response.use(response => {\n  console.log('API響應:', response.status, response.config.url);\n  return response;\n}, error => {\n  var _error$config, _error$config2, _error$response, _error$response2;\n  console.error('API錯誤:', {\n    url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n    method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method,\n    status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n    data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n    message: error.message\n  });\n  return Promise.reject(error);\n});\n\n// 名片相關API\nexport const getCards = () => api.get('/cards/');\nexport const getCard = id => api.get(`/cards/${id}`);\nexport const createCard = cardData => {\n  // 支持FormData格式，用於文件上傳\n  const formData = new FormData();\n\n  // 如果cardData已經是FormData，直接使用\n  if (cardData instanceof FormData) {\n    return api.post('/cards/', cardData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n\n  // 否則將對象轉換為FormData\n  Object.keys(cardData).forEach(key => {\n    if (cardData[key] !== null && cardData[key] !== undefined) {\n      formData.append(key, cardData[key]);\n    }\n  });\n  return api.post('/cards/', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\nexport const updateCard = (id, cardData) => {\n  // 支持FormData格式，用於文件上傳\n  const formData = new FormData();\n\n  // 如果cardData已經是FormData，直接使用\n  if (cardData instanceof FormData) {\n    return api.put(`/cards/${id}`, cardData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n\n  // 否則將對象轉換為FormData\n  Object.keys(cardData).forEach(key => {\n    if (cardData[key] !== null && cardData[key] !== undefined) {\n      formData.append(key, cardData[key]);\n    }\n  });\n  return api.put(`/cards/${id}`, formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\nexport const deleteCard = id => api.delete(`/cards/${id}`);\nexport const exportCards = (format = 'csv') => api.get(`/cards/export/download?format=${format}`, {\n  responseType: 'blob'\n});\n\n// OCR相關API\nexport const ocrImage = file => {\n  const formData = new FormData();\n  formData.append('file', file);\n  return api.post('/ocr/image', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\nexport const parseOcrFields = (ocrText, side) => {\n  return api.post('/ocr/parse-fields', {\n    ocr_text: ocrText,\n    side: side\n  }, {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "data", "error", "Promise", "reject", "response", "status", "_error$config", "_error$config2", "_error$response", "_error$response2", "message", "getCards", "get", "getCard", "id", "createCard", "cardData", "formData", "FormData", "post", "headers", "Object", "keys", "for<PERSON>ach", "key", "undefined", "append", "updateCard", "put", "deleteCard", "delete", "exportCards", "format", "responseType", "ocrImage", "file", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>s", "ocrText", "side", "ocr_text"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v3/src/api/cards.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = '/api/v1';\r\n\r\n// 創建axios實例\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 10000,\r\n  // 移除默認Content-Type，讓瀏覽器根據請求內容自動設置\r\n});\r\n\r\n// 請求攔截器\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    console.log('API請求:', config.method?.toUpperCase(), config.url, config.data);\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('請求攔截器錯誤:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 響應攔截器\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    console.log('API響應:', response.status, response.config.url);\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error('API錯誤:', {\r\n      url: error.config?.url,\r\n      method: error.config?.method,\r\n      status: error.response?.status,\r\n      data: error.response?.data,\r\n      message: error.message\r\n    });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// 名片相關API\r\nexport const getCards = () => api.get('/cards/');\r\n\r\nexport const getCard = (id) => api.get(`/cards/${id}`);\r\n\r\nexport const createCard = (cardData) => {\r\n  // 支持FormData格式，用於文件上傳\r\n  const formData = new FormData();\r\n\r\n  // 如果cardData已經是FormData，直接使用\r\n  if (cardData instanceof FormData) {\r\n    return api.post('/cards/', cardData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  }\r\n\r\n  // 否則將對象轉換為FormData\r\n  Object.keys(cardData).forEach(key => {\r\n    if (cardData[key] !== null && cardData[key] !== undefined) {\r\n      formData.append(key, cardData[key]);\r\n    }\r\n  });\r\n\r\n  return api.post('/cards/', formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n};\r\n\r\nexport const updateCard = (id, cardData) => {\r\n  // 支持FormData格式，用於文件上傳\r\n  const formData = new FormData();\r\n\r\n  // 如果cardData已經是FormData，直接使用\r\n  if (cardData instanceof FormData) {\r\n    return api.put(`/cards/${id}`, cardData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n  }\r\n\r\n  // 否則將對象轉換為FormData\r\n  Object.keys(cardData).forEach(key => {\r\n    if (cardData[key] !== null && cardData[key] !== undefined) {\r\n      formData.append(key, cardData[key]);\r\n    }\r\n  });\r\n\r\n  return api.put(`/cards/${id}`, formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n};\r\n\r\nexport const deleteCard = (id) => api.delete(`/cards/${id}`);\r\n\r\nexport const exportCards = (format = 'csv') =>\r\n  api.get(`/cards/export/download?format=${format}`, {\r\n    responseType: 'blob',\r\n  });\r\n\r\n// OCR相關API\r\nexport const ocrImage = (file) => {\r\n  const formData = new FormData();\r\n  formData.append('file', file);\r\n\r\n  return api.post('/ocr/image', formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n};\r\n\r\nexport const parseOcrFields = (ocrText, side) => {\r\n  return api.post('/ocr/parse-fields', {\r\n    ocr_text: ocrText,\r\n    side: side\r\n  }, {\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n};\r\n\r\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,SAAS;;AAE9B;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;EACT;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAAF,cAAA,GAAED,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,EAAEN,MAAM,CAACO,IAAI,CAAC;EAC5E,OAAOP,MAAM;AACf,CAAC,EACAQ,KAAK,IAAK;EACTN,OAAO,CAACM,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAChC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACI,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC1BY,QAAQ,IAAK;EACZT,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACX,MAAM,CAACM,GAAG,CAAC;EAC3D,OAAOK,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,aAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,gBAAA;EACTd,OAAO,CAACM,KAAK,CAAC,QAAQ,EAAE;IACtBF,GAAG,GAAAO,aAAA,GAAEL,KAAK,CAACR,MAAM,cAAAa,aAAA,uBAAZA,aAAA,CAAcP,GAAG;IACtBF,MAAM,GAAAU,cAAA,GAAEN,KAAK,CAACR,MAAM,cAAAc,cAAA,uBAAZA,cAAA,CAAcV,MAAM;IAC5BQ,MAAM,GAAAG,eAAA,GAAEP,KAAK,CAACG,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBH,MAAM;IAC9BL,IAAI,GAAAS,gBAAA,GAAER,KAAK,CAACG,QAAQ,cAAAK,gBAAA,uBAAdA,gBAAA,CAAgBT,IAAI;IAC1BU,OAAO,EAAET,KAAK,CAACS;EACjB,CAAC,CAAC;EACF,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,QAAQ,GAAGA,CAAA,KAAMzB,GAAG,CAAC0B,GAAG,CAAC,SAAS,CAAC;AAEhD,OAAO,MAAMC,OAAO,GAAIC,EAAE,IAAK5B,GAAG,CAAC0B,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;AAEtD,OAAO,MAAMC,UAAU,GAAIC,QAAQ,IAAK;EACtC;EACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE/B;EACA,IAAIF,QAAQ,YAAYE,QAAQ,EAAE;IAChC,OAAOhC,GAAG,CAACiC,IAAI,CAAC,SAAS,EAAEH,QAAQ,EAAE;MACnCI,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;;EAEA;EACAC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,CAACO,OAAO,CAACC,GAAG,IAAI;IACnC,IAAIR,QAAQ,CAACQ,GAAG,CAAC,KAAK,IAAI,IAAIR,QAAQ,CAACQ,GAAG,CAAC,KAAKC,SAAS,EAAE;MACzDR,QAAQ,CAACS,MAAM,CAACF,GAAG,EAAER,QAAQ,CAACQ,GAAG,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAOtC,GAAG,CAACiC,IAAI,CAAC,SAAS,EAAEF,QAAQ,EAAE;IACnCG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMO,UAAU,GAAGA,CAACb,EAAE,EAAEE,QAAQ,KAAK;EAC1C;EACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE/B;EACA,IAAIF,QAAQ,YAAYE,QAAQ,EAAE;IAChC,OAAOhC,GAAG,CAAC0C,GAAG,CAAC,UAAUd,EAAE,EAAE,EAAEE,QAAQ,EAAE;MACvCI,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;;EAEA;EACAC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAAC,CAACO,OAAO,CAACC,GAAG,IAAI;IACnC,IAAIR,QAAQ,CAACQ,GAAG,CAAC,KAAK,IAAI,IAAIR,QAAQ,CAACQ,GAAG,CAAC,KAAKC,SAAS,EAAE;MACzDR,QAAQ,CAACS,MAAM,CAACF,GAAG,EAAER,QAAQ,CAACQ,GAAG,CAAC,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAOtC,GAAG,CAAC0C,GAAG,CAAC,UAAUd,EAAE,EAAE,EAAEG,QAAQ,EAAE;IACvCG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMS,UAAU,GAAIf,EAAE,IAAK5B,GAAG,CAAC4C,MAAM,CAAC,UAAUhB,EAAE,EAAE,CAAC;AAE5D,OAAO,MAAMiB,WAAW,GAAGA,CAACC,MAAM,GAAG,KAAK,KACxC9C,GAAG,CAAC0B,GAAG,CAAC,iCAAiCoB,MAAM,EAAE,EAAE;EACjDC,YAAY,EAAE;AAChB,CAAC,CAAC;;AAEJ;AACA,OAAO,MAAMC,QAAQ,GAAIC,IAAI,IAAK;EAChC,MAAMlB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACS,MAAM,CAAC,MAAM,EAAES,IAAI,CAAC;EAE7B,OAAOjD,GAAG,CAACiC,IAAI,CAAC,YAAY,EAAEF,QAAQ,EAAE;IACtCG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,OAAO,EAAEC,IAAI,KAAK;EAC/C,OAAOpD,GAAG,CAACiC,IAAI,CAAC,mBAAmB,EAAE;IACnCoB,QAAQ,EAAEF,OAAO;IACjBC,IAAI,EAAEA;EACR,CAAC,EAAE;IACDlB,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAelC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}