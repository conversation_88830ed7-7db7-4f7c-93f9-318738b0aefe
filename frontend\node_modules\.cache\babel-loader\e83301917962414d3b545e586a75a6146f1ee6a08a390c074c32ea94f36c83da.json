{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useState(defaultValue), 2),\n    state = _a[0],\n    setState = _a[1];\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n    var toggle = function () {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n    var set = function (value) {\n      return setState(value);\n    };\n    var setLeft = function () {\n      return setState(defaultValue);\n    };\n    var setRight = function () {\n      return setState(reverseValueOrigin);\n    };\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    };\n    // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\nexport default useToggle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}