{"ast": null, "code": "import { useEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useEffect);\nexport default useEffectWithTarget;", "map": {"version": 3, "names": ["useEffect", "createEffectWithTarget", "useEffectWithTarget"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/utils/useEffectWithTarget.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useEffect);\nexport default useEffectWithTarget;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,IAAIC,mBAAmB,GAAGD,sBAAsB,CAACD,SAAS,CAAC;AAC3D,eAAeE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}