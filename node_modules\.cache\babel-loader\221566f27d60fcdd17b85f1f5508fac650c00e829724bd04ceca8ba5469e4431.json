{"ast": null, "code": "import * as React from \"react\";\nfunction CollectMoneyOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CollectMoneyOutline-CollectMoneyOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CollectMoneyOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CollectMoneyOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 Z M38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z M14.6,18 C14.8209139,18 15,18.1790861 15,18.4 L15,30.6 C15,30.8209139 14.8209139,31 14.6,31 L12.4,31 C12.1790861,31 12,30.8209139 12,30.6 L12,18.4 C12,18.1790861 12.1790861,18 12.4,18 L14.6,18 Z M21.6,18 C21.8209139,18 22,18.1790861 22,18.4 L22,30.6 C22,30.8209139 21.8209139,31 21.6,31 L19.4,31 C19.1790861,31 19,30.8209139 19,30.6 L19,18.4 C19,18.1790861 19.1790861,18 19.4,18 L21.6,18 Z M28.6,18 C28.8209139,18 29,18.1790861 29,18.4 L29,30.6 C29,30.8209139 28.8209139,31 28.6,31 L26.4,31 C26.1790861,31 26,30.8209139 26,30.6 L26,18.4 C26,18.1790861 26.1790861,18 26.4,18 L28.6,18 Z M35.6,18 C35.8209139,18 36,18.1790861 36,18.4 L36,30.6 C36,30.8209139 35.8209139,31 35.6,31 L33.4,31 C33.1790861,31 33,30.8209139 33,30.6 L33,18.4 C33,18.1790861 33.1790861,18 33.4,18 L35.6,18 Z\",\n    id: \"CollectMoneyOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\nexport default CollectMoneyOutline;", "map": {"version": 3, "names": ["React", "CollectMoneyOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/CollectMoneyOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction CollectMoneyOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CollectMoneyOutline-CollectMoneyOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"CollectMoneyOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"CollectMoneyOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,4 C41.3137085,4 44,6.6862915 44,10 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L38,4 Z M38,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,10 C41,8.40231912 39.75108,7.09633912 38.1762728,7.00509269 L38,7 Z M14.6,18 C14.8209139,18 15,18.1790861 15,18.4 L15,30.6 C15,30.8209139 14.8209139,31 14.6,31 L12.4,31 C12.1790861,31 12,30.8209139 12,30.6 L12,18.4 C12,18.1790861 12.1790861,18 12.4,18 L14.6,18 Z M21.6,18 C21.8209139,18 22,18.1790861 22,18.4 L22,30.6 C22,30.8209139 21.8209139,31 21.6,31 L19.4,31 C19.1790861,31 19,30.8209139 19,30.6 L19,18.4 C19,18.1790861 19.1790861,18 19.4,18 L21.6,18 Z M28.6,18 C28.8209139,18 29,18.1790861 29,18.4 L29,30.6 C29,30.8209139 28.8209139,31 28.6,31 L26.4,31 C26.1790861,31 26,30.8209139 26,30.6 L26,18.4 C26,18.1790861 26.1790861,18 26.4,18 L28.6,18 Z M35.6,18 C35.8209139,18 36,18.1790861 36,18.4 L36,30.6 C36,30.8209139 35.8209139,31 35.6,31 L33.4,31 C33.1790861,31 33,30.8209139 33,30.6 L33,18.4 C33,18.1790861 33.1790861,18 33.4,18 L35.6,18 Z\",\n    id: \"CollectMoneyOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\"\n  }))));\n}\n\nexport default CollectMoneyOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,yCAAyC;IAC7CC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,kCAAkC;IACtCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,mtCAAmtC;IACttCR,EAAE,EAAE,8CAA8C;IAClDG,IAAI,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAenB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}