{"ast": null, "code": "import { Globals } from '@react-spring/web';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nlet reduced = false;\nconst subscribers = new Set();\nfunction notify() {\n  subscribers.forEach(subscriber => {\n    subscriber();\n  });\n}\nexport function reduceMotion() {\n  reduced = true;\n  notify();\n  Globals.assign({\n    skipAnimation: true\n  });\n}\nexport function restoreMotion() {\n  reduced = false;\n  notify();\n  Globals.assign({\n    skipAnimation: false\n  });\n}\nexport function isMotionReduced() {\n  return reduced;\n}\nfunction subscribe(onStoreChange) {\n  subscribers.add(onStoreChange);\n  return () => {\n    subscribers.delete(onStoreChange);\n  };\n}\nexport function useMotionReduced() {\n  return useSyncExternalStore(subscribe, isMotionReduced, isMotionReduced);\n}", "map": {"version": 3, "names": ["Globals", "useSyncExternalStore", "reduced", "subscribers", "Set", "notify", "for<PERSON>ach", "subscriber", "reduceMotion", "assign", "skipAnimation", "restoreMotion", "isMotionReduced", "subscribe", "onStoreChange", "add", "delete", "useMotionReduced"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/reduce-and-restore-motion.js"], "sourcesContent": ["import { Globals } from '@react-spring/web';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nlet reduced = false;\nconst subscribers = new Set();\nfunction notify() {\n  subscribers.forEach(subscriber => {\n    subscriber();\n  });\n}\nexport function reduceMotion() {\n  reduced = true;\n  notify();\n  Globals.assign({\n    skipAnimation: true\n  });\n}\nexport function restoreMotion() {\n  reduced = false;\n  notify();\n  Globals.assign({\n    skipAnimation: false\n  });\n}\nexport function isMotionReduced() {\n  return reduced;\n}\nfunction subscribe(onStoreChange) {\n  subscribers.add(onStoreChange);\n  return () => {\n    subscribers.delete(onStoreChange);\n  };\n}\nexport function useMotionReduced() {\n  return useSyncExternalStore(subscribe, isMotionReduced, isMotionReduced);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,IAAIC,OAAO,GAAG,KAAK;AACnB,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC7B,SAASC,MAAMA,CAAA,EAAG;EAChBF,WAAW,CAACG,OAAO,CAACC,UAAU,IAAI;IAChCA,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC7BN,OAAO,GAAG,IAAI;EACdG,MAAM,CAAC,CAAC;EACRL,OAAO,CAACS,MAAM,CAAC;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC9BT,OAAO,GAAG,KAAK;EACfG,MAAM,CAAC,CAAC;EACRL,OAAO,CAACS,MAAM,CAAC;IACbC,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,eAAeA,CAAA,EAAG;EAChC,OAAOV,OAAO;AAChB;AACA,SAASW,SAASA,CAACC,aAAa,EAAE;EAChCX,WAAW,CAACY,GAAG,CAACD,aAAa,CAAC;EAC9B,OAAO,MAAM;IACXX,WAAW,CAACa,MAAM,CAACF,aAAa,CAAC;EACnC,CAAC;AACH;AACA,OAAO,SAASG,gBAAgBA,CAAA,EAAG;EACjC,OAAOhB,oBAAoB,CAACY,SAAS,EAAED,eAAe,EAAEA,eAAe,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}