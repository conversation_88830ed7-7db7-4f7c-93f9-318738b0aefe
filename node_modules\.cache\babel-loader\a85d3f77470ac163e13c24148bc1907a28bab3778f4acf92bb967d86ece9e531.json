{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar dumpIndex = function (step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n  if (index < 0) {\n    index = 0;\n  }\n  return index;\n};\nvar split = function (step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n  var _a = __read(useState({\n      present: initialValue,\n      past: [],\n      future: []\n    }), 2),\n    history = _a[0],\n    setHistory = _a[1];\n  var present = history.present,\n    past = history.past,\n    future = history.future;\n  var initialValueRef = useRef(initialValue);\n  var reset = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n  var updateValue = function (val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);\n    // maximum number of records exceeded\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n  var _forward = function (step) {\n    if (step === void 0) {\n      step = 1;\n    }\n    if (future.length === 0) {\n      return;\n    }\n    var _a = split(step, future),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n  var _backward = function (step) {\n    if (step === void 0) {\n      step = -1;\n    }\n    if (past.length === 0) {\n      return;\n    }\n    var _a = split(step, past),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n  var go = function (step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n    if (stepNum === 0) {\n      return;\n    }\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n    _backward(stepNum);\n  };\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}