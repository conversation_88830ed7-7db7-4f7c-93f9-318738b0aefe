{"ast": null, "code": "export var isObject = function (value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function (value) {\n  return typeof value === 'function';\n};\nexport var isString = function (value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function (value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function (value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function (value) {\n  return typeof value === 'undefined';\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}