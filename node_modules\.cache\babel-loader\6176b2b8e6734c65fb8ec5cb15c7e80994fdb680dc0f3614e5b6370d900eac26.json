{"ast": null, "code": "import { __rest } from \"tslib\";\nimport React from 'react';\nimport PickerView from '../picker-view';\nimport { useColumnsFn } from '../cascade-picker/cascade-picker-utils';\nexport const CascadePickerView = props => {\n  const {\n      options\n    } = props,\n    pickerProps = __rest(props, [\"options\"]);\n  const columnsFn = useColumnsFn(options);\n  return React.createElement(PickerView, Object.assign({}, pickerProps, {\n    columns: columnsFn\n  }));\n};", "map": {"version": 3, "names": ["__rest", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useColumnsFn", "CascadePickerView", "props", "options", "pickerProps", "columnsFn", "createElement", "Object", "assign", "columns"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascade-picker-view/cascade-picker-view.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport React from 'react';\nimport PickerView from '../picker-view';\nimport { useColumnsFn } from '../cascade-picker/cascade-picker-utils';\nexport const CascadePickerView = props => {\n  const {\n      options\n    } = props,\n    pickerProps = __rest(props, [\"options\"]);\n  const columnsFn = useColumnsFn(options);\n  return React.createElement(PickerView, Object.assign({}, pickerProps, {\n    columns: columnsFn\n  }));\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,YAAY,QAAQ,wCAAwC;AACrE,OAAO,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACxC,MAAM;MACFC;IACF,CAAC,GAAGD,KAAK;IACTE,WAAW,GAAGP,MAAM,CAACK,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC;EAC1C,MAAMG,SAAS,GAAGL,YAAY,CAACG,OAAO,CAAC;EACvC,OAAOL,KAAK,CAACQ,aAAa,CAACP,UAAU,EAAEQ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,WAAW,EAAE;IACpEK,OAAO,EAAEJ;EACX,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}