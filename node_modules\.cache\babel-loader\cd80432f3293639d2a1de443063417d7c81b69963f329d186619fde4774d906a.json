{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\nexport default useRafState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}