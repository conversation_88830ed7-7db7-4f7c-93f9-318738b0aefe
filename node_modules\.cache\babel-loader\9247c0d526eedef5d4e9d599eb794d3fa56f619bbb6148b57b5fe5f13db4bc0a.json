{"ast": null, "code": "import \"./selector.css\";\nimport { Selector } from './selector';\nexport default Selector;", "map": {"version": 3, "names": ["Selector"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/selector/index.js"], "sourcesContent": ["import \"./selector.css\";\nimport { Selector } from './selector';\nexport default Selector;"], "mappings": "AAAA,OAAO,gBAAgB;AACvB,SAASA,QAAQ,QAAQ,YAAY;AACrC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}