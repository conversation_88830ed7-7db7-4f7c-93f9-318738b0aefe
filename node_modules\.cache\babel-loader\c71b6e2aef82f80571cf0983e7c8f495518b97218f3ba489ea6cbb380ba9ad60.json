{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useMemo } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { getTreeDeep } from '../../utils/tree';\nimport { mergeProps } from '../../utils/with-default-props';\nimport Checkbox from '../checkbox';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { devWarning } from '../../utils/dev-log';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-tree-select-multiple`;\nexport const Multiple = p => {\n  const props = mergeProps({\n    options: [],\n    fieldNames: {},\n    allSelectText: [],\n    defaultExpandKeys: [],\n    defaultValue: []\n  }, p);\n  useEffect(() => {\n    devWarning('TreeSelect', 'TreeSelect.Multiple has been deprecated.');\n  }, []);\n  const [labelName, valueName, childrenName] = useFieldNames(props.fieldNames);\n  // 打开的 keys\n  const [expandKeys, setExpandKeys] = usePropsValue({\n    value: props.expandKeys,\n    defaultValue: props.defaultExpandKeys\n  });\n  // 选中的 value（聚合后）\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue\n  });\n  // 获取目标所有叶子节点 key 集合\n  const getLeafKeys = option => {\n    const keys = [];\n    const walker = op => {\n      var _a;\n      if (!op) {\n        return;\n      }\n      if ((_a = op[childrenName]) === null || _a === void 0 ? void 0 : _a.length) {\n        op[childrenName].forEach(i => walker(i));\n      } else {\n        keys.push(op[valueName]);\n      }\n    };\n    walker(option);\n    return keys;\n  };\n  const [deep, optionsMap, optionsParentMap] = useMemo(() => {\n    const deep = getTreeDeep(props.options, childrenName);\n    const optionsMap = new Map();\n    const optionsParentMap = new Map();\n    function traverse(current, children) {\n      children.forEach(item => {\n        optionsParentMap.set(item[valueName], current);\n        optionsMap.set(item[valueName], item);\n        if (item[childrenName]) {\n          traverse(item, item[childrenName]);\n        }\n      });\n    }\n    traverse(undefined, props.options);\n    return [deep, optionsMap, optionsParentMap];\n  }, [props.options]);\n  // 将聚合的 value 拆分开，获得叶子节点的 value 集合\n  const allSelectedLeafKeys = useMemo(() => {\n    let leafKeys = [];\n    value.forEach(v => {\n      const option = optionsMap.get(v);\n      leafKeys = leafKeys.concat(getLeafKeys(option));\n    });\n    return leafKeys;\n  }, [value, optionsMap]);\n  // 子级有被选中的节点集合\n  const dotMap = useMemo(() => {\n    const map = new Map();\n    // 遍历 allChildrenValues, 向上递归\n    const walker = key => {\n      const parentOption = optionsParentMap.get(key);\n      if (!parentOption) {\n        return;\n      }\n      map.set(parentOption[valueName], true);\n      walker(parentOption[valueName]);\n    };\n    allSelectedLeafKeys.forEach(key => {\n      map.set(key, true);\n      walker(key);\n    });\n    return map;\n  }, [optionsParentMap, value]);\n  const onChange = targetKeys => {\n    var _a;\n    let groupKeys = [...targetKeys];\n    let unusedKeys = [];\n    const walker = keys => {\n      keys.forEach(key => {\n        var _a;\n        if (unusedKeys.includes(key)) {\n          return;\n        }\n        const parent = optionsParentMap.get(key);\n        if (!parent) {\n          return;\n        }\n        const childrenKeys = ((_a = parent[childrenName]) === null || _a === void 0 ? void 0 : _a.map(i => i[valueName])) || [];\n        if (childrenKeys.every(i => groupKeys.includes(i))) {\n          groupKeys.push(parent[valueName]);\n          unusedKeys = unusedKeys.concat(childrenKeys);\n        }\n      });\n    };\n    // 遍历 deep 次 groupKeys，每次往上聚合一层\n    for (let i = 0; i < deep; i++) {\n      walker(groupKeys);\n    }\n    groupKeys = groupKeys.filter(i => !unusedKeys.includes(i));\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const groupOptions = groupKeys.map(i => optionsMap.get(i));\n    setValue(groupKeys);\n    (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, groupKeys, groupOptions);\n  };\n  const onItemSelect = option => {\n    var _a;\n    const parentNodes = [];\n    let current = option;\n    while (current) {\n      parentNodes.unshift(current);\n      const next = optionsParentMap.get(current[valueName]);\n      current = next;\n    }\n    const keys = parentNodes.map(i => i[valueName]);\n    setExpandKeys(keys);\n    (_a = props.onExpand) === null || _a === void 0 ? void 0 : _a.call(props, keys, parentNodes);\n  };\n  // 渲染全选节点\n  const renderSelectAllItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    let currentLeafKeys = [];\n    columnOptions.forEach(option => {\n      currentLeafKeys = currentLeafKeys.concat(getLeafKeys(option));\n    });\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: `${classPrefix}-item`\n    }, text);\n  };\n  // 渲染\n  const renderSelectAllLeafItem = (columnOptions, index) => {\n    var _a;\n    const text = (_a = props.selectAllText) === null || _a === void 0 ? void 0 : _a[index];\n    if (!text) {\n      return;\n    }\n    const currentLeafKeys = columnOptions.map(i => i[valueName]);\n    const allSelected = currentLeafKeys.every(i => allSelectedLeafKeys.includes(i));\n    const halfSelected = allSelected ? false : currentLeafKeys.some(i => allSelectedLeafKeys.includes(i));\n    return React.createElement(\"div\", {\n      onClick: () => {\n        if (allSelected) {\n          onChange(allSelectedLeafKeys.filter(i => !currentLeafKeys.includes(i)));\n        } else {\n          onChange(allSelectedLeafKeys.concat(currentLeafKeys));\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: allSelected,\n      indeterminate: halfSelected\n    }), text);\n  };\n  // 渲染节点\n  const renderItem = option => {\n    const isExpand = expandKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (!isExpand) {\n          onItemSelect(option);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-expand`]: isExpand\n      })\n    }, option[labelName], !!dotMap.get(option[valueName]) && React.createElement(\"div\", {\n      className: `${classPrefix}-dot`\n    }));\n  };\n  // 渲染叶子节点\n  const renderLeafItem = option => {\n    const isSelected = allSelectedLeafKeys.includes(option[valueName]);\n    return React.createElement(\"div\", {\n      key: option[valueName],\n      onClick: () => {\n        if (isSelected) {\n          onChange(allSelectedLeafKeys.filter(val => val !== option[valueName]));\n        } else {\n          onChange([...allSelectedLeafKeys, option[valueName]]);\n        }\n      },\n      className: classNames(`${classPrefix}-item`, `${classPrefix}-item-leaf`)\n    }, React.createElement(Checkbox, {\n      className: `${classPrefix}-item-checkbox`,\n      checked: isSelected\n    }), option[labelName]);\n  };\n  const renderItems = (columnOptions = [], index) => {\n    if (columnOptions.length === 0) {\n      return;\n    }\n    const isLeaf = deep === index + 1;\n    if (isLeaf) {\n      return React.createElement(React.Fragment, null, renderSelectAllLeafItem(columnOptions, index), columnOptions.map(option => renderLeafItem(option)));\n    }\n    return React.createElement(React.Fragment, null, renderSelectAllItem(columnOptions, index), columnOptions.map(option => renderItem(option)));\n  };\n  const renderColumns = () => {\n    var _a;\n    const columns = [];\n    for (let i = 0; i < deep; i++) {\n      let width = `${100 / deep}%`;\n      // 两列的第一列宽度为 33.33，两列的第二列为 66.67%\n      if (deep === 2 && i === 0) {\n        width = `33.33%`;\n      }\n      if (deep === 2 && i === 1) {\n        width = `66.67%`;\n      }\n      const column = React.createElement(\"div\", {\n        key: i,\n        className: classNames(`${classPrefix}-column`),\n        style: {\n          width\n        }\n      }, renderItems(i === 0 ? props.options : (_a = optionsMap.get(expandKeys[i - 1])) === null || _a === void 0 ? void 0 : _a[childrenName], i));\n      columns.push(column);\n    }\n    return columns;\n  };\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, renderColumns()));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}