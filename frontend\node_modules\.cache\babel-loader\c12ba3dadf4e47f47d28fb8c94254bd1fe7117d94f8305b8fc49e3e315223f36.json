{"ast": null, "code": "// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\nif (isBrowser) {\n  var revalidate = function () {\n    if (!isDocumentVisible() || !isOnline()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\nexport default subscribe;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}