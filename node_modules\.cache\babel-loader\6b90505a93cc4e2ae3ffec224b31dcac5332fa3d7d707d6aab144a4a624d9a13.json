{"ast": null, "code": "import * as React from \"react\";\nfunction KoubeiOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KoubeiOutline-KoubeiOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON>ubeiOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KoubeiOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,8 C41.2383969,8 43.8775718,10.5655749 43.9958615,13.7750617 L44,14 L44,20.2955401 C44,34.3726558 30.4644444,43.9454416 17.7133333,42.9256752 C17.2015873,42.881351 16.9830045,42.7191648 16.9543894,42.1924347 L16.9511111,42.0642981 L16.9511111,38.9746229 C9.5008547,36.2294318 4.16619172,28.8860579 4.00381161,20.6828507 L4,20.2977098 L4,14 C4,10.7616031 6.56557489,8.12242824 9.77506174,8.00413847 L10,8 L38,8 Z M38,11 L10,11 C8.40231912,11 7.09633912,12.24892 7.00509269,13.8237272 L7,14 L7,20.2977098 C7,27.1951962 11.3694012,33.557523 17.6612068,36.0349752 L17.9883469,36.1596376 L19.9511111,36.8828561 L19.951,40 L20.0780633,39.9996641 C30.9989771,39.7916707 40.7895801,31.328828 40.9966548,20.6410707 L41,20.2955401 L41,14 C41,12.4023191 39.75108,11.0963391 38.1762728,11.0050927 L38,11 Z M14.2173913,18.5 C15.0458184,18.5 15.7173913,19.1715729 15.7173913,20 C15.7173913,23.4745916 18.1212764,27.1166515 21.6293078,28.1552878 C25.1843467,29.2078419 29.2583855,27.6746151 30.9757091,25.1407885 C31.4404905,24.4550263 32.3731903,24.275886 33.0589525,24.7406674 C33.7447147,25.2054488 33.9238549,26.1381487 33.4590735,26.8239108 C31.019456,30.4234468 25.6050195,32.4611199 20.777631,31.0318564 C15.9222641,29.5943092 12.7173913,24.738695 12.7173913,20 C12.7173913,19.1715729 13.3889642,18.5 14.2173913,18.5 Z M34.5,18.4782609 C35.3375511,18.5091223 36,19.1715713 36,19.9782609 C36,20.7849505 35.3375511,21.4473994 34.5,21.4782609 C33.6624489,21.4473994 33,20.7849505 33,19.9782609 C33,19.1715713 33.6624489,18.5091223 34.5,18.4782609 Z\",\n    id: \"KoubeiOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default KoubeiOutline;", "map": {"version": 3, "names": ["React", "KoubeiOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/KoubeiOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction KoubeiOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"KoubeiOutline-KoubeiOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"<PERSON>ubeiOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"KoubeiOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M38,8 C41.2383969,8 43.8775718,10.5655749 43.9958615,13.7750617 L44,14 L44,20.2955401 C44,34.3726558 30.4644444,43.9454416 17.7133333,42.9256752 C17.2015873,42.881351 16.9830045,42.7191648 16.9543894,42.1924347 L16.9511111,42.0642981 L16.9511111,38.9746229 C9.5008547,36.2294318 4.16619172,28.8860579 4.00381161,20.6828507 L4,20.2977098 L4,14 C4,10.7616031 6.56557489,8.12242824 9.77506174,8.00413847 L10,8 L38,8 Z M38,11 L10,11 C8.40231912,11 7.09633912,12.24892 7.00509269,13.8237272 L7,14 L7,20.2977098 C7,27.1951962 11.3694012,33.557523 17.6612068,36.0349752 L17.9883469,36.1596376 L19.9511111,36.8828561 L19.951,40 L20.0780633,39.9996641 C30.9989771,39.7916707 40.7895801,31.328828 40.9966548,20.6410707 L41,20.2955401 L41,14 C41,12.4023191 39.75108,11.0963391 38.1762728,11.0050927 L38,11 Z M14.2173913,18.5 C15.0458184,18.5 15.7173913,19.1715729 15.7173913,20 C15.7173913,23.4745916 18.1212764,27.1166515 21.6293078,28.1552878 C25.1843467,29.2078419 29.2583855,27.6746151 30.9757091,25.1407885 C31.4404905,24.4550263 32.3731903,24.275886 33.0589525,24.7406674 C33.7447147,25.2054488 33.9238549,26.1381487 33.4590735,26.8239108 C31.019456,30.4234468 25.6050195,32.4611199 20.777631,31.0318564 C15.9222641,29.5943092 12.7173913,24.738695 12.7173913,20 C12.7173913,19.1715729 13.3889642,18.5 14.2173913,18.5 Z M34.5,18.4782609 C35.3375511,18.5091223 36,19.1715713 36,19.9782609 C36,20.7849505 35.3375511,21.4473994 34.5,21.4782609 C33.6624489,21.4473994 33,20.7849505 33,19.9782609 C33,19.1715713 33.6624489,18.5091223 34.5,18.4782609 Z\",\n    id: \"KoubeiOutline-\\u5F62\\u72B6\\u7ED3\\u5408\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default KoubeiOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,sgDAAsgD;IACzgDR,EAAE,EAAE,wCAAwC;IAC5CG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}