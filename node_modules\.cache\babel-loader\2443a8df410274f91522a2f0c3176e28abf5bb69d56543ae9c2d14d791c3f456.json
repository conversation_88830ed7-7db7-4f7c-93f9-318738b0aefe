{"ast": null, "code": "import { isMemo, isFragment } from 'react-is';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction shouldConstruct(Component) {\n  const prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n// https://github.com/facebook/react/blob/ce13860281f833de8a3296b7a3dad9caced102e9/packages/react-reconciler/src/ReactFiber.new.js#L225\nfunction isSimpleFunctionComponent(type) {\n  return typeof type === 'function' && !shouldConstruct(type) && type.defaultProps === undefined;\n}\nexport function isSafeSetRefComponent(component) {\n  if (isFragment(component)) return false;\n  if (isMemo(component)) return isSafeSetRefComponent(component.type);\n  return !isSimpleFunctionComponent(component.type);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}