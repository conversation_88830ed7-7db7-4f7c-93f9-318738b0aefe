{"ast": null, "code": "import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { RadioGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nconst defaultProps = {\n  disabled: false,\n  defaultValue: null\n};\nexport const Group = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  return React.createElement(RadioGroupContext.Provider\n  // TODO: 性能优化\n  , {\n    // TODO: 性能优化\n    value: {\n      value: value === null ? [] : [value],\n      check: v => {\n        setValue(v);\n      },\n      uncheck: () => {},\n      disabled: props.disabled\n    }\n  }, props.children);\n};", "map": {"version": 3, "names": ["React", "mergeProps", "RadioGroupContext", "usePropsValue", "defaultProps", "disabled", "defaultValue", "Group", "p", "props", "value", "setValue", "onChange", "v", "_a", "call", "createElement", "Provider", "check", "uncheck", "children"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/radio/group.js"], "sourcesContent": ["import React from 'react';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { RadioGroupContext } from './group-context';\nimport { usePropsValue } from '../../utils/use-props-value';\nconst defaultProps = {\n  disabled: false,\n  defaultValue: null\n};\nexport const Group = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue({\n    value: props.value,\n    defaultValue: props.defaultValue,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  return React.createElement(RadioGroupContext.Provider\n  // TODO: 性能优化\n  , {\n    // TODO: 性能优化\n    value: {\n      value: value === null ? [] : [value],\n      check: v => {\n        setValue(v);\n      },\n      uncheck: () => {},\n      disabled: props.disabled\n    }\n  }, props.children);\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,KAAK,GAAGC,CAAC,IAAI;EACxB,MAAMC,KAAK,GAAGR,UAAU,CAACG,YAAY,EAAEI,CAAC,CAAC;EACzC,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGR,aAAa,CAAC;IACtCO,KAAK,EAAED,KAAK,CAACC,KAAK;IAClBJ,YAAY,EAAEG,KAAK,CAACH,YAAY;IAChCM,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIC,EAAE;MACN,IAAID,CAAC,KAAK,IAAI,EAAE;MAChB,CAACC,EAAE,GAAGL,KAAK,CAACG,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACN,KAAK,EAAEI,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC;EACF,OAAOb,KAAK,CAACgB,aAAa,CAACd,iBAAiB,CAACe;EAC7C;EAAA,EACE;IACA;IACAP,KAAK,EAAE;MACLA,KAAK,EAAEA,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,CAACA,KAAK,CAAC;MACpCQ,KAAK,EAAEL,CAAC,IAAI;QACVF,QAAQ,CAACE,CAAC,CAAC;MACb,CAAC;MACDM,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;MACjBd,QAAQ,EAAEI,KAAK,CAACJ;IAClB;EACF,CAAC,EAAEI,KAAK,CAACW,QAAQ,CAAC;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}