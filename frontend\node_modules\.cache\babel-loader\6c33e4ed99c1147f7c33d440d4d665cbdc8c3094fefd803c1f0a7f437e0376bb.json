{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nexport var HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvar warningFunc = function warningFunc() {\n  warning(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldValue: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nexport default Context;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}