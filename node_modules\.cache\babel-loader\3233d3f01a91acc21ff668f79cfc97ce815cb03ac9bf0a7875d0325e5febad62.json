{"ast": null, "code": "import \"./dialog.css\";\nimport { show } from './show';\nimport { alert } from './alert';\nimport { confirm } from './confirm';\nimport { clear } from './clear';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Dialog } from './dialog';\nexport default attachPropertiesToComponent(Dialog, {\n  show,\n  alert,\n  confirm,\n  clear\n});", "map": {"version": 3, "names": ["show", "alert", "confirm", "clear", "attachPropertiesToComponent", "Dialog"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/dialog/index.js"], "sourcesContent": ["import \"./dialog.css\";\nimport { show } from './show';\nimport { alert } from './alert';\nimport { confirm } from './confirm';\nimport { clear } from './clear';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Dialog } from './dialog';\nexport default attachPropertiesToComponent(Dialog, {\n  show,\n  alert,\n  confirm,\n  clear\n});"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,MAAM,QAAQ,UAAU;AACjC,eAAeD,2BAA2B,CAACC,MAAM,EAAE;EACjDL,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}