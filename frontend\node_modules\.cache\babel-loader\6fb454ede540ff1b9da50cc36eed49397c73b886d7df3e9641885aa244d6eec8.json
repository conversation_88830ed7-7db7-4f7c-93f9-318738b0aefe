{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport { setCache, getCache } from '../utils/cache';\nimport { setCachePromise, getCachePromise } from '../utils/cachePromise';\nimport { trigger, subscribe } from '../utils/cacheSubscribe';\nvar useCachePlugin = function (fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n    _b = _a.cacheTime,\n    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n    _c = _a.staleTime,\n    staleTime = _c === void 0 ? 0 : _c,\n    customSetCache = _a.setCache,\n    customGetCache = _a.getCache;\n  var unSubscribeRef = useRef();\n  var currentPromiseRef = useRef();\n  var _setCache = function (key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      setCache(key, cacheTime, cachedData);\n    }\n    trigger(key, cachedData.data);\n  };\n  var _getCache = function (key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n    return getCache(key);\n  };\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    }\n    // get data from cache when init\n    var cacheData = _getCache(cacheKey);\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    }\n    // subscribe same cachekey update, trigger update\n    unSubscribeRef.current = subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n  if (!cacheKey) {\n    return {};\n  }\n  return {\n    onBefore: function (params) {\n      var cacheData = _getCache(cacheKey, params);\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      }\n      // If the data is fresh, stop request\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function (service, args) {\n      var servicePromise = getCachePromise(cacheKey);\n      // If has servicePromise, and is not trigger by self, then use it\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function (data, params) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function (data) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trigger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\nexport default useCachePlugin;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}