{"ast": null, "code": "import { __read } from \"tslib\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\nfunction useSize(target) {\n  var _a = __read(useRafState(function () {\n      var el = getTargetElement(target);\n      return el ? {\n        width: el.clientWidth,\n        height: el.clientHeight\n      } : undefined;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\nexport default useSize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}