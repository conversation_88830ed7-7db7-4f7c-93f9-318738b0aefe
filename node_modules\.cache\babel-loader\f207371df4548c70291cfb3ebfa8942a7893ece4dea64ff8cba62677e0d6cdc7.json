{"ast": null, "code": "var listeners = {};\nvar trigger = function (key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\nvar subscribe = function (key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\nexport { trigger, subscribe };", "map": {"version": 3, "names": ["listeners", "trigger", "key", "data", "for<PERSON>ach", "item", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js"], "sourcesContent": ["var listeners = {};\nvar trigger = function (key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\nvar subscribe = function (key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\nexport { trigger, subscribe };"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;EACjC,IAAIH,SAAS,CAACE,GAAG,CAAC,EAAE;IAClBF,SAAS,CAACE,GAAG,CAAC,CAACE,OAAO,CAAC,UAAUC,IAAI,EAAE;MACrC,OAAOA,IAAI,CAACF,IAAI,CAAC;IACnB,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIG,SAAS,GAAG,SAAAA,CAAUJ,GAAG,EAAEK,QAAQ,EAAE;EACvC,IAAI,CAACP,SAAS,CAACE,GAAG,CAAC,EAAE;IACnBF,SAAS,CAACE,GAAG,CAAC,GAAG,EAAE;EACrB;EACAF,SAAS,CAACE,GAAG,CAAC,CAACM,IAAI,CAACD,QAAQ,CAAC;EAC7B,OAAO,SAASE,WAAWA,CAAA,EAAG;IAC5B,IAAIC,KAAK,GAAGV,SAAS,CAACE,GAAG,CAAC,CAACS,OAAO,CAACJ,QAAQ,CAAC;IAC5CP,SAAS,CAACE,GAAG,CAAC,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACjC,CAAC;AACH,CAAC;AACD,SAAST,OAAO,EAAEK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}