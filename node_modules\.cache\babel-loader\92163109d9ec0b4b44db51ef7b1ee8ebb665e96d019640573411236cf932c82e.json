{"ast": null, "code": "import \"./result-page.css\";\nimport { ResultPage } from './result-page';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { ResultPageCard } from './result-page-card';\nexport default attachPropertiesToComponent(ResultPage, {\n  Card: ResultPageCard\n});", "map": {"version": 3, "names": ["ResultPage", "attachPropertiesToComponent", "ResultPageCard", "Card"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/result-page/index.js"], "sourcesContent": ["import \"./result-page.css\";\nimport { ResultPage } from './result-page';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { ResultPageCard } from './result-page-card';\nexport default attachPropertiesToComponent(ResultPage, {\n  Card: ResultPageCard\n});"], "mappings": "AAAA,OAAO,mBAAmB;AAC1B,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,cAAc,QAAQ,oBAAoB;AACnD,eAAeD,2BAA2B,CAACD,UAAU,EAAE;EACrDG,IAAI,EAAED;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}