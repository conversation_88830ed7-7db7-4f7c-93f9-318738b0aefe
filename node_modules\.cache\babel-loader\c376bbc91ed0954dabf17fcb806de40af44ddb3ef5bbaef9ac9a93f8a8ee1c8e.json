{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from \"react\";\nimport useMemoizedFn from \"../useMemoizedFn\";\nimport isBrowser from \"../utils/isBrowser\";\nexport var ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"LIGHT\"] = \"light\";\n  ThemeMode[\"DARK\"] = \"dark\";\n  ThemeMode[\"SYSTEM\"] = \"system\";\n})(ThemeMode || (ThemeMode = {}));\nvar useCurrentTheme = function () {\n  var matchMedia = isBrowser ? window.matchMedia(\"(prefers-color-scheme: dark)\") : undefined;\n  var _a = __read(useState(function () {\n      if (isBrowser) {\n        return (matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.matches) ? ThemeMode.DARK : ThemeMode.LIGHT;\n      } else {\n        return ThemeMode.LIGHT;\n      }\n    }), 2),\n    theme = _a[0],\n    setTheme = _a[1];\n  useEffect(function () {\n    var onThemeChange = function (event) {\n      if (event.matches) {\n        setTheme(ThemeMode.DARK);\n      } else {\n        setTheme(ThemeMode.LIGHT);\n      }\n    };\n    matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.addEventListener(\"change\", onThemeChange);\n    return function () {\n      matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.removeEventListener(\"change\", onThemeChange);\n    };\n  }, []);\n  return theme;\n};\nexport default function useTheme(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var localStorageKey = options.localStorageKey;\n  var _a = __read(useState(function () {\n      var preferredThemeMode = (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) && localStorage.getItem(localStorageKey);\n      return preferredThemeMode ? preferredThemeMode : ThemeMode.SYSTEM;\n    }), 2),\n    themeMode = _a[0],\n    setThemeMode = _a[1];\n  var setThemeModeWithLocalStorage = function (mode) {\n    setThemeMode(mode);\n    if (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) {\n      localStorage.setItem(localStorageKey, mode);\n    }\n  };\n  var currentTheme = useCurrentTheme();\n  var theme = themeMode === ThemeMode.SYSTEM ? currentTheme : themeMode;\n  return {\n    theme: theme,\n    themeMode: themeMode,\n    setThemeMode: useMemoizedFn(setThemeModeWithLocalStorage)\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}