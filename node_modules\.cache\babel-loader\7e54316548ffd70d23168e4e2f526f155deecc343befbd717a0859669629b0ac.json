{"ast": null, "code": "import React from 'react';\nexport const disconnectedImage = React.createElement(\"svg\", {\n  viewBox: '0 0 400 400',\n  xmlns: 'http://www.w3.org/2000/svg',\n  xmlnsXlink: 'http://www.w3.org/1999/xlink'\n}, React.createElement(\"title\", null, '@\\u53CD\\u9988/\\u5F02\\u5E38/\\u7F51\\u7EDC\\u670D\\u52A1\\u5F02\\u5E38'), React.createElement(\"defs\", null, React.createElement(\"linearGradient\", {\n  x1: '50%',\n  y1: '-116.862%',\n  x2: '50%',\n  y2: '90.764%',\n  id: 'error-block-image-disconnected-c'\n}, React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.207,\n  offset: '0%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.115,\n  offset: '80.072%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0,\n  offset: '100%'\n})), React.createElement(\"circle\", {\n  id: 'error-block-image-disconnected-d',\n  cx: 22.309,\n  cy: 22.309,\n  r: 22.309\n}), React.createElement(\"path\", {\n  id: 'error-block-image-disconnected-a',\n  d: 'M0 0h400v400H0z'\n})), React.createElement(\"g\", {\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-disconnected-b',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-disconnected-a'\n})), React.createElement(\"g\", {\n  mask: 'url(#error-block-image-disconnected-b)',\n  fill: 'url(#error-block-image-disconnected-c)'\n}, React.createElement(\"path\", {\n  d: 'M151.686 45.58c38.869-.623 77.391 34.03 103.046 70.573 17.416-21.644 34.253-32.465 50.51-32.465 26.666 0 56.701 28.548 90.105 85.643 3.539 6.05-7.164 14.87-32.107 26.462l-82.643 2.741c-14.686 11.745-62.45 1.252-138.305 2.467-55.58.89-91.518-2.468-107.816-10.074-23.505-3.07-33.154-7.983-28.946-14.74C59.313 89.813 108.03 46.278 151.686 45.58Zm186.195 99.06-.127.003-.126.01a2.32 2.32 0 0 0-.465.103l-.032.01-.031.01a2.364 2.364 0 0 0-.181.071 2.52 2.52 0 0 0-.116.054l-.133.067-.042.024-.036.02a2.946 2.946 0 0 0-.133.08l-.048.03a3.052 3.052 0 0 0-.126.087l-.047.033a3.274 3.274 0 0 0-.128.097c-.01.008-.02.017-.031.024a4.906 4.906 0 0 0-.31.27l-.036.032a6.654 6.654 0 0 0-.46.484l-.045.05c-3.344 3.91-5.755 14.083-5.755 17.908 0 4.547 3.409 8.275 7.74 8.625l.108.008v7.608c0 .779.502 1.41 1.121 1.41.62 0 1.121-.632 1.121-1.41v-7.762c3.838-.802 6.727-4.293 6.727-8.48 0-4.778-3.765-19.467-8.409-19.467Zm-200-10-.127.003-.126.01a2.32 2.32 0 0 0-.368.073l-.049.014-.048.016-.032.01-.031.01a2.364 2.364 0 0 0-.181.071l-.058.026-.058.028-.133.067-.042.024-.036.02-.066.039-.067.041-.048.03a3.052 3.052 0 0 0-.126.087l-.047.033a3.274 3.274 0 0 0-.128.097c-.01.008-.02.017-.031.024l-.156.13-.154.14-.036.032a6.654 6.654 0 0 0-.46.484l-.045.05c-3.344 3.91-5.755 14.083-5.755 17.908 0 4.547 3.409 8.275 7.74 8.625l.054.004.054.004v7.608c0 .779.502 1.41 1.121 1.41.58 0 1.058-.556 1.115-1.266l.006-.144v-7.762c3.838-.802 6.727-4.293 6.727-8.48 0-4.778-3.765-19.467-8.409-19.467Zm-28.029-12.373-.107.002-.106.006a2.978 2.978 0 0 0-.551.095 3.444 3.444 0 0 0-.323.104 3.962 3.962 0 0 0-.61.297c-.076.045-.15.092-.226.141-4.964 3.312-8.728 18.445-8.728 23.77 0 5.434 3.922 9.935 9.04 10.726l.28.04v9.236c0 .886.532 1.614 1.21 1.692l.121.007.122-.007c.638-.074 1.147-.723 1.204-1.538l.006-.155v-9.235c5.254-.668 9.32-5.234 9.32-10.767 0-5.993-4.77-24.414-10.652-24.414Z'\n})), React.createElement(\"g\", {\n  mask: 'url(#error-block-image-disconnected-b)'\n}, React.createElement(\"g\", {\n  transform: 'translate(85.858 150.644)'\n}, React.createElement(\"path\", {\n  d: 'M116.26 28.467c1.352 0 2.703.018 4.054.054 3.923.385 10.188 4.248 9.267 11.061-.878 6.496-5.836 9.089-8.962 9.529a130.762 130.762 0 0 0-4.36-.072c-28.567 0-60.654 10.149-96.22 30.676l-2.227 1.297c-.744.437-1.49.878-2.236 1.323-4.878 2.911-11.193 1.316-14.103-3.562C-1.438 73.894.157 67.58 5.035 64.67 45.34 40.62 82.4 28.467 116.26 28.467Zm22 11.63c1.03-5.942 6.376-8.618 11.084-8.08C172.14 36.91 194.83 46.86 217.37 61.794c4.735 3.138 6.03 9.52 2.893 14.255-3.138 4.736-9.52 6.031-14.256 2.893-20.111-13.325-40.075-22.165-59.935-26.584a9.974 9.974 0 0 0-.325-.088c-3.987-1.015-8.602-5.738-7.487-12.175ZM116.26 77.418c22.777 0 45.4 7.057 67.73 20.988 4.82 3.007 6.289 9.351 3.282 14.17-3.007 4.82-9.351 6.29-14.17 3.283-19.194-11.974-38.095-17.87-56.842-17.87s-37.648 5.896-56.842 17.87c-4.82 3.007-11.164 1.537-14.17-3.282-3.007-4.82-1.538-11.164 3.282-14.171 22.33-13.931 44.953-20.988 67.73-20.988ZM117.974 124.67c9.85 0 17.303 1.69 25.687 5.082l.82.337 2.9 1.231 3.008 1.252.77.305.107.04c5.326 1.976 8.042 7.895 6.066 13.221-1.976 5.326-7.895 8.042-13.221 6.067l-.713-.27-.726-.285-.763-.31-1.263-.527-2.944-1.26-1.125-.473c-6.393-2.648-11.433-3.838-18.603-3.838-8.223 0-16.532 2.126-25.028 6.475-5.056 2.588-11.254.587-13.842-4.47-2.589-5.056-.588-11.253 4.47-13.842 11.313-5.791 22.814-8.735 34.4-8.735ZM118.235 197.047c7.15 0 13.77-.897 19.841-2.721 5.44-1.635 8.526-7.37 6.892-12.81-1.635-5.44-7.37-8.526-12.81-6.892-4.072 1.224-8.707 1.851-13.923 1.851-4.36 0-8.79-1.045-13.373-3.21l-.626-.301c-5.095-2.512-11.262-.418-13.773 4.678-2.512 5.095-.418 11.261 4.678 13.773 7.559 3.727 15.288 5.632 23.094 5.632Z',\n  fill: '#377EFF',\n  fillRule: 'nonzero'\n}), React.createElement(\"path\", {\n  d: 'M198.35 62.413c2.755-4.967 9.016-6.76 13.984-4.004 13.068 7.25 19.124 18.535 17.615 30.952-1.157 9.515-6.83 18.757-14.096 24.352-13.364 10.29-34.915 9.401-49.363-1.91-4.472-3.502-5.26-9.967-1.758-14.44 3.436-4.388 9.724-5.229 14.185-1.952l.255.194c7.283 5.702 18.475 6.164 24.13 1.809 3.072-2.366 5.766-6.754 6.226-10.536.467-3.844-1.21-7.07-6.796-10.267l-.378-.213c-4.967-2.756-6.76-9.017-4.004-13.985ZM61.35 103.092c-2.84-4.92-9.13-6.607-14.05-3.768-20.662 11.922-21.772 35.751-6.018 51.69 13.752 13.914 33.192 13.447 50.507 1.158 4.633-3.288 5.723-9.708 2.436-14.34-3.288-4.633-9.709-5.724-14.341-2.436-9.763 6.928-18.07 7.128-23.97 1.158-6.761-6.84-6.498-14.501 1.35-19.225l.317-.187c4.92-2.84 6.608-9.13 3.769-14.05ZM129.103 135.702c1.688-5.424 7.454-8.453 12.878-6.764 14.776 4.599 23.437 13.727 25.259 25.58 1.316 8.561-1.228 17.533-5.58 24.052-3.132 4.688-7.388 8.287-12.504 11.112-3.03 1.673-5.75 2.811-9.37 4.066l-1.4.477c-5.387 1.806-11.217-1.097-13.022-6.484-1.805-5.386 1.098-11.216 6.484-13.02l1.09-.374c6.032-2.112 9.602-4.19 11.613-7.201 1.693-2.535 2.818-6.502 2.356-9.503-.564-3.673-3.432-6.696-11.04-9.063-5.424-1.689-8.452-7.454-6.764-12.878Z',\n  fill: '#377EFF',\n  fillRule: 'nonzero'\n}), React.createElement(\"path\", {\n  d: 'M148.072 181.58c3.718-7.98 4.172-14.9 1.36-20.76-2.81-5.86-6.236-9.096-10.275-9.707',\n  stroke: '#FFF',\n  strokeWidth: 0.571,\n  strokeLinecap: 'round'\n}), React.createElement(\"ellipse\", {\n  fill: '#7EACFF',\n  transform: 'rotate(10 147 41.933)',\n  cx: 147,\n  cy: 41.933,\n  rx: 9.143,\n  ry: 10.286\n}), React.createElement(\"path\", {\n  d: 'M210.422 107.472c3.718-7.98 4.172-14.9 1.36-20.76-2.81-5.86-6.668-9.883-11.572-12.067M51.604 131.769c-3.15-6.8-3.537-12.694-1.161-17.685 2.376-4.99 5.57-8.136 9.583-9.438',\n  stroke: '#003CFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n}), React.createElement(\"path\", {\n  d: 'M21.53 64.408c4.946-3.389 9.817-6.026 14.612-7.912',\n  stroke: '#FFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n}), React.createElement(\"path\", {\n  d: 'm113.243 15.444 9.588 8.314M144.31 9.405l-5.775 11.3m18.389-1.246-11.907 4.643M127.64 5.66l2.77 14.255',\n  stroke: '#4486FE',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n}))), React.createElement(\"g\", {\n  mask: 'url(#error-block-image-disconnected-b)'\n}, React.createElement(\"g\", {\n  transform: 'translate(275.143 302.571)'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-disconnected-e',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-disconnected-d'\n})), React.createElement(\"use\", {\n  fill: '#FBBE47',\n  fillRule: 'nonzero',\n  xlinkHref: '#error-block-image-disconnected-d'\n}), React.createElement(\"circle\", {\n  fill: '#FFCD6B',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-disconnected-e)',\n  cx: 16.457,\n  cy: 17.92,\n  r: 22.309\n}), React.createElement(\"circle\", {\n  fill: '#FFF',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-disconnected-e)',\n  cx: 14.263,\n  cy: 12.069,\n  r: 2.194\n}))), React.createElement(\"g\", {\n  mask: 'url(#error-block-image-disconnected-b)',\n  fill: '#FBBE47',\n  fillRule: 'nonzero'\n}, React.createElement(\"circle\", {\n  cx: 12,\n  cy: 12,\n  r: 12,\n  transform: 'translate(84 297.714)'\n}))));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}