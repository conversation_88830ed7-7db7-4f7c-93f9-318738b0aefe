{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement';\n// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\nexport default useRequest;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}