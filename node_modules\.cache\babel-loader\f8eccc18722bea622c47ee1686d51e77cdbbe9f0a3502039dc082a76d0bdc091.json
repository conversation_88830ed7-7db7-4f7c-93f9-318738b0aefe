{"ast": null, "code": "import React, { useRef } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { Star } from './star';\nimport { useDrag } from '@use-gesture/react';\nimport { bound } from '../../utils/bound';\nconst classPrefix = `adm-rate`;\nconst defaultProps = {\n  count: 5,\n  allowHalf: false,\n  character: React.createElement(Star, null),\n  defaultValue: 0,\n  readOnly: false,\n  allowClear: true\n};\nexport const Rate = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue(props);\n  const containerRef = useRef(null);\n  const starList = Array(props.count).fill(null);\n  function renderStar(v, half) {\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-star`, {\n        [`${classPrefix}-star-active`]: value >= v,\n        [`${classPrefix}-star-half`]: half,\n        [`${classPrefix}-star-readonly`]: props.readOnly\n      }),\n      role: 'radio',\n      \"aria-checked\": value >= v,\n      \"aria-label\": '' + v\n    }, props.character);\n  }\n  const bind = useDrag(state => {\n    if (props.readOnly) return;\n    const {\n      xy: [clientX],\n      tap\n    } = state;\n    const container = containerRef.current;\n    if (!container) return;\n    const rect = container.getBoundingClientRect();\n    const rawValue = (clientX - rect.left) / rect.width * props.count;\n    const ceiledValue = props.allowHalf ? Math.ceil(rawValue * 2) / 2 : Math.ceil(rawValue);\n    const boundValue = bound(ceiledValue, 0, props.count);\n    if (tap) {\n      if (props.allowClear && boundValue === value) {\n        setValue(0);\n        return;\n      }\n    }\n    setValue(boundValue);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    },\n    filterTaps: true\n  });\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classNames(classPrefix, {\n      [`${classPrefix}-half`]: props.allowHalf\n    }),\n    role: 'radiogroup',\n    \"aria-readonly\": props.readOnly,\n    ref: containerRef\n  }, bind()), starList.map((_, i) => React.createElement(\"div\", {\n    key: i,\n    className: classNames(`${classPrefix}-box`)\n  }, props.allowHalf && renderStar(i + 0.5, true), renderStar(i + 1, false)))));\n};", "map": {"version": 3, "names": ["React", "useRef", "classNames", "withNativeProps", "mergeProps", "usePropsValue", "Star", "useDrag", "bound", "classPrefix", "defaultProps", "count", "allowHalf", "character", "createElement", "defaultValue", "readOnly", "allowClear", "Rate", "p", "props", "value", "setValue", "containerRef", "starList", "Array", "fill", "renderStar", "v", "half", "className", "role", "bind", "state", "xy", "clientX", "tap", "container", "current", "rect", "getBoundingClientRect", "rawValue", "left", "width", "ceil<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "boundValue", "axis", "pointer", "touch", "filterTaps", "Object", "assign", "ref", "map", "_", "i", "key"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/rate/rate.js"], "sourcesContent": ["import React, { useRef } from 'react';\nimport classNames from 'classnames';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { Star } from './star';\nimport { useDrag } from '@use-gesture/react';\nimport { bound } from '../../utils/bound';\nconst classPrefix = `adm-rate`;\nconst defaultProps = {\n  count: 5,\n  allowHalf: false,\n  character: React.createElement(Star, null),\n  defaultValue: 0,\n  readOnly: false,\n  allowClear: true\n};\nexport const Rate = p => {\n  const props = mergeProps(defaultProps, p);\n  const [value, setValue] = usePropsValue(props);\n  const containerRef = useRef(null);\n  const starList = Array(props.count).fill(null);\n  function renderStar(v, half) {\n    return React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-star`, {\n        [`${classPrefix}-star-active`]: value >= v,\n        [`${classPrefix}-star-half`]: half,\n        [`${classPrefix}-star-readonly`]: props.readOnly\n      }),\n      role: 'radio',\n      \"aria-checked\": value >= v,\n      \"aria-label\": '' + v\n    }, props.character);\n  }\n  const bind = useDrag(state => {\n    if (props.readOnly) return;\n    const {\n      xy: [clientX],\n      tap\n    } = state;\n    const container = containerRef.current;\n    if (!container) return;\n    const rect = container.getBoundingClientRect();\n    const rawValue = (clientX - rect.left) / rect.width * props.count;\n    const ceiledValue = props.allowHalf ? Math.ceil(rawValue * 2) / 2 : Math.ceil(rawValue);\n    const boundValue = bound(ceiledValue, 0, props.count);\n    if (tap) {\n      if (props.allowClear && boundValue === value) {\n        setValue(0);\n        return;\n      }\n    }\n    setValue(boundValue);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    },\n    filterTaps: true\n  });\n  return withNativeProps(props, React.createElement(\"div\", Object.assign({\n    className: classNames(classPrefix, {\n      [`${classPrefix}-half`]: props.allowHalf\n    }),\n    role: 'radiogroup',\n    \"aria-readonly\": props.readOnly,\n    ref: containerRef\n  }, bind()), starList.map((_, i) => React.createElement(\"div\", {\n    key: i,\n    className: classNames(`${classPrefix}-box`)\n  }, props.allowHalf && renderStar(i + 0.5, true), renderStar(i + 1, false)))));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,KAAK,QAAQ,mBAAmB;AACzC,MAAMC,WAAW,GAAG,UAAU;AAC9B,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAEb,KAAK,CAACc,aAAa,CAACR,IAAI,EAAE,IAAI,CAAC;EAC1CS,YAAY,EAAE,CAAC;EACfC,QAAQ,EAAE,KAAK;EACfC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,MAAMC,IAAI,GAAGC,CAAC,IAAI;EACvB,MAAMC,KAAK,GAAGhB,UAAU,CAACM,YAAY,EAAES,CAAC,CAAC;EACzC,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,aAAa,CAACe,KAAK,CAAC;EAC9C,MAAMG,YAAY,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMuB,QAAQ,GAAGC,KAAK,CAACL,KAAK,CAACT,KAAK,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC;EAC9C,SAASC,UAAUA,CAACC,CAAC,EAAEC,IAAI,EAAE;IAC3B,OAAO7B,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;MAChCgB,SAAS,EAAE5B,UAAU,CAAC,GAAGO,WAAW,OAAO,EAAE;QAC3C,CAAC,GAAGA,WAAW,cAAc,GAAGY,KAAK,IAAIO,CAAC;QAC1C,CAAC,GAAGnB,WAAW,YAAY,GAAGoB,IAAI;QAClC,CAAC,GAAGpB,WAAW,gBAAgB,GAAGW,KAAK,CAACJ;MAC1C,CAAC,CAAC;MACFe,IAAI,EAAE,OAAO;MACb,cAAc,EAAEV,KAAK,IAAIO,CAAC;MAC1B,YAAY,EAAE,EAAE,GAAGA;IACrB,CAAC,EAAER,KAAK,CAACP,SAAS,CAAC;EACrB;EACA,MAAMmB,IAAI,GAAGzB,OAAO,CAAC0B,KAAK,IAAI;IAC5B,IAAIb,KAAK,CAACJ,QAAQ,EAAE;IACpB,MAAM;MACJkB,EAAE,EAAE,CAACC,OAAO,CAAC;MACbC;IACF,CAAC,GAAGH,KAAK;IACT,MAAMI,SAAS,GAAGd,YAAY,CAACe,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAChB,MAAME,IAAI,GAAGF,SAAS,CAACG,qBAAqB,CAAC,CAAC;IAC9C,MAAMC,QAAQ,GAAG,CAACN,OAAO,GAAGI,IAAI,CAACG,IAAI,IAAIH,IAAI,CAACI,KAAK,GAAGvB,KAAK,CAACT,KAAK;IACjE,MAAMiC,WAAW,GAAGxB,KAAK,CAACR,SAAS,GAAGiC,IAAI,CAACC,IAAI,CAACL,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGI,IAAI,CAACC,IAAI,CAACL,QAAQ,CAAC;IACvF,MAAMM,UAAU,GAAGvC,KAAK,CAACoC,WAAW,EAAE,CAAC,EAAExB,KAAK,CAACT,KAAK,CAAC;IACrD,IAAIyB,GAAG,EAAE;MACP,IAAIhB,KAAK,CAACH,UAAU,IAAI8B,UAAU,KAAK1B,KAAK,EAAE;QAC5CC,QAAQ,CAAC,CAAC,CAAC;QACX;MACF;IACF;IACAA,QAAQ,CAACyB,UAAU,CAAC;EACtB,CAAC,EAAE;IACDC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE;MACPC,KAAK,EAAE;IACT,CAAC;IACDC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,OAAOhD,eAAe,CAACiB,KAAK,EAAEpB,KAAK,CAACc,aAAa,CAAC,KAAK,EAAEsC,MAAM,CAACC,MAAM,CAAC;IACrEvB,SAAS,EAAE5B,UAAU,CAACO,WAAW,EAAE;MACjC,CAAC,GAAGA,WAAW,OAAO,GAAGW,KAAK,CAACR;IACjC,CAAC,CAAC;IACFmB,IAAI,EAAE,YAAY;IAClB,eAAe,EAAEX,KAAK,CAACJ,QAAQ;IAC/BsC,GAAG,EAAE/B;EACP,CAAC,EAAES,IAAI,CAAC,CAAC,CAAC,EAAER,QAAQ,CAAC+B,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKzD,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAC5D4C,GAAG,EAAED,CAAC;IACN3B,SAAS,EAAE5B,UAAU,CAAC,GAAGO,WAAW,MAAM;EAC5C,CAAC,EAAEW,KAAK,CAACR,SAAS,IAAIe,UAAU,CAAC8B,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE9B,UAAU,CAAC8B,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}