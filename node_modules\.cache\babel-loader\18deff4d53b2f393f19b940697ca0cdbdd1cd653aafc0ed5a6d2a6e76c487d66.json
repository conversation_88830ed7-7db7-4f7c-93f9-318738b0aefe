{"ast": null, "code": "import React, { isValidElement } from 'react';\nimport classNames from 'classnames';\nimport Badge from '../badge';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { Corner } from './corner';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-side-bar`;\n/* istanbul ignore next */\nexport const SideBarItem = () => {\n  return null;\n};\nexport const SideBar = props => {\n  var _a;\n  let firstActiveKey = null;\n  const items = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    items.push(child);\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const lastItem = items[items.length - 1];\n  const isLastItemActive = lastItem && lastItem.key === activeKey;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-items`\n  }, items.map((item, index) => {\n    const active = item.key === activeKey;\n    const isActiveNextSibling = items[index - 1] && items[index - 1].key === activeKey;\n    const isActivePreviousSibling = items[index + 1] && items[index + 1].key === activeKey;\n    return withNativeProps(item.props, React.createElement(\"div\", {\n      key: item.key,\n      onClick: () => {\n        const {\n          key\n        } = item;\n        if (key === undefined || key === null || item.props.disabled) return;\n        setActiveKey(key.toString());\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-active`]: active,\n        [`${classPrefix}-item-disabled`]: item.props.disabled\n      })\n    }, React.createElement(React.Fragment, null, isActiveNextSibling && React.createElement(Corner, {\n      className: `${classPrefix}-item-corner ${classPrefix}-item-corner-top`\n    }), isActivePreviousSibling && React.createElement(Corner, {\n      className: `${classPrefix}-item-corner ${classPrefix}-item-corner-bottom`\n    })), React.createElement(Badge, {\n      content: item.props.badge,\n      className: `${classPrefix}-badge`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-item-title`\n    }, active && React.createElement(\"div\", {\n      className: `${classPrefix}-item-highlight`\n    }), item.props.title))));\n  })), React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-extra-space`, isLastItemActive && `${classPrefix}-item-active-next-sibling`)\n  }, isLastItemActive && React.createElement(Corner, {\n    className: `${classPrefix}-item-corner ${classPrefix}-item-corner-top`\n  }))));\n};", "map": {"version": 3, "names": ["React", "isValidElement", "classNames", "Badge", "withNativeProps", "usePropsValue", "Corner", "traverseReactNode", "classPrefix", "SideBarItem", "SideBar", "props", "_a", "firstActiveKey", "items", "children", "child", "index", "key", "push", "active<PERSON><PERSON>", "setActiveKey", "value", "defaultValue", "defaultActiveKey", "onChange", "v", "call", "lastItem", "length", "isLastItemActive", "createElement", "className", "map", "item", "active", "isActiveNextSibling", "isActivePreviousSibling", "onClick", "undefined", "disabled", "toString", "Fragment", "content", "badge", "title"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/side-bar/side-bar.js"], "sourcesContent": ["import React, { isValidElement } from 'react';\nimport classNames from 'classnames';\nimport Badge from '../badge';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { Corner } from './corner';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nconst classPrefix = `adm-side-bar`;\n/* istanbul ignore next */\nexport const SideBarItem = () => {\n  return null;\n};\nexport const SideBar = props => {\n  var _a;\n  let firstActiveKey = null;\n  const items = [];\n  traverseReactNode(props.children, (child, index) => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    if (index === 0) {\n      firstActiveKey = key;\n    }\n    items.push(child);\n  });\n  const [activeKey, setActiveKey] = usePropsValue({\n    value: props.activeKey,\n    defaultValue: (_a = props.defaultActiveKey) !== null && _a !== void 0 ? _a : firstActiveKey,\n    onChange: v => {\n      var _a;\n      if (v === null) return;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, v);\n    }\n  });\n  const lastItem = items[items.length - 1];\n  const isLastItemActive = lastItem && lastItem.key === activeKey;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-items`\n  }, items.map((item, index) => {\n    const active = item.key === activeKey;\n    const isActiveNextSibling = items[index - 1] && items[index - 1].key === activeKey;\n    const isActivePreviousSibling = items[index + 1] && items[index + 1].key === activeKey;\n    return withNativeProps(item.props, React.createElement(\"div\", {\n      key: item.key,\n      onClick: () => {\n        const {\n          key\n        } = item;\n        if (key === undefined || key === null || item.props.disabled) return;\n        setActiveKey(key.toString());\n      },\n      className: classNames(`${classPrefix}-item`, {\n        [`${classPrefix}-item-active`]: active,\n        [`${classPrefix}-item-disabled`]: item.props.disabled\n      })\n    }, React.createElement(React.Fragment, null, isActiveNextSibling && React.createElement(Corner, {\n      className: `${classPrefix}-item-corner ${classPrefix}-item-corner-top`\n    }), isActivePreviousSibling && React.createElement(Corner, {\n      className: `${classPrefix}-item-corner ${classPrefix}-item-corner-bottom`\n    })), React.createElement(Badge, {\n      content: item.props.badge,\n      className: `${classPrefix}-badge`\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-item-title`\n    }, active && React.createElement(\"div\", {\n      className: `${classPrefix}-item-highlight`\n    }), item.props.title))));\n  })), React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-extra-space`, isLastItemActive && `${classPrefix}-item-active-next-sibling`)\n  }, isLastItemActive && React.createElement(Corner, {\n    className: `${classPrefix}-item-corner ${classPrefix}-item-corner-top`\n  }))));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,cAAc,QAAQ,OAAO;AAC7C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,MAAMC,WAAW,GAAG,cAAc;AAClC;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,OAAO,IAAI;AACb,CAAC;AACD,OAAO,MAAMC,OAAO,GAAGC,KAAK,IAAI;EAC9B,IAAIC,EAAE;EACN,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,KAAK,GAAG,EAAE;EAChBP,iBAAiB,CAACI,KAAK,CAACI,QAAQ,EAAE,CAACC,KAAK,EAAEC,KAAK,KAAK;IAClD,IAAI,CAAChB,cAAc,CAACe,KAAK,CAAC,EAAE;IAC5B,MAAME,GAAG,GAAGF,KAAK,CAACE,GAAG;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC7B,IAAID,KAAK,KAAK,CAAC,EAAE;MACfJ,cAAc,GAAGK,GAAG;IACtB;IACAJ,KAAK,CAACK,IAAI,CAACH,KAAK,CAAC;EACnB,CAAC,CAAC;EACF,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGhB,aAAa,CAAC;IAC9CiB,KAAK,EAAEX,KAAK,CAACS,SAAS;IACtBG,YAAY,EAAE,CAACX,EAAE,GAAGD,KAAK,CAACa,gBAAgB,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGC,cAAc;IAC3FY,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAId,EAAE;MACN,IAAIc,CAAC,KAAK,IAAI,EAAE;MAChB,CAACd,EAAE,GAAGD,KAAK,CAACc,QAAQ,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,IAAI,CAAChB,KAAK,EAAEe,CAAC,CAAC;IAC9E;EACF,CAAC,CAAC;EACF,MAAME,QAAQ,GAAGd,KAAK,CAACA,KAAK,CAACe,MAAM,GAAG,CAAC,CAAC;EACxC,MAAMC,gBAAgB,GAAGF,QAAQ,IAAIA,QAAQ,CAACV,GAAG,KAAKE,SAAS;EAC/D,OAAOhB,eAAe,CAACO,KAAK,EAAEX,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAExB;EACb,CAAC,EAAER,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAGxB,WAAW;EAC3B,CAAC,EAAEM,KAAK,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEjB,KAAK,KAAK;IAC5B,MAAMkB,MAAM,GAAGD,IAAI,CAAChB,GAAG,KAAKE,SAAS;IACrC,MAAMgB,mBAAmB,GAAGtB,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIH,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,CAACC,GAAG,KAAKE,SAAS;IAClF,MAAMiB,uBAAuB,GAAGvB,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIH,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,CAACC,GAAG,KAAKE,SAAS;IACtF,OAAOhB,eAAe,CAAC8B,IAAI,CAACvB,KAAK,EAAEX,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;MAC5Db,GAAG,EAAEgB,IAAI,CAAChB,GAAG;MACboB,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM;UACJpB;QACF,CAAC,GAAGgB,IAAI;QACR,IAAIhB,GAAG,KAAKqB,SAAS,IAAIrB,GAAG,KAAK,IAAI,IAAIgB,IAAI,CAACvB,KAAK,CAAC6B,QAAQ,EAAE;QAC9DnB,YAAY,CAACH,GAAG,CAACuB,QAAQ,CAAC,CAAC,CAAC;MAC9B,CAAC;MACDT,SAAS,EAAE9B,UAAU,CAAC,GAAGM,WAAW,OAAO,EAAE;QAC3C,CAAC,GAAGA,WAAW,cAAc,GAAG2B,MAAM;QACtC,CAAC,GAAG3B,WAAW,gBAAgB,GAAG0B,IAAI,CAACvB,KAAK,CAAC6B;MAC/C,CAAC;IACH,CAAC,EAAExC,KAAK,CAAC+B,aAAa,CAAC/B,KAAK,CAAC0C,QAAQ,EAAE,IAAI,EAAEN,mBAAmB,IAAIpC,KAAK,CAAC+B,aAAa,CAACzB,MAAM,EAAE;MAC9F0B,SAAS,EAAE,GAAGxB,WAAW,gBAAgBA,WAAW;IACtD,CAAC,CAAC,EAAE6B,uBAAuB,IAAIrC,KAAK,CAAC+B,aAAa,CAACzB,MAAM,EAAE;MACzD0B,SAAS,EAAE,GAAGxB,WAAW,gBAAgBA,WAAW;IACtD,CAAC,CAAC,CAAC,EAAER,KAAK,CAAC+B,aAAa,CAAC5B,KAAK,EAAE;MAC9BwC,OAAO,EAAET,IAAI,CAACvB,KAAK,CAACiC,KAAK;MACzBZ,SAAS,EAAE,GAAGxB,WAAW;IAC3B,CAAC,EAAER,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAGxB,WAAW;IAC3B,CAAC,EAAE2B,MAAM,IAAInC,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;MACtCC,SAAS,EAAE,GAAGxB,WAAW;IAC3B,CAAC,CAAC,EAAE0B,IAAI,CAACvB,KAAK,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,EAAE7C,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC9BC,SAAS,EAAE9B,UAAU,CAAC,GAAGM,WAAW,cAAc,EAAEsB,gBAAgB,IAAI,GAAGtB,WAAW,2BAA2B;EACnH,CAAC,EAAEsB,gBAAgB,IAAI9B,KAAK,CAAC+B,aAAa,CAACzB,MAAM,EAAE;IACjD0B,SAAS,EAAE,GAAGxB,WAAW,gBAAgBA,WAAW;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}