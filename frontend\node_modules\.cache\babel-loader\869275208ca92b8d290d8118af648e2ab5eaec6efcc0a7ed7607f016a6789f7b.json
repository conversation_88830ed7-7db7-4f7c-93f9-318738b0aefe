{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport var Context = /*#__PURE__*/React.createContext({});\nexport default function MotionProvider(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: props\n  }, children);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}