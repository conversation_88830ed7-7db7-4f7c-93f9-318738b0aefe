{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nimport { DatePicker } from './date-picker';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(DatePicker, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: val => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "renderToBody", "DatePicker", "prompt", "props", "Promise", "resolve", "Wrapper", "visible", "setVisible", "createElement", "Object", "assign", "onConfirm", "val", "_a", "call", "onClose", "afterClose", "unmount"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/date-picker/prompt.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { renderToBody } from '../../utils/render-to-body';\nimport { DatePicker } from './date-picker';\nexport function prompt(props) {\n  return new Promise(resolve => {\n    const Wrapper = () => {\n      const [visible, setVisible] = useState(false);\n      useEffect(() => {\n        setVisible(true);\n      }, []);\n      return React.createElement(DatePicker, Object.assign({}, props, {\n        visible: visible,\n        onConfirm: val => {\n          var _a;\n          (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val);\n          resolve(val);\n        },\n        onClose: () => {\n          var _a;\n          (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          setVisible(false);\n          resolve(null);\n        },\n        afterClose: () => {\n          var _a;\n          (_a = props.afterClose) === null || _a === void 0 ? void 0 : _a.call(props);\n          unmount();\n        }\n      }));\n    };\n    const unmount = renderToBody(React.createElement(Wrapper, null));\n  });\n}"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5B,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAAC,MAAM;QACdU,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;MACN,OAAOX,KAAK,CAACY,aAAa,CAACR,UAAU,EAAES,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,KAAK,EAAE;QAC9DI,OAAO,EAAEA,OAAO;QAChBK,SAAS,EAAEC,GAAG,IAAI;UAChB,IAAIC,EAAE;UACN,CAACA,EAAE,GAAGX,KAAK,CAACS,SAAS,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACZ,KAAK,EAAEU,GAAG,CAAC;UAC/ER,OAAO,CAACQ,GAAG,CAAC;QACd,CAAC;QACDG,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIF,EAAE;UACN,CAACA,EAAE,GAAGX,KAAK,CAACa,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACZ,KAAK,CAAC;UACxEK,UAAU,CAAC,KAAK,CAAC;UACjBH,OAAO,CAAC,IAAI,CAAC;QACf,CAAC;QACDY,UAAU,EAAEA,CAAA,KAAM;UAChB,IAAIH,EAAE;UACN,CAACA,EAAE,GAAGX,KAAK,CAACc,UAAU,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACZ,KAAK,CAAC;UAC3Ee,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC,CAAC;IACL,CAAC;IACD,MAAMA,OAAO,GAAGlB,YAAY,CAACH,KAAK,CAACY,aAAa,CAACH,OAAO,EAAE,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}