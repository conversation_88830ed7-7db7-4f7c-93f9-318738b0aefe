{"ast": null, "code": "import \"./tag.css\";\nimport { Tag } from './tag';\nexport default Tag;", "map": {"version": 3, "names": ["Tag"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/tag/index.js"], "sourcesContent": ["import \"./tag.css\";\nimport { Tag } from './tag';\nexport default Tag;"], "mappings": "AAAA,OAAO,WAAW;AAClB,SAASA,GAAG,QAAQ,OAAO;AAC3B,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}