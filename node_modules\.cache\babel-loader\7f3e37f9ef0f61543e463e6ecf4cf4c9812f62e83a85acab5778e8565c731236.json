{"ast": null, "code": "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n      screenY = event.screenY,\n      clientX = event.clientX,\n      clientY = event.clientY,\n      pageX = event.pageX,\n      pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n        left = _a.left,\n        top_1 = _a.top,\n        width = _a.width,\n        height = _a.height;\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n    setState(newState);\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return state;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}