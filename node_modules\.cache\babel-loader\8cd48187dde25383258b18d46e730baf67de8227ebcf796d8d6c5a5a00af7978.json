{"ast": null, "code": "import \"./global.css\";\nimport { canUseDom } from '../utils/can-use-dom';\nif (canUseDom) {\n  // Make sure the `:active` CSS selector of `button` and `a` take effect\n  // See: https://stackoverflow.com/questions/3885018/active-pseudo-class-doesnt-work-in-mobile-safari\n  document.addEventListener('touchstart', () => {}, true);\n}\n// Only for debugging. Must COMMENT this line before commit:\n// import './css-vars-patch.less'", "map": {"version": 3, "names": ["canUseDom", "document", "addEventListener"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/global/index.js"], "sourcesContent": ["import \"./global.css\";\nimport { canUseDom } from '../utils/can-use-dom';\nif (canUseDom) {\n  // Make sure the `:active` CSS selector of `button` and `a` take effect\n  // See: https://stackoverflow.com/questions/3885018/active-pseudo-class-doesnt-work-in-mobile-safari\n  document.addEventListener('touchstart', () => {}, true);\n}\n// Only for debugging. Must COMMENT this line before commit:\n// import './css-vars-patch.less'"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAASA,SAAS,QAAQ,sBAAsB;AAChD,IAAIA,SAAS,EAAE;EACb;EACA;EACAC,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;AACzD;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}