{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nexport default function limit(fn, timespan) {\n  var pending = false;\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (pending) return;\n    pending = true;\n    fn.apply(void 0, __spreadArray([], __read(args), false));\n    setTimeout(function () {\n      pending = false;\n    }, timespan);\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}