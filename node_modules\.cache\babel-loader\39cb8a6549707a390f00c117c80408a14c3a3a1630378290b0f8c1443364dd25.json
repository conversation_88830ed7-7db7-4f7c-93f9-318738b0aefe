{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider`;\nconst Ticks = ({\n  points,\n  max,\n  min,\n  upperBound,\n  lowerBound\n}) => {\n  const range = max - min;\n  const elements = points.map(point => {\n    const offset = `${Math.abs(point - min) / range * 100}%`;\n    const isActived = point <= upperBound && point >= lowerBound;\n    const style = {\n      left: offset\n    };\n    const pointClassName = classNames({\n      [`${classPrefix}-tick`]: true,\n      [`${classPrefix}-tick-active`]: isActived\n    });\n    return React.createElement(\"span\", {\n      className: pointClassName,\n      style: style,\n      key: point\n    });\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-ticks`\n  }, elements);\n};\nexport default Ticks;", "map": {"version": 3, "names": ["React", "classNames", "classPrefix", "Ticks", "points", "max", "min", "upperBound", "lowerBound", "range", "elements", "map", "point", "offset", "Math", "abs", "isActived", "style", "left", "pointClassName", "createElement", "className", "key"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/slider/ticks.js"], "sourcesContent": ["import React from 'react';\nimport classNames from 'classnames';\nconst classPrefix = `adm-slider`;\nconst Ticks = ({\n  points,\n  max,\n  min,\n  upperBound,\n  lowerBound\n}) => {\n  const range = max - min;\n  const elements = points.map(point => {\n    const offset = `${Math.abs(point - min) / range * 100}%`;\n    const isActived = point <= upperBound && point >= lowerBound;\n    const style = {\n      left: offset\n    };\n    const pointClassName = classNames({\n      [`${classPrefix}-tick`]: true,\n      [`${classPrefix}-tick-active`]: isActived\n    });\n    return React.createElement(\"span\", {\n      className: pointClassName,\n      style: style,\n      key: point\n    });\n  });\n  return React.createElement(\"div\", {\n    className: `${classPrefix}-ticks`\n  }, elements);\n};\nexport default Ticks;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,KAAK,GAAGA,CAAC;EACbC,MAAM;EACNC,GAAG;EACHC,GAAG;EACHC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGJ,GAAG,GAAGC,GAAG;EACvB,MAAMI,QAAQ,GAAGN,MAAM,CAACO,GAAG,CAACC,KAAK,IAAI;IACnC,MAAMC,MAAM,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACH,KAAK,GAAGN,GAAG,CAAC,GAAGG,KAAK,GAAG,GAAG,GAAG;IACxD,MAAMO,SAAS,GAAGJ,KAAK,IAAIL,UAAU,IAAIK,KAAK,IAAIJ,UAAU;IAC5D,MAAMS,KAAK,GAAG;MACZC,IAAI,EAAEL;IACR,CAAC;IACD,MAAMM,cAAc,GAAGlB,UAAU,CAAC;MAChC,CAAC,GAAGC,WAAW,OAAO,GAAG,IAAI;MAC7B,CAAC,GAAGA,WAAW,cAAc,GAAGc;IAClC,CAAC,CAAC;IACF,OAAOhB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE;MACjCC,SAAS,EAAEF,cAAc;MACzBF,KAAK,EAAEA,KAAK;MACZK,GAAG,EAAEV;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOZ,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAChCC,SAAS,EAAE,GAAGnB,WAAW;EAC3B,CAAC,EAAEQ,QAAQ,CAAC;AACd,CAAC;AACD,eAAeP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}