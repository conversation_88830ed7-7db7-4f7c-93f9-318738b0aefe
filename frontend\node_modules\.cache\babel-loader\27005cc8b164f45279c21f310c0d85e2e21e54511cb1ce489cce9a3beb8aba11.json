{"ast": null, "code": "!function (t, n) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = n() : \"function\" == typeof define && define.amd ? define(n) : (t = \"undefined\" != typeof globalThis ? globalThis : t || self).dayjs_plugin_quarterOfYear = n();\n}(this, function () {\n  \"use strict\";\n\n  var t = \"month\",\n    n = \"quarter\";\n  return function (e, i) {\n    var r = i.prototype;\n    r.quarter = function (t) {\n      return this.$utils().u(t) ? Math.ceil((this.month() + 1) / 3) : this.month(this.month() % 3 + 3 * (t - 1));\n    };\n    var s = r.add;\n    r.add = function (e, i) {\n      return e = Number(e), this.$utils().p(i) === n ? this.add(3 * e, t) : s.bind(this)(e, i);\n    };\n    var u = r.startOf;\n    r.startOf = function (e, i) {\n      var r = this.$utils(),\n        s = !!r.u(i) || i;\n      if (r.p(e) === n) {\n        var o = this.quarter() - 1;\n        return s ? this.month(3 * o).startOf(t).startOf(\"day\") : this.month(3 * o + 2).endOf(t).endOf(\"day\");\n      }\n      return u.bind(this)(e, i);\n    };\n  };\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}