{"ast": null, "code": "import * as React from \"react\";\nfunction FillinOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FillinOutline-FillinOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FillinOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25,4.4 L25,6.6 C25,6.8209139 24.8209139,7 24.6,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,23.4 C41,23.1790861 41.1790861,23 41.4,23 L43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,38 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L24.6,4 C24.8209139,4 25,4.1790861 25,4.4 Z M41.9601551,5.53984489 C43.9127766,7.49246635 43.9127766,10.6582912 41.9601551,12.6109127 L28.6118774,25.9591904 C28.5300903,26.0409775 28.4169042,26.0833578 28.3015138,26.0753998 L22.2158336,25.6556977 C22.0166108,25.6419582 21.8580418,25.4833892 21.8443023,25.2841664 L21.4246002,19.1984862 C21.4166422,19.0830958 21.4590225,18.9699097 21.5408096,18.8881226 L34.8890873,5.53984489 L34.8890873,5.53984489 C36.8417088,3.58722343 40.0075336,3.58722343 41.9601551,5.53984489 Z M33.7750741,10.8963788 L24.6278496,20.0434238 C24.5460625,20.1252092 24.5036812,20.2383934 24.5116369,20.3537828 L24.6815479,22.8181501 L24.6815479,22.8181501 L27.1466809,22.9874098 C27.2620365,22.9953303 27.3751761,22.9529456 27.4569339,22.8711819 L36.6030741,13.7243788 L36.6030741,13.7243788 L33.7750741,10.8963788 Z M37.1198311,7.55949982 L37.0104076,7.66116524 L35.8960741,8.7743788 L38.7250741,11.6033788 L39.8388348,10.4895924 C40.5843811,9.74404598 40.6182696,8.55633208 39.9405002,7.77058865 L39.8388348,7.66116524 C39.0932884,6.91561886 37.9055745,6.88173039 37.1198311,7.55949982 Z\",\n    id: \"FillinOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default FillinOutline;", "map": {"version": 3, "names": ["React", "FillinOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/FillinOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction FillinOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"FillinOutline-FillinOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", null, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"FillinOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M25,4.4 L25,6.6 C25,6.8209139 24.8209139,7 24.6,7 L10,7 L10,7 C8.40231912,7 7.09633912,8.24891996 7.00509269,9.82372721 L7,10 L7,38 C7,39.5976809 8.24891996,40.9036609 9.82372721,40.9949073 L10,41 L38,41 C39.5976809,41 40.9036609,39.75108 40.9949073,38.1762728 L41,38 L41,23.4 C41,23.1790861 41.1790861,23 41.4,23 L43.6,23 C43.8209139,23 44,23.1790861 44,23.4 L44,38 L44,38 C44,41.3137085 41.3137085,44 38,44 L10,44 C6.6862915,44 4,41.3137085 4,38 L4,10 C4,6.6862915 6.6862915,4 10,4 L24.6,4 C24.8209139,4 25,4.1790861 25,4.4 Z M41.9601551,5.53984489 C43.9127766,7.49246635 43.9127766,10.6582912 41.9601551,12.6109127 L28.6118774,25.9591904 C28.5300903,26.0409775 28.4169042,26.0833578 28.3015138,26.0753998 L22.2158336,25.6556977 C22.0166108,25.6419582 21.8580418,25.4833892 21.8443023,25.2841664 L21.4246002,19.1984862 C21.4166422,19.0830958 21.4590225,18.9699097 21.5408096,18.8881226 L34.8890873,5.53984489 L34.8890873,5.53984489 C36.8417088,3.58722343 40.0075336,3.58722343 41.9601551,5.53984489 Z M33.7750741,10.8963788 L24.6278496,20.0434238 C24.5460625,20.1252092 24.5036812,20.2383934 24.5116369,20.3537828 L24.6815479,22.8181501 L24.6815479,22.8181501 L27.1466809,22.9874098 C27.2620365,22.9953303 27.3751761,22.9529456 27.4569339,22.8711819 L36.6030741,13.7243788 L36.6030741,13.7243788 L33.7750741,10.8963788 Z M37.1198311,7.55949982 L37.0104076,7.66116524 L35.8960741,8.7743788 L38.7250741,11.6033788 L39.8388348,10.4895924 C40.5843811,9.74404598 40.6182696,8.55633208 39.9405002,7.77058865 L39.8388348,7.66116524 C39.0932884,6.91561886 37.9055745,6.88173039 37.1198311,7.55949982 Z\",\n    id: \"FillinOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default FillinOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,6BAA6B;IACjCC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IACtFc,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,6jDAA6jD;IAChkDR,EAAE,EAAE,4BAA4B;IAChCG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}