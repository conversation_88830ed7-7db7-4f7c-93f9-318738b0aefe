{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport isDev from '../../utils/isDev';\nimport Fetch from './Fetch';\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  var _a = options.manual,\n    manual = _a === void 0 ? false : _a,\n    _b = options.ready,\n    ready = _b === void 0 ? true : _b,\n    rest = __rest(options, [\"manual\", \"ready\"]);\n  if (isDev) {\n    if (options.defaultParams && !Array.isArray(options.defaultParams)) {\n      console.warn(\"expected defaultParams is array, got \".concat(typeof options.defaultParams));\n    }\n  }\n  var fetchOptions = __assign({\n    manual: manual,\n    ready: ready\n  }, rest);\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions;\n  // run all plugins hooks\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual && ready) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || [];\n      // @ts-ignore\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\nexport default useRequestImplement;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}