{"ast": null, "code": "import { __rest } from \"tslib\";\nimport classNames from 'classnames';\nimport RcSegmented from 'rc-segmented';\nimport * as React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nfunction isSegmentedLabeledOptionWithIcon(option) {\n  return typeof option === 'object' && !!(option === null || option === void 0 ? void 0 : option.icon);\n}\nconst classPrefix = `adm-segmented`;\nconst Segmented = React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      block,\n      options = []\n    } = props,\n    restProps = __rest(props\n    // syntactic sugar to support `icon` for Segmented Item\n    , [\"prefixCls\", \"className\", \"block\", \"options\"]);\n  // syntactic sugar to support `icon` for Segmented Item\n  const extendedOptions = React.useMemo(() => options.map(option => {\n    if (isSegmentedLabeledOptionWithIcon(option)) {\n      const {\n          icon,\n          label\n        } = option,\n        restOption = __rest(option, [\"icon\", \"label\"]);\n      return Object.assign(Object.assign({}, restOption), {\n        label: React.createElement(React.Fragment, null, React.createElement(\"span\", {\n          className: `${classPrefix}-item-icon`\n        }, icon), label && React.createElement(\"span\", null, label))\n      });\n    }\n    return option;\n  }), [options, classPrefix]);\n  return withNativeProps(props, React.createElement(RcSegmented, Object.assign({}, restProps, {\n    className: classNames(className, {\n      [`${classPrefix}-block`]: block\n    }),\n    options: extendedOptions,\n    ref: ref,\n    prefixCls: classPrefix\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nexport { Segmented };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}