{"ast": null, "code": "import { animated, useSpring } from '@react-spring/web';\nimport { useMount } from 'ahooks';\nimport { DownOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { isValidElement, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { observe } from '../../utils/use-mutation-effect';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nconst classPrefix = `adm-collapse`;\nexport const CollapsePanel = () => {\n  return null;\n};\nconst CollapsePanelContent = props => {\n  const {\n    visible\n  } = props;\n  const innerRef = useRef(null);\n  const shouldRender = useShouldRender(visible, props.forceRender, props.destroyOnClose);\n  const [{\n    height\n  }, api] = useSpring(() => ({\n    from: {\n      height: 0\n    },\n    config: {\n      precision: 0.01,\n      mass: 1,\n      tension: 200,\n      friction: 25,\n      clamp: true\n    }\n  }));\n  useMount(() => {\n    if (!visible) return;\n    const inner = innerRef.current;\n    if (!inner) return;\n    api.start({\n      height: inner.offsetHeight,\n      immediate: true\n    });\n  });\n  useIsomorphicUpdateLayoutEffect(() => {\n    const inner = innerRef.current;\n    if (!inner) return;\n    if (visible) {\n      let lastMotionId = 0;\n      let cancelObserve = () => {};\n      const handleMotion = () => {\n        lastMotionId += 1;\n        const motionId = lastMotionId;\n        api.start({\n          height: inner.offsetHeight\n        })[0].then(() => {\n          if (motionId === lastMotionId) {\n            cancelObserve();\n          }\n        });\n      };\n      cancelObserve = observe(inner, {\n        childList: true,\n        subtree: true\n      }, handleMotion);\n      handleMotion();\n      return cancelObserve;\n    } else {\n      api.start({\n        height: inner.offsetHeight,\n        immediate: true\n      });\n      api.start({\n        height: 0\n      });\n    }\n  }, [visible]);\n  return React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-panel-content`, {\n      [`${classPrefix}-panel-content-active`]: visible\n    }),\n    style: {\n      height: height.to(v => {\n        if (height.idle && visible) {\n          return 'auto';\n        } else {\n          return v;\n        }\n      })\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-panel-content-inner`,\n    ref: innerRef\n  }, React.createElement(List.Item, null, shouldRender && props.children)));\n};\nexport const Collapse = props => {\n  const {\n    collapse: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const panels = [];\n  traverseReactNode(mergedProps.children, child => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    panels.push(child);\n  });\n  const handlePropsValue = () => {\n    var _a;\n    if (!mergedProps.accordion) {\n      return {\n        value: mergedProps.activeKey,\n        defaultValue: (_a = mergedProps.defaultActiveKey) !== null && _a !== void 0 ? _a : [],\n        onChange: mergedProps.onChange\n      };\n    }\n    const initValue = {\n      value: [],\n      defaultValue: [],\n      onChange: v => {\n        var _a, _b;\n        (_a = mergedProps.onChange) === null || _a === void 0 ? void 0 : _a.call(mergedProps, (_b = v[0]) !== null && _b !== void 0 ? _b : null);\n      }\n    };\n    if (mergedProps.activeKey === undefined) {\n      initValue.value = undefined;\n    } else if (mergedProps.activeKey !== null) {\n      initValue.value = [mergedProps.activeKey];\n    }\n    if (![null, undefined].includes(mergedProps.defaultActiveKey)) {\n      initValue.defaultValue = [mergedProps.defaultActiveKey];\n    }\n    return initValue;\n  };\n  const [activeKey, setActiveKey] = usePropsValue(handlePropsValue());\n  const activeKeyList = activeKey === null ? [] : Array.isArray(activeKey) ? activeKey : [activeKey];\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(List, null, panels.map(panel => {\n    const key = panel.key;\n    const active = activeKeyList.includes(key);\n    function handleClick(event) {\n      var _a, _b;\n      if (mergedProps.accordion) {\n        if (active) {\n          setActiveKey([]);\n        } else {\n          setActiveKey([key]);\n        }\n      } else {\n        if (active) {\n          setActiveKey(activeKeyList.filter(v => v !== key));\n        } else {\n          setActiveKey([...activeKeyList, key]);\n        }\n      }\n      (_b = (_a = panel.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    }\n    const arrow = mergeProp(React.createElement(DownOutline, null), mergedProps.arrow, mergedProps.arrowIcon, panel.props.arrow, panel.props.arrowIcon);\n    const arrowIcon = typeof arrow === 'function' ? arrow(active) : React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-arrow`, {\n        [`${classPrefix}-arrow-active`]: active\n      })\n    }, arrow);\n    return React.createElement(React.Fragment, {\n      key: key\n    }, withNativeProps(panel.props, React.createElement(List.Item, {\n      className: `${classPrefix}-panel-header`,\n      onClick: handleClick,\n      disabled: panel.props.disabled,\n      arrowIcon: arrowIcon\n    }, panel.props.title)), React.createElement(CollapsePanelContent, {\n      visible: active,\n      forceRender: !!panel.props.forceRender,\n      destroyOnClose: !!panel.props.destroyOnClose\n    }, panel.props.children));\n  }))));\n};", "map": {"version": 3, "names": ["animated", "useSpring", "useMount", "DownOutline", "classNames", "React", "isValidElement", "useRef", "withNativeProps", "useShouldRender", "traverseReactNode", "useIsomorphicUpdateLayoutEffect", "observe", "usePropsValue", "mergeProp", "mergeProps", "useConfig", "List", "classPrefix", "CollapsePanel", "CollapsePanelContent", "props", "visible", "innerRef", "shouldRender", "forceRender", "destroyOnClose", "height", "api", "from", "config", "precision", "mass", "tension", "friction", "clamp", "inner", "current", "start", "offsetHeight", "immediate", "lastMotionId", "cancelObserve", "handleMotion", "motionId", "then", "childList", "subtree", "createElement", "div", "className", "style", "to", "v", "idle", "ref", "<PERSON><PERSON>", "children", "Collapse", "collapse", "componentConfig", "mergedProps", "panels", "child", "key", "push", "handlePropsValue", "_a", "accordion", "value", "active<PERSON><PERSON>", "defaultValue", "defaultActiveKey", "onChange", "initValue", "_b", "call", "undefined", "includes", "setActiveKey", "activeKeyList", "Array", "isArray", "map", "panel", "active", "handleClick", "event", "filter", "onClick", "arrow", "arrowIcon", "Fragment", "disabled", "title"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/collapse/collapse.js"], "sourcesContent": ["import { animated, useSpring } from '@react-spring/web';\nimport { useMount } from 'ahooks';\nimport { DownOutline } from 'antd-mobile-icons';\nimport classNames from 'classnames';\nimport React, { isValidElement, useRef } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { useShouldRender } from '../../utils/should-render';\nimport { traverseReactNode } from '../../utils/traverse-react-node';\nimport { useIsomorphicUpdateLayoutEffect } from '../../utils/use-isomorphic-update-layout-effect';\nimport { observe } from '../../utils/use-mutation-effect';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProp, mergeProps } from '../../utils/with-default-props';\nimport { useConfig } from '../config-provider';\nimport List from '../list';\nconst classPrefix = `adm-collapse`;\nexport const CollapsePanel = () => {\n  return null;\n};\nconst CollapsePanelContent = props => {\n  const {\n    visible\n  } = props;\n  const innerRef = useRef(null);\n  const shouldRender = useShouldRender(visible, props.forceRender, props.destroyOnClose);\n  const [{\n    height\n  }, api] = useSpring(() => ({\n    from: {\n      height: 0\n    },\n    config: {\n      precision: 0.01,\n      mass: 1,\n      tension: 200,\n      friction: 25,\n      clamp: true\n    }\n  }));\n  useMount(() => {\n    if (!visible) return;\n    const inner = innerRef.current;\n    if (!inner) return;\n    api.start({\n      height: inner.offsetHeight,\n      immediate: true\n    });\n  });\n  useIsomorphicUpdateLayoutEffect(() => {\n    const inner = innerRef.current;\n    if (!inner) return;\n    if (visible) {\n      let lastMotionId = 0;\n      let cancelObserve = () => {};\n      const handleMotion = () => {\n        lastMotionId += 1;\n        const motionId = lastMotionId;\n        api.start({\n          height: inner.offsetHeight\n        })[0].then(() => {\n          if (motionId === lastMotionId) {\n            cancelObserve();\n          }\n        });\n      };\n      cancelObserve = observe(inner, {\n        childList: true,\n        subtree: true\n      }, handleMotion);\n      handleMotion();\n      return cancelObserve;\n    } else {\n      api.start({\n        height: inner.offsetHeight,\n        immediate: true\n      });\n      api.start({\n        height: 0\n      });\n    }\n  }, [visible]);\n  return React.createElement(animated.div, {\n    className: classNames(`${classPrefix}-panel-content`, {\n      [`${classPrefix}-panel-content-active`]: visible\n    }),\n    style: {\n      height: height.to(v => {\n        if (height.idle && visible) {\n          return 'auto';\n        } else {\n          return v;\n        }\n      })\n    }\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-panel-content-inner`,\n    ref: innerRef\n  }, React.createElement(List.Item, null, shouldRender && props.children)));\n};\nexport const Collapse = props => {\n  const {\n    collapse: componentConfig = {}\n  } = useConfig();\n  const mergedProps = mergeProps(componentConfig, props);\n  const panels = [];\n  traverseReactNode(mergedProps.children, child => {\n    if (!isValidElement(child)) return;\n    const key = child.key;\n    if (typeof key !== 'string') return;\n    panels.push(child);\n  });\n  const handlePropsValue = () => {\n    var _a;\n    if (!mergedProps.accordion) {\n      return {\n        value: mergedProps.activeKey,\n        defaultValue: (_a = mergedProps.defaultActiveKey) !== null && _a !== void 0 ? _a : [],\n        onChange: mergedProps.onChange\n      };\n    }\n    const initValue = {\n      value: [],\n      defaultValue: [],\n      onChange: v => {\n        var _a, _b;\n        (_a = mergedProps.onChange) === null || _a === void 0 ? void 0 : _a.call(mergedProps, (_b = v[0]) !== null && _b !== void 0 ? _b : null);\n      }\n    };\n    if (mergedProps.activeKey === undefined) {\n      initValue.value = undefined;\n    } else if (mergedProps.activeKey !== null) {\n      initValue.value = [mergedProps.activeKey];\n    }\n    if (![null, undefined].includes(mergedProps.defaultActiveKey)) {\n      initValue.defaultValue = [mergedProps.defaultActiveKey];\n    }\n    return initValue;\n  };\n  const [activeKey, setActiveKey] = usePropsValue(handlePropsValue());\n  const activeKeyList = activeKey === null ? [] : Array.isArray(activeKey) ? activeKey : [activeKey];\n  return withNativeProps(mergedProps, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(List, null, panels.map(panel => {\n    const key = panel.key;\n    const active = activeKeyList.includes(key);\n    function handleClick(event) {\n      var _a, _b;\n      if (mergedProps.accordion) {\n        if (active) {\n          setActiveKey([]);\n        } else {\n          setActiveKey([key]);\n        }\n      } else {\n        if (active) {\n          setActiveKey(activeKeyList.filter(v => v !== key));\n        } else {\n          setActiveKey([...activeKeyList, key]);\n        }\n      }\n      (_b = (_a = panel.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    }\n    const arrow = mergeProp(React.createElement(DownOutline, null), mergedProps.arrow, mergedProps.arrowIcon, panel.props.arrow, panel.props.arrowIcon);\n    const arrowIcon = typeof arrow === 'function' ? arrow(active) : React.createElement(\"div\", {\n      className: classNames(`${classPrefix}-arrow`, {\n        [`${classPrefix}-arrow-active`]: active\n      })\n    }, arrow);\n    return React.createElement(React.Fragment, {\n      key: key\n    }, withNativeProps(panel.props, React.createElement(List.Item, {\n      className: `${classPrefix}-panel-header`,\n      onClick: handleClick,\n      disabled: panel.props.disabled,\n      arrowIcon: arrowIcon\n    }, panel.props.title)), React.createElement(CollapsePanelContent, {\n      visible: active,\n      forceRender: !!panel.props.forceRender,\n      destroyOnClose: !!panel.props.destroyOnClose\n    }, panel.props.children));\n  }))));\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,IAAIC,cAAc,EAAEC,MAAM,QAAQ,OAAO;AACrD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,+BAA+B,QAAQ,iDAAiD;AACjG,SAASC,OAAO,QAAQ,iCAAiC;AACzD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AACtE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,SAAS;AAC1B,MAAMC,WAAW,GAAG,cAAc;AAClC,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAO,IAAI;AACb,CAAC;AACD,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,QAAQ,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMiB,YAAY,GAAGf,eAAe,CAACa,OAAO,EAAED,KAAK,CAACI,WAAW,EAAEJ,KAAK,CAACK,cAAc,CAAC;EACtF,MAAM,CAAC;IACLC;EACF,CAAC,EAAEC,GAAG,CAAC,GAAG3B,SAAS,CAAC,OAAO;IACzB4B,IAAI,EAAE;MACJF,MAAM,EAAE;IACV,CAAC;IACDG,MAAM,EAAE;MACNC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC;EACHjC,QAAQ,CAAC,MAAM;IACb,IAAI,CAACoB,OAAO,EAAE;IACd,MAAMc,KAAK,GAAGb,QAAQ,CAACc,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IACZR,GAAG,CAACU,KAAK,CAAC;MACRX,MAAM,EAAES,KAAK,CAACG,YAAY;MAC1BC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACF7B,+BAA+B,CAAC,MAAM;IACpC,MAAMyB,KAAK,GAAGb,QAAQ,CAACc,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IACZ,IAAId,OAAO,EAAE;MACX,IAAImB,YAAY,GAAG,CAAC;MACpB,IAAIC,aAAa,GAAGA,CAAA,KAAM,CAAC,CAAC;MAC5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzBF,YAAY,IAAI,CAAC;QACjB,MAAMG,QAAQ,GAAGH,YAAY;QAC7Bb,GAAG,CAACU,KAAK,CAAC;UACRX,MAAM,EAAES,KAAK,CAACG;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,MAAM;UACf,IAAID,QAAQ,KAAKH,YAAY,EAAE;YAC7BC,aAAa,CAAC,CAAC;UACjB;QACF,CAAC,CAAC;MACJ,CAAC;MACDA,aAAa,GAAG9B,OAAO,CAACwB,KAAK,EAAE;QAC7BU,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;MACX,CAAC,EAAEJ,YAAY,CAAC;MAChBA,YAAY,CAAC,CAAC;MACd,OAAOD,aAAa;IACtB,CAAC,MAAM;MACLd,GAAG,CAACU,KAAK,CAAC;QACRX,MAAM,EAAES,KAAK,CAACG,YAAY;QAC1BC,SAAS,EAAE;MACb,CAAC,CAAC;MACFZ,GAAG,CAACU,KAAK,CAAC;QACRX,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACL,OAAO,CAAC,CAAC;EACb,OAAOjB,KAAK,CAAC2C,aAAa,CAAChD,QAAQ,CAACiD,GAAG,EAAE;IACvCC,SAAS,EAAE9C,UAAU,CAAC,GAAGc,WAAW,gBAAgB,EAAE;MACpD,CAAC,GAAGA,WAAW,uBAAuB,GAAGI;IAC3C,CAAC,CAAC;IACF6B,KAAK,EAAE;MACLxB,MAAM,EAAEA,MAAM,CAACyB,EAAE,CAACC,CAAC,IAAI;QACrB,IAAI1B,MAAM,CAAC2B,IAAI,IAAIhC,OAAO,EAAE;UAC1B,OAAO,MAAM;QACf,CAAC,MAAM;UACL,OAAO+B,CAAC;QACV;MACF,CAAC;IACH;EACF,CAAC,EAAEhD,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAE;IAC5BE,SAAS,EAAE,GAAGhC,WAAW,sBAAsB;IAC/CqC,GAAG,EAAEhC;EACP,CAAC,EAAElB,KAAK,CAAC2C,aAAa,CAAC/B,IAAI,CAACuC,IAAI,EAAE,IAAI,EAAEhC,YAAY,IAAIH,KAAK,CAACoC,QAAQ,CAAC,CAAC,CAAC;AAC3E,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGrC,KAAK,IAAI;EAC/B,MAAM;IACJsC,QAAQ,EAAEC,eAAe,GAAG,CAAC;EAC/B,CAAC,GAAG5C,SAAS,CAAC,CAAC;EACf,MAAM6C,WAAW,GAAG9C,UAAU,CAAC6C,eAAe,EAAEvC,KAAK,CAAC;EACtD,MAAMyC,MAAM,GAAG,EAAE;EACjBpD,iBAAiB,CAACmD,WAAW,CAACJ,QAAQ,EAAEM,KAAK,IAAI;IAC/C,IAAI,CAACzD,cAAc,CAACyD,KAAK,CAAC,EAAE;IAC5B,MAAMC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC7BF,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;EACpB,CAAC,CAAC;EACF,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,EAAE;IACN,IAAI,CAACN,WAAW,CAACO,SAAS,EAAE;MAC1B,OAAO;QACLC,KAAK,EAAER,WAAW,CAACS,SAAS;QAC5BC,YAAY,EAAE,CAACJ,EAAE,GAAGN,WAAW,CAACW,gBAAgB,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QACrFM,QAAQ,EAAEZ,WAAW,CAACY;MACxB,CAAC;IACH;IACA,MAAMC,SAAS,GAAG;MAChBL,KAAK,EAAE,EAAE;MACTE,YAAY,EAAE,EAAE;MAChBE,QAAQ,EAAEpB,CAAC,IAAI;QACb,IAAIc,EAAE,EAAEQ,EAAE;QACV,CAACR,EAAE,GAAGN,WAAW,CAACY,QAAQ,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAACf,WAAW,EAAE,CAACc,EAAE,GAAGtB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIsB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC;MAC1I;IACF,CAAC;IACD,IAAId,WAAW,CAACS,SAAS,KAAKO,SAAS,EAAE;MACvCH,SAAS,CAACL,KAAK,GAAGQ,SAAS;IAC7B,CAAC,MAAM,IAAIhB,WAAW,CAACS,SAAS,KAAK,IAAI,EAAE;MACzCI,SAAS,CAACL,KAAK,GAAG,CAACR,WAAW,CAACS,SAAS,CAAC;IAC3C;IACA,IAAI,CAAC,CAAC,IAAI,EAAEO,SAAS,CAAC,CAACC,QAAQ,CAACjB,WAAW,CAACW,gBAAgB,CAAC,EAAE;MAC7DE,SAAS,CAACH,YAAY,GAAG,CAACV,WAAW,CAACW,gBAAgB,CAAC;IACzD;IACA,OAAOE,SAAS;EAClB,CAAC;EACD,MAAM,CAACJ,SAAS,EAAES,YAAY,CAAC,GAAGlE,aAAa,CAACqD,gBAAgB,CAAC,CAAC,CAAC;EACnE,MAAMc,aAAa,GAAGV,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGW,KAAK,CAACC,OAAO,CAACZ,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;EAClG,OAAO9D,eAAe,CAACqD,WAAW,EAAExD,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAE;IAC7DE,SAAS,EAAEhC;EACb,CAAC,EAAEb,KAAK,CAAC2C,aAAa,CAAC/B,IAAI,EAAE,IAAI,EAAE6C,MAAM,CAACqB,GAAG,CAACC,KAAK,IAAI;IACrD,MAAMpB,GAAG,GAAGoB,KAAK,CAACpB,GAAG;IACrB,MAAMqB,MAAM,GAAGL,aAAa,CAACF,QAAQ,CAACd,GAAG,CAAC;IAC1C,SAASsB,WAAWA,CAACC,KAAK,EAAE;MAC1B,IAAIpB,EAAE,EAAEQ,EAAE;MACV,IAAId,WAAW,CAACO,SAAS,EAAE;QACzB,IAAIiB,MAAM,EAAE;UACVN,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACLA,YAAY,CAAC,CAACf,GAAG,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAIqB,MAAM,EAAE;UACVN,YAAY,CAACC,aAAa,CAACQ,MAAM,CAACnC,CAAC,IAAIA,CAAC,KAAKW,GAAG,CAAC,CAAC;QACpD,CAAC,MAAM;UACLe,YAAY,CAAC,CAAC,GAAGC,aAAa,EAAEhB,GAAG,CAAC,CAAC;QACvC;MACF;MACA,CAACW,EAAE,GAAG,CAACR,EAAE,GAAGiB,KAAK,CAAC/D,KAAK,EAAEoE,OAAO,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACT,EAAE,EAAEoB,KAAK,CAAC;IAC3F;IACA,MAAMG,KAAK,GAAG5E,SAAS,CAACT,KAAK,CAAC2C,aAAa,CAAC7C,WAAW,EAAE,IAAI,CAAC,EAAE0D,WAAW,CAAC6B,KAAK,EAAE7B,WAAW,CAAC8B,SAAS,EAAEP,KAAK,CAAC/D,KAAK,CAACqE,KAAK,EAAEN,KAAK,CAAC/D,KAAK,CAACsE,SAAS,CAAC;IACnJ,MAAMA,SAAS,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACL,MAAM,CAAC,GAAGhF,KAAK,CAAC2C,aAAa,CAAC,KAAK,EAAE;MACzFE,SAAS,EAAE9C,UAAU,CAAC,GAAGc,WAAW,QAAQ,EAAE;QAC5C,CAAC,GAAGA,WAAW,eAAe,GAAGmE;MACnC,CAAC;IACH,CAAC,EAAEK,KAAK,CAAC;IACT,OAAOrF,KAAK,CAAC2C,aAAa,CAAC3C,KAAK,CAACuF,QAAQ,EAAE;MACzC5B,GAAG,EAAEA;IACP,CAAC,EAAExD,eAAe,CAAC4E,KAAK,CAAC/D,KAAK,EAAEhB,KAAK,CAAC2C,aAAa,CAAC/B,IAAI,CAACuC,IAAI,EAAE;MAC7DN,SAAS,EAAE,GAAGhC,WAAW,eAAe;MACxCuE,OAAO,EAAEH,WAAW;MACpBO,QAAQ,EAAET,KAAK,CAAC/D,KAAK,CAACwE,QAAQ;MAC9BF,SAAS,EAAEA;IACb,CAAC,EAAEP,KAAK,CAAC/D,KAAK,CAACyE,KAAK,CAAC,CAAC,EAAEzF,KAAK,CAAC2C,aAAa,CAAC5B,oBAAoB,EAAE;MAChEE,OAAO,EAAE+D,MAAM;MACf5D,WAAW,EAAE,CAAC,CAAC2D,KAAK,CAAC/D,KAAK,CAACI,WAAW;MACtCC,cAAc,EAAE,CAAC,CAAC0D,KAAK,CAAC/D,KAAK,CAACK;IAChC,CAAC,EAAE0D,KAAK,CAAC/D,KAAK,CAACoC,QAAQ,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}