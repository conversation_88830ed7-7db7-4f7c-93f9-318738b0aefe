{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\nfunction isAsyncGenerator(val) {\n  return isFunction(val[Symbol.asyncIterator]);\n}\nfunction useAsyncEffect(effect, deps) {\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3 /*break*/, 4];\n              _a.label = 1;\n            case 1:\n              if (!true) return [3 /*break*/, 3];\n              return [4 /*yield*/, e.next()];\n            case 2:\n              result = _a.sent();\n              if (result.done || cancelled) {\n                return [3 /*break*/, 3];\n              }\n              return [3 /*break*/, 1];\n            case 3:\n              return [3 /*break*/, 6];\n            case 4:\n              return [4 /*yield*/, e];\n            case 5:\n              _a.sent();\n              _a.label = 6;\n            case 6:\n              return [2 /*return*/];\n          }\n        });\n      });\n    }\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\nexport default useAsyncEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}