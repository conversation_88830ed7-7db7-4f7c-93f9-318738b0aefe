{"ast": null, "code": "import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport Tabs from '../tabs';\nimport CheckList from '../check-list';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useCascaderValueExtend } from './use-cascader-value-extend';\nimport { useConfig } from '../config-provider';\nimport { optionSkeleton } from './option-skeleton';\nimport Skeleton from '../skeleton';\nimport { useUpdateEffect } from 'ahooks';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-cascader-view`;\nconst defaultProps = {\n  defaultValue: []\n};\nexport const CascaderView = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const [labelName, valueName, childrenName, disabledName] = useFieldNames(props.fieldNames);\n  const generateValueExtend = useCascaderValueExtend(props.options, {\n    valueName,\n    childrenName\n  });\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, val, generateValueExtend(val));\n    }\n  }));\n  const [tabActiveIndex, setTabActiveIndex] = useState(0);\n  const levels = useMemo(() => {\n    const ret = [];\n    let currentOptions = props.options;\n    let reachedEnd = false;\n    for (const v of value) {\n      const target = currentOptions.find(option => option[valueName] === v);\n      ret.push({\n        selected: target,\n        options: currentOptions\n      });\n      if (!target || !target[childrenName]) {\n        reachedEnd = true;\n        break;\n      }\n      currentOptions = target[childrenName];\n    }\n    if (!reachedEnd) {\n      ret.push({\n        selected: undefined,\n        options: currentOptions\n      });\n    }\n    return ret;\n  }, [value, props.options]);\n  useUpdateEffect(() => {\n    var _a;\n    (_a = props.onTabsChange) === null || _a === void 0 ? void 0 : _a.call(props, tabActiveIndex);\n  }, [tabActiveIndex]);\n  useEffect(() => {\n    setTabActiveIndex(levels.length - 1);\n  }, [value]);\n  useEffect(() => {\n    const max = levels.length - 1;\n    if (tabActiveIndex > max) {\n      setTabActiveIndex(max);\n    }\n  }, [tabActiveIndex, levels]);\n  const onItemSelect = (selectValue, depth) => {\n    const next = value.slice(0, depth);\n    if (selectValue !== undefined) {\n      next[depth] = selectValue;\n    }\n    setValue(next);\n  };\n  const whetherLoading = options => props.loading || options === optionSkeleton;\n  const placeholder = props.placeholder || locale.Cascader.placeholder;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(Tabs, {\n    activeKey: tabActiveIndex.toString(),\n    onChange: key => {\n      const activeIndex = parseInt(key);\n      setTabActiveIndex(activeIndex);\n    },\n    stretch: false,\n    className: `${classPrefix}-tabs`\n  }, levels.map((level, index) => {\n    const selected = level.selected;\n    return React.createElement(Tabs.Tab, {\n      key: index.toString(),\n      title: React.createElement(\"div\", {\n        className: `${classPrefix}-header-title`\n      }, selected ? selected[labelName] : typeof placeholder === 'function' ? placeholder(index) : placeholder),\n      forceRender: true\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`\n    }, whetherLoading(level.options) ? React.createElement(\"div\", {\n      className: `${classPrefix}-skeleton`\n    }, React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-1`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-2`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-3`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-4`,\n      animated: true\n    })) : React.createElement(CheckList, {\n      value: [value[index]],\n      onChange: selectValue => onItemSelect(selectValue[0], index),\n      activeIcon: props.activeIcon\n    }, level.options.map(option => {\n      const active = value[index] === option[valueName];\n      return React.createElement(CheckList.Item, {\n        value: option[valueName],\n        key: option[valueName],\n        disabled: option[disabledName],\n        className: classNames(`${classPrefix}-item`, {\n          [`${classPrefix}-item-active`]: active\n        })\n      }, option[labelName]);\n    }))));\n  }))));\n};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "classNames", "Tabs", "CheckList", "withNativeProps", "mergeProps", "usePropsValue", "useCascaderValueExtend", "useConfig", "optionSkeleton", "Skeleton", "useUpdateEffect", "useFieldNames", "classPrefix", "defaultProps", "defaultValue", "Cascader<PERSON>iew", "p", "props", "locale", "labelName", "valueName", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "fieldNames", "generateValueExtend", "options", "value", "setValue", "Object", "assign", "onChange", "val", "_a", "call", "tabActiveIndex", "setTabActiveIndex", "levels", "ret", "currentOptions", "reachedEnd", "v", "target", "find", "option", "push", "selected", "undefined", "onTabsChange", "length", "max", "onItemSelect", "selectValue", "depth", "next", "slice", "whetherLoading", "loading", "placeholder", "<PERSON>r", "createElement", "className", "active<PERSON><PERSON>", "toString", "key", "activeIndex", "parseInt", "stretch", "map", "level", "index", "Tab", "title", "forceRender", "animated", "activeIcon", "active", "<PERSON><PERSON>", "disabled"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/cascader-view/cascader-view.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport Tabs from '../tabs';\nimport CheckList from '../check-list';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { useCascaderValueExtend } from './use-cascader-value-extend';\nimport { useConfig } from '../config-provider';\nimport { optionSkeleton } from './option-skeleton';\nimport Skeleton from '../skeleton';\nimport { useUpdateEffect } from 'ahooks';\nimport { useFieldNames } from '../../hooks';\nconst classPrefix = `adm-cascader-view`;\nconst defaultProps = {\n  defaultValue: []\n};\nexport const CascaderView = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    locale\n  } = useConfig();\n  const [labelName, valueName, childrenName, disabledName] = useFieldNames(props.fieldNames);\n  const generateValueExtend = useCascaderValueExtend(props.options, {\n    valueName,\n    childrenName\n  });\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, val, generateValueExtend(val));\n    }\n  }));\n  const [tabActiveIndex, setTabActiveIndex] = useState(0);\n  const levels = useMemo(() => {\n    const ret = [];\n    let currentOptions = props.options;\n    let reachedEnd = false;\n    for (const v of value) {\n      const target = currentOptions.find(option => option[valueName] === v);\n      ret.push({\n        selected: target,\n        options: currentOptions\n      });\n      if (!target || !target[childrenName]) {\n        reachedEnd = true;\n        break;\n      }\n      currentOptions = target[childrenName];\n    }\n    if (!reachedEnd) {\n      ret.push({\n        selected: undefined,\n        options: currentOptions\n      });\n    }\n    return ret;\n  }, [value, props.options]);\n  useUpdateEffect(() => {\n    var _a;\n    (_a = props.onTabsChange) === null || _a === void 0 ? void 0 : _a.call(props, tabActiveIndex);\n  }, [tabActiveIndex]);\n  useEffect(() => {\n    setTabActiveIndex(levels.length - 1);\n  }, [value]);\n  useEffect(() => {\n    const max = levels.length - 1;\n    if (tabActiveIndex > max) {\n      setTabActiveIndex(max);\n    }\n  }, [tabActiveIndex, levels]);\n  const onItemSelect = (selectValue, depth) => {\n    const next = value.slice(0, depth);\n    if (selectValue !== undefined) {\n      next[depth] = selectValue;\n    }\n    setValue(next);\n  };\n  const whetherLoading = options => props.loading || options === optionSkeleton;\n  const placeholder = props.placeholder || locale.Cascader.placeholder;\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(Tabs, {\n    activeKey: tabActiveIndex.toString(),\n    onChange: key => {\n      const activeIndex = parseInt(key);\n      setTabActiveIndex(activeIndex);\n    },\n    stretch: false,\n    className: `${classPrefix}-tabs`\n  }, levels.map((level, index) => {\n    const selected = level.selected;\n    return React.createElement(Tabs.Tab, {\n      key: index.toString(),\n      title: React.createElement(\"div\", {\n        className: `${classPrefix}-header-title`\n      }, selected ? selected[labelName] : typeof placeholder === 'function' ? placeholder(index) : placeholder),\n      forceRender: true\n    }, React.createElement(\"div\", {\n      className: `${classPrefix}-content`\n    }, whetherLoading(level.options) ? React.createElement(\"div\", {\n      className: `${classPrefix}-skeleton`\n    }, React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-1`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-2`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-3`,\n      animated: true\n    }), React.createElement(Skeleton, {\n      className: `${classPrefix}-skeleton-line-4`,\n      animated: true\n    })) : React.createElement(CheckList, {\n      value: [value[index]],\n      onChange: selectValue => onItemSelect(selectValue[0], index),\n      activeIcon: props.activeIcon\n    }, level.options.map(option => {\n      const active = value[index] === option[valueName];\n      return React.createElement(CheckList.Item, {\n        value: option[valueName],\n        key: option[valueName],\n        disabled: option[disabledName],\n        className: classNames(`${classPrefix}-item`, {\n          [`${classPrefix}-item-active`]: active\n        })\n      }, option[labelName]);\n    }))));\n  }))));\n};"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,SAAS,MAAM,eAAe;AACrC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,eAAe,QAAQ,QAAQ;AACxC,SAASC,aAAa,QAAQ,aAAa;AAC3C,MAAMC,WAAW,GAAG,mBAAmB;AACvC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,YAAY,GAAGC,CAAC,IAAI;EAC/B,MAAMC,KAAK,GAAGb,UAAU,CAACS,YAAY,EAAEG,CAAC,CAAC;EACzC,MAAM;IACJE;EACF,CAAC,GAAGX,SAAS,CAAC,CAAC;EACf,MAAM,CAACY,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAGX,aAAa,CAACM,KAAK,CAACM,UAAU,CAAC;EAC1F,MAAMC,mBAAmB,GAAGlB,sBAAsB,CAACW,KAAK,CAACQ,OAAO,EAAE;IAChEL,SAAS;IACTC;EACF,CAAC,CAAC;EACF,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,aAAa,CAACuB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,KAAK,CAAC,EAAE;IAC9Ea,QAAQ,EAAEC,GAAG,IAAI;MACf,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGf,KAAK,CAACa,QAAQ,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAChB,KAAK,EAAEc,GAAG,EAAEP,mBAAmB,CAACO,GAAG,CAAC,CAAC;IAC1G;EACF,CAAC,CAAC,CAAC;EACH,MAAM,CAACG,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMuC,MAAM,GAAGrC,OAAO,CAAC,MAAM;IAC3B,MAAMsC,GAAG,GAAG,EAAE;IACd,IAAIC,cAAc,GAAGrB,KAAK,CAACQ,OAAO;IAClC,IAAIc,UAAU,GAAG,KAAK;IACtB,KAAK,MAAMC,CAAC,IAAId,KAAK,EAAE;MACrB,MAAMe,MAAM,GAAGH,cAAc,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACvB,SAAS,CAAC,KAAKoB,CAAC,CAAC;MACrEH,GAAG,CAACO,IAAI,CAAC;QACPC,QAAQ,EAAEJ,MAAM;QAChBhB,OAAO,EAAEa;MACX,CAAC,CAAC;MACF,IAAI,CAACG,MAAM,IAAI,CAACA,MAAM,CAACpB,YAAY,CAAC,EAAE;QACpCkB,UAAU,GAAG,IAAI;QACjB;MACF;MACAD,cAAc,GAAGG,MAAM,CAACpB,YAAY,CAAC;IACvC;IACA,IAAI,CAACkB,UAAU,EAAE;MACfF,GAAG,CAACO,IAAI,CAAC;QACPC,QAAQ,EAAEC,SAAS;QACnBrB,OAAO,EAAEa;MACX,CAAC,CAAC;IACJ;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAACX,KAAK,EAAET,KAAK,CAACQ,OAAO,CAAC,CAAC;EAC1Bf,eAAe,CAAC,MAAM;IACpB,IAAIsB,EAAE;IACN,CAACA,EAAE,GAAGf,KAAK,CAAC8B,YAAY,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAChB,KAAK,EAAEiB,cAAc,CAAC;EAC/F,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpBpC,SAAS,CAAC,MAAM;IACdqC,iBAAiB,CAACC,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC;EACtC,CAAC,EAAE,CAACtB,KAAK,CAAC,CAAC;EACX5B,SAAS,CAAC,MAAM;IACd,MAAMmD,GAAG,GAAGb,MAAM,CAACY,MAAM,GAAG,CAAC;IAC7B,IAAId,cAAc,GAAGe,GAAG,EAAE;MACxBd,iBAAiB,CAACc,GAAG,CAAC;IACxB;EACF,CAAC,EAAE,CAACf,cAAc,EAAEE,MAAM,CAAC,CAAC;EAC5B,MAAMc,YAAY,GAAGA,CAACC,WAAW,EAAEC,KAAK,KAAK;IAC3C,MAAMC,IAAI,GAAG3B,KAAK,CAAC4B,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;IAClC,IAAID,WAAW,KAAKL,SAAS,EAAE;MAC7BO,IAAI,CAACD,KAAK,CAAC,GAAGD,WAAW;IAC3B;IACAxB,QAAQ,CAAC0B,IAAI,CAAC;EAChB,CAAC;EACD,MAAME,cAAc,GAAG9B,OAAO,IAAIR,KAAK,CAACuC,OAAO,IAAI/B,OAAO,KAAKjB,cAAc;EAC7E,MAAMiD,WAAW,GAAGxC,KAAK,CAACwC,WAAW,IAAIvC,MAAM,CAACwC,QAAQ,CAACD,WAAW;EACpE,OAAOtD,eAAe,CAACc,KAAK,EAAErB,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEhD;EACb,CAAC,EAAEhB,KAAK,CAAC+D,aAAa,CAAC1D,IAAI,EAAE;IAC3B4D,SAAS,EAAE3B,cAAc,CAAC4B,QAAQ,CAAC,CAAC;IACpChC,QAAQ,EAAEiC,GAAG,IAAI;MACf,MAAMC,WAAW,GAAGC,QAAQ,CAACF,GAAG,CAAC;MACjC5B,iBAAiB,CAAC6B,WAAW,CAAC;IAChC,CAAC;IACDE,OAAO,EAAE,KAAK;IACdN,SAAS,EAAE,GAAGhD,WAAW;EAC3B,CAAC,EAAEwB,MAAM,CAAC+B,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC9B,MAAMxB,QAAQ,GAAGuB,KAAK,CAACvB,QAAQ;IAC/B,OAAOjD,KAAK,CAAC+D,aAAa,CAAC1D,IAAI,CAACqE,GAAG,EAAE;MACnCP,GAAG,EAAEM,KAAK,CAACP,QAAQ,CAAC,CAAC;MACrBS,KAAK,EAAE3E,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;QAChCC,SAAS,EAAE,GAAGhD,WAAW;MAC3B,CAAC,EAAEiC,QAAQ,GAAGA,QAAQ,CAAC1B,SAAS,CAAC,GAAG,OAAOsC,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACY,KAAK,CAAC,GAAGZ,WAAW,CAAC;MACzGe,WAAW,EAAE;IACf,CAAC,EAAE5E,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MAC5BC,SAAS,EAAE,GAAGhD,WAAW;IAC3B,CAAC,EAAE2C,cAAc,CAACa,KAAK,CAAC3C,OAAO,CAAC,GAAG7B,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE;MAC5DC,SAAS,EAAE,GAAGhD,WAAW;IAC3B,CAAC,EAAEhB,KAAK,CAAC+D,aAAa,CAAClD,QAAQ,EAAE;MAC/BmD,SAAS,EAAE,GAAGhD,WAAW,kBAAkB;MAC3C6D,QAAQ,EAAE;IACZ,CAAC,CAAC,EAAE7E,KAAK,CAAC+D,aAAa,CAAClD,QAAQ,EAAE;MAChCmD,SAAS,EAAE,GAAGhD,WAAW,kBAAkB;MAC3C6D,QAAQ,EAAE;IACZ,CAAC,CAAC,EAAE7E,KAAK,CAAC+D,aAAa,CAAClD,QAAQ,EAAE;MAChCmD,SAAS,EAAE,GAAGhD,WAAW,kBAAkB;MAC3C6D,QAAQ,EAAE;IACZ,CAAC,CAAC,EAAE7E,KAAK,CAAC+D,aAAa,CAAClD,QAAQ,EAAE;MAChCmD,SAAS,EAAE,GAAGhD,WAAW,kBAAkB;MAC3C6D,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC,GAAG7E,KAAK,CAAC+D,aAAa,CAACzD,SAAS,EAAE;MACnCwB,KAAK,EAAE,CAACA,KAAK,CAAC2C,KAAK,CAAC,CAAC;MACrBvC,QAAQ,EAAEqB,WAAW,IAAID,YAAY,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEkB,KAAK,CAAC;MAC5DK,UAAU,EAAEzD,KAAK,CAACyD;IACpB,CAAC,EAAEN,KAAK,CAAC3C,OAAO,CAAC0C,GAAG,CAACxB,MAAM,IAAI;MAC7B,MAAMgC,MAAM,GAAGjD,KAAK,CAAC2C,KAAK,CAAC,KAAK1B,MAAM,CAACvB,SAAS,CAAC;MACjD,OAAOxB,KAAK,CAAC+D,aAAa,CAACzD,SAAS,CAAC0E,IAAI,EAAE;QACzClD,KAAK,EAAEiB,MAAM,CAACvB,SAAS,CAAC;QACxB2C,GAAG,EAAEpB,MAAM,CAACvB,SAAS,CAAC;QACtByD,QAAQ,EAAElC,MAAM,CAACrB,YAAY,CAAC;QAC9BsC,SAAS,EAAE5D,UAAU,CAAC,GAAGY,WAAW,OAAO,EAAE;UAC3C,CAAC,GAAGA,WAAW,cAAc,GAAG+D;QAClC,CAAC;MACH,CAAC,EAAEhC,MAAM,CAACxB,SAAS,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}