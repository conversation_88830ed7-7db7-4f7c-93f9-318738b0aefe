{"ast": null, "code": "import React, { useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport Image from '../image';\nimport SpinLoading from '../spin-loading';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-image-uploader`;\nconst PreviewItem = props => {\n  const {\n    locale\n  } = useConfig();\n  const {\n    url,\n    file,\n    deletable,\n    deleteIcon,\n    onDelete,\n    imageFit\n  } = props;\n  const src = useMemo(() => {\n    if (url) {\n      return url;\n    }\n    if (file) {\n      return URL.createObjectURL(file);\n    }\n    return '';\n  }, [url, file]);\n  useEffect(() => {\n    return () => {\n      if (file) URL.revokeObjectURL(src);\n    };\n  }, [src, file]);\n  function renderLoading() {\n    return props.status === 'pending' && React.createElement(\"div\", {\n      className: `${classPrefix}-cell-mask`\n    }, React.createElement(\"span\", {\n      className: `${classPrefix}-cell-loading`\n    }, React.createElement(SpinLoading, {\n      color: 'white'\n    }), React.createElement(\"span\", {\n      className: `${classPrefix}-cell-mask-message`\n    }, locale.ImageUploader.uploading)));\n  }\n  function renderDelete() {\n    return deletable && React.createElement(\"span\", {\n      className: `${classPrefix}-cell-delete`,\n      onClick: onDelete\n    }, deleteIcon);\n  }\n  return React.createElement(\"div\", {\n    className: classNames(`${classPrefix}-cell`, props.status === 'fail' && `${classPrefix}-cell-fail`)\n  }, React.createElement(Image, {\n    className: `${classPrefix}-cell-image`,\n    src: src,\n    fit: imageFit,\n    onClick: props.onClick\n  }), renderLoading(), renderDelete());\n};\nexport default PreviewItem;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}