{"ast": null, "code": "import { __assign, __read, __rest, __values } from \"tslib\";\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useInViewport(target, options) {\n  var _a = options || {},\n    callback = _a.callback,\n    option = __rest(_a, [\"callback\"]);\n  var _b = __read(useState(), 2),\n    state = _b[0],\n    setState = _b[1];\n  var _c = __read(useState(), 2),\n    ratio = _c[0],\n    setRatio = _c[1];\n  useEffectWithTarget(function () {\n    var targets = Array.isArray(target) ? target : [target];\n    var els = targets.map(function (element) {\n      return getTargetElement(element);\n    }).filter(Boolean);\n    if (!els.length) {\n      return;\n    }\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n          callback === null || callback === void 0 ? void 0 : callback(entry);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1.return)) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, option), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    els.forEach(function (el) {\n      return observer.observe(el);\n    });\n    return function () {\n      observer.disconnect();\n    };\n  }, [options === null || options === void 0 ? void 0 : options.rootMargin, options === null || options === void 0 ? void 0 : options.threshold, callback], target);\n  return [state, ratio];\n}\nexport default useInViewport;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}