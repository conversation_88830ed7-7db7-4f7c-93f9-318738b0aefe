{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.enable,\n    enable = _a === void 0 ? true : _a;\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    if (!enable) {\n      return;\n    }\n    var targetElement = getTargetElement(options.target, window);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var eventListener = function (event) {\n      return handlerRef.current(event);\n    };\n    var eventNameArray = Array.isArray(eventName) ? eventName : [eventName];\n    eventNameArray.forEach(function (event) {\n      targetElement.addEventListener(event, eventListener, {\n        capture: options.capture,\n        once: options.once,\n        passive: options.passive\n      });\n    });\n    return function () {\n      eventNameArray.forEach(function (event) {\n        targetElement.removeEventListener(event, eventListener, {\n          capture: options.capture\n        });\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive, enable], options.target);\n}\nexport default useEventListener;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}