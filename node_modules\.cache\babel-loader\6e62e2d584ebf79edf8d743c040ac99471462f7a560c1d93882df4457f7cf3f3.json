{"ast": null, "code": "import React from 'react';\nimport classNames from 'classnames';\nimport <PERSON><PERSON> from '../button';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ModalActionButton = props => {\n  const {\n    action\n  } = props;\n  return withNativeProps(props.action, React.createElement(<PERSON><PERSON>, {\n    key: action.key,\n    onClick: props.onAction,\n    className: classNames('adm-modal-button', {\n      'adm-modal-button-primary': props.action.primary\n    }),\n    fill: props.action.primary ? 'solid' : 'none',\n    size: props.action.primary ? 'large' : 'middle',\n    block: true,\n    color: action.danger ? 'danger' : 'primary',\n    loading: 'auto',\n    disabled: action.disabled\n  }, action.text));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}