{"ast": null, "code": "import \"./ellipsis.css\";\nimport { Ellipsis } from './ellipsis';\nexport default Ellipsis;", "map": {"version": 3, "names": ["El<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/ellipsis/index.js"], "sourcesContent": ["import \"./ellipsis.css\";\nimport { Ellipsis } from './ellipsis';\nexport default Ellipsis;"], "mappings": "AAAA,OAAO,gBAAgB;AACvB,SAASA,QAAQ,QAAQ,YAAY;AACrC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}