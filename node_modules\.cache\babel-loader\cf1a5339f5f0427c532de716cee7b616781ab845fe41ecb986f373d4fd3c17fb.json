{"ast": null, "code": "import { useRef } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\n// k:v 原对象:代理过的对象\nvar proxyMap = new WeakMap();\n// k:v 代理过的对象:原对象\nvar rawMap = new WeakMap();\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal);\n  // 添加缓存 防止重新构建proxy\n  if (existingProxy) {\n    return existingProxy;\n  }\n  // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n  var proxy = new Proxy(initialVal, {\n    get: function (target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      // https://github.com/alibaba/hooks/issues/1317\n      var descriptor = Reflect.getOwnPropertyDescriptor(target, key);\n      if (!(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable) && !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.writable)) {\n        return res;\n      }\n      // Only proxy plain object or array,\n      // otherwise it will cause: https://github.com/alibaba/hooks/issues/2080\n      return isPlainObject(res) || Array.isArray(res) ? observer(res, cb) : res;\n    },\n    set: function (target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function (target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\nexport default useReactive;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}