{"ast": null, "code": "import * as React from \"react\";\nfunction AudioOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioOutline-AudioOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AudioOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.0280234,24.3721176 C11.0554205,24.7535019 11.0834856,25.0501619 11.1122187,25.2620977 C11.9726084,31.6083434 17.4147757,36.5 24,36.5 C30.5563593,36.5 35.9796602,31.6511327 36.8762035,25.3454661 C36.9089551,25.115114 36.9406933,24.7904814 36.9714181,24.3715686 C36.9866919,24.1625336 37.1607543,24.0007701 37.3703465,24.0007701 C37.634525,24.0007701 37.8444095,24.0007701 38,24.0007701 C38.2582838,24.0007701 38.591617,24.0007701 39,24.0007701 C39.1309734,24.0007701 39.3230245,24.0007701 39.5761532,24.0007701 C39.7971012,24.0007084 39.9762149,24.1798221 39.9762149,24.4007701 C39.9762149,24.4090903 39.9759553,24.4174084 39.9754366,24.4257124 C39.9477159,24.8694729 39.9187796,25.2146393 39.8886277,25.4612117 C39.0287343,32.4931489 33.6046985,38.1156092 26.6678639,39.2787918 C26.4729481,39.3114756 26.2013766,39.3485576 25.8531495,39.3900377 C25.6518802,39.4139065 25.5003394,39.5845847 25.5003569,39.7872643 C25.5003843,40.1036252 25.5004059,40.3526672 25.5004216,40.5343904 C25.50051,41.5562603 25.5005984,42.5781301 25.5006868,43.6 C25.5007405,43.8209139 25.3216699,44.0000155 25.100756,44.0000346 C25.1007444,44.0000346 25.1007329,44.0000346 25.1007214,44 L22.8992786,44 C22.6783647,44.0000346 22.4992786,43.8209485 22.4992786,43.6000346 C22.4992786,43.6000231 22.4992786,43.6000115 22.4993132,43.6 C22.4994011,42.583939 22.499489,41.567878 22.4995769,40.551817 C22.4995929,40.3665507 22.4996149,40.1124027 22.4996429,39.7893728 C22.4997293,39.5859497 22.3470932,39.4148995 22.1449805,39.3918467 C21.9611406,39.370878 21.8134608,39.3523813 21.7019412,39.3363565 C14.5024883,38.3018333 8.85079948,32.4787457 8.0816193,25.200152 C8.06165814,25.0112636 8.04204009,24.7526316 8.02276516,24.4242559 C8.00977186,24.2036984 8.17807611,24.0144039 8.39863633,24.0014575 C8.40644177,24.0009993 8.41425897,24.0007701 8.42207785,24.0007701 C8.66215694,24.0007701 8.85479766,24.0007701 9,24.0007701 C9.2583604,24.0007701 9.59169394,24.0007701 10,24.0007701 C10.1400474,24.0007701 10.3497683,24.0007701 10.6291627,24.0007701 C10.8388973,24.0008816 11.0129956,24.1629221 11.0280234,24.3721176 Z M24,4 C29.2492283,4 33.5045687,8.25329488 33.5045687,13.5 L33.5045687,23.5 C33.5045687,28.7467051 29.2492283,33 24,33 C18.7507717,33 14.4954313,28.7467051 14.4954313,23.5 L14.4954313,13.5 C14.4954313,8.25329488 18.7507717,4 24,4 Z M24,7 C20.4865005,7 17.6237845,9.78499621 17.500979,13.2668659 L17.4968741,13.5 L17.4968741,23.5 C17.4968741,27.0898509 20.4084227,30 24,30 C27.5134995,30 30.3762155,27.2150038 30.499021,23.7331341 L30.5031259,23.5 L30.5031259,13.5 C30.5031259,9.91014913 27.5915773,7 24,7 Z\",\n    id: \"AudioOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\nexport default AudioOutline;", "map": {"version": 3, "names": ["React", "AudioOutline", "props", "createElement", "Object", "assign", "width", "height", "viewBox", "xmlns", "xmlnsXlink", "style", "verticalAlign", "className", "filter", "Boolean", "join", "id", "stroke", "strokeWidth", "fill", "fillRule", "opacity", "x", "y", "d"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile-icons/es/AudioOutline.js"], "sourcesContent": ["import * as React from \"react\";\n\nfunction AudioOutline(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 48 48\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\"\n  }, props, {\n    style: Object.assign({\n      verticalAlign: '-0.125em'\n    }, props.style),\n    className: ['antd-mobile-icon', props.className].filter(Boolean).join(' ')\n  }), /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioOutline-AudioOutline\",\n    stroke: \"none\",\n    strokeWidth: 1,\n    fill: \"none\",\n    fillRule: \"evenodd\"\n  }, /*#__PURE__*/React.createElement(\"g\", {\n    id: \"AudioOutline-\\u7F16\\u7EC4\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    id: \"AudioOutline-\\u77E9\\u5F62\",\n    fill: \"#FFFFFF\",\n    opacity: 0,\n    x: 0,\n    y: 0,\n    width: 48,\n    height: 48\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.0280234,24.3721176 C11.0554205,24.7535019 11.0834856,25.0501619 11.1122187,25.2620977 C11.9726084,31.6083434 17.4147757,36.5 24,36.5 C30.5563593,36.5 35.9796602,31.6511327 36.8762035,25.3454661 C36.9089551,25.115114 36.9406933,24.7904814 36.9714181,24.3715686 C36.9866919,24.1625336 37.1607543,24.0007701 37.3703465,24.0007701 C37.634525,24.0007701 37.8444095,24.0007701 38,24.0007701 C38.2582838,24.0007701 38.591617,24.0007701 39,24.0007701 C39.1309734,24.0007701 39.3230245,24.0007701 39.5761532,24.0007701 C39.7971012,24.0007084 39.9762149,24.1798221 39.9762149,24.4007701 C39.9762149,24.4090903 39.9759553,24.4174084 39.9754366,24.4257124 C39.9477159,24.8694729 39.9187796,25.2146393 39.8886277,25.4612117 C39.0287343,32.4931489 33.6046985,38.1156092 26.6678639,39.2787918 C26.4729481,39.3114756 26.2013766,39.3485576 25.8531495,39.3900377 C25.6518802,39.4139065 25.5003394,39.5845847 25.5003569,39.7872643 C25.5003843,40.1036252 25.5004059,40.3526672 25.5004216,40.5343904 C25.50051,41.5562603 25.5005984,42.5781301 25.5006868,43.6 C25.5007405,43.8209139 25.3216699,44.0000155 25.100756,44.0000346 C25.1007444,44.0000346 25.1007329,44.0000346 25.1007214,44 L22.8992786,44 C22.6783647,44.0000346 22.4992786,43.8209485 22.4992786,43.6000346 C22.4992786,43.6000231 22.4992786,43.6000115 22.4993132,43.6 C22.4994011,42.583939 22.499489,41.567878 22.4995769,40.551817 C22.4995929,40.3665507 22.4996149,40.1124027 22.4996429,39.7893728 C22.4997293,39.5859497 22.3470932,39.4148995 22.1449805,39.3918467 C21.9611406,39.370878 21.8134608,39.3523813 21.7019412,39.3363565 C14.5024883,38.3018333 8.85079948,32.4787457 8.0816193,25.200152 C8.06165814,25.0112636 8.04204009,24.7526316 8.02276516,24.4242559 C8.00977186,24.2036984 8.17807611,24.0144039 8.39863633,24.0014575 C8.40644177,24.0009993 8.41425897,24.0007701 8.42207785,24.0007701 C8.66215694,24.0007701 8.85479766,24.0007701 9,24.0007701 C9.2583604,24.0007701 9.59169394,24.0007701 10,24.0007701 C10.1400474,24.0007701 10.3497683,24.0007701 10.6291627,24.0007701 C10.8388973,24.0008816 11.0129956,24.1629221 11.0280234,24.3721176 Z M24,4 C29.2492283,4 33.5045687,8.25329488 33.5045687,13.5 L33.5045687,23.5 C33.5045687,28.7467051 29.2492283,33 24,33 C18.7507717,33 14.4954313,28.7467051 14.4954313,23.5 L14.4954313,13.5 C14.4954313,8.25329488 18.7507717,4 24,4 Z M24,7 C20.4865005,7 17.6237845,9.78499621 17.500979,13.2668659 L17.4968741,13.5 L17.4968741,23.5 C17.4968741,27.0898509 20.4084227,30 24,30 C27.5134995,30 30.3762155,27.2150038 30.499021,23.7331341 L30.5031259,23.5 L30.5031259,13.5 C30.5031259,9.91014913 27.5915773,7 24,7 Z\",\n    id: \"AudioOutline-\\u5F62\\u72B6\",\n    fill: \"currentColor\",\n    fillRule: \"nonzero\"\n  }))));\n}\n\nexport default AudioOutline;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,aAAaF,KAAK,CAACG,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE;EACd,CAAC,EAAER,KAAK,EAAE;IACRS,KAAK,EAAEP,MAAM,CAACC,MAAM,CAAC;MACnBO,aAAa,EAAE;IACjB,CAAC,EAAEV,KAAK,CAACS,KAAK,CAAC;IACfE,SAAS,EAAE,CAAC,kBAAkB,EAAEX,KAAK,CAACW,SAAS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG;EAC3E,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACxCc,EAAE,EAAE,2BAA2B;IAC/BC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,CAAC;IACdC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarB,KAAK,CAACG,aAAa,CAAC,GAAG,EAAE;IACvCc,EAAE,EAAE;EACN,CAAC,EAAE,aAAajB,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1Cc,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,SAAS;IACfE,OAAO,EAAE,CAAC;IACVC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJlB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAaP,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC3CsB,CAAC,EAAE,siFAAsiF;IACziFR,EAAE,EAAE,2BAA2B;IAC/BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}