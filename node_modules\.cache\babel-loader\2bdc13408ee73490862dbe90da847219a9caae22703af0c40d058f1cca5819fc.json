{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport throttle from 'lodash/throttle';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useThrottleFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useThrottleFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var throttled = useMemo(function () {\n    return throttle(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    throttled.cancel();\n  });\n  return {\n    run: throttled,\n    cancel: throttled.cancel,\n    flush: throttled.flush\n  };\n}\nexport default useThrottleFn;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}