{"ast": null, "code": "import { eachProp, is, toArray, getFluidValue, isAnimatedString, Globals, useIsomorphicLayoutEffect, each, easings, raf, flush, FluidValue, deprecateInterpolate, callFluidObservers, frameLoop, hasFluidValue, flushCalls, isEqual, getFluidObservers, addFluidObserver, removeFluidObserver, noop, useMemoOne, deprecateDirectCall, useForceUpdate, usePrev, useOnce, useConstant, onScroll, onResize, createInterpolator, createStringInterpolator } from '@react-spring/shared';\nexport { Globals, createInterpolator, easings, useIsomorphicLayoutEffect, useReducedMotion } from '@react-spring/shared';\nimport * as React from 'react';\nimport { useContext, useMemo, useRef, useState } from 'react';\nimport { getAnimated, AnimatedValue, getPayload, AnimatedString, getAnimatedType, setAnimated } from '@react-spring/animated';\nexport * from '@react-spring/types/animated';\nexport * from '@react-spring/types/interpolation';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction callProp(value, ...args) {\n  return is.fun(value) ? value(...args) : value;\n}\nconst matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));\nconst resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;\nconst getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : undefined;\nconst noopTransform = value => value;\nconst getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!is.und(value)) {\n      defaults[key] = value;\n    }\n  }\n  return defaults;\n};\nconst DEFAULT_PROPS = ['config', 'onProps', 'onStart', 'onChange', 'onPause', 'onResume', 'onRest'];\nconst RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to = getForwardProps(props);\n  if (to) {\n    const out = {\n      to\n    };\n    eachProp(props, (val, key) => key in to || (out[key] = val));\n    return out;\n  }\n  return _extends({}, props);\n}\nfunction computeGoal(value) {\n  value = getFluidValue(value);\n  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to) {\n  return is.fun(to) || is.arr(to) && is.obj(to[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  var _ctrl$ref;\n  (_ctrl$ref = ctrl.ref) == null ? void 0 : _ctrl$ref.delete(ctrl);\n  ref == null ? void 0 : ref.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    var _ctrl$ref2;\n    (_ctrl$ref2 = ctrl.ref) == null ? void 0 : _ctrl$ref2.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\nfunction useChain(refs, timeSteps, timeFrame = 1000) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      each(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;else prevDelay = delay;\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              const memoizedDelayProp = props.delay;\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      each(refs, ref => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            each(controllers, (ctrl, i) => each(queues[i] || [], update => ctrl.queue.push(update)));\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\nconst config = {\n  default: {\n    tension: 170,\n    friction: 26\n  },\n  gentle: {\n    tension: 120,\n    friction: 14\n  },\n  wobbly: {\n    tension: 180,\n    friction: 12\n  },\n  stiff: {\n    tension: 210,\n    friction: 20\n  },\n  slow: {\n    tension: 280,\n    friction: 60\n  },\n  molasses: {\n    tension: 280,\n    friction: 120\n  }\n};\nconst defaults = _extends({}, config.default, {\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false\n});\nclass AnimationConfig {\n  constructor() {\n    this.tension = void 0;\n    this.friction = void 0;\n    this.frequency = void 0;\n    this.damping = void 0;\n    this.mass = void 0;\n    this.velocity = 0;\n    this.restVelocity = void 0;\n    this.precision = void 0;\n    this.progress = void 0;\n    this.duration = void 0;\n    this.easing = void 0;\n    this.clamp = void 0;\n    this.bounce = void 0;\n    this.decay = void 0;\n    this.round = void 0;\n    Object.assign(this, defaults);\n  }\n}\nfunction mergeConfig(config, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = _extends({}, defaultConfig);\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = _extends({}, defaultConfig, newConfig);\n  }\n  sanitizeConfig(config, newConfig);\n  Object.assign(config, newConfig);\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key];\n    }\n  }\n  let {\n    mass,\n    frequency,\n    damping\n  } = config;\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config;\n}\nfunction sanitizeConfig(config, props) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined;\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction);\n    if (isTensionConfig || !is.und(props.frequency) || !is.und(props.damping) || !is.und(props.mass)) {\n      config.duration = undefined;\n      config.decay = undefined;\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined;\n    }\n  }\n}\nconst emptyArray = [];\nclass Animation {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.to = void 0;\n    this.from = void 0;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n}\nfunction scheduleProps(callId, {\n  key,\n  props,\n  defaultProps,\n  state,\n  actions\n}) {\n  return new Promise((resolve, reject) => {\n    var _props$cancel;\n    let delay;\n    let timeout;\n    let cancel = matchProp((_props$cancel = props.cancel) != null ? _props$cancel : defaultProps == null ? void 0 : defaultProps.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps == null ? void 0 : defaultProps.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start(_extends({}, props, {\n          callId,\n          cancel\n        }), resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\nconst getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some(result => result.cancelled) ? getCancelledResult(target.get()) : results.every(result => result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every(result => result.finished));\nconst getNoopResult = value => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nconst getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nconst getCancelledResult = value => ({\n  value,\n  cancelled: true,\n  finished: false\n});\nfunction runAsync(to, props, state, target) {\n  const {\n    callId,\n    parentId,\n    onRest\n  } = props;\n  const {\n    asyncTo: prevTo,\n    promise: prevPromise\n  } = state;\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to;\n    const defaultProps = getDefaultProps(props, (value, key) => key === 'onRest' ? undefined : value);\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise((resolve, reject) => (preventBail = resolve, bail = reject));\n    const bailIfEnded = bailSignal => {\n      const bailResult = callId <= (state.cancelId || 0) && getCancelledResult(target) || callId !== state.asyncId && getFinishedResult(target, false);\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n          to: arg1\n        });\n        props.parentId = callId;\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value;\n          }\n        });\n        const result = await target.start(props);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise(resume => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result;\n      })();\n    };\n    let result;\n    if (Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (is.arr(to)) {\n        animating = (async queue => {\n          for (const props of queue) {\n            await animate(props);\n          }\n        })(to);\n      } else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : undefined;\n        state.promise = parentId ? prevPromise : undefined;\n      }\n    }\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  flush(state.timeouts, t => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = undefined;\n  if (cancelId) state.cancelId = cancelId;\n}\nclass BailSignal extends Error {\n  constructor() {\n    super('An async animation has been interrupted. You see this error because you ' + 'forgot to use `await` or `.catch(...)` on its returned promise.');\n    this.result = void 0;\n  }\n}\nclass SkipAnimationSignal extends Error {\n  constructor() {\n    super('SkipAnimationSignal');\n    this.result = void 0;\n  }\n}\nconst isFrameValue = value => value instanceof FrameValue;\nlet nextId$1 = 1;\nclass FrameValue extends FluidValue {\n  constructor(...args) {\n    super(...args);\n    this.id = nextId$1++;\n    this.key = void 0;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  get() {\n    const node = getAnimated(this);\n    return node && node.getValue();\n  }\n  to(...args) {\n    return Globals.to(this, args);\n  }\n  interpolate(...args) {\n    deprecateInterpolate();\n    return Globals.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  _attach() {}\n  _detach() {}\n  _onChange(value, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle\n    });\n  }\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      frameLoop.sort(this);\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority\n    });\n  }\n}\nconst $P = Symbol.for('SpringPhase');\nconst HAS_ANIMATED = 1;\nconst IS_ANIMATING = 2;\nconst IS_PAUSED = 4;\nconst hasAnimated = target => (target[$P] & HAS_ANIMATED) > 0;\nconst isAnimating = target => (target[$P] & IS_ANIMATING) > 0;\nconst isPaused = target => (target[$P] & IS_PAUSED) > 0;\nconst setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nconst setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\nclass SpringValue extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    this.key = void 0;\n    this.animation = new Animation();\n    this.queue = void 0;\n    this.defaultProps = {};\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._pendingCalls = new Set();\n    this._lastCallId = 0;\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n        from: arg1\n      });\n      if (is.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return getFluidValue(this.animation.to);\n  }\n  get velocity() {\n    const node = getAnimated(this);\n    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map(node => node.lastVelocity || 0);\n  }\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  get isPaused() {\n    return isPaused(this);\n  }\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let {\n      config,\n      toValues\n    } = anim;\n    const payload = getPayload(anim.to);\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to));\n    }\n    anim.values.forEach((node, i) => {\n      if (node.done) return;\n      const to = node.constructor == AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];\n      let finished = anim.immediate;\n      let position = to;\n      if (!finished) {\n        position = node.lastPosition;\n        if (config.tension <= 0) {\n          node.done = true;\n          return;\n        }\n        let elapsed = node.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node.v0 != null ? node.v0 : node.v0 = is.arr(config.velocity) ? config.velocity[i] : config.velocity;\n        let velocity;\n        const precision = config.precision || (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001));\n        if (!is.und(config.duration)) {\n          let p = 1;\n          if (config.duration > 0) {\n            if (this._memoizedDuration !== config.duration) {\n              this._memoizedDuration = config.duration;\n              if (node.durationProgress > 0) {\n                node.elapsedTime = config.duration * node.durationProgress;\n                elapsed = node.elapsedTime += dt;\n              }\n            }\n            p = (config.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node.durationProgress = p;\n          }\n          position = from + config.easing(p) * (to - from);\n          velocity = (position - node.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity;\n          const restVelocity = config.restVelocity || precision / 10;\n          const bounceFactor = config.clamp ? 0 : config.bounce;\n          const canBounce = !is.und(bounceFactor);\n          const isGrowing = from == to ? node.v0 > 0 : from < to;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to;\n              }\n            }\n            const springForce = -config.tension * 0.000001 * (position - to);\n            const dampingForce = -config.friction * 0.001 * velocity;\n            const acceleration = (springForce + dampingForce) / config.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node.done = true;\n      } else {\n        idle = false;\n      }\n      if (node.setValue(position, config.round)) {\n        changed = true;\n      }\n    });\n    const node = getAnimated(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = getFluidValue(anim.to);\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  set(value) {\n    raf.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  pause() {\n    this._update({\n      pause: true\n    });\n  }\n  resume() {\n    this._update({\n      pause: false\n    });\n  }\n  finish() {\n    if (isAnimating(this)) {\n      const {\n        to,\n        config\n      } = this.animation;\n      raf.batchedUpdates(() => {\n        this._onStart();\n        if (!config.decay) {\n          this._set(to, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to, arg2) {\n    let queue;\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : _extends({}, arg2, {\n        to\n      })];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(queue.map(props => {\n      const up = this._update(props);\n      return up;\n    })).then(results => getCombinedResult(this, results));\n  }\n  stop(cancel) {\n    const {\n      to\n    } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    raf.batchedUpdates(() => this._stop(to, cancel));\n    return this;\n  }\n  reset() {\n    this._update({\n      reset: true\n    });\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._start();\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1;\n    }\n  }\n  _prepareNode(props) {\n    const key = this.key || '';\n    let {\n      to,\n      from\n    } = props;\n    to = is.obj(to) ? to[key] : to;\n    if (to == null || isAsyncTo(to)) {\n      to = undefined;\n    }\n    from = is.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = undefined;\n    }\n    const range = {\n      to,\n      from\n    };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to];\n      from = getFluidValue(from);\n      if (!is.und(from)) {\n        this._set(from);\n      } else if (!getAnimated(this)) {\n        this._set(to);\n      }\n    }\n    return range;\n  }\n  _update(_ref, isLoop) {\n    let props = _extends({}, _ref);\n    const {\n      key,\n      defaultProps\n    } = this;\n    if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value));\n    mergeActiveFn(this, props, 'onProps');\n    sendEvent(this, 'onProps', props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error('Cannot animate a `SpringValue` object that is frozen. ' + 'Did you forget to pass your component to `animated(...)` before animating its props?');\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            flushCalls(state.pauseQueue);\n            sendEvent(this, 'onPause', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            flushCalls(state.resumeQueue);\n            sendEvent(this, 'onResume', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !is.und(range.to);\n    const hasFromProp = !is.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const {\n      key,\n      defaultProps,\n      animation: anim\n    } = this;\n    const {\n      to: prevTo,\n      from: prevFrom\n    } = anim;\n    let {\n      to = prevTo,\n      from = prevFrom\n    } = range;\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from;\n    }\n    if (props.reverse) [to, from] = [from, to];\n    const hasFromChanged = !isEqual(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = getFluidValue(from);\n    const hasToChanged = !isEqual(to, prevTo);\n    if (hasToChanged) {\n      this._focus(to);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const {\n      config\n    } = anim;\n    const {\n      decay,\n      velocity\n    } = config;\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(config, callProp(props.config, key), props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);\n    }\n    let node = getAnimated(this);\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset = is.und(props.reset) ? hasFromProp && !props.default : !is.und(from) && matchProp(props.reset, key);\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to);\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`);\n      }\n    }\n    const goalType = node.constructor;\n    let started = hasFluidValue(to);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config.decay, decay) || !isEqual(config.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = hasFluidValue(to) ? null : goalType == AnimatedString ? [1] : toArray(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const {\n          onRest\n        } = anim;\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        flushCalls(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed) raf.batchedUpdates(() => {\n          anim.changed = !reset;\n          onRest == null ? void 0 : onRest(result, this);\n          if (reset) {\n            callProp(defaultProps.onRest, result);\n          } else {\n            anim.onStart == null ? void 0 : anim.onStart(result, this);\n          }\n        });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if (getFluidObservers(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const {\n      to\n    } = this.animation;\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this);\n      if (isFrameValue(to)) {\n        priority = to.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const {\n      to\n    } = this.animation;\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this);\n    }\n  }\n  _set(arg, idle = true) {\n    const value = getFluidValue(arg);\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this);\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        const nodeType = getAnimatedType(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return getAnimated(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(this, 'onStart', getFinishedResult(this, checkFinished(this, anim.to)), this);\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  _start() {\n    const anim = this.animation;\n    getAnimated(this).reset(getFluidValue(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (Globals.skipAnimation) {\n      this.finish();\n    } else {\n      frameLoop.start(this);\n    }\n  }\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      each(anim.values, node => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined;\n      }\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal != null ? goal : anim.to));\n      flushCalls(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, 'onRest', result, this);\n      }\n    }\n  }\n}\nfunction checkFinished(target, to) {\n  const goal = computeGoal(to);\n  const value = computeGoal(target.get());\n  return isEqual(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to = props.to) {\n  let loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate(_extends({}, props, {\n      loop,\n      default: false,\n      pause: undefined,\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n      from: reset ? props.from : undefined,\n      reset\n    }, overrides));\n  }\n}\nfunction createUpdate(props) {\n  const {\n    to,\n    from\n  } = props = inferTo(props);\n  const keys = new Set();\n  if (is.obj(to)) findDefined(to, keys);\n  if (is.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update = createUpdate(props);\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update);\n  }\n  return update;\n}\nfunction findDefined(values, keys) {\n  eachProp(values, (value, key) => value != null && keys.add(key));\n}\nconst ACTIVE_EVENTS = ['onStart', 'onRest', 'onChange', 'onPause', 'onResume'];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : undefined;\n}\nfunction sendEvent(target, type, ...args) {\n  var _target$animation$typ, _target$animation, _target$defaultProps$, _target$defaultProps;\n  (_target$animation$typ = (_target$animation = target.animation)[type]) == null ? void 0 : _target$animation$typ.call(_target$animation, ...args);\n  (_target$defaultProps$ = (_target$defaultProps = target.defaultProps)[type]) == null ? void 0 : _target$defaultProps$.call(_target$defaultProps, ...args);\n}\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'];\nlet nextId = 1;\nclass Controller {\n  constructor(props, flush) {\n    this.id = nextId++;\n    this.springs = {};\n    this.queue = [];\n    this.ref = void 0;\n    this._flush = void 0;\n    this._initialProps = void 0;\n    this._lastAsyncId = 0;\n    this._active = new Set();\n    this._changed = new Set();\n    this._started = false;\n    this._item = void 0;\n    this._state = {\n      paused: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._events = {\n      onStart: new Map(),\n      onChange: new Map(),\n      onRest: new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush) {\n      this._flush = flush;\n    }\n    if (props) {\n      this.start(_extends({\n        default: true\n      }, props));\n    }\n  }\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every(spring => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  start(props) {\n    let {\n      queue\n    } = this;\n    if (props) {\n      queue = toArray(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each(spring => spring.stop(!!arg));\n    }\n    return this;\n  }\n  pause(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: true\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].pause());\n    }\n    return this;\n  }\n  resume(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: false\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].resume());\n    }\n    return this;\n  }\n  each(iterator) {\n    eachProp(this.springs, iterator);\n  }\n  _onFrame() {\n    const {\n      onStart,\n      onChange,\n      onRest\n    } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get();\n        onStart(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values;\n        onChange(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      flush(onRest, ([onRest, result]) => {\n        result.value = values;\n        onRest(result, this, this._item);\n      });\n    }\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else return;\n    raf.onFrame(this._onFrame);\n  }\n}\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(results => getCombinedResult(ctrl, results));\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const {\n    keys,\n    to,\n    from,\n    loop,\n    onRest,\n    onResolve\n  } = props;\n  const defaults = is.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined;\n  if (asyncTo) {\n    props.to = undefined;\n    props.onRest = undefined;\n    if (defaults) {\n      defaults.onRest = undefined;\n    }\n  } else {\n    each(BATCHED_EVENTS, key => {\n      const handler = props[key];\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key];\n        props[key] = ({\n          finished,\n          cancelled\n        }) => {\n          const result = queue.get(handler);\n          if (result) {\n            if (!finished) result.finished = false;\n            if (cancelled) result.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults) {\n          defaults[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl['_state'];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(key => ctrl.springs[key].start(props));\n  const cancel = props.cancel === true || getDefaultProp(props, 'cancel') === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(scheduleProps(++ctrl['_lastAsyncId'], {\n      props,\n      state,\n      actions: {\n        pause: noop,\n        resume: noop,\n        start(props, resolve) {\n          if (cancel) {\n            stopAsync(state, ctrl['_lastAsyncId']);\n            resolve(getCancelledResult(ctrl));\n          } else {\n            props.onRest = onRest;\n            resolve(runAsync(asyncTo, props, state, ctrl));\n          }\n        }\n      }\n    }));\n  }\n  if (state.paused) {\n    await new Promise(resume => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = _extends({}, ctrl.springs);\n  if (props) {\n    each(toArray(props), props => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props);\n      }\n      if (!is.obj(props.to)) {\n        props = _extends({}, props, {\n          to: undefined\n        });\n      }\n      prepareSprings(springs, props, key => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      addFluidObserver(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    addFluidObserver(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring['_prepareNode'](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst _excluded$6 = [\"children\"];\nconst SpringContext = _ref => {\n  let {\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$6);\n  const inherited = useContext(ctx);\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate;\n  props = useMemoOne(() => ({\n    pause,\n    immediate\n  }), [pause, immediate]);\n  const {\n    Provider\n  } = ctx;\n  return React.createElement(Provider, {\n    value: props\n  }, children);\n};\nconst ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\nfunction makeContext(target, init) {\n  Object.assign(target, React.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\nconst SpringRef = () => {\n  const current = [];\n  const SpringRef = function SpringRef(props) {\n    deprecateDirectCall();\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = _getProps(props, ctrl, i);\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef.current = current;\n  SpringRef.add = function (ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef.delete = function (ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef.set = function (values) {\n    each(current, ctrl => ctrl.set(values));\n  };\n  SpringRef.start = function (props) {\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = this._getProps(props, ctrl, i);\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef.update = function (props) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function _getProps(arg, ctrl, index) {\n    return is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef._getProps = _getProps;\n  return SpringRef;\n};\nfunction useSprings(length, props, deps) {\n  const propsFn = is.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const layoutId = useRef(0);\n  const forceUpdate = useForceUpdate();\n  const state = useMemo(() => ({\n    ctrls: [],\n    queue: [],\n    flush(ctrl, updates) {\n      const springs = getSprings(ctrl, updates);\n      const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs).some(key => !ctrl.springs[key]);\n      return canFlushSync ? flushUpdateQueue(ctrl, updates) : new Promise(resolve => {\n        setSprings(ctrl, springs);\n        state.queue.push(() => {\n          resolve(flushUpdateQueue(ctrl, updates));\n        });\n        forceUpdate();\n      });\n    }\n  }), []);\n  const ctrls = useRef([...state.ctrls]);\n  const updates = [];\n  const prevLength = usePrev(length) || 0;\n  useMemo(() => {\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update) {\n        updates[i] = declareUpdate(update);\n      }\n    }\n  }\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const {\n      queue\n    } = state;\n    if (queue.length) {\n      state.queue = [];\n      each(queue, cb => cb());\n    }\n    each(ctrls.current, (ctrl, i) => {\n      ref == null ? void 0 : ref.add(ctrl);\n      if (hasContext) {\n        ctrl.start({\n          default: context\n        });\n      }\n      const update = updates[i];\n      if (update) {\n        replaceRef(ctrl, update.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update);\n        } else {\n          ctrl.start(update);\n        }\n      }\n    });\n  });\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true));\n  });\n  const values = springs.map(x => _extends({}, x));\n  return ref ? [values, ref] : values;\n}\nfunction useSpring(props, deps) {\n  const isFn = is.fun(props);\n  const [[values], ref] = useSprings(1, isFn ? props : [props], isFn ? deps || [] : deps);\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\nconst initSpringRef = () => SpringRef();\nconst useSpringRef = () => useState(initSpringRef)[0];\nconst useSpringValue = (initial, props) => {\n  const springValue = useConstant(() => new SpringValue(initial, props));\n  useOnce(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = is.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = undefined;\n  const result = useSprings(length, (i, ctrl) => {\n    const props = propsFn ? propsFn(i, ctrl) : propsArg;\n    passedRef = props.ref;\n    reverse = reverse && props.reverse;\n    return props;\n  }, deps || [{}]);\n  useIsomorphicLayoutEffect(() => {\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({\n            to: parent.springs\n          });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({\n          to: parent.springs\n        });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    var _passedRef;\n    const ref = (_passedRef = passedRef) != null ? _passedRef : result[1];\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\nlet TransitionPhase;\n(function (TransitionPhase) {\n  TransitionPhase[\"MOUNT\"] = \"mount\";\n  TransitionPhase[\"ENTER\"] = \"enter\";\n  TransitionPhase[\"UPDATE\"] = \"update\";\n  TransitionPhase[\"LEAVE\"] = \"leave\";\n})(TransitionPhase || (TransitionPhase = {}));\nfunction useTransition(data, props, deps) {\n  const propsFn = is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const items = toArray(data);\n  const transitions = [];\n  const usedTransitions = useRef(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions;\n  });\n  useOnce(() => {\n    each(transitions, t => {\n      ref == null ? void 0 : ref.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      each(usedTransitions.current, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  useIsomorphicLayoutEffect(() => each(expired, ({\n    ctrl,\n    item,\n    key\n  }) => {\n    detachRefs(ctrl, ref);\n    callProp(onDestroyed, item, key);\n  }));\n  const reused = [];\n  if (prevTransitions) each(prevTransitions, (t, i) => {\n    if (t.expired) {\n      clearTimeout(t.expirationId);\n      expired.push(t);\n    } else {\n      i = reused[i] = keys.indexOf(t.key);\n      if (~i) transitions[i] = t;\n    }\n  });\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const {\n      leave\n    } = propsFn ? propsFn() : props;\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = _extends({}, t, {\n          item: items[keyIndex]\n        });\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = useForceUpdate();\n  const defaultProps = getDefaultProps(props);\n  const changes = new Map();\n  const exitingTransitions = useRef(new Map());\n  const forceChange = useRef(false);\n  each(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to;\n    let phase;\n    let propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter;\n      phase = TransitionPhase.ENTER;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave;\n          phase = TransitionPhase.LEAVE;\n        } else if (to = p.update) {\n          phase = TransitionPhase.UPDATE;\n        } else return;\n      } else if (!isLeave) {\n        to = p.enter;\n        phase = TransitionPhase.ENTER;\n      } else return;\n    }\n    to = callProp(to, t.item, i);\n    to = is.obj(to) ? inferTo(to) : {\n      to\n    };\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config;\n      to.config = callProp(config, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = _extends({}, defaultProps, {\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      reset: false\n    }, to);\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const _p = propsFn ? propsFn() : props;\n      const from = is.und(_p.initial) || prevTransitions ? _p.from : _p.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const {\n      onResolve\n    } = payload;\n    payload.onResolve = result => {\n      callProp(onResolve, result);\n      const transitions = usedTransitions.current;\n      const t = transitions.find(t => t.key === key);\n      if (!t) return;\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        return;\n      }\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle);\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 0x7fffffff) t.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions.some(t => t.expired)) {\n          exitingTransitions.current.delete(t);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    } else {\n      changes.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    }\n  });\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({\n          default: context\n        });\n      });\n    }\n  }, [context]);\n  each(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  useIsomorphicLayoutEffect(() => {\n    each(exitingTransitions.current.size ? exitingTransitions.current : changes, ({\n      phase,\n      payload\n    }, t) => {\n      const {\n        ctrl\n      } = t;\n      t.phase = phase;\n      ref == null ? void 0 : ref.add(ctrl);\n      if (hasContext && phase == TransitionPhase.ENTER) {\n        ctrl.start({\n          default: context\n        });\n      }\n      if (payload) {\n        replaceRef(ctrl, payload.ref);\n        if ((ctrl.ref || ref) && !forceChange.current) {\n          ctrl.update(payload);\n        } else {\n          ctrl.start(payload);\n          if (forceChange.current) {\n            forceChange.current = false;\n          }\n        }\n      }\n    });\n  }, reset ? void 0 : deps);\n  const renderTransitions = render => React.createElement(React.Fragment, null, transitions.map((t, i) => {\n    const {\n      springs\n    } = changes.get(t) || t.ctrl;\n    const elem = render(_extends({}, springs), t.item, t, i);\n    return elem && elem.type ? React.createElement(elem.type, _extends({}, elem.props, {\n      key: is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id,\n      ref: elem.ref\n    })) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nlet nextKey = 1;\nfunction getKeys(items, {\n  key,\n  keys = key\n}, prevTransitions) {\n  if (keys === null) {\n    const reused = new Set();\n    return items.map(item => {\n      const t = prevTransitions && prevTransitions.find(t => t.item === item && t.phase !== TransitionPhase.LEAVE && !reused.has(t));\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys);\n}\nconst _excluded$5 = [\"container\"];\nconst useScroll = (_ref = {}) => {\n  let {\n      container\n    } = _ref,\n    springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$5);\n  const [scrollValues, api] = useSpring(() => _extends({\n    scrollX: 0,\n    scrollY: 0,\n    scrollXProgress: 0,\n    scrollYProgress: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(({\n      x,\n      y\n    }) => {\n      api.start({\n        scrollX: x.current,\n        scrollXProgress: x.progress,\n        scrollY: y.current,\n        scrollYProgress: y.progress\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(scrollValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\nconst _excluded$4 = [\"container\"];\nconst useResize = _ref => {\n  let {\n      container\n    } = _ref,\n    springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$4);\n  const [sizeValues, api] = useSpring(() => _extends({\n    width: 0,\n    height: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(({\n      width,\n      height\n    }) => {\n      api.start({\n        width,\n        height,\n        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(sizeValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\nconst _excluded$3 = [\"to\", \"from\"],\n  _excluded2 = [\"root\", \"once\", \"amount\"];\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = useState(false);\n  const ref = useRef();\n  const propsFn = is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const {\n      to = {},\n      from = {}\n    } = springsProps,\n    restSpringProps = _objectWithoutPropertiesLoose(springsProps, _excluded$3);\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => _extends({\n    from\n  }, restSpringProps), []);\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current;\n    const _ref = intersectionArguments != null ? intersectionArguments : {},\n      {\n        root,\n        once,\n        amount = 'any'\n      } = _ref,\n      restArgs = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    if (!element || once && isInView || typeof IntersectionObserver === 'undefined') return;\n    const activeIntersections = new WeakMap();\n    const onEnter = () => {\n      if (to) {\n        api.start(to);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? undefined : cleanup;\n    };\n    const handleIntersection = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, _extends({\n      root: root && root.current || undefined,\n      threshold: typeof amount === 'number' || Array.isArray(amount) ? amount : defaultThresholdOptions[amount]\n    }, restArgs));\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\nconst _excluded$2 = [\"children\"];\nfunction Spring(_ref) {\n  let {\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n  return children(useSpring(props));\n}\nconst _excluded$1 = [\"items\", \"children\"];\nfunction Trail(_ref) {\n  let {\n      items,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return is.fun(result) ? result(trails[index]) : result;\n  });\n}\nconst _excluded = [\"items\", \"children\"];\nfunction Transition(_ref) {\n  let {\n      items,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return useTransition(items, props)(children);\n}\nclass Interpolation extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.key = void 0;\n    this.idle = true;\n    this.calc = void 0;\n    this._active = new Set();\n    this.source = source;\n    this.calc = createInterpolator(...args);\n    const value = this._get();\n    const nodeType = getAnimatedType(value);\n    setAnimated(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = is.arr(this.source) ? this.source.map(getFluidValue) : toArray(getFluidValue(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      each(getPayload(this), node => {\n        node.done = false;\n      });\n      if (Globals.skipAnimation) {\n        raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        frameLoop.start(this);\n      }\n    }\n  }\n  _attach() {\n    let priority = 1;\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce((highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);\n    }\n  }\n}\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    each(getPayload(self), node => {\n      node.done = true;\n    });\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self\n    });\n  }\n}\nconst to = (source, ...args) => new Interpolation(source, args);\nconst interpolate = (source, ...args) => (deprecateInterpolate(), new Interpolation(source, args));\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nconst update = frameLoop.advance;\nexport { BailSignal, Controller, FrameValue, Interpolation, Spring, SpringContext, SpringRef, SpringValue, Trail, Transition, config, inferTo, interpolate, to, update, useChain, useInView, useResize, useScroll, useSpring, useSpringRef, useSpringValue, useSprings, useTrail, useTransition };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}