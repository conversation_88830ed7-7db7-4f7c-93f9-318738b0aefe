{"ast": null, "code": "var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;\n}\nmodule.exports = baseTrim;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}