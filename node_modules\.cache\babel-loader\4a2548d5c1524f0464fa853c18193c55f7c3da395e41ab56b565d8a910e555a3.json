{"ast": null, "code": "import \"./collapse.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Collapse, CollapsePanel } from './collapse';\nexport default attachPropertiesToComponent(Collapse, {\n  Panel: CollapsePanel\n});", "map": {"version": 3, "names": ["attachPropertiesToComponent", "Collapse", "CollapsePanel", "Panel"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/collapse/index.js"], "sourcesContent": ["import \"./collapse.css\";\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Collapse, CollapsePanel } from './collapse';\nexport default attachPropertiesToComponent(Collapse, {\n  Panel: CollapsePanel\n});"], "mappings": "AAAA,OAAO,gBAAgB;AACvB,SAASA,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,QAAQ,EAAEC,aAAa,QAAQ,YAAY;AACpD,eAAeF,2BAA2B,CAACC,QAAQ,EAAE;EACnDE,KAAK,EAAED;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}