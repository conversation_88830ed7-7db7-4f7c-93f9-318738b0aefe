{"ast": null, "code": "import { __values } from \"tslib\";\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport isAppleDevice from '../utils/isAppleDevice';\n// 键盘事件 keyCode 别名\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  delete: 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  meta: isAppleDevice ? [91, 93] : [91, 92],\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n};\n// 修饰键\nvar modifierKey = {\n  ctrl: function (event) {\n    return event.ctrlKey;\n  },\n  shift: function (event) {\n    return event.shiftKey;\n  },\n  alt: function (event) {\n    return event.altKey;\n  },\n  meta: function (event) {\n    if (event.type === 'keyup') {\n      return aliasKeyCodeMap.meta.includes(event.keyCode);\n    }\n    return event.metaKey;\n  }\n};\n// 判断合法的按键类型\nfunction isValidKeyType(value) {\n  return isString(value) || isNumber(value);\n}\n// 根据 event 计算激活键数量\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n    return total;\n  }, 0);\n  // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns string | number | boolean\n */\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a;\n  // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n  if (!event.key) {\n    return false;\n  }\n  // 数字类型直接匹配事件的 keyCode\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter ? keyFilter : false;\n  }\n  // 字符串依次判断是否有组合键\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value;\n      // 组合键\n      var genModifier = modifierKey[key];\n      // keyCode 别名\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1.return)) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length ? keyFilter : false;\n  }\n  return genLen === genArr.length ? keyFilter : false;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n  if (isValidKeyType(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.find(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n  return function () {\n    return Boolean(keyFilter);\n  };\n}\nvar defaultEvents = ['keydown'];\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n    _b = _a.events,\n    events = _b === void 0 ? defaultEvents : _b,\n    target = _a.target,\n    _c = _a.exactMatch,\n    exactMatch = _c === void 0 ? false : _c,\n    _d = _a.useCapture,\n    useCapture = _d === void 0 ? false : _d;\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n    var _b;\n    var el = getTargetElement(target, window);\n    if (!el) {\n      return;\n    }\n    var callbackHandler = function (event) {\n      var _a;\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n      var keyGuard = genGuard(event);\n      var firedKey = isValidKeyType(keyGuard) ? keyGuard : event.key;\n      if (keyGuard) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event, firedKey);\n      }\n    };\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1.return)) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return function () {\n      var e_3, _a;\n      var _b;\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2.return)) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\nexport default useKeyPress;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}