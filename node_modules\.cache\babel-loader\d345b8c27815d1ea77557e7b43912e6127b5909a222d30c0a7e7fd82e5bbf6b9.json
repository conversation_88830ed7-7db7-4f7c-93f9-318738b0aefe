{"ast": null, "code": "import { useMemoizedFn } from 'ahooks';\nimport { useEffect } from 'react';\nexport function observe(element, options, callback) {\n  if (element && typeof MutationObserver !== 'undefined') {\n    let observer = new MutationObserver(() => {\n      callback();\n    });\n    observer.observe(element, options);\n    // Return cleanup function\n    return () => {\n      if (observer) {\n        observer.disconnect();\n        observer = null;\n      }\n    };\n  }\n  return () => {};\n}\nexport function useMutationEffect(effect, targetRef, options) {\n  const fn = useMemoizedFn(effect);\n  useEffect(() => {\n    const cleanup = observe(targetRef.current, options, fn);\n    return cleanup;\n  }, [targetRef]);\n}", "map": {"version": 3, "names": ["useMemoizedFn", "useEffect", "observe", "element", "options", "callback", "MutationObserver", "observer", "disconnect", "useMutationEffect", "effect", "targetRef", "fn", "cleanup", "current"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/use-mutation-effect.js"], "sourcesContent": ["import { useMemoizedFn } from 'ahooks';\nimport { useEffect } from 'react';\nexport function observe(element, options, callback) {\n  if (element && typeof MutationObserver !== 'undefined') {\n    let observer = new MutationObserver(() => {\n      callback();\n    });\n    observer.observe(element, options);\n    // Return cleanup function\n    return () => {\n      if (observer) {\n        observer.disconnect();\n        observer = null;\n      }\n    };\n  }\n  return () => {};\n}\nexport function useMutationEffect(effect, targetRef, options) {\n  const fn = useMemoizedFn(effect);\n  useEffect(() => {\n    const cleanup = observe(targetRef.current, options, fn);\n    return cleanup;\n  }, [targetRef]);\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AACtC,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAO,SAASC,OAAOA,CAACC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAClD,IAAIF,OAAO,IAAI,OAAOG,gBAAgB,KAAK,WAAW,EAAE;IACtD,IAAIC,QAAQ,GAAG,IAAID,gBAAgB,CAAC,MAAM;MACxCD,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;IACFE,QAAQ,CAACL,OAAO,CAACC,OAAO,EAAEC,OAAO,CAAC;IAClC;IACA,OAAO,MAAM;MACX,IAAIG,QAAQ,EAAE;QACZA,QAAQ,CAACC,UAAU,CAAC,CAAC;QACrBD,QAAQ,GAAG,IAAI;MACjB;IACF,CAAC;EACH;EACA,OAAO,MAAM,CAAC,CAAC;AACjB;AACA,OAAO,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,SAAS,EAAEP,OAAO,EAAE;EAC5D,MAAMQ,EAAE,GAAGZ,aAAa,CAACU,MAAM,CAAC;EAChCT,SAAS,CAAC,MAAM;IACd,MAAMY,OAAO,GAAGX,OAAO,CAACS,SAAS,CAACG,OAAO,EAAEV,OAAO,EAAEQ,EAAE,CAAC;IACvD,OAAOC,OAAO;EAChB,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}