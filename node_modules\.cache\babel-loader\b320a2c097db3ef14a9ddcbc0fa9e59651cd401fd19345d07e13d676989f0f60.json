{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useState } from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nimport { mergeProps } from '../../utils/with-default-props';\nconst classPrefix = `adm-water-mark`;\nconst defaultProps = {\n  fullPage: true\n};\nexport const WaterMark = p => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    zIndex,\n    gapX = 24,\n    gapY = 48,\n    width = 120,\n    height = 64,\n    rotate = -22,\n    image,\n    imageWidth = 120,\n    imageHeight = 64,\n    content,\n    fontStyle = 'normal',\n    fontWeight = 'normal',\n    fontColor = 'rgba(0,0,0,.15)',\n    fontSize = 14,\n    fontFamily = 'sans-serif'\n  } = props;\n  const [base64Url, setBase64Url] = useState('');\n  useEffect(() => {\n    const canvas = document.createElement('canvas');\n    const ratio = window.devicePixelRatio;\n    const ctx = canvas.getContext('2d');\n    const canvasWidth = `${(gapX + width) * ratio}px`;\n    const canvasHeight = `${(gapY + height) * ratio}px`;\n    const markWidth = width * ratio;\n    const markHeight = height * ratio;\n    canvas.setAttribute('width', canvasWidth);\n    canvas.setAttribute('height', canvasHeight);\n    if (ctx) {\n      if (image) {\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const img = new Image();\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.onload = () => {\n          ctx.drawImage(img, -imageWidth * ratio / 2, -imageHeight * ratio / 2, imageWidth * ratio, imageHeight * ratio);\n          ctx.restore();\n          setBase64Url(canvas.toDataURL());\n        };\n        img.src = image;\n      } else if (content) {\n        ctx.textBaseline = 'middle';\n        ctx.textAlign = 'center';\n        // 文字绕中间旋转\n        ctx.translate(markWidth / 2, markHeight / 2);\n        ctx.rotate(Math.PI / 180 * Number(rotate));\n        const markSize = Number(fontSize) * ratio;\n        ctx.font = `${fontStyle} normal ${fontWeight} ${markSize}px/${markHeight}px ${fontFamily}`;\n        ctx.fillStyle = fontColor;\n        if (Array.isArray(content)) {\n          content.forEach((item, index) => ctx.fillText(item, 0, index * markSize));\n        } else {\n          ctx.fillText(content, 0, 0);\n        }\n        ctx.restore();\n        setBase64Url(canvas.toDataURL());\n      }\n    } else {\n      throw new Error('Canvas is not supported in the current environment');\n    }\n  }, [gapX, gapY, rotate, fontStyle, fontWeight, width, height, fontFamily, fontColor, image, content, fontSize]);\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classNames(classPrefix, {\n      [`${classPrefix}-full-page`]: props.fullPage\n    }),\n    style: {\n      zIndex,\n      backgroundSize: `${gapX + width}px`,\n      // Not give `url` if its empty. Which will cause 404 error.\n      backgroundImage: base64Url === '' ? undefined : `url('${base64Url}')`\n    }\n  }));\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}