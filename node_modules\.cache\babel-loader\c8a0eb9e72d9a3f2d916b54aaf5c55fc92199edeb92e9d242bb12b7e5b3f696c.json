{"ast": null, "code": "import React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport runes from 'runes2';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { devError } from '../../utils/dev-log';\nimport useInputHandleKeyDown from '../../components/input/useInputHandleKeyDown';\nconst classPrefix = 'adm-text-area';\nconst defaultProps = {\n  rows: 2,\n  showCount: false,\n  autoSize: false,\n  defaultValue: ''\n};\nexport const TextArea = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    autoSize,\n    showCount,\n    maxLength\n  } = props;\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    value: props.value === null ? '' : props.value\n  }));\n  if (props.value === null) {\n    devError('TextArea', '`value` prop on `TextArea` should not be `null`. Consider using an empty string to clear the component.');\n  }\n  const nativeTextAreaRef = useRef(null);\n  // https://github.com/ant-design/ant-design-mobile/issues/5961\n  const heightRef = useRef('auto');\n  // https://github.com/ant-design/ant-design-mobile/issues/6051\n  const hiddenTextAreaRef = useRef(null);\n  const handleKeydown = useInputHandleKeyDown({\n    onEnterPress: props.onEnterPress,\n    onKeyDown: props.onKeyDown,\n    nativeInputRef: nativeTextAreaRef,\n    enterKeyHint: props.enterKeyHint\n  });\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      setValue('');\n    },\n    focus: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      return nativeTextAreaRef.current;\n    }\n  }));\n  useIsomorphicLayoutEffect(() => {\n    if (!autoSize) return;\n    const textArea = nativeTextAreaRef.current;\n    const hiddenTextArea = hiddenTextAreaRef.current;\n    if (!textArea) return;\n    textArea.style.height = heightRef.current;\n    if (!hiddenTextArea) return;\n    let height = hiddenTextArea.scrollHeight;\n    if (typeof autoSize === 'object') {\n      const computedStyle = window.getComputedStyle(textArea);\n      const lineHeight = parseFloat(computedStyle.lineHeight);\n      if (autoSize.minRows) {\n        height = Math.max(height, autoSize.minRows * lineHeight);\n      }\n      if (autoSize.maxRows) {\n        height = Math.min(height, autoSize.maxRows * lineHeight);\n      }\n    }\n    heightRef.current = `${height}px`;\n    textArea.style.height = `${height}px`;\n  }, [value, autoSize]);\n  const compositingRef = useRef(false);\n  let count;\n  const valueLength = runes(value).length;\n  if (typeof showCount === 'function') {\n    count = showCount(valueLength, maxLength);\n  } else if (showCount) {\n    count = React.createElement(\"div\", {\n      className: `${classPrefix}-count`\n    }, maxLength === undefined ? valueLength : valueLength + '/' + maxLength);\n  }\n  let rows = props.rows;\n  if (typeof autoSize === 'object') {\n    if (autoSize.maxRows && rows > autoSize.maxRows) {\n      rows = autoSize.maxRows;\n    }\n    if (autoSize.minRows && rows < autoSize.minRows) {\n      rows = autoSize.minRows;\n    }\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"textarea\", {\n    ref: nativeTextAreaRef,\n    className: `${classPrefix}-element`,\n    rows: rows,\n    value: value,\n    placeholder: props.placeholder,\n    onChange: e => {\n      let v = e.target.value;\n      if (maxLength && !compositingRef.current) {\n        v = runes(v).slice(0, maxLength).join('');\n      }\n      setValue(v);\n    },\n    id: props.id,\n    onCompositionStart: e => {\n      var _a;\n      compositingRef.current = true;\n      (_a = props.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      compositingRef.current = false;\n      if (maxLength) {\n        const v = e.target.value;\n        setValue(runes(v).slice(0, maxLength).join(''));\n      }\n      (_a = props.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    autoComplete: props.autoComplete,\n    autoFocus: props.autoFocus,\n    disabled: props.disabled,\n    readOnly: props.readOnly,\n    name: props.name,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur,\n    onClick: props.onClick,\n    onKeyDown: handleKeydown\n  }), count, autoSize && React.createElement(\"textarea\", {\n    ref: hiddenTextAreaRef,\n    className: `${classPrefix}-element ${classPrefix}-element-hidden`,\n    value: value,\n    rows: rows,\n    \"aria-hidden\": true,\n    readOnly: true\n  })));\n});\nTextArea.defaultProps = defaultProps;", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useRef", "useIsomorphicLayoutEffect", "runes", "withNativeProps", "usePropsValue", "mergeProps", "dev<PERSON><PERSON><PERSON>", "useInputHandleKeyDown", "classPrefix", "defaultProps", "rows", "showCount", "autoSize", "defaultValue", "TextArea", "p", "ref", "props", "max<PERSON><PERSON><PERSON>", "value", "setValue", "Object", "assign", "nativeTextAreaRef", "heightRef", "hiddenTextAreaRef", "handleKeydown", "onEnterPress", "onKeyDown", "nativeInputRef", "enterKeyHint", "clear", "focus", "_a", "current", "blur", "nativeElement", "textArea", "hiddenTextArea", "style", "height", "scrollHeight", "computedStyle", "window", "getComputedStyle", "lineHeight", "parseFloat", "minRows", "Math", "max", "maxRows", "min", "compositingRef", "count", "valueLength", "length", "createElement", "className", "undefined", "placeholder", "onChange", "e", "v", "target", "slice", "join", "id", "onCompositionStart", "call", "onCompositionEnd", "autoComplete", "autoFocus", "disabled", "readOnly", "name", "onFocus", "onBlur", "onClick"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/text-area/text-area.js"], "sourcesContent": ["import React, { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { useIsomorphicLayoutEffect } from 'ahooks';\nimport runes from 'runes2';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { devError } from '../../utils/dev-log';\nimport useInputHandleKeyDown from '../../components/input/useInputHandleKeyDown';\nconst classPrefix = 'adm-text-area';\nconst defaultProps = {\n  rows: 2,\n  showCount: false,\n  autoSize: false,\n  defaultValue: ''\n};\nexport const TextArea = forwardRef((p, ref) => {\n  const props = mergeProps(defaultProps, p);\n  const {\n    autoSize,\n    showCount,\n    maxLength\n  } = props;\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    value: props.value === null ? '' : props.value\n  }));\n  if (props.value === null) {\n    devError('TextArea', '`value` prop on `TextArea` should not be `null`. Consider using an empty string to clear the component.');\n  }\n  const nativeTextAreaRef = useRef(null);\n  // https://github.com/ant-design/ant-design-mobile/issues/5961\n  const heightRef = useRef('auto');\n  // https://github.com/ant-design/ant-design-mobile/issues/6051\n  const hiddenTextAreaRef = useRef(null);\n  const handleKeydown = useInputHandleKeyDown({\n    onEnterPress: props.onEnterPress,\n    onKeyDown: props.onKeyDown,\n    nativeInputRef: nativeTextAreaRef,\n    enterKeyHint: props.enterKeyHint\n  });\n  useImperativeHandle(ref, () => ({\n    clear: () => {\n      setValue('');\n    },\n    focus: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    },\n    blur: () => {\n      var _a;\n      (_a = nativeTextAreaRef.current) === null || _a === void 0 ? void 0 : _a.blur();\n    },\n    get nativeElement() {\n      return nativeTextAreaRef.current;\n    }\n  }));\n  useIsomorphicLayoutEffect(() => {\n    if (!autoSize) return;\n    const textArea = nativeTextAreaRef.current;\n    const hiddenTextArea = hiddenTextAreaRef.current;\n    if (!textArea) return;\n    textArea.style.height = heightRef.current;\n    if (!hiddenTextArea) return;\n    let height = hiddenTextArea.scrollHeight;\n    if (typeof autoSize === 'object') {\n      const computedStyle = window.getComputedStyle(textArea);\n      const lineHeight = parseFloat(computedStyle.lineHeight);\n      if (autoSize.minRows) {\n        height = Math.max(height, autoSize.minRows * lineHeight);\n      }\n      if (autoSize.maxRows) {\n        height = Math.min(height, autoSize.maxRows * lineHeight);\n      }\n    }\n    heightRef.current = `${height}px`;\n    textArea.style.height = `${height}px`;\n  }, [value, autoSize]);\n  const compositingRef = useRef(false);\n  let count;\n  const valueLength = runes(value).length;\n  if (typeof showCount === 'function') {\n    count = showCount(valueLength, maxLength);\n  } else if (showCount) {\n    count = React.createElement(\"div\", {\n      className: `${classPrefix}-count`\n    }, maxLength === undefined ? valueLength : valueLength + '/' + maxLength);\n  }\n  let rows = props.rows;\n  if (typeof autoSize === 'object') {\n    if (autoSize.maxRows && rows > autoSize.maxRows) {\n      rows = autoSize.maxRows;\n    }\n    if (autoSize.minRows && rows < autoSize.minRows) {\n      rows = autoSize.minRows;\n    }\n  }\n  return withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"textarea\", {\n    ref: nativeTextAreaRef,\n    className: `${classPrefix}-element`,\n    rows: rows,\n    value: value,\n    placeholder: props.placeholder,\n    onChange: e => {\n      let v = e.target.value;\n      if (maxLength && !compositingRef.current) {\n        v = runes(v).slice(0, maxLength).join('');\n      }\n      setValue(v);\n    },\n    id: props.id,\n    onCompositionStart: e => {\n      var _a;\n      compositingRef.current = true;\n      (_a = props.onCompositionStart) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    onCompositionEnd: e => {\n      var _a;\n      compositingRef.current = false;\n      if (maxLength) {\n        const v = e.target.value;\n        setValue(runes(v).slice(0, maxLength).join(''));\n      }\n      (_a = props.onCompositionEnd) === null || _a === void 0 ? void 0 : _a.call(props, e);\n    },\n    autoComplete: props.autoComplete,\n    autoFocus: props.autoFocus,\n    disabled: props.disabled,\n    readOnly: props.readOnly,\n    name: props.name,\n    onFocus: props.onFocus,\n    onBlur: props.onBlur,\n    onClick: props.onClick,\n    onKeyDown: handleKeydown\n  }), count, autoSize && React.createElement(\"textarea\", {\n    ref: hiddenTextAreaRef,\n    className: `${classPrefix}-element ${classPrefix}-element-hidden`,\n    value: value,\n    rows: rows,\n    \"aria-hidden\": true,\n    readOnly: true\n  })));\n});\nTextArea.defaultProps = defaultProps;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AACtE,SAASC,yBAAyB,QAAQ,QAAQ;AAClD,OAAOC,KAAK,MAAM,QAAQ;AAC1B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,OAAOC,qBAAqB,MAAM,8CAA8C;AAChF,MAAMC,WAAW,GAAG,eAAe;AACnC,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,CAAC;EACPC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,QAAQ,GAAGhB,UAAU,CAAC,CAACiB,CAAC,EAAEC,GAAG,KAAK;EAC7C,MAAMC,KAAK,GAAGZ,UAAU,CAACI,YAAY,EAAEM,CAAC,CAAC;EACzC,MAAM;IACJH,QAAQ;IACRD,SAAS;IACTO;EACF,CAAC,GAAGD,KAAK;EACT,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,aAAa,CAACiB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAE;IAC9EE,KAAK,EAAEF,KAAK,CAACE,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGF,KAAK,CAACE;EAC3C,CAAC,CAAC,CAAC;EACH,IAAIF,KAAK,CAACE,KAAK,KAAK,IAAI,EAAE;IACxBb,QAAQ,CAAC,UAAU,EAAE,yGAAyG,CAAC;EACjI;EACA,MAAMiB,iBAAiB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACtC;EACA,MAAMwB,SAAS,GAAGxB,MAAM,CAAC,MAAM,CAAC;EAChC;EACA,MAAMyB,iBAAiB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM0B,aAAa,GAAGnB,qBAAqB,CAAC;IAC1CoB,YAAY,EAAEV,KAAK,CAACU,YAAY;IAChCC,SAAS,EAAEX,KAAK,CAACW,SAAS;IAC1BC,cAAc,EAAEN,iBAAiB;IACjCO,YAAY,EAAEb,KAAK,CAACa;EACtB,CAAC,CAAC;EACF/B,mBAAmB,CAACiB,GAAG,EAAE,OAAO;IAC9Be,KAAK,EAAEA,CAAA,KAAM;MACXX,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC;IACDY,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIC,EAAE;MACN,CAACA,EAAE,GAAGV,iBAAiB,CAACW,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,KAAK,CAAC,CAAC;IAClF,CAAC;IACDG,IAAI,EAAEA,CAAA,KAAM;MACV,IAAIF,EAAE;MACN,CAACA,EAAE,GAAGV,iBAAiB,CAACW,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAAC,CAAC;IACjF,CAAC;IACD,IAAIC,aAAaA,CAAA,EAAG;MAClB,OAAOb,iBAAiB,CAACW,OAAO;IAClC;EACF,CAAC,CAAC,CAAC;EACHjC,yBAAyB,CAAC,MAAM;IAC9B,IAAI,CAACW,QAAQ,EAAE;IACf,MAAMyB,QAAQ,GAAGd,iBAAiB,CAACW,OAAO;IAC1C,MAAMI,cAAc,GAAGb,iBAAiB,CAACS,OAAO;IAChD,IAAI,CAACG,QAAQ,EAAE;IACfA,QAAQ,CAACE,KAAK,CAACC,MAAM,GAAGhB,SAAS,CAACU,OAAO;IACzC,IAAI,CAACI,cAAc,EAAE;IACrB,IAAIE,MAAM,GAAGF,cAAc,CAACG,YAAY;IACxC,IAAI,OAAO7B,QAAQ,KAAK,QAAQ,EAAE;MAChC,MAAM8B,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACP,QAAQ,CAAC;MACvD,MAAMQ,UAAU,GAAGC,UAAU,CAACJ,aAAa,CAACG,UAAU,CAAC;MACvD,IAAIjC,QAAQ,CAACmC,OAAO,EAAE;QACpBP,MAAM,GAAGQ,IAAI,CAACC,GAAG,CAACT,MAAM,EAAE5B,QAAQ,CAACmC,OAAO,GAAGF,UAAU,CAAC;MAC1D;MACA,IAAIjC,QAAQ,CAACsC,OAAO,EAAE;QACpBV,MAAM,GAAGQ,IAAI,CAACG,GAAG,CAACX,MAAM,EAAE5B,QAAQ,CAACsC,OAAO,GAAGL,UAAU,CAAC;MAC1D;IACF;IACArB,SAAS,CAACU,OAAO,GAAG,GAAGM,MAAM,IAAI;IACjCH,QAAQ,CAACE,KAAK,CAACC,MAAM,GAAG,GAAGA,MAAM,IAAI;EACvC,CAAC,EAAE,CAACrB,KAAK,EAAEP,QAAQ,CAAC,CAAC;EACrB,MAAMwC,cAAc,GAAGpD,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIqD,KAAK;EACT,MAAMC,WAAW,GAAGpD,KAAK,CAACiB,KAAK,CAAC,CAACoC,MAAM;EACvC,IAAI,OAAO5C,SAAS,KAAK,UAAU,EAAE;IACnC0C,KAAK,GAAG1C,SAAS,CAAC2C,WAAW,EAAEpC,SAAS,CAAC;EAC3C,CAAC,MAAM,IAAIP,SAAS,EAAE;IACpB0C,KAAK,GAAGxD,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;MACjCC,SAAS,EAAE,GAAGjD,WAAW;IAC3B,CAAC,EAAEU,SAAS,KAAKwC,SAAS,GAAGJ,WAAW,GAAGA,WAAW,GAAG,GAAG,GAAGpC,SAAS,CAAC;EAC3E;EACA,IAAIR,IAAI,GAAGO,KAAK,CAACP,IAAI;EACrB,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;IAChC,IAAIA,QAAQ,CAACsC,OAAO,IAAIxC,IAAI,GAAGE,QAAQ,CAACsC,OAAO,EAAE;MAC/CxC,IAAI,GAAGE,QAAQ,CAACsC,OAAO;IACzB;IACA,IAAItC,QAAQ,CAACmC,OAAO,IAAIrC,IAAI,GAAGE,QAAQ,CAACmC,OAAO,EAAE;MAC/CrC,IAAI,GAAGE,QAAQ,CAACmC,OAAO;IACzB;EACF;EACA,OAAO5C,eAAe,CAACc,KAAK,EAAEpB,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;IACvDC,SAAS,EAAEjD;EACb,CAAC,EAAEX,KAAK,CAAC2D,aAAa,CAAC,UAAU,EAAE;IACjCxC,GAAG,EAAEO,iBAAiB;IACtBkC,SAAS,EAAE,GAAGjD,WAAW,UAAU;IACnCE,IAAI,EAAEA,IAAI;IACVS,KAAK,EAAEA,KAAK;IACZwC,WAAW,EAAE1C,KAAK,CAAC0C,WAAW;IAC9BC,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIC,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC5C,KAAK;MACtB,IAAID,SAAS,IAAI,CAACkC,cAAc,CAAClB,OAAO,EAAE;QACxC4B,CAAC,GAAG5D,KAAK,CAAC4D,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE9C,SAAS,CAAC,CAAC+C,IAAI,CAAC,EAAE,CAAC;MAC3C;MACA7C,QAAQ,CAAC0C,CAAC,CAAC;IACb,CAAC;IACDI,EAAE,EAAEjD,KAAK,CAACiD,EAAE;IACZC,kBAAkB,EAAEN,CAAC,IAAI;MACvB,IAAI5B,EAAE;MACNmB,cAAc,CAAClB,OAAO,GAAG,IAAI;MAC7B,CAACD,EAAE,GAAGhB,KAAK,CAACkD,kBAAkB,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,IAAI,CAACnD,KAAK,EAAE4C,CAAC,CAAC;IACxF,CAAC;IACDQ,gBAAgB,EAAER,CAAC,IAAI;MACrB,IAAI5B,EAAE;MACNmB,cAAc,CAAClB,OAAO,GAAG,KAAK;MAC9B,IAAIhB,SAAS,EAAE;QACb,MAAM4C,CAAC,GAAGD,CAAC,CAACE,MAAM,CAAC5C,KAAK;QACxBC,QAAQ,CAAClB,KAAK,CAAC4D,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE9C,SAAS,CAAC,CAAC+C,IAAI,CAAC,EAAE,CAAC,CAAC;MACjD;MACA,CAAChC,EAAE,GAAGhB,KAAK,CAACoD,gBAAgB,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,IAAI,CAACnD,KAAK,EAAE4C,CAAC,CAAC;IACtF,CAAC;IACDS,YAAY,EAAErD,KAAK,CAACqD,YAAY;IAChCC,SAAS,EAAEtD,KAAK,CAACsD,SAAS;IAC1BC,QAAQ,EAAEvD,KAAK,CAACuD,QAAQ;IACxBC,QAAQ,EAAExD,KAAK,CAACwD,QAAQ;IACxBC,IAAI,EAAEzD,KAAK,CAACyD,IAAI;IAChBC,OAAO,EAAE1D,KAAK,CAAC0D,OAAO;IACtBC,MAAM,EAAE3D,KAAK,CAAC2D,MAAM;IACpBC,OAAO,EAAE5D,KAAK,CAAC4D,OAAO;IACtBjD,SAAS,EAAEF;EACb,CAAC,CAAC,EAAE2B,KAAK,EAAEzC,QAAQ,IAAIf,KAAK,CAAC2D,aAAa,CAAC,UAAU,EAAE;IACrDxC,GAAG,EAAES,iBAAiB;IACtBgC,SAAS,EAAE,GAAGjD,WAAW,YAAYA,WAAW,iBAAiB;IACjEW,KAAK,EAAEA,KAAK;IACZT,IAAI,EAAEA,IAAI;IACV,aAAa,EAAE,IAAI;IACnB+D,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF3D,QAAQ,CAACL,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}