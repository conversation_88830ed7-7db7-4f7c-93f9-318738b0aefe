{"ast": null, "code": "import { createContext } from 'react';\nexport const RadioGroupContext = createContext(null);", "map": {"version": 3, "names": ["createContext", "RadioGroupContext"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/radio/group-context.js"], "sourcesContent": ["import { createContext } from 'react';\nexport const RadioGroupContext = createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,MAAMC,iBAAiB,GAAGD,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}