{"ast": null, "code": "import React, { useState, useEffect, forwardRef, useImperativeHandle, memo } from 'react';\nimport classNames from 'classnames';\nimport Popup from '../popup';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport PickerView from '../picker-view';\nimport { generateColumnsExtend, useColumnsExtend } from '../picker-view/columns-extend';\nimport { useConfig } from '../config-provider';\nimport { useMemoizedFn } from 'ahooks';\nimport SafeArea from '../safe-area';\nimport { defaultRenderLabel } from './picker-utils';\nconst classPrefix = `adm-picker`;\nconst defaultProps = {\n  defaultValue: [],\n  closeOnMaskClick: true,\n  renderLabel: defaultRenderLabel,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const Picker = memo(forwardRef((p, ref) => {\n  var _a;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel\n  }, p);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: false,\n    onChange: v => {\n      var _a;\n      if (v === false) {\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      }\n    }\n  });\n  const actions = {\n    toggle: () => {\n      setVisible(v => !v);\n    },\n    open: () => {\n      setVisible(true);\n    },\n    close: () => {\n      setVisible(false);\n    }\n  };\n  useImperativeHandle(ref, () => actions);\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      const extend = generateColumnsExtend(props.columns, val);\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n    }\n  }));\n  const extend = useColumnsExtend(props.columns, value);\n  const [innerValue, setInnerValue] = useState(value);\n  useEffect(() => {\n    if (innerValue !== value) {\n      setInnerValue(value);\n    }\n  }, [visible]);\n  useEffect(() => {\n    if (!visible) {\n      setInnerValue(value);\n    }\n  }, [value]);\n  const onChange = useMemoizedFn((val, ext) => {\n    var _a;\n    setInnerValue(val);\n    if (visible) {\n      (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, val, ext);\n    }\n  });\n  const pickerElement = withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"a\", {\n    role: 'button',\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    }\n  }, props.cancelText), React.createElement(\"div\", {\n    className: `${classPrefix}-header-title`\n  }, props.title), React.createElement(\"a\", {\n    role: 'button',\n    className: classNames(`${classPrefix}-header-button`, props.loading && `${classPrefix}-header-button-disabled`),\n    onClick: () => {\n      if (props.loading) return;\n      setValue(innerValue, true);\n      setVisible(false);\n    },\n    \"aria-disabled\": props.loading\n  }, props.confirmText)), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(PickerView, {\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    columns: props.columns,\n    renderLabel: props.renderLabel,\n    value: innerValue,\n    mouseWheel: props.mouseWheel,\n    onChange: onChange\n  }))));\n  const popupElement = React.createElement(Popup, {\n    style: props.popupStyle,\n    className: classNames(`${classPrefix}-popup`, props.popupClassName),\n    visible: visible,\n    position: 'bottom',\n    onMaskClick: () => {\n      var _a;\n      if (!props.closeOnMaskClick) return;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    },\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    forceRender: props.forceRender,\n    stopPropagation: props.stopPropagation\n  }, pickerElement, React.createElement(SafeArea, {\n    position: 'bottom'\n  }));\n  return React.createElement(React.Fragment, null, popupElement, (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, extend.items, actions));\n}));\nPicker.displayName = 'Picker';", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "memo", "classNames", "Popup", "mergeProps", "withNativeProps", "usePropsValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generateColumnsExtend", "useColumnsExtend", "useConfig", "useMemoizedFn", "SafeArea", "defaultRenderLabel", "classPrefix", "defaultProps", "defaultValue", "closeOnMaskClick", "renderLabel", "destroyOnClose", "forceRender", "Picker", "p", "ref", "_a", "locale", "props", "confirmText", "common", "confirm", "cancelText", "cancel", "visible", "setVisible", "value", "onChange", "v", "onClose", "call", "actions", "toggle", "open", "close", "setValue", "Object", "assign", "val", "extend", "columns", "onConfirm", "innerValue", "setInnerValue", "ext", "onSelect", "pickerElement", "createElement", "className", "role", "onClick", "onCancel", "title", "loading", "loadingContent", "mouseWheel", "popupElement", "style", "popupStyle", "popupClassName", "position", "onMaskClick", "getContainer", "afterShow", "afterClose", "stopPropagation", "Fragment", "children", "items", "displayName"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/picker/picker.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle, memo } from 'react';\nimport classNames from 'classnames';\nimport Popup from '../popup';\nimport { mergeProps } from '../../utils/with-default-props';\nimport { withNativeProps } from '../../utils/native-props';\nimport { usePropsValue } from '../../utils/use-props-value';\nimport PickerView from '../picker-view';\nimport { generateColumnsExtend, useColumnsExtend } from '../picker-view/columns-extend';\nimport { useConfig } from '../config-provider';\nimport { useMemoizedFn } from 'ahooks';\nimport SafeArea from '../safe-area';\nimport { defaultRenderLabel } from './picker-utils';\nconst classPrefix = `adm-picker`;\nconst defaultProps = {\n  defaultValue: [],\n  closeOnMaskClick: true,\n  renderLabel: defaultRenderLabel,\n  destroyOnClose: false,\n  forceRender: false\n};\nexport const Picker = memo(forwardRef((p, ref) => {\n  var _a;\n  const {\n    locale\n  } = useConfig();\n  const props = mergeProps(defaultProps, {\n    confirmText: locale.common.confirm,\n    cancelText: locale.common.cancel\n  }, p);\n  const [visible, setVisible] = usePropsValue({\n    value: props.visible,\n    defaultValue: false,\n    onChange: v => {\n      var _a;\n      if (v === false) {\n        (_a = props.onClose) === null || _a === void 0 ? void 0 : _a.call(props);\n      }\n    }\n  });\n  const actions = {\n    toggle: () => {\n      setVisible(v => !v);\n    },\n    open: () => {\n      setVisible(true);\n    },\n    close: () => {\n      setVisible(false);\n    }\n  };\n  useImperativeHandle(ref, () => actions);\n  const [value, setValue] = usePropsValue(Object.assign(Object.assign({}, props), {\n    onChange: val => {\n      var _a;\n      const extend = generateColumnsExtend(props.columns, val);\n      (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(props, val, extend);\n    }\n  }));\n  const extend = useColumnsExtend(props.columns, value);\n  const [innerValue, setInnerValue] = useState(value);\n  useEffect(() => {\n    if (innerValue !== value) {\n      setInnerValue(value);\n    }\n  }, [visible]);\n  useEffect(() => {\n    if (!visible) {\n      setInnerValue(value);\n    }\n  }, [value]);\n  const onChange = useMemoizedFn((val, ext) => {\n    var _a;\n    setInnerValue(val);\n    if (visible) {\n      (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, val, ext);\n    }\n  });\n  const pickerElement = withNativeProps(props, React.createElement(\"div\", {\n    className: classPrefix\n  }, React.createElement(\"div\", {\n    className: `${classPrefix}-header`\n  }, React.createElement(\"a\", {\n    role: 'button',\n    className: `${classPrefix}-header-button`,\n    onClick: () => {\n      var _a;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    }\n  }, props.cancelText), React.createElement(\"div\", {\n    className: `${classPrefix}-header-title`\n  }, props.title), React.createElement(\"a\", {\n    role: 'button',\n    className: classNames(`${classPrefix}-header-button`, props.loading && `${classPrefix}-header-button-disabled`),\n    onClick: () => {\n      if (props.loading) return;\n      setValue(innerValue, true);\n      setVisible(false);\n    },\n    \"aria-disabled\": props.loading\n  }, props.confirmText)), React.createElement(\"div\", {\n    className: `${classPrefix}-body`\n  }, React.createElement(PickerView, {\n    loading: props.loading,\n    loadingContent: props.loadingContent,\n    columns: props.columns,\n    renderLabel: props.renderLabel,\n    value: innerValue,\n    mouseWheel: props.mouseWheel,\n    onChange: onChange\n  }))));\n  const popupElement = React.createElement(Popup, {\n    style: props.popupStyle,\n    className: classNames(`${classPrefix}-popup`, props.popupClassName),\n    visible: visible,\n    position: 'bottom',\n    onMaskClick: () => {\n      var _a;\n      if (!props.closeOnMaskClick) return;\n      (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(props);\n      setVisible(false);\n    },\n    getContainer: props.getContainer,\n    destroyOnClose: props.destroyOnClose,\n    afterShow: props.afterShow,\n    afterClose: props.afterClose,\n    onClick: props.onClick,\n    forceRender: props.forceRender,\n    stopPropagation: props.stopPropagation\n  }, pickerElement, React.createElement(SafeArea, {\n    position: 'bottom'\n  }));\n  return React.createElement(React.Fragment, null, popupElement, (_a = props.children) === null || _a === void 0 ? void 0 : _a.call(props, extend.items, actions));\n}));\nPicker.displayName = 'Picker';"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,IAAI,QAAQ,OAAO;AACzF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,qBAAqB,EAAEC,gBAAgB,QAAQ,+BAA+B;AACvF,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,aAAa,QAAQ,QAAQ;AACtC,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,MAAMC,WAAW,GAAG,YAAY;AAChC,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,EAAE;EAChBC,gBAAgB,EAAE,IAAI;EACtBC,WAAW,EAAEL,kBAAkB;EAC/BM,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,MAAMC,MAAM,GAAGpB,IAAI,CAACF,UAAU,CAAC,CAACuB,CAAC,EAAEC,GAAG,KAAK;EAChD,IAAIC,EAAE;EACN,MAAM;IACJC;EACF,CAAC,GAAGf,SAAS,CAAC,CAAC;EACf,MAAMgB,KAAK,GAAGtB,UAAU,CAACW,YAAY,EAAE;IACrCY,WAAW,EAAEF,MAAM,CAACG,MAAM,CAACC,OAAO;IAClCC,UAAU,EAAEL,MAAM,CAACG,MAAM,CAACG;EAC5B,CAAC,EAAET,CAAC,CAAC;EACL,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAG3B,aAAa,CAAC;IAC1C4B,KAAK,EAAER,KAAK,CAACM,OAAO;IACpBhB,YAAY,EAAE,KAAK;IACnBmB,QAAQ,EAAEC,CAAC,IAAI;MACb,IAAIZ,EAAE;MACN,IAAIY,CAAC,KAAK,KAAK,EAAE;QACf,CAACZ,EAAE,GAAGE,KAAK,CAACW,OAAO,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,CAAC;MAC1E;IACF;EACF,CAAC,CAAC;EACF,MAAMa,OAAO,GAAG;IACdC,MAAM,EAAEA,CAAA,KAAM;MACZP,UAAU,CAACG,CAAC,IAAI,CAACA,CAAC,CAAC;IACrB,CAAC;IACDK,IAAI,EAAEA,CAAA,KAAM;MACVR,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC;IACDS,KAAK,EAAEA,CAAA,KAAM;MACXT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACDjC,mBAAmB,CAACuB,GAAG,EAAE,MAAMgB,OAAO,CAAC;EACvC,MAAM,CAACL,KAAK,EAAES,QAAQ,CAAC,GAAGrC,aAAa,CAACsC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,KAAK,CAAC,EAAE;IAC9ES,QAAQ,EAAEW,GAAG,IAAI;MACf,IAAItB,EAAE;MACN,MAAMuB,MAAM,GAAGvC,qBAAqB,CAACkB,KAAK,CAACsB,OAAO,EAAEF,GAAG,CAAC;MACxD,CAACtB,EAAE,GAAGE,KAAK,CAACuB,SAAS,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,EAAEoB,GAAG,EAAEC,MAAM,CAAC;IACzF;EACF,CAAC,CAAC,CAAC;EACH,MAAMA,MAAM,GAAGtC,gBAAgB,CAACiB,KAAK,CAACsB,OAAO,EAAEd,KAAK,CAAC;EACrD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAACqC,KAAK,CAAC;EACnDpC,SAAS,CAAC,MAAM;IACd,IAAIoD,UAAU,KAAKhB,KAAK,EAAE;MACxBiB,aAAa,CAACjB,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;EACblC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,OAAO,EAAE;MACZmB,aAAa,CAACjB,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,MAAMC,QAAQ,GAAGxB,aAAa,CAAC,CAACmC,GAAG,EAAEM,GAAG,KAAK;IAC3C,IAAI5B,EAAE;IACN2B,aAAa,CAACL,GAAG,CAAC;IAClB,IAAId,OAAO,EAAE;MACX,CAACR,EAAE,GAAGE,KAAK,CAAC2B,QAAQ,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,EAAEoB,GAAG,EAAEM,GAAG,CAAC;IACrF;EACF,CAAC,CAAC;EACF,MAAME,aAAa,GAAGjD,eAAe,CAACqB,KAAK,EAAE9B,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;IACtEC,SAAS,EAAE1C;EACb,CAAC,EAAElB,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;IAC5BC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAElB,KAAK,CAAC2D,aAAa,CAAC,GAAG,EAAE;IAC1BE,IAAI,EAAE,QAAQ;IACdD,SAAS,EAAE,GAAG1C,WAAW,gBAAgB;IACzC4C,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIlC,EAAE;MACN,CAACA,EAAE,GAAGE,KAAK,CAACiC,QAAQ,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,CAAC;MACzEO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAEP,KAAK,CAACI,UAAU,CAAC,EAAElC,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;IAC/CC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAEY,KAAK,CAACkC,KAAK,CAAC,EAAEhE,KAAK,CAAC2D,aAAa,CAAC,GAAG,EAAE;IACxCE,IAAI,EAAE,QAAQ;IACdD,SAAS,EAAEtD,UAAU,CAAC,GAAGY,WAAW,gBAAgB,EAAEY,KAAK,CAACmC,OAAO,IAAI,GAAG/C,WAAW,yBAAyB,CAAC;IAC/G4C,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIhC,KAAK,CAACmC,OAAO,EAAE;MACnBlB,QAAQ,CAACO,UAAU,EAAE,IAAI,CAAC;MAC1BjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACD,eAAe,EAAEP,KAAK,CAACmC;EACzB,CAAC,EAAEnC,KAAK,CAACC,WAAW,CAAC,CAAC,EAAE/B,KAAK,CAAC2D,aAAa,CAAC,KAAK,EAAE;IACjDC,SAAS,EAAE,GAAG1C,WAAW;EAC3B,CAAC,EAAElB,KAAK,CAAC2D,aAAa,CAAChD,UAAU,EAAE;IACjCsD,OAAO,EAAEnC,KAAK,CAACmC,OAAO;IACtBC,cAAc,EAAEpC,KAAK,CAACoC,cAAc;IACpCd,OAAO,EAAEtB,KAAK,CAACsB,OAAO;IACtB9B,WAAW,EAAEQ,KAAK,CAACR,WAAW;IAC9BgB,KAAK,EAAEgB,UAAU;IACjBa,UAAU,EAAErC,KAAK,CAACqC,UAAU;IAC5B5B,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,MAAM6B,YAAY,GAAGpE,KAAK,CAAC2D,aAAa,CAACpD,KAAK,EAAE;IAC9C8D,KAAK,EAAEvC,KAAK,CAACwC,UAAU;IACvBV,SAAS,EAAEtD,UAAU,CAAC,GAAGY,WAAW,QAAQ,EAAEY,KAAK,CAACyC,cAAc,CAAC;IACnEnC,OAAO,EAAEA,OAAO;IAChBoC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAEA,CAAA,KAAM;MACjB,IAAI7C,EAAE;MACN,IAAI,CAACE,KAAK,CAACT,gBAAgB,EAAE;MAC7B,CAACO,EAAE,GAAGE,KAAK,CAACiC,QAAQ,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,CAAC;MACzEO,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACDqC,YAAY,EAAE5C,KAAK,CAAC4C,YAAY;IAChCnD,cAAc,EAAEO,KAAK,CAACP,cAAc;IACpCoD,SAAS,EAAE7C,KAAK,CAAC6C,SAAS;IAC1BC,UAAU,EAAE9C,KAAK,CAAC8C,UAAU;IAC5Bd,OAAO,EAAEhC,KAAK,CAACgC,OAAO;IACtBtC,WAAW,EAAEM,KAAK,CAACN,WAAW;IAC9BqD,eAAe,EAAE/C,KAAK,CAAC+C;EACzB,CAAC,EAAEnB,aAAa,EAAE1D,KAAK,CAAC2D,aAAa,CAAC3C,QAAQ,EAAE;IAC9CwD,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;EACH,OAAOxE,KAAK,CAAC2D,aAAa,CAAC3D,KAAK,CAAC8E,QAAQ,EAAE,IAAI,EAAEV,YAAY,EAAE,CAACxC,EAAE,GAAGE,KAAK,CAACiD,QAAQ,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACZ,KAAK,EAAEqB,MAAM,CAAC6B,KAAK,EAAErC,OAAO,CAAC,CAAC;AAClK,CAAC,CAAC,CAAC;AACHlB,MAAM,CAACwD,WAAW,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}