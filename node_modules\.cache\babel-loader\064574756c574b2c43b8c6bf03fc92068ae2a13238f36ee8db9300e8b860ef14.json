{"ast": null, "code": "import React, { useRef, useState } from 'react';\nimport { useDrag } from '@use-gesture/react';\nimport { ThumbIcon } from './thumb-icon';\nimport Popover from '../popover';\nimport { useConfig } from '../config-provider';\nconst classPrefix = `adm-slider`;\nconst Thumb = props => {\n  const {\n    value,\n    min,\n    max,\n    disabled,\n    icon,\n    residentPopover,\n    onDrag\n  } = props;\n  const prevValue = useRef(value);\n  const {\n    locale\n  } = useConfig();\n  const currentPosition = () => {\n    return {\n      left: `${(value - min) / (max - min) * 100}%`,\n      right: 'auto'\n    };\n  };\n  const [dragging, setDragging] = useState(false);\n  const bind = useDrag(state => {\n    var _a;\n    if (disabled) return;\n    if (state.first) {\n      prevValue.current = value;\n    }\n    const x = state.xy[0] - state.initial[0];\n    const sliderOffsetWith = (_a = props.trackRef.current) === null || _a === void 0 ? void 0 : _a.offsetWidth;\n    if (!sliderOffsetWith) return;\n    const diff = x / Math.ceil(sliderOffsetWith) * (max - min);\n    onDrag(prevValue.current + diff, state.first, state.last);\n    setDragging(!state.last);\n  }, {\n    axis: 'x',\n    pointer: {\n      touch: true\n    }\n  });\n  const renderPopoverContent = typeof props.popover === 'function' ? props.popover : props.popover ? value => value.toString() : null;\n  const thumbElement = React.createElement(\"div\", {\n    className: `${classPrefix}-thumb`\n  }, icon ? icon : React.createElement(ThumbIcon, {\n    className: `${classPrefix}-thumb-icon`\n  }));\n  return React.createElement(\"div\", Object.assign({\n    className: `${classPrefix}-thumb-container`,\n    style: currentPosition()\n  }, bind(), {\n    role: 'slider',\n    \"aria-label\": props['aria-label'] || locale.Slider.name,\n    \"aria-valuemax\": max,\n    \"aria-valuemin\": min,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled\n  }), renderPopoverContent ? React.createElement(Popover, {\n    content: renderPopoverContent(value),\n    placement: 'top',\n    visible: residentPopover || dragging,\n    getContainer: null,\n    mode: 'dark'\n  }, thumbElement) : thumbElement);\n};\nexport default Thumb;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}