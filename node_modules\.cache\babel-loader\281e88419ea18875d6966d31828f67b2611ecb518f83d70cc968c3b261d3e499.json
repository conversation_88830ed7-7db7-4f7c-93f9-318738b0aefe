{"ast": null, "code": "import \"./modal.css\";\nimport { show } from './show';\nimport { alert } from './alert';\nimport { confirm } from './confirm';\nimport { clear } from './clear';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Modal } from './modal';\nexport default attachPropertiesToComponent(Modal, {\n  show,\n  alert,\n  confirm,\n  clear\n});", "map": {"version": 3, "names": ["show", "alert", "confirm", "clear", "attachPropertiesToComponent", "Modal"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/modal/index.js"], "sourcesContent": ["import \"./modal.css\";\nimport { show } from './show';\nimport { alert } from './alert';\nimport { confirm } from './confirm';\nimport { clear } from './clear';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { Modal } from './modal';\nexport default attachPropertiesToComponent(Modal, {\n  show,\n  alert,\n  confirm,\n  clear\n});"], "mappings": "AAAA,OAAO,aAAa;AACpB,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,KAAK,QAAQ,SAAS;AAC/B,eAAeD,2BAA2B,CAACC,KAAK,EAAE;EAChDL,IAAI;EACJC,KAAK;EACLC,OAAO;EACPC;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}