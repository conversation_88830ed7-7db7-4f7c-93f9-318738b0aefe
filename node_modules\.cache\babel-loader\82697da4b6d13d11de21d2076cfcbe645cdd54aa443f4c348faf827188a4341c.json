{"ast": null, "code": "import \"./swiper.css\";\nimport { Swiper } from './swiper';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { SwiperItem } from './swiper-item';\nexport default attachPropertiesToComponent(Swiper, {\n  Item: SwiperItem\n});", "map": {"version": 3, "names": ["Swiper", "attachPropertiesToComponent", "SwiperItem", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/swiper/index.js"], "sourcesContent": ["import \"./swiper.css\";\nimport { Swiper } from './swiper';\nimport { attachPropertiesToComponent } from '../../utils/attach-properties-to-component';\nimport { SwiperItem } from './swiper-item';\nexport default attachPropertiesToComponent(Swiper, {\n  Item: SwiperItem\n});"], "mappings": "AAAA,OAAO,cAAc;AACrB,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,2BAA2B,QAAQ,4CAA4C;AACxF,SAASC,UAAU,QAAQ,eAAe;AAC1C,eAAeD,2BAA2B,CAACD,MAAM,EAAE;EACjDG,IAAI,EAAED;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}