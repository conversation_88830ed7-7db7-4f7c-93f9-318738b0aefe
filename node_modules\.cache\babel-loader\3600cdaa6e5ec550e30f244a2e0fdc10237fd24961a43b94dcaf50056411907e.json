{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\nimport * as React from 'react';\nimport OriginCSSMotion from \"./CSSMotion\";\nimport { diffKeys, parseKeys, STATUS_ADD, STATUS_KEEP, STATUS_REMOVE, STATUS_REMOVED } from \"./util/diff\";\nimport { supportTransition } from \"./util/motion\";\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nexport function genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : OriginCSSMotion;\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    _inherits(CSSMotionList, _React$Component);\n    var _super = _createSuper(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      _classCallCheck(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      _defineProperty(_assertThisInitialized(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      _defineProperty(_assertThisInitialized(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return _objectSpread(_objectSpread({}, entity), {}, {\n              status: STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    _createClass(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = _objectWithoutProperties(_this$props, _excluded);\n        var Component = component || React.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/React.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = _objectWithoutProperties(_ref2, _excluded2);\n          var visible = status === STATUS_ADD || status === STATUS_KEEP;\n          return /*#__PURE__*/React.createElement(CSSMotion, _extends({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children(_objectSpread(_objectSpread({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = parseKeys(keys);\n        var mixedKeyEntities = diffKeys(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === STATUS_REMOVED && entity.status === STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(React.Component);\n  _defineProperty(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\nexport default genCSSMotionList(supportTransition);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}