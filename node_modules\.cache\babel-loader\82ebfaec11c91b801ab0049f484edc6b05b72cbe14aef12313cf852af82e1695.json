{"ast": null, "code": "import { useEvent } from 'rc-util';\nimport React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport runes from 'runes2';\nconst ELLIPSIS_TEXT = '...';\nconst measureStyle = {\n  visibility: 'hidden',\n  whiteSpace: 'inherit',\n  lineHeight: 'inherit',\n  fontSize: 'inherit'\n};\nexport default function useMeasure(containerRef, content, rows, direction, expanded, expandNode, collapseNode) {\n  const contentChars = React.useMemo(() => runes(content), [content]);\n  const [maxHeight, setMaxHeight] = React.useState(0);\n  const [walkingIndexes, setWalkingIndexes] = React.useState([0, 0]);\n  const midIndex = Math.ceil((walkingIndexes[0] + walkingIndexes[1]) / 2);\n  const [status, setStatus] = React.useState(100 /* STABLE_NO_ELLIPSIS */);\n  // ============================ Refs ============================\n  const singleRowMeasureRef = React.useRef(null);\n  const fullMeasureRef = React.useRef(null);\n  const midMeasureRef = React.useRef(null);\n  const startMeasure = useEvent(() => {\n    // use batch update to avoid async update trigger 2 render\n    unstable_batchedUpdates(() => {\n      setStatus(1 /* PREPARE */);\n      setWalkingIndexes([0, direction === 'middle' ? Math.ceil(contentChars.length / 2) : contentChars.length]);\n    });\n  });\n  // Initialize\n  React.useLayoutEffect(() => {\n    startMeasure();\n  }, [contentChars, rows]);\n  // Measure element height\n  React.useLayoutEffect(() => {\n    var _a, _b;\n    if (status === 1 /* PREPARE */) {\n      const fullMeasureHeight = ((_a = fullMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      const singleRowMeasureHeight = ((_b = singleRowMeasureRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n      const rowMeasureHeight = singleRowMeasureHeight * (rows + 0.5);\n      if (fullMeasureHeight <= rowMeasureHeight) {\n        setStatus(100 /* STABLE_NO_ELLIPSIS */);\n      } else {\n        setMaxHeight(rowMeasureHeight);\n        setStatus(2 /* MEASURE_WALKING */);\n      }\n    }\n  }, [status]);\n  // Walking measure\n  React.useLayoutEffect(() => {\n    var _a;\n    if (status === 2 /* MEASURE_WALKING */) {\n      const diff = walkingIndexes[1] - walkingIndexes[0];\n      const midHeight = ((_a = midMeasureRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n      if (diff > 1) {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], midIndex]);\n        } else {\n          setWalkingIndexes([midIndex, walkingIndexes[1]]);\n        }\n      } else {\n        if (midHeight > maxHeight) {\n          setWalkingIndexes([walkingIndexes[0], walkingIndexes[0]]);\n        } else {\n          setWalkingIndexes([walkingIndexes[1], walkingIndexes[1]]);\n        }\n        setStatus(99 /* STABLE_ELLIPSIS */);\n      }\n    }\n  }, [status, walkingIndexes]);\n  // =========================== Render ===========================\n  /** Render by cut index */\n  const renderContent = index => {\n    const prefixContent = contentChars.slice(0, index);\n    const suffixContent = contentChars.slice(contentChars.length - index);\n    return React.createElement(React.Fragment, null, direction === 'start' && React.createElement(React.Fragment, null, expandNode, ELLIPSIS_TEXT), direction !== 'start' && prefixContent.join(''), direction === 'middle' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode, ELLIPSIS_TEXT), direction !== 'end' && suffixContent.join(''), direction === 'end' && React.createElement(React.Fragment, null, ELLIPSIS_TEXT, expandNode));\n  };\n  const finalContent = React.useMemo(() => {\n    if (expanded || status === 100 /* STABLE_NO_ELLIPSIS */) {\n      return React.createElement(React.Fragment, {\n        key: 'display'\n      }, content, status === 99 /* STABLE_ELLIPSIS */ && collapseNode);\n    }\n    if (status === 99 /* STABLE_ELLIPSIS */) {\n      return renderContent(midIndex);\n    }\n    return null;\n  }, [expanded, status, content, collapseNode, midIndex]);\n  const allNodes = React.createElement(React.Fragment, null, status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'full',\n    \"aria-hidden\": true,\n    ref: fullMeasureRef,\n    style: measureStyle\n  }, content, expandNode), status === 1 /* PREPARE */ && React.createElement(\"div\", {\n    key: 'stable',\n    \"aria-hidden\": true,\n    ref: singleRowMeasureRef,\n    style: measureStyle\n  }, '\\u00A0'), status === 2 /* MEASURE_WALKING */ && React.createElement(\"div\", {\n    key: 'walking-mid',\n    \"aria-hidden\": true,\n    ref: midMeasureRef,\n    style: measureStyle\n  }, renderContent(midIndex)), finalContent);\n  return [allNodes, startMeasure];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}