{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport { setCache, getCache } from '../utils/cache';\nimport { setCachePromise, getCachePromise } from '../utils/cachePromise';\nimport { trigger, subscribe } from '../utils/cacheSubscribe';\nvar useCachePlugin = function (fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n    _b = _a.cacheTime,\n    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n    _c = _a.staleTime,\n    staleTime = _c === void 0 ? 0 : _c,\n    customSetCache = _a.setCache,\n    customGetCache = _a.getCache;\n  var unSubscribeRef = useRef();\n  var currentPromiseRef = useRef();\n  var _setCache = function (key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      setCache(key, cacheTime, cachedData);\n    }\n    trigger(key, cachedData.data);\n  };\n  var _getCache = function (key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n    return getCache(key);\n  };\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    }\n    // get data from cache when init\n    var cacheData = _getCache(cacheKey);\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    }\n    // subscribe same cachekey update, trigger update\n    unSubscribeRef.current = subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n  if (!cacheKey) {\n    return {};\n  }\n  return {\n    onBefore: function (params) {\n      var cacheData = _getCache(cacheKey, params);\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      }\n      // If the data is fresh, stop request\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function (service, args) {\n      var servicePromise = getCachePromise(cacheKey);\n      // If has servicePromise, and is not trigger by self, then use it\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function (data, params) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function (data) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trigger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\nexport default useCachePlugin;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useRef", "useCreation", "useUnmount", "setCache", "getCache", "setCachePromise", "getCachePromise", "trigger", "subscribe", "useCachePlugin", "fetchInstance", "_a", "cache<PERSON>ey", "_b", "cacheTime", "_c", "staleTime", "customSetCache", "customGetCache", "unSubscribeRef", "currentPromiseRef", "_setCache", "key", "cachedData", "data", "_getCache", "params", "cacheData", "Object", "hasOwnProperty", "call", "state", "Date", "getTime", "time", "loading", "current", "setState", "onBefore", "error", "undefined", "returnNow", "onRequest", "service", "args", "servicePromise", "apply", "onSuccess", "d", "onMutate"], "sources": ["C:/Users/<USER>/node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport { setCache, getCache } from '../utils/cache';\nimport { setCachePromise, getCachePromise } from '../utils/cachePromise';\nimport { trigger, subscribe } from '../utils/cacheSubscribe';\nvar useCachePlugin = function (fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n    _b = _a.cacheTime,\n    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n    _c = _a.staleTime,\n    staleTime = _c === void 0 ? 0 : _c,\n    customSetCache = _a.setCache,\n    customGetCache = _a.getCache;\n  var unSubscribeRef = useRef();\n  var currentPromiseRef = useRef();\n  var _setCache = function (key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      setCache(key, cacheTime, cachedData);\n    }\n    trigger(key, cachedData.data);\n  };\n  var _getCache = function (key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n    return getCache(key);\n  };\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    }\n    // get data from cache when init\n    var cacheData = _getCache(cacheKey);\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    }\n    // subscribe same cachekey update, trigger update\n    unSubscribeRef.current = subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n  if (!cacheKey) {\n    return {};\n  }\n  return {\n    onBefore: function (params) {\n      var cacheData = _getCache(cacheKey, params);\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      }\n      // If the data is fresh, stop request\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function (service, args) {\n      var servicePromise = getCachePromise(cacheKey);\n      // If has servicePromise, and is not trigger by self, then use it\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function (data, params) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function (data) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trigger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\nexport default useCachePlugin;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,MAAM,QAAQ,OAAO;AAC9B,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACnD,SAASC,eAAe,EAAEC,eAAe,QAAQ,uBAAuB;AACxE,SAASC,OAAO,EAAEC,SAAS,QAAQ,yBAAyB;AAC5D,IAAIC,cAAc,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EAChD,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;IACxBC,EAAE,GAAGF,EAAE,CAACG,SAAS;IACjBA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAGA,EAAE;IAC9CE,EAAE,GAAGJ,EAAE,CAACK,SAAS;IACjBA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IAClCE,cAAc,GAAGN,EAAE,CAACR,QAAQ;IAC5Be,cAAc,GAAGP,EAAE,CAACP,QAAQ;EAC9B,IAAIe,cAAc,GAAGnB,MAAM,CAAC,CAAC;EAC7B,IAAIoB,iBAAiB,GAAGpB,MAAM,CAAC,CAAC;EAChC,IAAIqB,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAEC,UAAU,EAAE;IACzC,IAAIN,cAAc,EAAE;MAClBA,cAAc,CAACM,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLpB,QAAQ,CAACmB,GAAG,EAAER,SAAS,EAAES,UAAU,CAAC;IACtC;IACAhB,OAAO,CAACe,GAAG,EAAEC,UAAU,CAACC,IAAI,CAAC;EAC/B,CAAC;EACD,IAAIC,SAAS,GAAG,SAAAA,CAAUH,GAAG,EAAEI,MAAM,EAAE;IACrC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG,EAAE;IACb;IACA,IAAIR,cAAc,EAAE;MAClB,OAAOA,cAAc,CAACQ,MAAM,CAAC;IAC/B;IACA,OAAOtB,QAAQ,CAACkB,GAAG,CAAC;EACtB,CAAC;EACDrB,WAAW,CAAC,YAAY;IACtB,IAAI,CAACW,QAAQ,EAAE;MACb;IACF;IACA;IACA,IAAIe,SAAS,GAAGF,SAAS,CAACb,QAAQ,CAAC;IACnC,IAAIe,SAAS,IAAIC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACH,SAAS,EAAE,MAAM,CAAC,EAAE;MAC9DjB,aAAa,CAACqB,KAAK,CAACP,IAAI,GAAGG,SAAS,CAACH,IAAI;MACzCd,aAAa,CAACqB,KAAK,CAACL,MAAM,GAAGC,SAAS,CAACD,MAAM;MAC7C,IAAIV,SAAS,KAAK,CAAC,CAAC,IAAI,IAAIgB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGN,SAAS,CAACO,IAAI,IAAIlB,SAAS,EAAE;QAC1EN,aAAa,CAACqB,KAAK,CAACI,OAAO,GAAG,KAAK;MACrC;IACF;IACA;IACAhB,cAAc,CAACiB,OAAO,GAAG5B,SAAS,CAACI,QAAQ,EAAE,UAAUY,IAAI,EAAE;MAC3Dd,aAAa,CAAC2B,QAAQ,CAAC;QACrBb,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNtB,UAAU,CAAC,YAAY;IACrB,IAAIS,EAAE;IACN,CAACA,EAAE,GAAGQ,cAAc,CAACiB,OAAO,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACX,cAAc,CAAC;EAC5F,CAAC,CAAC;EACF,IAAI,CAACP,QAAQ,EAAE;IACb,OAAO,CAAC,CAAC;EACX;EACA,OAAO;IACL0B,QAAQ,EAAE,SAAAA,CAAUZ,MAAM,EAAE;MAC1B,IAAIC,SAAS,GAAGF,SAAS,CAACb,QAAQ,EAAEc,MAAM,CAAC;MAC3C,IAAI,CAACC,SAAS,IAAI,CAACC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACH,SAAS,EAAE,MAAM,CAAC,EAAE;QAChE,OAAO,CAAC,CAAC;MACX;MACA;MACA,IAAIX,SAAS,KAAK,CAAC,CAAC,IAAI,IAAIgB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGN,SAAS,CAACO,IAAI,IAAIlB,SAAS,EAAE;QAC1E,OAAO;UACLmB,OAAO,EAAE,KAAK;UACdX,IAAI,EAAEG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACH,IAAI;UAC1Ee,KAAK,EAAEC,SAAS;UAChBC,SAAS,EAAE;QACb,CAAC;MACH,CAAC,MAAM;QACL;QACA,OAAO;UACLjB,IAAI,EAAEG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACH,IAAI;UAC1Ee,KAAK,EAAEC;QACT,CAAC;MACH;IACF,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAUC,OAAO,EAAEC,IAAI,EAAE;MAClC,IAAIC,cAAc,GAAGvC,eAAe,CAACM,QAAQ,CAAC;MAC9C;MACA,IAAIiC,cAAc,IAAIA,cAAc,KAAKzB,iBAAiB,CAACgB,OAAO,EAAE;QAClE,OAAO;UACLS,cAAc,EAAEA;QAClB,CAAC;MACH;MACAA,cAAc,GAAGF,OAAO,CAACG,KAAK,CAAC,KAAK,CAAC,EAAE/C,aAAa,CAAC,EAAE,EAAED,MAAM,CAAC8C,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;MAC9ExB,iBAAiB,CAACgB,OAAO,GAAGS,cAAc;MAC1CxC,eAAe,CAACO,QAAQ,EAAEiC,cAAc,CAAC;MACzC,OAAO;QACLA,cAAc,EAAEA;MAClB,CAAC;IACH,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAUvB,IAAI,EAAEE,MAAM,EAAE;MACjC,IAAIf,EAAE;MACN,IAAIC,QAAQ,EAAE;QACZ;QACA,CAACD,EAAE,GAAGQ,cAAc,CAACiB,OAAO,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACX,cAAc,CAAC;QAC1FE,SAAS,CAACT,QAAQ,EAAE;UAClBY,IAAI,EAAEA,IAAI;UACVE,MAAM,EAAEA,MAAM;UACdQ,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;QAC3B,CAAC,CAAC;QACF;QACAd,cAAc,CAACiB,OAAO,GAAG5B,SAAS,CAACI,QAAQ,EAAE,UAAUoC,CAAC,EAAE;UACxDtC,aAAa,CAAC2B,QAAQ,CAAC;YACrBb,IAAI,EAAEwB;UACR,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUzB,IAAI,EAAE;MACxB,IAAIb,EAAE;MACN,IAAIC,QAAQ,EAAE;QACZ;QACA,CAACD,EAAE,GAAGQ,cAAc,CAACiB,OAAO,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACX,cAAc,CAAC;QAC1FE,SAAS,CAACT,QAAQ,EAAE;UAClBY,IAAI,EAAEA,IAAI;UACVE,MAAM,EAAEhB,aAAa,CAACqB,KAAK,CAACL,MAAM;UAClCQ,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;QAC3B,CAAC,CAAC;QACF;QACAd,cAAc,CAACiB,OAAO,GAAG5B,SAAS,CAACI,QAAQ,EAAE,UAAUoC,CAAC,EAAE;UACxDtC,aAAa,CAAC2B,QAAQ,CAAC;YACrBb,IAAI,EAAEwB;UACR,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAevC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}