{"ast": null, "code": "export function bound(position, min, max) {\n  let ret = position;\n  if (min !== undefined) {\n    ret = Math.max(position, min);\n  }\n  if (max !== undefined) {\n    ret = Math.min(ret, max);\n  }\n  return ret;\n}", "map": {"version": 3, "names": ["bound", "position", "min", "max", "ret", "undefined", "Math"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/utils/bound.js"], "sourcesContent": ["export function bound(position, min, max) {\n  let ret = position;\n  if (min !== undefined) {\n    ret = Math.max(position, min);\n  }\n  if (max !== undefined) {\n    ret = Math.min(ret, max);\n  }\n  return ret;\n}"], "mappings": "AAAA,OAAO,SAASA,KAAKA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACxC,IAAIC,GAAG,GAAGH,QAAQ;EAClB,IAAIC,GAAG,KAAKG,SAAS,EAAE;IACrBD,GAAG,GAAGE,IAAI,CAACH,GAAG,CAACF,QAAQ,EAAEC,GAAG,CAAC;EAC/B;EACA,IAAIC,GAAG,KAAKE,SAAS,EAAE;IACrBD,GAAG,GAAGE,IAAI,CAACJ,GAAG,CAACE,GAAG,EAAED,GAAG,CAAC;EAC1B;EACA,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}