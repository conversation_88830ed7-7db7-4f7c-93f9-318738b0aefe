{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafTimeout = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n  var handle = {\n    id: 0\n  };\n  var startTime = new Date().getTime();\n  var loop = function () {\n    var current = new Date().getTime();\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafTimeout = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafTimeout;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}