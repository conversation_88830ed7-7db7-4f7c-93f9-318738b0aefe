{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var target = val;\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n  return target;\n}\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var _a = __read(useState(function () {\n      return getTargetValue(initialValue, {\n        min: min,\n        max: max\n      });\n    }), 2),\n    current = _a[0],\n    setCurrent = _a[1];\n  var setValue = function (value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n  var inc = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n  var dec = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n  var set = function (value) {\n    setValue(value);\n  };\n  var reset = function () {\n    setValue(initialValue);\n  };\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useCounter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}