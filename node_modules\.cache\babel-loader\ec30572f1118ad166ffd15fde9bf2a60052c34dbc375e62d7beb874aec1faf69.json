{"ast": null, "code": "import { ErrorBlock } from './error-block';\nexport { createErrorBlock } from './create-error-block';\nexport default ErrorBlock;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createErrorBlock"], "sources": ["C:/Users/<USER>/node_modules/antd-mobile/es/components/error-block/index.js"], "sourcesContent": ["import { ErrorBlock } from './error-block';\nexport { createErrorBlock } from './create-error-block';\nexport default ErrorBlock;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}